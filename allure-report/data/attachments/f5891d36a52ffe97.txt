INFO     root:general.py:54 ================================================================================
INFO     root:general.py:54 Test Case Name: test_edit_message
INFO     root:general.py:54 ================================================================================
INFO     root:general.py:54 Test Case ID: 6477568
                                 Test Case Title: Edit QR message (bulk mode)
                                 Test Case Pre-requisite: None
                                 Test Case Steps:
                                      None
                                 Test Case Expect Result: None
                                 Test Case Priority: critical
INFO     root:general.py:54 ================================================================================
DEBUG    root:general.py:58 		KTF: set()
DEBUG    root:general.py:58 		KTF TO DELETE: set()
WARNING  root:general.py:66 warning
INFO     root:general.py:54 API - Gb1Settings: Reset
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&reset=1 HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Reset returns: None
INFO     root:general.py:54 API - Gb1Settings: Reset impersonation mode 
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-beta.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-beta.gubagoo.com:443 "GET /admin_tools/debug/impersonation/reset.php?impersonator_id=202890 HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Reset impersonation mode resultNo active sessions
INFO     root:general.py:54 API - Gb1Settings: Reset impersonation mode  returns: No active sessions
INFO     root:general.py:54 API - Gb1Settings: Set CBO Payments Dealer Id setting
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&settings%5Bcbo_payments_provider%5D=rey_rey HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Set CBO Payments Dealer Id setting returns: None
INFO     root:general.py:54 API - Gb1Settings: Set CBO Payments Dealer Id setting
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&settings%5Bcbo_payments_dealer_id%5D=16-01 HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Set CBO Payments Dealer Id setting returns: None
INFO     root:general.py:54 API - Gb1Settings: Set service scheduling provider setting
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&settings%5Bservice_scheduling_id%5D=xts9010&settings%5Bservice_scheduling_provider%5D=Xtime&settings%5Bera_system_number%5D=&settings%5Bservice_portal_profile_token%5D= HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Set service scheduling provider setting returns: None
INFO     root:general.py:54 API - Gb1Settings: Set offer form setting
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&offer%5Bid%5D=1583&offer%5Bform%5D=xtime&offer%5Bname%5D=Publisher+Scheduling+Offer+with+Xtime HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Set offer form setting returns: None
INFO     root:general.py:54 API - Gb1Settings: Reset
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202752&reset=1 HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Reset returns: None
INFO     root:general.py:54 API - Gb1Settings: Set hide transition messages setting
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&settings%5Bchat_onclick_hide_transition_msgs%5D=0 HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Set hide transition messages setting returns: None
INFO     root:general.py:54 API - Gb1Settings: Set Show all unassigned and queued chats setting
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&settings%5Bresq_self_managed%5D=0 HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Set Show all unassigned and queued chats setting returns: None
INFO     root:general.py:54 API - Gb1Settings: Set bundle setting
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&tbs%5Bbundle%5D=v4&tb%5Bdefault_state%5D=ui_v3&tbs%5Btheme%5D= HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Loader wait for regeneration by 'default_state'
DEBUG    root:general.py:58 		https://cdn.gubagoo.io/development/toolbars/202751/loader_202751_1.js
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): cdn.gubagoo.io:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://cdn.gubagoo.io:443 "GET /development/toolbars/202751/loader_202751_1.js HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Loader Current value of default_state is ui_v3!
INFO     root:general.py:54 API - Gb1Settings: Loader wait for regeneration by 'default_state' returns: True
INFO     root:general.py:54 API - Gb1Settings: Loader wait for regeneration by 'chat_version'
DEBUG    root:general.py:58 		https://cdn.gubagoo.io/development/toolbars/202751/loader_202751_1.js
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): cdn.gubagoo.io:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://cdn.gubagoo.io:443 "GET /development/toolbars/202751/loader_202751_1.js HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Loader Current value of chat_version is v4!
INFO     root:general.py:54 API - Gb1Settings: Loader wait for regeneration by 'chat_version' returns: True
INFO     root:general.py:54 API - Gb1Settings: Set bundle setting returns: None
INFO     root:general.py:54 API - Gb1Settings: Set bundle setting
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202752&tbs%5Bbundle%5D=v4&tb%5Bdefault_state%5D=ui_v3&tbs%5Btheme%5D= HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Loader wait for regeneration by 'default_state'
DEBUG    root:general.py:58 		https://cdn.gubagoo.io/development/toolbars/202752/loader_202752_1.js
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): cdn.gubagoo.io:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://cdn.gubagoo.io:443 "GET /development/toolbars/202752/loader_202752_1.js HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Loader Current value of default_state is ui_v3!
INFO     root:general.py:54 API - Gb1Settings: Loader wait for regeneration by 'default_state' returns: True
INFO     root:general.py:54 API - Gb1Settings: Loader wait for regeneration by 'chat_version'
DEBUG    root:general.py:58 		https://cdn.gubagoo.io/development/toolbars/202752/loader_202752_1.js
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): cdn.gubagoo.io:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://cdn.gubagoo.io:443 "GET /development/toolbars/202752/loader_202752_1.js HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Loader Current value of chat_version is v4!
INFO     root:general.py:54 API - Gb1Settings: Loader wait for regeneration by 'chat_version' returns: True
INFO     root:general.py:54 API - Gb1Settings: Set bundle setting returns: None
INFO     root:general.py:54 API - Gb1Settings: Chat reset
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "GET /admin_tools/qa_tool.php?account_id=202751&chat_reset=1 HTTP/1.1" 200 None
INFO     root:general.py:54 API - Gb1Settings: Chat reset returns: None
DEBUG    root:general.py:58 		Page 'ChatConsolePage' existence verification. Wait time = 0
DEBUG    root:general.py:58 		Open ChatConsolePage web page
INFO     root:general.py:54 WEB - ChatConsolePage: Open Browser and enter Chat Console Page URL
DEBUG    root:general.py:58 		Opening https://chat-alpha.gubagoo.com url
DEBUG    root:general.py:58 		Creating an instance of a Browser.
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:59 Selenium Manager binary found at: /Library/Frameworks/Python.framework/Versions/3.9/lib/python3.9/site-packages/selenium/webdriver/common/macos/selenium-manager
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:116 Executing process: /Library/Frameworks/Python.framework/Versions/3.9/lib/python3.9/site-packages/selenium/webdriver/common/macos/selenium-manager --browser chrome --debug --output json
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Checking chromedriver in PATH
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Running command: chromedriver --version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Output: ""
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chromedriver not found in PATH
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chrome detected at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Using shell command to find out chrome version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Running command: /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Output: "Google Chrome 127.0.6533.120 "
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Detected browser: chrome 127.0.6533.120
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Reading metadata from https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Required driver: chromedriver 127.0.6533.119
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chromedriver 127.0.6533.119 already in the cache
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Driver path: /Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Browser path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:94 Using driver at: /Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver
DEBUG    selenium.webdriver.common.service:service.py:216 Started executable: `/Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver` in a child process with pid: 53343
DEBUG    urllib3.connectionpool:connectionpool.py:227 Starting new HTTP connection (1): localhost:54839
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session HTTP/1.1" 200 892
DEBUG    root:general.py:58 		Browser version 127.0.6533.120
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/url HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Page 'ChatConsoleSignInPage' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ChatConsoleSignInPage: Login with '202751' account id 'chat_admin' user name and 'nZ5qP2nF5' password
DEBUG    root:general.py:58 		Clearing css selector: input[name='account_id'] input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.6/element HTTP/1.1" 200 125
DEBUG    root:general.py:58 		Clicks an "css selector: input[name='account_id']" element.
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Sending 202751 keys to the 'css selector: input[name='account_id']' input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.6/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.7/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.7/value HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clearing css selector: input[name='username'] input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.6/element HTTP/1.1" 200 125
DEBUG    root:general.py:58 		Clicks an "css selector: input[name='username']" element.
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Sending chat_admin keys to the 'css selector: input[name='username']' input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.6/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.9/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.9/value HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clearing css selector: input[name='password'] input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.6/element HTTP/1.1" 200 126
DEBUG    root:general.py:58 		Clicks an "css selector: input[name='password']" element.
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Sending nZ5qP2nF5 keys to the 'css selector: input[name='password']' input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.6/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.10/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.10/value HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "css selector: button[type='submit']" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.6/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.11/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.11/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ChatConsoleSignInPage: Login with '202751' account id 'chat_admin' user name and 'nZ5qP2nF5' password returns: None
DEBUG    root:general.py:58 		Page 'ChatConsolePage' existence verification. Wait time = 5
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    root:general.py:58 		Wait for css selector: .cc-loader spinner to appear and disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2181
WARNING  root:general.py:66 Spinner was not appeared after clicking submit button
INFO     root:general.py:54 WEB - ChatConsolePage: Open Browser and enter Chat Console Page URL returns: None
DEBUG    root:general.py:58 		Page 'ChatConsolePage' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Component 'QuickResponsesComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2184
DEBUG    root:general.py:58 		Component 'QuickResponsesComponent' was not found
DEBUG    root:general.py:58 		Open QuickResponsesComponent web component
INFO     root:general.py:54 WEB - QuickResponsesComponent: Open "Quick Replies" Component
INFO     root:general.py:54 WEB - ModalOperator: Open Quick Responses
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2186
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' was not found
DEBUG    root:general.py:58 		Open ModalOperatorComponent web component
INFO     root:general.py:54 WEB - ModalOperator: Open "Modal Operator" Component
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 404 2185
DEBUG    root:general.py:58 		Clicking on the "css selector: .cc-sidebar__user img" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.24/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.24/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Open "Modal Operator" Component returns: None
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "xpath: .//*[@class='modal-operator__links']/li/a[text()='Quick Responses']" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.33/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.34/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.34/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Open Quick Responses returns: None
INFO     root:general.py:54 WEB - QuickResponsesComponent: Open "Quick Replies" Component returns: None
DEBUG    root:general.py:58 		Component 'QuickResponsesComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - QuickResponsesComponent: Select account
DEBUG    root:general.py:58 		Wait for xpath: .//*[contains(@class, 'loadingIndicator')] spinner to appear and disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.41/text HTTP/1.1" 200 26
DEBUG    root:general.py:58 		Text from "SelectExtended" with css selector: .account-select locator - "Select account"
DEBUG    root:general.py:58 		Selecting e2e-chat-console-env-2 in the 'css selector: .account-select' select list
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 12
DEBUG    root:general.py:58 		Clicking on the "css selector: .account-select" "SelectExtended"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.41/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.41/click HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 128
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.43/text HTTP/1.1" 200 34
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.43/text HTTP/1.1" 200 34
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.43/click HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Wait for css selector: .list-loading spinner to appear and disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2184
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1649
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1649
INFO     root:general.py:54 WEB - QuickResponsesComponent: Select account returns: None
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.64/text HTTP/1.1" 200 34
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-DEALERSHIP LINKS-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -DEALERSHIP LINKS-Edit
DEBUG    root:general.py:58 		Component 'BulkEditComponent' existence verification. Wait time = 2
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    root:general.py:58 		Component 'BulkEditComponent' was not found
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get all messages of category
INFO     root:general.py:54 WEB - QuickResponsesComponent: Selected category
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.64/text HTTP/1.1" 200 34
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-DEALERSHIP LINKS-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -DEALERSHIP LINKS-Edit
DEBUG    root:general.py:58 		Selecting 'QrCategory.CUSTOMER_COMPLAINT_LEAD_GENERATION_SET_UP_CLOSE' section in 'css selector: .qr-category-list li' menu
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1649
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.50/text HTTP/1.1" 200 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.51/text HTTP/1.1" 200 38
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.52/text HTTP/1.1" 200 26
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.53/text HTTP/1.1" 200 32
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.54/text HTTP/1.1" 200 40
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.55/text HTTP/1.1" 200 35
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.56/text HTTP/1.1" 200 44
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.57/text HTTP/1.1" 200 39
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.58/text HTTP/1.1" 200 50
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.59/text HTTP/1.1" 200 45
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.60/text HTTP/1.1" 200 63
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.60/click HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Selected 'QrCategory.CUSTOMER_COMPLAINT_LEAD_GENERATION_SET_UP_CLOSE' section in 'css selector: .qr-category-list li' menu
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.64/text HTTP/1.1" 200 67
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.64/text HTTP/1.1" 200 67
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit
INFO     root:general.py:54 WEB - QuickResponsesComponent: Selected category returns: None
DEBUG    root:general.py:58 		Getting column txt_message values from the table: css selector: .qr-list__item
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.67/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.67/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.67/text HTTP/1.1" 200 84
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I'm sorry to hear that, let me see how I can help. May I have your name?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.68/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.68/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.68/text HTTP/1.1" 200 80
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "My apologies about that. Can you tell me on what date this occurred?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.69/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.69/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.69/text HTTP/1.1" 200 66
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I understand, do you recall who you were working with?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.70/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.70/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.70/text HTTP/1.1" 200 154
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.71/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.71/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.71/text HTTP/1.1" 200 126
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.72/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.72/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.72/text HTTP/1.1" 200 70
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "Which method of contact would you prefer to be reached on?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.73/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.73/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.73/text HTTP/1.1" 200 105
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I appreciate you confirming, may I please have your last name and phone number/email address?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.74/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.74/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.74/text HTTP/1.1" 200 124
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "Thank you. A manager will follow up with you as soon as possible. Is there anything else I can do for you today?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.75/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.75/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.75/text HTTP/1.1" 200 28
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "###ESCALATION###"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.76/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.76/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.76/text HTTP/1.1" 200 115
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I'm sorry to hear that. Let me have a member of my team contact you. What's a good number to reach you?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.77/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.77/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.77/text HTTP/1.1" 200 265
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.78/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.78/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.78/text HTTP/1.1" 200 105
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible."
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get all messages of category returns: ["I'm sorry to hear that, let me see how I can help. May I have your name?", 'My apologies about that. Can you tell me on what date this occurred?', 'I understand, do you recall who you were working with?', 'I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?', 'I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?', 'Which method of contact would you prefer to be reached on?', 'I appreciate you confirming, may I please have your last name and phone number/email address?', 'Thank you. A manager will follow up with you as soon as possible. Is there anything else I can do for you today?', '###ESCALATION###', "I'm sorry to hear that. Let me have a member of my team contact you. What's a good number to reach you?", "I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?", 'Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible.']
DEBUG    root:general.py:58 		Component 'QuickResponsesComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - QuickResponsesComponent: Select account
DEBUG    root:general.py:58 		Wait for xpath: .//*[contains(@class, 'loadingIndicator')] spinner to appear and disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    root:general.py:58 		Account selector spinner was not displayed
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.41/text HTTP/1.1" 200 34
DEBUG    root:general.py:58 		Text from "SelectExtended" with css selector: .account-select locator - "e2e-chat-console-env-2"
INFO     root:general.py:54 Account e2e-chat-console-env-2 was already selected
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1649
INFO     root:general.py:54 WEB - QuickResponsesComponent: Select account returns: None
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.64/text HTTP/1.1" 200 67
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit
DEBUG    root:general.py:58 		Component 'BulkEditComponent' existence verification. Wait time = 2
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    root:general.py:58 		Component 'BulkEditComponent' was not found
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
INFO     root:general.py:54 WEB - QuickResponsesComponent: Bulk Edit message in category
DEBUG    root:general.py:58 		Component 'BulkEditComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    root:general.py:58 		Component 'BulkEditComponent' was not found
DEBUG    root:general.py:58 		Open BulkEditComponent web component
DEBUG    root:general.py:58 		Component 'QuickResponsesComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - QuickResponsesComponent: Select account
DEBUG    root:general.py:58 		Wait for xpath: .//*[contains(@class, 'loadingIndicator')] spinner to appear and disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2206
DEBUG    root:general.py:58 		Account selector spinner was not displayed
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.41/text HTTP/1.1" 200 34
DEBUG    root:general.py:58 		Text from "SelectExtended" with css selector: .account-select locator - "e2e-chat-console-env-2"
INFO     root:general.py:54 Account e2e-chat-console-env-2 was already selected
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/elements HTTP/1.1" 200 1649
INFO     root:general.py:54 WEB - QuickResponsesComponent: Select account returns: None
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.64/text HTTP/1.1" 200 67
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit
DEBUG    root:general.py:58 		Component 'BulkEditComponent' existence verification. Wait time = 2
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2179
DEBUG    root:general.py:58 		Component 'BulkEditComponent' was not found
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
INFO     root:general.py:54 WEB - QuickResponsesComponent: Selected category
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.64/text HTTP/1.1" 200 67
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.64/text HTTP/1.1" 200 67
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit
INFO     root:general.py:54 WEB - QuickResponsesComponent: Selected category returns: None
DEBUG    root:general.py:58 		Clicking on the "css selector: .qr-content-switch span" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.79/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.79/click HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Component 'BulkEditComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - BulkEditComponent: Edit messages
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.86/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.87/text HTTP/1.1" 200 1212
DEBUG    root:general.py:58 		Text from "Input" with css selector: div[data-contents="true"] locator - "I'm sorry to hear that, let me see how I can help. May I have your name?
My apologies about that. Can you tell me on what date this occurred?
I understand, do you recall who you were working with?
I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?
I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?
Which method of contact would you prefer to be reached on?
I appreciate you confirming, may I please have your last name and phone number/email address?
Thank you. A manager will follow up with you as soon as possible. Is there anything else I can do for you today?
###ESCALATION###
I'm sorry to hear that. Let me have a member of my team contact you. What's a good number to reach you?
I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?
Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible."
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.86/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.87/text HTTP/1.1" 200 1212
DEBUG    root:general.py:58 		Text from "Input" with css selector: div[data-contents="true"] locator - "I'm sorry to hear that, let me see how I can help. May I have your name?
My apologies about that. Can you tell me on what date this occurred?
I understand, do you recall who you were working with?
I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?
I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?
Which method of contact would you prefer to be reached on?
I appreciate you confirming, may I please have your last name and phone number/email address?
Thank you. A manager will follow up with you as soon as possible. Is there anything else I can do for you today?
###ESCALATION###
I'm sorry to hear that. Let me have a member of my team contact you. What's a good number to reach you?
I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?
Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible."
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.86/elements HTTP/1.1" 200 1415
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/screenshot HTTP/1.1" 200 420676
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/screenshot HTTP/1.1" 200 420116
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/actions HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "css selector: button" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.86/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.103/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.103/click HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Wait for css selector: .list-loading spinner to appear and disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.7279722CECB9658C4C7EDEE5C2DE2614.e.38/element HTTP/1.1" 404 2184
INFO     root:general.py:54 WEB - BulkEditComponent: Edit messages returns: None
INFO     root:general.py:54 WEB - QuickResponsesComponent: Bulk Edit message in category returns: None
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/refresh HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 20
DEBUG    root:general.py:58 		Component 'QuickResponsesComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - QuickResponsesComponent: Select account
DEBUG    root:general.py:58 		Wait for xpath: .//*[contains(@class, 'loadingIndicator')] spinner to appear and disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 404 2206
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.122/text HTTP/1.1" 200 34
DEBUG    root:general.py:58 		Text from "SelectExtended" with css selector: .account-select locator - "e2e-chat-console-env-2"
INFO     root:general.py:54 Account e2e-chat-console-env-2 was already selected
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1663
INFO     root:general.py:54 WEB - QuickResponsesComponent: Select account returns: None
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.137/text HTTP/1.1" 200 67
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit
DEBUG    root:general.py:58 		Component 'BulkEditComponent' existence verification. Wait time = 2
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    root:general.py:58 		Scrolling to 'css selector: .qr-content-switch span' element by JavaScript
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "css selector: .qr-content-switch span" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.139/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.139/click HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.139/text HTTP/1.1" 200 16
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get all messages of category
INFO     root:general.py:54 WEB - QuickResponsesComponent: Selected category
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.137/text HTTP/1.1" 200 67
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.137/text HTTP/1.1" 200 67
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: .qr-content-top__title>div:first-child locator - "-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit"
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get selected category returns: -CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-Edit
INFO     root:general.py:54 WEB - QuickResponsesComponent: Selected category returns: None
DEBUG    root:general.py:58 		Getting column txt_message values from the table: css selector: .qr-list__item
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.146/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.146/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.146/text HTTP/1.1" 200 84
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I'm sorry to hear that, let me see how I can help. May I have your name?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.147/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.147/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.147/text HTTP/1.1" 200 80
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "My apologies about that. Can you tell me on what date this occurred?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.148/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.148/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.148/text HTTP/1.1" 200 66
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I understand, do you recall who you were working with?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.143/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.143/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.143/text HTTP/1.1" 200 154
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.149/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.149/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.149/text HTTP/1.1" 200 126
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.150/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.150/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.150/text HTTP/1.1" 200 70
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "Which method of contact would you prefer to be reached on?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.144/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.144/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.144/text HTTP/1.1" 200 105
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I appreciate you confirming, may I please have your last name and phone number/email address?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.151/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.151/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.151/text HTTP/1.1" 200 103
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "Thank you. A manager will follow up with you as soon Testing editing message in position 8."
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.152/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.152/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.152/text HTTP/1.1" 200 109
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "Testing editing message in position 9. of my team contact you. What's a good number to reach you?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.153/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.153/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.153/text HTTP/1.1" 200 265
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.154/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.113/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.114/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "POST /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.154/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "GET /session/edddc31ee7360960fa891feed9535009/element/f.5E060BDC2C3399BAB5727DD912D88CF1.d.00D40C64D39BE1BF65F9B527168455EF.e.154/text HTTP/1.1" 200 105
DEBUG    root:general.py:58 		Text from "TextBlock" with xpath: . locator - "Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible."
INFO     root:general.py:54 WEB - QuickResponsesComponent: Get all messages of category returns: ["I'm sorry to hear that, let me see how I can help. May I have your name?", 'My apologies about that. Can you tell me on what date this occurred?', 'I understand, do you recall who you were working with?', 'I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?', 'I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?', 'Which method of contact would you prefer to be reached on?', 'I appreciate you confirming, may I please have your last name and phone number/email address?', 'Thank you. A manager will follow up with you as soon Testing editing message in position 8.', "Testing editing message in position 9. of my team contact you. What's a good number to reach you?", "I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?", 'Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible.']