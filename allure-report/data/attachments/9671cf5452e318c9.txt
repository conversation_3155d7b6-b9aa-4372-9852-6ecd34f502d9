INFO     root:general.py:54 ================================================================================
INFO     root:general.py:54 Test Case Name: test_edit_message_in_chat
INFO     root:general.py:54 ================================================================================
INFO     root:general.py:54 Test Case ID: 6477569
                                 Test Case Title: Edit QR message in chat (bulk mode)
                                 Test Case Pre-requisite: None
                                 Test Case Steps:
                                      None
                                 Test Case Expect Result: None
                                 Test Case Priority: critical
INFO     root:general.py:54 ================================================================================
DEBUG    root:general.py:58 		KTF: set()
DEBUG    root:general.py:58 		KTF TO DELETE: set()
WARNING  root:general.py:66 warning
DEBUG    root:general.py:58 		Page 'ChatConsolePage' existence verification. Wait time = 0
DEBUG    root:general.py:58 		Open ChatConsolePage web page
INFO     root:general.py:54 WEB - ChatConsolePage: Open Browser and enter Chat Console Page URL
DEBUG    root:general.py:58 		Opening https://chat-alpha.gubagoo.com url
DEBUG    root:general.py:58 		Creating an instance of a Browser.
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:59 Selenium Manager binary found at: /Library/Frameworks/Python.framework/Versions/3.9/lib/python3.9/site-packages/selenium/webdriver/common/macos/selenium-manager
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:116 Executing process: /Library/Frameworks/Python.framework/Versions/3.9/lib/python3.9/site-packages/selenium/webdriver/common/macos/selenium-manager --browser chrome --debug --output json
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Checking chromedriver in PATH
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Running command: chromedriver --version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Output: ""
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chromedriver not found in PATH
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chrome detected at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Using shell command to find out chrome version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Running command: /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Output: "Google Chrome 127.0.6533.120 "
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Detected browser: chrome 127.0.6533.120
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Required driver: chromedriver 127.0.6533.119
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chromedriver 127.0.6533.119 already in the cache
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Driver path: /Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Browser path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:94 Using driver at: /Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver
DEBUG    selenium.webdriver.common.service:service.py:216 Started executable: `/Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver` in a child process with pid: 53467
DEBUG    urllib3.connectionpool:connectionpool.py:227 Starting new HTTP connection (1): localhost:54908
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session HTTP/1.1" 200 892
DEBUG    root:general.py:58 		Browser version 127.0.6533.120
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/url HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Page 'ChatConsoleSignInPage' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ChatConsoleSignInPage: Login with '202751' account id 'chat_operator' user name and 'nZ5qP2nF5' password
DEBUG    root:general.py:58 		Clearing css selector: input[name='account_id'] input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.6/element HTTP/1.1" 200 125
DEBUG    root:general.py:58 		Clicks an "css selector: input[name='account_id']" element.
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/actions HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Sending 202751 keys to the 'css selector: input[name='account_id']' input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.6/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.7/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.7/value HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clearing css selector: input[name='username'] input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.6/element HTTP/1.1" 200 125
DEBUG    root:general.py:58 		Clicks an "css selector: input[name='username']" element.
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/actions HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Sending chat_operator keys to the 'css selector: input[name='username']' input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.6/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.9/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.9/value HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clearing css selector: input[name='password'] input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.6/element HTTP/1.1" 200 126
DEBUG    root:general.py:58 		Clicks an "css selector: input[name='password']" element.
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/actions HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Sending nZ5qP2nF5 keys to the 'css selector: input[name='password']' input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.6/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.10/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.10/value HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "css selector: button[type='submit']" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 125
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.6/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.11/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.11/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ChatConsoleSignInPage: Login with '202751' account id 'chat_operator' user name and 'nZ5qP2nF5' password returns: None
DEBUG    root:general.py:58 		Page 'ChatConsolePage' existence verification. Wait time = 5
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    root:general.py:58 		Wait for css selector: .cc-loader spinner to appear and disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2181
WARNING  root:general.py:66 Spinner was not appeared after clicking submit button
INFO     root:general.py:54 WEB - ChatConsolePage: Open Browser and enter Chat Console Page URL returns: None
DEBUG    root:general.py:58 		Page 'ChatConsolePage' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - SideBar: Go online
INFO     root:general.py:54 API - ChatConsolePage: Get online status
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/202889'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_operator', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"839ae1851f33cbba8f533bbf059babb431ad055561b511be","operator":{"id":202889,"account_id":202751,"location_id":0,"username":"chat_operator","display_name":"Jack S","first_name":"Jack","last_name":"Shepard","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":0,"group":1,"accounts_group_id":2214,"channels":"4","auto_accept":0,"parent_id":0,"gender":"F","new_ui_photo":1085843,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":1}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/202889',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': '839ae1851f33cbba8f533bbf059babb431ad055561b511be'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/202889 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":0,"break_id":0,"display_name":"Jack S","first_name":"Jack","group":1,"id":202889,"last_name":"Shepard","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","role_id":1,"status":0,"username":"chat_operator"}
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info returns: {'id': 202889, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_operator', 'display_name': 'Jack S', 'first_name': 'Jack', 'last_name': 'Shepard', 'status': 0, 'group': 1, 'auto_accept': 0, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_', 'role_id': 1}
INFO     root:general.py:54 API - ChatConsolePage: Get online status returns: 0
INFO     root:general.py:54 API - ChatConsolePage: Get auto accept status
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/202889'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_operator', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"a2dffb977f5504b82c113c5b27f9ca0f9520f364b6a3480c","operator":{"id":202889,"account_id":202751,"location_id":0,"username":"chat_operator","display_name":"Jack S","first_name":"Jack","last_name":"Shepard","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":0,"group":1,"accounts_group_id":2214,"channels":"4","auto_accept":0,"parent_id":0,"gender":"F","new_ui_photo":1085843,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":1}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/202889',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': 'a2dffb977f5504b82c113c5b27f9ca0f9520f364b6a3480c'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/202889 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":0,"break_id":0,"display_name":"Jack S","first_name":"Jack","group":1,"id":202889,"last_name":"Shepard","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","role_id":1,"status":0,"username":"chat_operator"}
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info returns: {'id': 202889, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_operator', 'display_name': 'Jack S', 'first_name': 'Jack', 'last_name': 'Shepard', 'status': 0, 'group': 1, 'auto_accept': 0, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_', 'role_id': 1}
INFO     root:general.py:54 API - ChatConsolePage: Get auto accept status returns: 0
INFO     root:general.py:54 WEB - SideBar: Set online toggle True
INFO     root:general.py:54 API - ChatConsolePage: Get online status
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/202889'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_operator', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"937ff0cc719fc4f17e62d4a00551d612e62ab1bf230e50bc","operator":{"id":202889,"account_id":202751,"location_id":0,"username":"chat_operator","display_name":"Jack S","first_name":"Jack","last_name":"Shepard","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":0,"group":1,"accounts_group_id":2214,"channels":"4","auto_accept":0,"parent_id":0,"gender":"F","new_ui_photo":1085843,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":1}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/202889',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': '937ff0cc719fc4f17e62d4a00551d612e62ab1bf230e50bc'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/202889 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":0,"break_id":0,"display_name":"Jack S","first_name":"Jack","group":1,"id":202889,"last_name":"Shepard","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","role_id":1,"status":0,"username":"chat_operator"}
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info returns: {'id': 202889, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_operator', 'display_name': 'Jack S', 'first_name': 'Jack', 'last_name': 'Shepard', 'status': 0, 'group': 1, 'auto_accept': 0, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_', 'role_id': 1}
INFO     root:general.py:54 API - ChatConsolePage: Get online status returns: 0
INFO     root:general.py:54 WEB - SideBar: Get online status
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 58
DEBUG    root:general.py:58 		Attribute value "class" from "Button" with "css selector: .m-operator span" is "%cc-sidebar__status cc-sidebar__status--offline"
INFO     root:general.py:54 WEB - SideBar: Get online status returns: False
INFO     root:general.py:54 WEB - ModalOperator: Click online toggle
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2186
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' was not found
DEBUG    root:general.py:58 		Open ModalOperatorComponent web component
INFO     root:general.py:54 WEB - ModalOperator: Open "Modal Operator" Component
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2185
DEBUG    root:general.py:58 		Clicking on the "css selector: .cc-sidebar__user img" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.24/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.24/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Open "Modal Operator" Component returns: None
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "css selector: label[for='status']" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.32/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.32/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Click online toggle returns: None
INFO     root:general.py:54 API - ChatConsolePage: Get online status
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/202889'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_operator', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"e9a5e8faa8ef5bd880897c2a1485b006ae0cd6cb20b0612c","operator":{"id":202889,"account_id":202751,"location_id":0,"username":"chat_operator","display_name":"Jack S","first_name":"Jack","last_name":"Shepard","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":1,"group":1,"accounts_group_id":2214,"channels":"4","auto_accept":0,"parent_id":0,"gender":"F","new_ui_photo":1085843,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":1}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/202889',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': 'e9a5e8faa8ef5bd880897c2a1485b006ae0cd6cb20b0612c'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/202889 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":0,"break_id":0,"display_name":"Jack S","first_name":"Jack","group":1,"id":202889,"last_name":"Shepard","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","role_id":1,"status":1,"username":"chat_operator"}
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info returns: {'id': 202889, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_operator', 'display_name': 'Jack S', 'first_name': 'Jack', 'last_name': 'Shepard', 'status': 1, 'group': 1, 'auto_accept': 0, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_', 'role_id': 1}
INFO     root:general.py:54 API - ChatConsolePage: Get online status returns: 1
INFO     root:general.py:54 WEB - ModalOperator: Close "Modal Operator" Component
DEBUG    root:general.py:58 		Clicking on the "css selector: .cc-sidebar__user img" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.24/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.24/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Close "Modal Operator" Component returns: None
INFO     root:general.py:54 WEB - SideBar: Set online toggle True returns: None
INFO     root:general.py:54 WEB - SideBar: Set auto accept toggle True
INFO     root:general.py:54 API - ChatConsolePage: Get auto accept status
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/202889'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_operator', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"61e544307feace932219d1c65868e9a53ce278b2814c61d2","operator":{"id":202889,"account_id":202751,"location_id":0,"username":"chat_operator","display_name":"Jack S","first_name":"Jack","last_name":"Shepard","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":1,"group":1,"accounts_group_id":2214,"channels":"4","auto_accept":0,"parent_id":0,"gender":"F","new_ui_photo":1085843,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":1}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/202889',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': '61e544307feace932219d1c65868e9a53ce278b2814c61d2'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/202889 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":0,"break_id":0,"display_name":"Jack S","first_name":"Jack","group":1,"id":202889,"last_name":"Shepard","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","role_id":1,"status":1,"username":"chat_operator"}
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info returns: {'id': 202889, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_operator', 'display_name': 'Jack S', 'first_name': 'Jack', 'last_name': 'Shepard', 'status': 1, 'group': 1, 'auto_accept': 0, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_', 'role_id': 1}
INFO     root:general.py:54 API - ChatConsolePage: Get auto accept status returns: 0
INFO     root:general.py:54 WEB - SideBar: Get auto accept status
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2186
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' was not found
DEBUG    root:general.py:58 		Open ModalOperatorComponent web component
INFO     root:general.py:54 WEB - ModalOperator: Open "Modal Operator" Component
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2185
DEBUG    root:general.py:58 		Clicking on the "css selector: .cc-sidebar__user img" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.24/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.24/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Open "Modal Operator" Component returns: None
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Check is the 'css selector: #autoAccept' check box is selected
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.40/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Attribute value "checked" from "CheckBox" with "css selector: #autoAccept" is "%None"
INFO     root:general.py:54 WEB - SideBar: Get auto accept status returns: False
INFO     root:general.py:54 WEB - ModalOperator: Click auto-accept toggle
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "css selector: label[for='autoAccept']" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.40/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.43/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.43/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Click auto-accept toggle returns: None
INFO     root:general.py:54 API - ChatConsolePage: Get auto accept status
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/202889'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_operator', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"1a401018a8006d1a026b315800ac2b785853089df43d74c9","operator":{"id":202889,"account_id":202751,"location_id":0,"username":"chat_operator","display_name":"Jack S","first_name":"Jack","last_name":"Shepard","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":1,"group":1,"accounts_group_id":2214,"channels":"4","auto_accept":1,"parent_id":0,"gender":"F","new_ui_photo":1085843,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":1}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/202889',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': '1a401018a8006d1a026b315800ac2b785853089df43d74c9'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/202889 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":1,"break_id":0,"display_name":"Jack S","first_name":"Jack","group":1,"id":202889,"last_name":"Shepard","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","role_id":1,"status":1,"username":"chat_operator"}
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info returns: {'id': 202889, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_operator', 'display_name': 'Jack S', 'first_name': 'Jack', 'last_name': 'Shepard', 'status': 1, 'group': 1, 'auto_accept': 1, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_', 'role_id': 1}
INFO     root:general.py:54 API - ChatConsolePage: Get auto accept status returns: 1
INFO     root:general.py:54 WEB - ChatComponent:  Accept chats from queue if it is present
DEBUG    root:general.py:58 		Component 'ChatQueueComponent' existence verification. Wait time = 1
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2189
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 404 2189
DEBUG    root:general.py:58 		Component 'ChatQueueComponent' was not found
INFO     root:general.py:54 WEB - ChatComponent:  Accept chats from queue if it is present returns: False
INFO     root:general.py:54 API - ChatConsolePage: Get auto accept status
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/202889'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_operator', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"a8f87c5274cd4434e265e44122e3cb6d38436e4512f0906d","operator":{"id":202889,"account_id":202751,"location_id":0,"username":"chat_operator","display_name":"Jack S","first_name":"Jack","last_name":"Shepard","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":1,"group":1,"accounts_group_id":2214,"channels":"4","auto_accept":1,"parent_id":0,"gender":"F","new_ui_photo":1085843,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":1}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/202889',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': 'a8f87c5274cd4434e265e44122e3cb6d38436e4512f0906d'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/202889 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":1,"break_id":0,"display_name":"Jack S","first_name":"Jack","group":1,"id":202889,"last_name":"Shepard","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","role_id":1,"status":1,"username":"chat_operator"}
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info returns: {'id': 202889, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_operator', 'display_name': 'Jack S', 'first_name': 'Jack', 'last_name': 'Shepard', 'status': 1, 'group': 1, 'auto_accept': 1, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_', 'role_id': 1}
INFO     root:general.py:54 API - ChatConsolePage: Get auto accept status returns: 1
INFO     root:general.py:54 WEB - SideBar: Get auto accept status
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Check is the 'css selector: #autoAccept' check box is selected
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.40/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 16
DEBUG    root:general.py:58 		Attribute value "checked" from "CheckBox" with "css selector: #autoAccept" is "%true"
INFO     root:general.py:54 WEB - SideBar: Get auto accept status returns: True
INFO     root:general.py:54 WEB - ModalOperator: Close "Modal Operator" Component
DEBUG    root:general.py:58 		Clicking on the "css selector: .cc-sidebar__user img" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.24/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.55C107C1896A816BF0AD360F5FFB67C9.e.24/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Close "Modal Operator" Component returns: None
INFO     root:general.py:54 WEB - SideBar: Set auto accept toggle True returns: None
INFO     root:general.py:54 WEB - SideBar: Go online returns: None
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/refresh HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 20
DEBUG    root:general.py:58 		Page 'DesktopConsumerPage' existence verification. Wait time = 0
DEBUG    root:general.py:58 		Open DesktopConsumerPage web page
INFO     root:general.py:54 WEB - ConsumerPage: Open Browser and enter Consumer Page URL
DEBUG    root:general.py:58 		Opening https://guba:<EMAIL>/index.html?gb1e2e=1&disableCacheApi=true url
DEBUG    root:general.py:58 		Creating an instance of a Browser.
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:59 Selenium Manager binary found at: /Library/Frameworks/Python.framework/Versions/3.9/lib/python3.9/site-packages/selenium/webdriver/common/macos/selenium-manager
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:116 Executing process: /Library/Frameworks/Python.framework/Versions/3.9/lib/python3.9/site-packages/selenium/webdriver/common/macos/selenium-manager --browser chrome --debug --output json
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Checking chromedriver in PATH
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Running command: chromedriver --version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Output: ""
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chromedriver not found in PATH
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chrome detected at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Using shell command to find out chrome version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Running command: /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Output: "Google Chrome 127.0.6533.120 "
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Detected browser: chrome 127.0.6533.120
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Required driver: chromedriver 127.0.6533.119
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 chromedriver 127.0.6533.119 already in the cache
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Driver path: /Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:135 Browser path: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
DEBUG    selenium.webdriver.common.selenium_manager:selenium_manager.py:94 Using driver at: /Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver
DEBUG    selenium.webdriver.common.service:service.py:216 Started executable: `/Users/<USER>/.cache/selenium/chromedriver/mac-x64/127.0.6533.119/chromedriver` in a child process with pid: 53608
DEBUG    urllib3.connectionpool:connectionpool.py:227 Starting new HTTP connection (1): localhost:54977
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session HTTP/1.1" 200 892
DEBUG    root:general.py:58 		Browser version 127.0.6533.120
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/url HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ConsumerPage: Open Browser and enter Consumer Page URL returns: None
DEBUG    root:general.py:58 		Page 'DesktopConsumerPage' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ConsumerPage: Start chat
DEBUG    root:general.py:58 		Component 'ChatComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 404 2236
DEBUG    root:general.py:58 		Component 'ChatComponent' was not found
DEBUG    root:general.py:58 		Open ChatComponent web component
INFO     root:general.py:54 WEB - ChatComponent: Open "Chat" Component sending a message
INFO     root:general.py:54 WEB - ChatComponent: Reopen "Chat" Component
DEBUG    root:general.py:58 		Component 'WelcomeViewComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 404 2229
DEBUG    root:general.py:58 		Component 'WelcomeViewComponent' was not found
DEBUG    root:general.py:58 		Open WelcomeViewComponent web component
INFO     root:general.py:54 WEB - WelcomeViewComponent: Open "Welcome View" Component
DEBUG    root:general.py:58 		Clicking on the "css selector: button[aria-label='Toggle Chat Window']" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "GET /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.16/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.16/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - WelcomeViewComponent: Open "Welcome View" Component returns: None
DEBUG    root:general.py:58 		Component 'WelcomeViewComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 404 2229
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - WelcomeViewComponent: Send a 'Hello, my name is Adam. I want to talk to your manager Simon' message
DEBUG    root:general.py:58 		Sending Hello, my name is Adam. I want to talk to your manager Simon keys to the 'css selector: [aria-label='Type your message']' input field
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 15
ERROR    root:general.py:62 Error on performing 'send_keys' action. Retrying...
ERROR    root:general.py:62 Unable to send_keys on WebElement of Input type with the css selector: [aria-label='Type your message']
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "GET /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.18/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.18/value HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "css selector: button[aria-label='Send Message']" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 15
ERROR    root:general.py:62 Error on performing 'click' action. Retrying...
ERROR    root:general.py:62 Unable to click on WebElement of Button type with the css selector: button[aria-label='Send Message']
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.17/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "GET /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.22/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.22/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - WelcomeViewComponent: Send a 'Hello, my name is Adam. I want to talk to your manager Simon' message returns: None
DEBUG    root:general.py:58 		Component 'ChatComponent' existence verification. Wait time = 5
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ChatComponent: Reopen "Chat" Component returns: <src.web.consumer.desktop.v4.chat_component.ChatComponent object at 0x7fc7ff9da160>
INFO     root:general.py:54 WEB - ChatComponent: Open "Chat" Component sending a message returns: None
DEBUG    root:general.py:58 		Component 'ChatComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/element/f.AE4BEAF954F7F02148068BC9525E296B.d.3D78F376BBB8AD4D3EB672B0F74D6012.e.15/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ConsumerPage: Get Chat id
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "POST /session/eb4ff625c42fa9e941d762dc13cdd6d9/execute/sync HTTP/1.1" 200 17
INFO     root:general.py:54 WEB - ConsumerPage: Get Chat id returns: 6085933
INFO     root:general.py:54 WEB - ConsumerPage: Start chat returns: 6085933
INFO     root:general.py:54 WEB - ConsumerPage: Get Chat id
INFO     root:general.py:54 WEB - ConsumerPage: Get Chat id returns: 6085933
INFO     root:general.py:54 WEB - ChatComponent: Select Chat by '6085933' id
DEBUG    root:general.py:58 		Component 'ChatComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 19
DEBUG    root:general.py:58 		Attribute value "data-id" from "AnyType" with "css selector: .reply-box" is "%6085933"
INFO     root:general.py:54 Chat id 6085933 is active
INFO     root:general.py:54 WEB - ChatComponent: Select Chat by '6085933' id returns: None
INFO     root:general.py:54 WEB - QuickRepliesComponent: Get available messages from category
DEBUG    root:general.py:58 		Component 'QuickRepliesComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 404 2254
DEBUG    root:general.py:58 		Component 'QuickRepliesComponent' was not found
DEBUG    root:general.py:58 		Open QuickRepliesComponent web component
INFO     root:general.py:54 WEB - QuickRepliesComponent: Open "Quick Replies" Component
DEBUG    root:general.py:58 		Clicking on the "css selector: span[data-testid='icon-qr-test']" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.85/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.85/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - QuickRepliesComponent: Open "Quick Replies" Component returns: None
DEBUG    root:general.py:58 		Component 'QuickRepliesComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 404 2254
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 404 2254
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 31
DEBUG    root:general.py:58 		Attribute value "class" from "Button" with "css selector: span[data-testid='icon-qr-test']" is "%ico icon-qr  active"
INFO     root:general.py:54 WEB - QuickRepliesComponent:  Select quick reply '-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-' category
INFO     root:general.py:54 WEB - QuickRepliesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.87/element HTTP/1.1" 404 2219
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.87/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.88/text HTTP/1.1" 200 27
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: [data-testid='quick-response-title'],[class='widget-top__title'] locator - "Quick Responses"
INFO     root:general.py:54 WEB - QuickRepliesComponent: Get selected category returns: Quick Responses
INFO     root:general.py:54 WEB - QuickRepliesComponent: Get selected category
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.87/element HTTP/1.1" 404 2219
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.87/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.88/text HTTP/1.1" 200 27
DEBUG    root:general.py:58 		Text from "TextBlock" with css selector: [data-testid='quick-response-title'],[class='widget-top__title'] locator - "Quick Responses"
INFO     root:general.py:54 WEB - QuickRepliesComponent: Get selected category returns: Quick Responses
DEBUG    root:general.py:58 		Selecting 'QrCategory.CUSTOMER_COMPLAINT_LEAD_GENERATION_SET_UP_CLOSE' section in 'css selector: [data-testid='quick-response-category'],[data-testid='i-list-categories-test-item']' menu
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.87/elements HTTP/1.1" 200 1652
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.89/text HTTP/1.1" 200 34
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.90/text HTTP/1.1" 200 42
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.91/text HTTP/1.1" 200 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.92/text HTTP/1.1" 200 36
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.93/text HTTP/1.1" 200 44
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.94/text HTTP/1.1" 200 39
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.95/text HTTP/1.1" 200 48
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.96/text HTTP/1.1" 200 43
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.97/text HTTP/1.1" 200 54
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.98/text HTTP/1.1" 200 50
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.99/text HTTP/1.1" 200 68
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.99/text HTTP/1.1" 200 68
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.99/click HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Selected '11.
-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-' section in 'css selector: [data-testid='quick-response-category'],[data-testid='i-list-categories-test-item']' menu
INFO     root:general.py:54 WEB - QuickRepliesComponent:  Select quick reply '-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-' category returns: None
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.87/elements HTTP/1.1" 200 1309
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.104/text HTTP/1.1" 200 86
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.105/text HTTP/1.1" 200 82
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.106/text HTTP/1.1" 200 68
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.107/text HTTP/1.1" 200 156
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.108/text HTTP/1.1" 200 128
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.109/text HTTP/1.1" 200 72
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.110/text HTTP/1.1" 200 107
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.111/text HTTP/1.1" 200 105
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.112/text HTTP/1.1" 200 111
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.113/text HTTP/1.1" 200 268
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.114/text HTTP/1.1" 200 108
DEBUG    root:general.py:58 		Text of list of elements css selector: [data-testid='quick-response-message'],[data-testid='li-list-message-test-item'] is ["1.I'm sorry to hear that, let me see how I can help. May I have your name?", '2.My apologies about that. Can you tell me on what date this occurred?', '3.I understand, do you recall who you were working with?', '4.I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?', '5.I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?', '6.Which method of contact would you prefer to be reached on?', '7.I appreciate you confirming, may I please have your last name and phone number/email address?', '8.Thank you. A manager will follow up with you as soon Testing editing message in position 8.', "9.Testing editing message in position 9. of my team contact you. What's a good number to reach you?", "10.I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?", '11.Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible.']
INFO     root:general.py:54 WEB - QuickRepliesComponent: Get available messages from category returns: ["I'm sorry to hear that, let me see how I can help. May I have your name?", 'My apologies about that. Can you tell me on what date this occurred?', 'I understand, do you recall who you were working with?', 'I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?', 'I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?', 'Which method of contact would you prefer to be reached on?', 'I appreciate you confirming, may I please have your last name and phone number/email address?', 'Thank you. A manager will follow up with you as soon Testing editing message in position 8.', "Testing editing message in position 9. of my team contact you. What's a good number to reach you?", "I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?", 'Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible.']
INFO     root:general.py:54 WEB - ConsumerPage: Get Chat id
INFO     root:general.py:54 WEB - ConsumerPage: Get Chat id returns: 6085933
INFO     root:general.py:54 WEB - ChatComponent: Close chat
INFO     root:general.py:54 WEB - ChatComponent: Select Chat by '6085933' id
DEBUG    root:general.py:58 		Component 'ChatComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 19
DEBUG    root:general.py:58 		Attribute value "data-id" from "AnyType" with "css selector: .reply-box" is "%6085933"
INFO     root:general.py:54 Chat id 6085933 is active
INFO     root:general.py:54 WEB - ChatComponent: Select Chat by '6085933' id returns: None
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/elements HTTP/1.1" 200 129
DEBUG    root:general.py:58 		Component 'CloseChatComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 404 2185
DEBUG    root:general.py:58 		Component 'CloseChatComponent' was not found
DEBUG    root:general.py:58 		Open CloseChatComponent web component
INFO     root:general.py:54 WEB - CloseChatComponent: Open "Close Chat" Component
DEBUG    root:general.py:58 		Clicking on the "css selector: .chat-message__close" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.116/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.116/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - CloseChatComponent: Open "Close Chat" Component returns: None
DEBUG    root:general.py:58 		Component 'CloseChatComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - CloseChatComponent: Close Chat
DEBUG    root:general.py:58 		Clicking on the "xpath: .//div[@class='modal-footer']/button[contains(text(), 'Close chat')]" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.120/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.123/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.123/click HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 404 2185
INFO     root:general.py:54 WEB - CloseChatComponent: Close Chat returns: None
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.83/text HTTP/1.1" 200 2115
INFO     root:general.py:54 WEB - ChatComponent: Close chat returns: None
DEBUG    root:general.py:58 		Closing the browser
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54977 "DELETE /session/eb4ff625c42fa9e941d762dc13cdd6d9 HTTP/1.1" 200 14
INFO     root:general.py:54 API - Operator: Get '202751' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/quick-responses/202751/restore-default'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_admin', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"2e59bf6ae142419bca3b7e2a421f8711458d0e6be0c41749","operator":{"id":203286,"account_id":202751,"location_id":0,"username":"chat_admin","display_name":"Kera J","first_name":"Kera","last_name":"Juliet","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":0,"group":4,"accounts_group_id":2214,"channels":"4","auto_accept":0,"parent_id":0,"gender":"F","new_ui_photo":1085816,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvOGQyOWVkN2U2MDUxNmRlZjIyZGRkMDczNWE3ZDE3OGUzNjhhYjVhNS5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":4}}
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/quick-responses/202751/restore-default',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': (), 'headers': {'api-token': '2e59bf6ae142419bca3b7e2a421f8711458d0e6be0c41749'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "POST /v1/quick-responses/202751/restore-default HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"items":[{"category":{"id":69286,"name":"-DEALERSHIP LINKS-","order":0},"messages":[{"id":589946,"text":"Here is the link for directions to our location:","order":0},{"id":589947,"text":"Here is the link to our staff details:","order":1},{"id":589948,"text":"Here is the link to our employment opportunities:","order":2},{"id":589949,"text":"Here is the link to our service scheduling tool: ___ That allows you access to the available times and dates.","order":3},{"id":589950,"text":"Here is the link to our maintenance menu:","order":4},{"id":589951,"text":"Here is the link for our detailing services:","order":5},{"id":589952,"text":"Here is the link to our rental car details: ___ That allows you access to pricing and availability.","order":6},{"id":589953,"text":"Here is the link to our body shop information:","order":7},{"id":589954,"text":"Here is the link to our express lane service information:","order":8},{"id":589955,"text":"Here is the link to our online parts order form:","order":9},{"id":589956,"text":"Here is the link to our accessories information:","order":10},{"id":589957,"text":"Here is the link to our rewards program:","order":11},{"id":589958,"text":"Here is the link to our service specials:","order":12},{"id":589959,"text":"Here is the link to our parts specials:","order":13},{"id":589960,"text":"Here is the link to our credit application: ___ This allows our finance department a chance to assess your needs and help you determine a financing plan.","order":14},{"id":589961,"text":"Here is the link to our payment calculator: INSERT LINK OR On each VDP","order":15},{"id":589962,"text":"Here is the link to our trade evaluation tool:","order":16},{"id":589963,"text":"Here is the link to our vehicle specifications:","order":17},{"id":589964,"text":"Here is the link to our current new sales specials:","order":18},{"id":589965,"text":"Here is the link to our current pre owned sales specials:","order":19},{"id":589966,"text":"Here is the link to our factory incentives:","order":20}]},{"category":{"id":69287,"name":"-CUSTOMER ACKNOWLEDGEMENT-","order":1},"messages":[{"id":589967,"text":"Hey, how can I help you?","order":0},{"id":589968,"text":"I would be happy to help you with ____.","order":1},{"id":589969,"text":"Is this regarding sales, service or parts?","order":2},{"id":589970,"text":"May I have your name?","order":3},{"id":589971,"text":"Pleasure chatting with you, ____.","order":4},{"id":589972,"text":"Thanks! I will make sure to put that into your request.","order":5},{"id":589973,"text":"Okay! Take your time.","order":6},{"id":589974,"text":"Let me look into that for you, just a moment.","order":7},{"id":589975,"text":"One more moment as I am still looking into that for you, thank you for your patience.","order":8},{"id":589976,"text":"I can assure you I am a real person and my name is ____.","order":9},{"id":589977,"text":"I am a liaison for the online department. While I am a part of the online support team, we work from a different building.","order":10},{"id":589978,"text":"I see you were chatting with another agent, one moment while I review your messages please.","order":11},{"id":589979,"text":"I see that your information was already submitted. My team will contact you as soon as possible.","order":12},{"id":589980,"text":"I am not able to confirm exactly when my team will reach out to you, but I can assure you they will be in touch as soon as they are available!","order":13},{"id":589981,"text":"Thank you for the positive feedback! We value you taking the time to let us know of your experience. Is there anything else I can do for you?","order":14},{"id":589982,"text":"### Contact Info ###","order":15},{"id":589983,"text":"I'm currently unable to pull up any general contact information for ____. I can submit all inquiries and have them follow up with you. May I ask what this is in regards to?","order":16},{"id":589984,"text":"Our ____ name is ____. I'd be happy to take a message for you. May I ask what this is in regards to?","order":17},{"id":589985,"text":"### Transcript ###","order":18},{"id":589986,"text":"Once this chat has ended, you will see \"Get chat transcript\" located at the bottom of the window. You can then click this to receive a copy of our conversation to your preferred email.","order":19}]},{"category":{"id":69288,"name":"-FACEBOOK\/SMS-","order":2},"messages":[{"id":589987,"text":"I'm sorry, I don't see that particular message. Can you clarify what you're referring to please?","order":0},{"id":589988,"text":"I'm sorry, I didn't see that post, can you confirm what it is about?","order":1},{"id":589989,"text":"Thank you for your application, it has been passed to the HR department for review.","order":2},{"id":589990,"text":"Do you have any questions regarding this offer?","order":3},{"id":589991,"text":"Just to clarify, am I speaking with ____?","order":4},{"id":589992,"text":"Let me get with my team about ____. I have your number listed here as ______. Just to finish up, can I have your last name and email address?","order":5},{"id":589993,"text":"Sure thing! I'll have them contact you via Facebook Messenger.","order":6}]},{"category":{"id":69289,"name":"-INVENTORY MATCHING-","order":3},"messages":[{"id":589994,"text":"Do you have the stock number on that vehicle?","order":0},{"id":589995,"text":"Just to be sure, is this the vehicle?","order":1},{"id":589996,"text":"Here's one:","order":2},{"id":589997,"text":"Are there any special features or color you would like?","order":3},{"id":589998,"text":"How do you feel about this ___?","order":4},{"id":589999,"text":"It appears to be available.","order":5},{"id":590000,"text":"I am not seeing that ___ listed. Let me check with my team to confirm availability and options. Are there any special features or color you would like?","order":6}]},{"category":{"id":69290,"name":"-SALES ENGAGEMENT QUESTIONS-","order":4},"messages":[{"id":590001,"text":"Do you have a specific model in mind?","order":0},{"id":590002,"text":"Do you prefer new or pre-owned?","order":1},{"id":590003,"text":"Are you looking to purchase or lease?","order":2},{"id":590004,"text":"Typically, leases are offered for new models. Are you interested in leasing a new model?","order":3},{"id":590005,"text":"Do you know what lease term or mileage package you will need?","order":4},{"id":590006,"text":"Do you have a trade-in?","order":5},{"id":590007,"text":"What is the year, make, model and mileage of your trade?","order":6},{"id":590008,"text":"Thanks! What model are you interested in trading in for?","order":7},{"id":590009,"text":"What price range do you want to stay within?","order":8},{"id":590010,"text":"Are you looking for a sedan, SUV, van, or truck?","order":9},{"id":590011,"text":"What colors do you like?","order":10},{"id":590012,"text":"Any special features that you're looking for?","order":11}]},{"category":{"id":69291,"name":"-SALES LEAD GENERATION-","order":5},"messages":[{"id":590013,"text":"Pleasure chatting with you, ____.","order":0},{"id":590014,"text":"Let me get with my team about ____. What's the best phone number and email to follow up with you?","order":1},{"id":590015,"text":"Let me do some research on ____ and get back with you. What's the best phone number and email to follow up?","order":2},{"id":590016,"text":"We look forward to seeing you! I'll send this over to my team so that they are ready for you. What's the best phone number and email for you please?","order":3},{"id":590017,"text":"Thanks, just to finish up, can I have your last name and phone number\/email address?","order":4},{"id":590018,"text":"Thanks, I'll have our sales team follow up with you as soon as possible regarding BRIEF_RECAP_HERE. Is there anything else I can do for you today?","order":5}]},{"category":{"id":69292,"name":"-SECOND LEAD GENERATION ATTEMPT-","order":6},"messages":[{"id":590019,"text":"No problem, we can email you with ___ if you prefer. May I have your email address please?","order":0},{"id":590020,"text":"I need a little more time to get ____. I can have my team follow up with you as soon as they are available. What's the best phone number and email for you please?","order":1},{"id":590021,"text":"Our online inventory is just a small representation of what is available. Let me check with my team and have them follow up with you on your options. What's the best phone number and email for you please?","order":2}]},{"category":{"id":69293,"name":"-SALES APPOINTMENT SETTING-","order":7},"messages":[{"id":590022,"text":"When would you like to come in to take a look at that vehicle?","order":0},{"id":590023,"text":"When would you like to come in to take a look at our selection?","order":1},{"id":590024,"text":"When would you like to come in to discuss your options?","order":2},{"id":590025,"text":"When would you like to come in for your appraisal?","order":3},{"id":590026,"text":"What time would work best for you?","order":4}]},{"category":{"id":69294,"name":"-SERVICE & PARTS ENGAGEMENT QUESTIONS-","order":8},"messages":[{"id":590027,"text":"Were you looking to schedule an appointment?","order":0},{"id":590028,"text":"What day would you like to bring your vehicle in?","order":1},{"id":590029,"text":"An appointment is recommended to ensure our availability. What day are you wanting to schedule for?","order":2},{"id":590030,"text":"I apologize, I am not authorized to schedule service appointments within 48 business hours of this chat. Here is the link to our service scheduling tool: ___ That allows you access to the available times and dates. Is there anything else I can do for you?","order":3},{"id":590031,"text":"I am unable to cancel your appointment. To do so, please contact our service department during regular business hours.","order":4},{"id":590032,"text":"What service do you need?","order":5},{"id":590033,"text":"Do you need the key programmed as well?","order":6},{"id":590034,"text":"What is the year, make and model of your vehicle?","order":7},{"id":590035,"text":"Do you know the approximate mileage?","order":8},{"id":590036,"text":"Do you have that recall number handy?","order":9},{"id":590037,"text":"I am unable to confirm an appointment for your recall. I need to have my service team confirm if the parts needed to make the repairs on your vehicle are in stock, or if they need to be ordered.","order":10},{"id":590038,"text":"What part are you looking for?","order":11},{"id":590039,"text":"Would you also like to have that installed?","order":12}]},{"category":{"id":69295,"name":"-SERVICE & PARTS LEAD GENERATION-","order":9},"messages":[{"id":590040,"text":"I need to get with my service team about ____. What's the best phone number and email to reach you?","order":0},{"id":590041,"text":"I can submit a service appointment request and have my service team confirm ____ for you. What is the best phone number to reach you?","order":1},{"id":590042,"text":"I need to get with my parts team for you on ____. What's the best phone number and email to reach you?","order":2},{"id":590043,"text":"Thanks, just to finish up, may I please have your last name and phone number\/email address as an alternate contact?","order":3},{"id":590044,"text":"Thanks! I will have our service team follow up with you as soon as possible about BRIEF_RECAP_HERE. Is there anything else I can do for you today?","order":4},{"id":590045,"text":"Thanks! I will have our parts team follow up with you as soon as possible about BRIEF_RECAP_HERE. Is there anything else I can do for you today?","order":5}]},{"category":{"id":69296,"name":"-CUSTOMER COMPLAINT LEAD GENERATION SET UP & CLOSE-","order":10},"messages":[{"id":590046,"text":"I'm sorry to hear that, let me see how I can help. May I have your name?","order":0},{"id":590047,"text":"My apologies about that. Can you tell me on what date this occurred?","order":1},{"id":590048,"text":"I understand, do you recall who you were working with?","order":2},{"id":590049,"text":"I understand your frustration, let me have a manager contact you as soon as possible to address this. Would you prefer an email or phone call?","order":3},{"id":590050,"text":"I appreciate your patience, let me have a manager contact you about this. Would you prefer an email or phone call?","order":4},{"id":590051,"text":"Which method of contact would you prefer to be reached on?","order":5},{"id":590052,"text":"I appreciate you confirming, may I please have your last name and phone number\/email address?","order":6},{"id":590053,"text":"Thank you. A manager will follow up with you as soon as possible. Is there anything else I can do for you today?","order":7},{"id":590054,"text":"###ESCALATION###","order":8},{"id":590055,"text":"I'm sorry to hear that. Let me have a member of my team contact you. What's a good number to reach you?","order":9},{"id":590056,"text":"I understand. Please contact our office if you would like to speak further regarding this situation. I've forwarded your information over to my team and they will follow up with you as soon as they are available. Is there anything else I can do for you?","order":10},{"id":590057,"text":"Thank you, I appreciate your feedback. A manager will follow up with you as soon as possible.","order":11}]},{"category":{"id":69297,"name":"-NO REPLY-","order":11},"messages":[{"id":590058,"text":"Just checking if you are still there. I'll be here if you need help.","order":0},{"id":590059,"text":"Do you need another moment?","order":1},{"id":590060,"text":"Are you still with me?","order":2}]},{"category":{"id":69298,"name":"-CLOSING-","order":12},"messages":[{"id":590061,"text":"Do you have any other questions?","order":0},{"id":590062,"text":"Can I help you with anything else?","order":1},{"id":590063,"text":"Is there anything else I can do for you?","order":2},{"id":590064,"text":"Thank you for visiting us. Have a great day!","order":3},{"id":590065,"text":"### Only Use for Irrelevant Chat ###","order":4},{"id":590066,"text":"Let us know if you need anything. Have a nice day!","order":5}]},{"category":{"id":69299,"name":"-DEALERSHIP INFORMATION-","order":13},"messages":[{"id":590067,"text":"INSERT DEALERSHIP NAME HERE","order":0},{"id":590068,"text":"We are located at ______.","order":1},{"id":590069,"text":"Here is our local phone number: _______. A representative will be able to answer all of your questions to your satisfaction in a timely manner.","order":2},{"id":590070,"text":"Here is the phone number for our sales department: _______. A representative will be able to answer all of your questions to your satisfaction in a timely manner.","order":3},{"id":590071,"text":"Here is the phone number for our service department: _______. A representative will be able to answer all of your questions to your satisfaction in a timely manner.","order":4},{"id":590072,"text":"Here is the phone number for our parts department: _______. A representative will be able to answer all of your questions to your satisfaction in a timely manner.","order":5},{"id":590073,"text":"Here is our fax number: __________.","order":6},{"id":590074,"text":"The sales department is open Monday through Friday from 00am - 00pm, Saturday from 00am - 00pm and Sunday from 00am - 00pm.","order":7},{"id":590075,"text":"The service department is open Monday through Friday from 00am - 00pm, Saturday from 00am - 00pm and Sunday from 00am - 00pm.","order":8},{"id":590076,"text":"The parts department is open Monday through Friday from 00am - 00pm, Saturday from 00am - 00pm and Sunday from 00am - 00pm.","order":9}]}],"_total":14}
INFO     root:general.py:54 API - Operator: Get '202751' Operator Info returns: None
INFO     root:general.py:54 WEB - SideBar: Sign out
INFO     root:general.py:54 API - ChatConsolePage: Get online status
INFO     root:general.py:54 API - Operator: Get '203286' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/203286'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_admin', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"7bbd6aa8f0365f3ab21aa78d57b8ef290b66def1aae81505","operator":{"id":203286,"account_id":202751,"location_id":0,"username":"chat_admin","display_name":"Kera J","first_name":"Kera","last_name":"Juliet","created_at":**********,"updated_at":**********,"last_chat_stamp":0,"status":0,"group":4,"accounts_group_id":2214,"channels":"4","auto_accept":0,"parent_id":0,"gender":"F","new_ui_photo":1085816,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvOGQyOWVkN2U2MDUxNmRlZjIyZGRkMDczNWE3ZDE3OGUzNjhhYjVhNS5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":4}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/203286',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': '7bbd6aa8f0365f3ab21aa78d57b8ef290b66def1aae81505'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/203286 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":0,"break_id":0,"display_name":"Kera J","first_name":"Kera","group":4,"id":203286,"last_name":"Juliet","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvOGQyOWVkN2U2MDUxNmRlZjIyZGRkMDczNWE3ZDE3OGUzNjhhYjVhNS5qcGc=_50_","role_id":4,"status":0,"username":"chat_admin"}
INFO     root:general.py:54 API - Operator: Get '203286' Operator Info returns: {'id': 203286, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_admin', 'display_name': 'Kera J', 'first_name': 'Kera', 'last_name': 'Juliet', 'status': 0, 'group': 4, 'auto_accept': 0, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvOGQyOWVkN2U2MDUxNmRlZjIyZGRkMDczNWE3ZDE3OGUzNjhhYjVhNS5qcGc=_50_', 'role_id': 4}
INFO     root:general.py:54 API - ChatConsolePage: Get online status returns: 0
INFO     root:general.py:54 WEB - SideBar: Sign out returns: None
DEBUG    root:general.py:58 		Closing the browser
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54839 "DELETE /session/edddc31ee7360960fa891feed9535009 HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - SideBar: Sign out
INFO     root:general.py:54 API - ChatConsolePage: Get online status
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info
INFO     root:general.py:54 API - Operator: Get Operator Info from URL: 'https://operators-api.gubagoo.dev/v1/users/202889'
DEBUG    root:general.py:58 		Sending POST request
DEBUG    root:general.py:58 		Request args: ('https://gb1-rc.gubagoo.com/rest_api/operators/login',)
DEBUG    root:general.py:58 		Request kwargs: {'data': (), 'json': {'account_id': '202751', 'password': 'nZ5qP2nF5', 'username': 'chat_operator', 'mfa_type': 'mail'}, 'headers': (), 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): gb1-rc.gubagoo.com:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://gb1-rc.gubagoo.com:443 "POST /rest_api/operators/login HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"token":"e6a023af386d7389a1b7656e0f62d93aa861a115ea916c84","operator":{"id":202889,"account_id":202751,"location_id":0,"username":"chat_operator","display_name":"Jack S","first_name":"Jack","last_name":"Shepard","created_at":**********,"updated_at":**********,"last_chat_stamp":**************,"status":1,"group":1,"accounts_group_id":2214,"channels":"4","auto_accept":1,"parent_id":0,"gender":"F","new_ui_photo":1085843,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","avatar":0,"hourly_rate":0,"display_in_schedule":1,"display_in_timeclock_summary":1,"socket_io":0,"auto_accept_queued":0,"chat_notifications":0,"enabled":1,"ip_filter":null,"used_channels":0,"certifications":null,"trained_certifications":"{\"2214\": **********}","email":"<EMAIL>","role_id":1}}
DEBUG    root:general.py:58 		Sending GET request
DEBUG    root:general.py:58 		Request args: ('https://operators-api.gubagoo.dev/v1/users/202889',)
DEBUG    root:general.py:58 		Request kwargs: {'headers': {'api-token': 'e6a023af386d7389a1b7656e0f62d93aa861a115ea916c84'}, 'auth': None}
DEBUG    urllib3.connectionpool:connectionpool.py:971 Starting new HTTPS connection (1): operators-api.gubagoo.dev:443
DEBUG    urllib3.connectionpool:connectionpool.py:452 https://operators-api.gubagoo.dev:443 "GET /v1/users/202889 HTTP/1.1" 200 None
DEBUG    root:general.py:58 		Respond status: 200
DEBUG    root:general.py:58 		Respond text: {"account":{"id":202751,"name":"e2e-chat-console-env-2"},"account_id":202751,"auto_accept":1,"break_id":0,"display_name":"Jack S","first_name":"Jack","group":1,"id":202889,"last_name":"Shepard","location_id":0,"new_ui_photo_url":"https:\/\/cdn.gubagoo.io\/t\/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_","role_id":1,"status":1,"username":"chat_operator"}
INFO     root:general.py:54 API - Operator: Get '202889' Operator Info returns: {'id': 202889, 'account_id': 202751, 'break_id': 0, 'location_id': 0, 'username': 'chat_operator', 'display_name': 'Jack S', 'first_name': 'Jack', 'last_name': 'Shepard', 'status': 1, 'group': 1, 'auto_accept': 1, 'new_ui_photo_url': 'https://cdn.gubagoo.io/t/aHR0cHM6Ly9jZG4uZ3ViYWdvby5pby9nYjEvYTA4MjhjNGY0MGIzMjZlNTkxYjVmZDA4NzQ3NmRlMDkwYmFjMzUyYi5qcGc=_50_', 'role_id': 1}
INFO     root:general.py:54 API - ChatConsolePage: Get online status returns: 1
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 0
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 404 2186
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' was not found
DEBUG    root:general.py:58 		Open ModalOperatorComponent web component
INFO     root:general.py:54 WEB - ModalOperator: Open "Modal Operator" Component
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 404 2185
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 404 2185
DEBUG    root:general.py:58 		Clicking on the "css selector: .cc-sidebar__user img" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.49/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.49/click HTTP/1.1" 200 14
INFO     root:general.py:54 WEB - ModalOperator: Open "Modal Operator" Component returns: None
DEBUG    root:general.py:58 		Component 'ModalOperatorComponent' existence verification. Wait time = 30
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Clicking on the "css selector: .modal-operator__footer .anchor" "Button"
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 200 126
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.82/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.128/element HTTP/1.1" 200 127
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/execute/sync HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "GET /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.129/enabled HTTP/1.1" 200 14
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element/f.96DFF56C0FF6399D0FF3541ADD342DDD.d.35178DA9CE267251654DE610EA80B968.e.129/click HTTP/1.1" 200 14
DEBUG    root:general.py:58 		Waiting for the 'Logging you out...' text to disappear
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "POST /session/706e6056c47573903f10577812632c31/element HTTP/1.1" 404 2045
INFO     root:general.py:54 WEB - SideBar: Sign out returns: None
DEBUG    root:general.py:58 		Closing the browser
DEBUG    urllib3.connectionpool:connectionpool.py:452 http://localhost:54908 "DELETE /session/706e6056c47573903f10577812632c31 HTTP/1.1" 200 14
DEBUG    root:general.py:58 		test_rail_run_id 49678, build_url None