import re


class Converter:

    @staticmethod
    def get_int_value_rounded(value: str):
        return round(float(value.replace("$", "").replace(",", "").replace("-", "").replace(" / mo", "")))

    @staticmethod
    def extract_digits(value: str):
        return re.sub(r"\D", "", value)

    @staticmethod
    def is_sorted(any_lists: list, asc=True, regex: str = r"\D"):
        if not asc:
            any_lists = any_lists[::-1]
        return all(int(re.sub(regex, "", any_lists[i])) <= int(re.sub(regex, "", any_lists[i + 1]))
                   for i in range(len(any_lists) - 1))

    @staticmethod
    def replace_in_list(any_lists: list, value, value_to_replace):
        return [value_to_replace if x == value else x for x in any_lists]

    @staticmethod
    def int_to_currency(value: int, only_int: bool = True):
        import locale
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        result = locale.currency(value, grouping=True)
        if only_int:
            result = result.split(".")[0]
        return result
