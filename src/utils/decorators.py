from functools import wraps

import allure
from allure_commons._allure import step
from allure_commons._core import plugin_manager
from allure_commons.utils import func_parameters, represent
from allure_commons.utils import uuid4

from kom_framework.src.general import Log


# def step_decorator(step_desctiption):
#     def wrap(f):
#         def wrapped_f(*args, **kwargs):
#             step_desctiption_formated = step_desctiption
#             if len(args) > 1:
#                 step_desctiption_formated = step_desctiption.format(*args)
#             elif len(kwargs) > 0:
#                 if "{" in step_desctiption_formated:
#                     keys = [key for key in kwargs.keys()]
#                     for i in range(len(keys)):
#                         step_desctiption_formated = step_desctiption_formated.replace(f"{{{i+1}}}", f"{{{keys[i]}}}")
#                     step_desctiption_formated = step_desctiption_formated.format(**kwargs)
#             Log.info(step_desctiption_formated)
#             with step(step_desctiption_formated):
#                 return f(*args, **kwargs)
#         return wrapped_f
#     return wrap

def step_decorator(title):
    if callable(title):
        return StepContext(title.__name__, {})(title)
    else:
        return StepContext(title, {})


def component(*component):
    return allure.label("component", *component)


def device(*device_mode):
    return allure.label("device_mode", *device_mode)


def layer(*layer):
    return allure.label("layer", *layer)


@step("Result")
def result(result_value):
    pass


class StepContext:

    def __init__(self, title, params):
        self.title = title
        self.params = params
        self.uuid = uuid4()

    def __enter__(self):
        plugin_manager.hook.start_step(uuid=self.uuid, title=self.title, params=self.params)

    def __exit__(self, exc_type, exc_val, exc_tb):
        plugin_manager.hook.stop_step(uuid=self.uuid, title=self.title, exc_type=exc_type, exc_val=exc_val,
                                      exc_tb=exc_tb)

    def __call__(self, func):
        @wraps(func)
        def impl(*a, **kw):
            __tracebackhide__ = True
            params = func_parameters(func, *a, **kw)
            args = list(map(lambda x: represent(x), a))
            if len(args) > 1:
                pass
            elif len(params) > 0 and "{" in self.title:
                keys = [key for key in params.keys()]
                for i in range(len(keys)):
                    self.title = self.title.replace(f"{{{i + 1}}}", f"{{{keys[i]}}}")
            Log.info(self.title.format(*args, **params))
            # func.__qualname__ to extract class name
            with StepContext(self.title.format(*args, **params), params):
                res = func(*a, **kw)
                Log.info(f'{self.title.format(*args, **params)} returns: {res}')
                if res is not None:
                    result(res)
                return res

        return impl
