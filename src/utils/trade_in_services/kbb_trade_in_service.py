import demjson

from src.utils.trade_in_services.trade_in_providers import TradeInService
from src.web.entities.trade_in_entity import TradeInEntity


class KBBTradeInService(TradeInService):
    @classmethod
    def get_default_price(cls, uuid: str, trade_in_vehicle: TradeInEntity, zip_code: str):
        params = cls.get_params(uuid, trade_in_vehicle, zip_code)
        url = cls.get_route() + f"kbb/value/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_makes(cls, uuid: str = None, year: str = None):
        params = {"year": year}
        url = cls.get_route() + f"kbb/makes/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_models(cls, uuid: str = None, year: str = None, make_id: str = None):
        params = {"year": year, "make": make_id}
        url = cls.get_route() + f"kbb/models/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_trims(cls, uuid: str = None, year: str = None, model_id: str = None, make=None):
        params = {"year": year, "model": model_id}
        url = cls.get_route() + f"kbb/trims/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_trim_id(cls, uuid: str = None, trade_in_vehicle: TradeInEntity = None) -> dict:
        makes = cls.get_makes(uuid, trade_in_vehicle.year)
        make = [make for make in makes if make['makeName'] == trade_in_vehicle.make]
        assert len(make) == 1
        models = cls.get_models(uuid, trade_in_vehicle.year, make[0]['makeId'])
        model = [model for model in models if model['modelName'] == trade_in_vehicle.model]
        assert len(model) == 1
        trims = cls.get_trims(uuid, trade_in_vehicle.year, model[0]['modelId'])
        trim = [trim for trim in trims if trim['trimName'] == trade_in_vehicle.trim]
        assert len(trim) == 1
        return {"vehicle": trim[0]['trimId']}

    @classmethod
    def get_adjustments(cls, trade_in_vehicle: TradeInEntity) -> dict:
        adjustment_values = {}
        if trade_in_vehicle.accidents:
            adjustment_values.update(vars(trade_in_vehicle.accidents))
        if trade_in_vehicle.mechanical_issues:
            for issue in trade_in_vehicle.mechanical_issues:
                adjustment_values[issue.value['api_key']] = 'true'
        return {"adjustments": str(adjustment_values).replace("'", '"')}

    @classmethod
    def get_adjustments_asbury(cls, trade_in_vehicle: TradeInEntity) -> dict:
        adjustment_values = {}
        adjustment_values["has_had_accidents"] = str(bool(trade_in_vehicle.accidents)).lower()
        if trade_in_vehicle.mechanical_issues:
            for issue in trade_in_vehicle.mechanical_issues:
                adjustment_values[issue.value['api_key']] = 'true'
        else:
            adjustment_values["has_had_mechanical_issues"] = str(bool(trade_in_vehicle.mechanical_issues)).lower()
        adjustment_values["makingPayments"] = str(bool(trade_in_vehicle.loan)).lower()
        return {"adjustments": str(adjustment_values).replace("'", '"')}

    @classmethod
    def get_condition(cls, trade_in_vehicle: TradeInEntity) -> dict:
        return {"condition": trade_in_vehicle.condition}

    @classmethod
    def get_params(cls, uuid: str, trade_in_vehicle: TradeInEntity, zip_code: str) -> dict:
        params = {}
        if not trade_in_vehicle.vin:
            params.update(cls.get_trim_id(uuid, trade_in_vehicle))
        else:
            params.update({"vin": trade_in_vehicle.vin})
        params.update(cls.get_adjustments(trade_in_vehicle))
        params.update(cls.get_condition(trade_in_vehicle))
        params.update({"mileage": trade_in_vehicle.mileage})
        params.update({"zip": zip_code})
        return params
