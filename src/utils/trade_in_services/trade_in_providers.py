from abc import abstractmethod

import demjson

from src.rest_api.services import Service
from src.web.entities.trade_in_entity import TradeInEntity, TradeInProvider


class TradeInService(Service):
    _Service__backend_link = "https://cbo-test.gubagoo.io/"

    @classmethod
    def get_route(cls) -> str:
        return cls.get_url()

    @classmethod
    @abstractmethod
    def get_default_price(cls, uuid: str, trade_in_vehicle: TradeInEntity, zip_code: str):
        pass

    @classmethod
    @abstractmethod
    def get_makes(cls, uuid: str = None, year: str = None):
        pass

    @classmethod
    @abstractmethod
    def get_models(cls, uuid: str = None, year: str = None, make_id: str = None):
        pass

    @classmethod
    @abstractmethod
    def get_trims(cls, uuid: str = None, year: str = None, model: str = None, make=None):
        pass

    @classmethod
    @abstractmethod
    def get_trim_id(cls, uuid: str, trade_in_vehicle: TradeInEntity):
        pass

    @classmethod
    @abstractmethod
    def get_adjustments(cls, trade_in_vehicle: TradeInEntity):
        pass

    @classmethod
    @abstractmethod
    def get_condition(cls, trade_in_vehicle: TradeInEntity):
        return {"condition": trade_in_vehicle.condition}

    @classmethod
    @abstractmethod
    def get_params(cls, uuid: str, trade_in_vehicle: TradeInEntity, zip_code: str) -> dict:
        pass

    @classmethod
    def get_details_by_licence(cls, uuid: str, trade_in_vehicle: TradeInEntity):
        params = {"state": trade_in_vehicle.state[:2].upper(), "plate": trade_in_vehicle.license_plate}
        url = cls.get_route() + f"trade-in/vehicle/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)


def get_trade_in_service(trade_in_provider: TradeInProvider) -> TradeInService:
    if trade_in_provider == TradeInProvider.KBB:
        from src.utils.trade_in_services.kbb_trade_in_service import KBBTradeInService
        return KBBTradeInService()
    elif trade_in_provider == TradeInProvider.ASBURY:
        from src.utils.trade_in_services.asbury_trade_in_service import AsburyTradeInService
        return AsburyTradeInService()
    elif trade_in_provider == TradeInProvider.TRADE_IN_VALET:
        from src.utils.trade_in_services.trade_in_valet_trade_in_service import TradeInValetTradeInService
        return TradeInValetTradeInService()
    elif trade_in_provider == TradeInProvider.TRADE_PENDING:
        from src.utils.trade_in_services.trade_pending_trade_In_service import TradePendingTradeInService
        return TradePendingTradeInService()
    else:
        raise NotImplementedError(f'not implemented service for {trade_in_provider}')


def get_default_price_key(trade_in_provider: TradeInProvider) -> str:
    return "original_value" if trade_in_provider == TradeInProvider.TRADE_PENDING else "value"
