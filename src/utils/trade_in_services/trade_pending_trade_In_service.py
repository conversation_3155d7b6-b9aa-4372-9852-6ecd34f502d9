import demjson

from src.utils.trade_in_services.trade_in_providers import TradeInService
from src.web.entities.trade_in_entity import TradeInEntity


class TradePendingTradeInService(TradeInService):
    @classmethod
    def get_default_price(cls, uuid: str, trade_in_vehicle: TradeInEntity, zip_code: str):
        params = cls.get_params(uuid, trade_in_vehicle, zip_code)
        url = cls.get_route() + f"trade-in/value/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_makes(cls, uuid: str = None, year: str = None):
        pass

    @classmethod
    def get_models(cls, uuid: str = None, year: str = None, make_id: str = None):
        pass

    @classmethod
    def get_trims(cls, uuid: str = None, year: str = None, model: str = None, make=None):
        pass

    @classmethod
    def get_trim_id_by_vin(cls, uuid: str, vin: str):
        params = {"vin": vin}
        url = cls.get_route() + f"trade-in/trims/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_adjustments(cls, trade_in_vehicle: TradeInEntity):
        pass

    @classmethod
    def get_condition(cls, trade_in_vehicle: TradeInEntity):
        pass

    @classmethod
    def get_params(cls, uuid: str, trade_in_vehicle: TradeInEntity, zip_code: str) -> dict:
        params = {}
        if trade_in_vehicle.license_plate:
            result = cls.get_details_by_licence(uuid, trade_in_vehicle)
            params.update({"vin": result["vin"]})
        elif not trade_in_vehicle.vin:
            params.update(cls.get_trim_id(uuid, trade_in_vehicle))
        else:
            params.update({"vin": trade_in_vehicle.vin})
            params.update(cls.get_adjustments(trade_in_vehicle))
        params.update({"zip": zip_code})
        params.update({"mileage": trade_in_vehicle.mileage})
        return params
