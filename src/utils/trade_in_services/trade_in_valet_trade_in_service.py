import demjson

from src.utils.trade_in_services.trade_in_providers import TradeInService
from src.web.entities.trade_in_entity import TradeInEntity


class TradeInValetTradeInService(TradeInService):
    @classmethod
    def get_adjustments(cls, trade_in_vehicle: TradeInEntity):
        pass

    @classmethod
    def get_default_price(cls, uuid: str, trade_in_vehicle: TradeInEntity, zip_code: str):
        params = cls.get_params(uuid, trade_in_vehicle, zip_code)
        url = cls.get_route() + f"trade-in-valet/values/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_trim_id(cls, uuid: str, trade_in_vehicle: TradeInEntity):
        makes = cls.get_makes(uuid, trade_in_vehicle.year)
        make = [make for make in makes['makes'] if make == trade_in_vehicle.make]
        assert len(make) == 1
        models = cls.get_models(uuid, trade_in_vehicle.year, make[0])
        model = [model for model in models['models'] if model == trade_in_vehicle.model]
        assert len(model) == 1

    @classmethod
    def get_makes(cls, uuid: str = None, year: str = None):
        params = {"year": year}
        url = cls.get_route() + f"trade-in-valet/makes/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_models(cls, uuid: str = None, year: str = None, make_id: str = None):
        params = {"year": year, "make": make_id}
        url = cls.get_route() + f"trade-in-valet/models/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_trims(cls, uuid: str = None, year: str = None, model_id: str = None, make=None):
        params = {"year": year, "model": model_id}
        url = cls.get_route() + f"trade-in-valet/trims/{uuid}"
        response = cls.send_get_request_no_log(url, params=params)
        return demjson.decode(response.text)

    @classmethod
    def get_condition(cls, trade_in_vehicle: TradeInEntity):
        return {"condition-type": trade_in_vehicle.condition}

    @classmethod
    def get_params(cls, uuid: str, trade_in_vehicle: TradeInEntity, zip_code: str) -> dict:
        params = {}
        if trade_in_vehicle.license_plate:
            result = cls.get_details_by_licence(uuid, trade_in_vehicle)
            params.update({"vin": result["vin"]})
            params.update({"trim": result["trim"]})
        elif not trade_in_vehicle.vin:
            params.update(cls.get_trim_id(uuid, trade_in_vehicle))
        else:
            params.update({"vin": trade_in_vehicle.vin})
            params.update(cls.get_adjustments(trade_in_vehicle))
            params.update({"zip": zip_code})
        params.update(cls.get_condition(trade_in_vehicle))
        params.update({"odometer": trade_in_vehicle.mileage})
        return params