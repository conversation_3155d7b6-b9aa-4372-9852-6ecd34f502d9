class CategoryEntity:
    def __init__(self, name: str = None, matched_statuses: list = None, message_regex: str = None):
        self.name = name
        self.matchedStatuses = matched_statuses
        self.messageRegex = message_regex


class CategoryFactory:
    @classmethod
    def create_category(cls, name, matched_statuses, message_regex):
        return CategoryEntity(name=name, matched_statuses=matched_statuses, message_regex=message_regex)

    @classmethod
    def create_selenium_time_out_category(cls):
        return CategoryEntity(name="Selenium TimeoutException", matched_statuses=["broken"],
                              message_regex=".*selenium.*")

    @classmethod
    def create_inventory_issue_category(cls):
        return CategoryEntity(name="Inventory issue", matched_statuses=["broken", "failed"],
                              message_regex=".*Vehicle details was not found by vin number.*")
