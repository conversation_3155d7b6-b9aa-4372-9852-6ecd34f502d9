import datetime
import random

import dateutil.relativedelta as REL

UPCOMING_HOLIDAYS = {
    datetime.date(2000, 1, 1): "No Holiday",
    datetime.date(2025, 1, 1): "New Year's Day",
    datetime.date(2025, 1, 20): "Martin <PERSON> King Jr. Day",
    datetime.date(2025, 2, 17): "Presidents' Day",
    datetime.date(2025, 3, 1): "St. David's Day",
    datetime.date(2025, 3, 3): "Read Across America Day",
    datetime.date(2025, 3, 7): "Employee Appreciation Day",
    datetime.date(2025, 3, 17): "St. Patrick's Day",
    datetime.date(2025, 3, 29): "National Vietnam War Veterans Day",
    datetime.date(2025, 3, 30): "Doctors' Day",
    datetime.date(2025, 4, 6): "National Tartan Day",
    datetime.date(2025, 4, 8): "National Library Workers' Day",
    datetime.date(2025, 4, 15): "Tax Day",
    datetime.date(2025, 4, 16): "Emancipation Day",
    datetime.date(2025, 4, 18): "Good Friday",
    datetime.date(2025, 4, 20): "Easter Sunday",
    datetime.date(2025, 4, 21): "Easter Monday",
    datetime.date(2025, 4, 23): "Administrative Professionals Day",
    datetime.date(2025, 4, 24): "Take our Daughters and Sons to Work Day",
    datetime.date(2025, 5, 1): "Law Day",
    datetime.date(2025, 5, 3): "National Explosive Ordnance Disposal (EOD) Day",
    datetime.date(2025, 5, 5): "Cinco de Mayo",
    datetime.date(2025, 5, 6): "National Nurses Day",
    datetime.date(2025, 5, 9): "Military Spouse Appreciation Day",
    datetime.date(2025, 5, 11): "Mother's Day",
    datetime.date(2025, 5, 15): "Peace Officers Memorial Day",
    datetime.date(2025, 5, 16): "National Defense Transportation Day",
    datetime.date(2025, 5, 17): "Armed Forces Day",
    datetime.date(2025, 5, 21): "Emergency Medical Services for Children Day",
    datetime.date(2025, 5, 22): "National Maritime Day",
    datetime.date(2025, 5, 26): "Memorial Day",
    datetime.date(2025, 6, 1): "First Day of Pride Month",
    datetime.date(2025, 6, 8): "Native American Day",
    datetime.date(2025, 6, 12): "Loving Day",
    datetime.date(2025, 6, 14): "Army Birthday",
    datetime.date(2025, 6, 15): "Father's Day",
    datetime.date(2025, 6, 16): "Juneteenth Freedom Day (substitute)",
    datetime.date(2025, 6, 17): "Bunker Hill Day",
    datetime.date(2025, 6, 19): "Juneteenth",
    datetime.date(2025, 6, 20): "Juneteenth Day",
    datetime.date(2025, 6, 21): "Juneteenth",
    datetime.date(2025, 7, 4): "Independence Day",
    datetime.date(2025, 7, 14): "Bastille Day",
    datetime.date(2025, 7, 16): "Rural Transit Day",
    datetime.date(2025, 7, 27): "Parents' Day",
    datetime.date(2025, 8, 3): "American Family Day",
    datetime.date(2025, 8, 4): "Coast Guard Birthday",
    datetime.date(2025, 8, 7): "Purple Heart Day",
    datetime.date(2025, 8, 17): "National Navajo Code Talkers Day",
    datetime.date(2025, 8, 19): "National Aviation Day",
    datetime.date(2025, 9, 1): "Labor Day",
    datetime.date(2025, 9, 6): "Carl Garner Federal Lands Cleanup Day",
    datetime.date(2025, 9, 7): "National Grandparents Day",
    datetime.date(2025, 9, 9): "California Admission Day",
    datetime.date(2025, 9, 11): "Patriot Day",
    datetime.date(2025, 9, 14): "Constitution Commemoration Day",
    datetime.date(2025, 9, 15): "First Day of National Hispanic Heritage Month",
    datetime.date(2025, 9, 17): "Constitution Day and Citizenship Day",
    datetime.date(2025, 9, 18): "Air Force Birthday",
    datetime.date(2025, 9, 19): "National POW/MIA Recognition Day",
    datetime.date(2025, 9, 23): "Rosh Hashana",
    datetime.date(2025, 9, 27): "National Public Lands Day",
    datetime.date(2025, 9, 28): "Gold Star Mother's Day",
    datetime.date(2025, 10, 2): "Yom Kippur",
    datetime.date(2025, 10, 6): "Frances Xavier Cabrini Day",
    datetime.date(2025, 10, 13): "Navy Birthday",
    datetime.date(2025, 10, 15): "White Cane Safety Day",
    datetime.date(2025, 10, 16): "Boss's Day",
    datetime.date(2025, 10, 18): "Sweetest Day",
    datetime.date(2025, 10, 31): "Halloween",
    datetime.date(2025, 11, 1): "First Day of Native American Heritage Month",
    datetime.date(2025, 11, 10): "Marine Corps Birthday",
    datetime.date(2025, 11, 11): "Veterans Day",
    datetime.date(2025, 11, 27): "Thanksgiving Day",
    datetime.date(2025, 11, 28): "Presidents' Day (Observed)",
    datetime.date(2025, 12, 1): "Cyber Monday",
    datetime.date(2025, 12, 2): "Giving Tuesday",
    datetime.date(2025, 12, 6): "St Nicholas Day",
    datetime.date(2025, 12, 13): "National Guard Birthday",
    datetime.date(2025, 12, 15): "Bill of Rights Day",
    datetime.date(2025, 12, 17): "Pan American Aviation Day",
    datetime.date(2025, 12, 24): "Christmas Eve",
    datetime.date(2025, 12, 25): "Christmas Day",
    datetime.date(2025, 12, 31): "New Year's Eve",
    datetime.date(2026, 1, 1): "New Year's Day",
    datetime.date(2026, 1, 13): "Stephen Foster Memorial Day",
    datetime.date(2026, 1, 19): "Martin Luther King Jr. Day",
    datetime.date(2026, 1, 29): "Kansas Day",
    datetime.date(2026, 2, 1): "National Freedom Day",
    datetime.date(2026, 2, 2): "Groundhog Day",
    datetime.date(2026, 2, 4): "Rosa Parks Day",
    datetime.date(2026, 2, 6): "National Wear Red Day",
    datetime.date(2026, 2, 14): "Valentine's Day",
    datetime.date(2026, 2, 16): "Presidents' Day",
    datetime.date(2026, 2, 17): "Lunar New Year",
    datetime.date(2026, 3, 1): "St. David's Day",
    datetime.date(2026, 3, 2): "Read Across America Day",
    datetime.date(2026, 3, 6): "Employee Appreciation Day",
    datetime.date(2026, 3, 17): "St. Patrick's Day",
    datetime.date(2026, 3, 29): "National Vietnam War Veterans Day",
    datetime.date(2026, 3, 30): "Doctors' Day",
    datetime.date(2026, 4, 3): "Good Friday",
    datetime.date(2026, 4, 5): "Easter Sunday",
    datetime.date(2026, 4, 6): "Easter Monday",
    datetime.date(2026, 4, 14): "National Library Workers' Day",
    datetime.date(2026, 4, 15): "Tax Day",
    datetime.date(2026, 4, 16): "Emancipation Day",
    datetime.date(2026, 4, 20): "Patriots' Day",
    datetime.date(2026, 4, 22): "Administrative Professionals Day",
    datetime.date(2026, 4, 23): "Take our Daughters and Sons to Work Day",
    datetime.date(2026, 5, 1): "Law Day",
    datetime.date(2026, 5, 2): "National Explosive Ordnance Disposal (EOD) Day",
    datetime.date(2026, 5, 5): "Cinco de Mayo",
    datetime.date(2026, 5, 6): "National Nurses Day",
    datetime.date(2026, 5, 7): "National Day of Prayer",
    datetime.date(2026, 5, 8): "Native American Day",
    datetime.date(2026, 5, 10): "Mother's Day",
    datetime.date(2026, 5, 15): "Peace Officers Memorial Day",
    datetime.date(2026, 5, 16): "Armed Forces Day",
    datetime.date(2026, 5, 20): "Emergency Medical Services for Children Day",
    datetime.date(2026, 5, 22): "National Maritime Day",
    datetime.date(2026, 5, 25): "Memorial Day",
    datetime.date(2026, 6, 1): "First Day of Pride Month",
    datetime.date(2026, 6, 7): "Native American Day",
    datetime.date(2026, 6, 12): "Loving Day",
    datetime.date(2026, 6, 14): "Army Birthday",
    datetime.date(2026, 6, 15): "Juneteenth Freedom Day (substitute)",
    datetime.date(2026, 6, 17): "Bunker Hill Day",
    datetime.date(2026, 6, 19): "Juneteenth",
    datetime.date(2026, 6, 20): "Juneteenth Day",
    datetime.date(2026, 6, 21): "Father's Day",
    datetime.date(2026, 7, 3): "Independence Day (substitute)",
    datetime.date(2026, 7, 4): "Independence Day",
    datetime.date(2026, 7, 14): "Bastille Day",
    datetime.date(2026, 7, 16): "Rural Transit Day",
    datetime.date(2026, 7, 26): "Parents' Day",
    datetime.date(2026, 7, 27): "National Korean War Veterans Armistice Day",
    datetime.date(2026, 8, 2): "American Family Day",
    datetime.date(2026, 8, 4): "Coast Guard Birthday",
    datetime.date(2026, 8, 7): "Purple Heart Day",
    datetime.date(2026, 8, 16): "National Navajo Code Talkers Day",
    datetime.date(2026, 8, 19): "National Aviation Day",
    datetime.date(2026, 9, 7): "Labor Day",
    datetime.date(2026, 9, 9): "California Admission Day",
    datetime.date(2026, 9, 11): "First Responders Day",
    datetime.date(2026, 9, 12): "Carl Garner Federal Lands Cleanup Day",
    datetime.date(2026, 9, 13): "National Grandparents Day",
    datetime.date(2026, 9, 15): "First Day of National Hispanic Heritage Month",
    datetime.date(2026, 9, 17): "Constitution Day and Citizenship Day",
    datetime.date(2026, 9, 18): "Air Force Birthday",
    datetime.date(2026, 9, 21): "Yom Kippur",
    datetime.date(2026, 9, 25): "Native American Day",
    datetime.date(2026, 9, 26): "National Public Lands Day",
    datetime.date(2026, 9, 27): "Gold Star Mother's Day",
    datetime.date(2026, 10, 5): "Frances Xavier Cabrini Day",
    datetime.date(2026, 10, 6): "German American Day",
    datetime.date(2026, 10, 12): "Columbus Day",
    datetime.date(2026, 10, 13): "Navy Birthday",
    datetime.date(2026, 10, 15): "White Cane Safety Day",
    datetime.date(2026, 10, 16): "Boss's Day",
    datetime.date(2026, 10, 17): "Sweetest Day",
    datetime.date(2026, 10, 31): "Halloween",
    datetime.date(2026, 11, 1): "First Day of Native American Heritage Month",
    datetime.date(2026, 11, 10): "Marine Corps Birthday",
    datetime.date(2026, 11, 11): "Veterans Day",
    datetime.date(2026, 11, 26): "Thanksgiving Day",
    datetime.date(2026, 11, 27): "Presidents' Day",
    datetime.date(2026, 11, 30): "Cyber Monday",
    datetime.date(2026, 12, 1): "Rosa Parks Day",
    datetime.date(2026, 12, 6): "St Nicholas Day",
    datetime.date(2026, 12, 13): "National Guard Birthday",
    datetime.date(2026, 12, 15): "Bill of Rights Day",
    datetime.date(2026, 12, 17): "Pan American Aviation Day",
    datetime.date(2026, 12, 24): "Christmas Eve",
    datetime.date(2026, 12, 25): "Christmas Day",
    datetime.date(2026, 12, 31): "New Year's Eve",
}


holidays_dates = [i for i in UPCOMING_HOLIDAYS.keys() if
    datetime.date.today() <= i <= datetime.date.today() + REL.relativedelta(days=30)]

if 0 == len(holidays_dates):
    holidays_dates = [datetime.date(2000, 1, 1)]

upcoming_holidays_dates = holidays_dates.copy()
if datetime.date.today() in upcoming_holidays_dates:
    upcoming_holidays_dates.remove(datetime.date.today())


def next_date_not_in_holiday_list(range_start=1):
    for i in range(range_start, 28):
        event_date = (datetime.datetime.today() + datetime.timedelta(days=i)).date()
        if event_date not in holidays_dates:
            return event_date


class WD:
    def __init__(self, from_h=None, to=None, active=True):
        self.from_h = from_h
        self.to = to
        self.active = active
        self.closed = not active

    def __dict__(self):
        return {"from": self.from_h, "to": self.to, "active": self.active, "closed": self.closed}


class DWD:
    def __init__(self, sales: WD = None, service: WD = None, parts: WD = None,
   holiday=None, special_event=None, re_apply="1", note=None, date=None):
        self.sales = sales
        self.parts = parts
        self.service = service
        self.holiday = holiday
        self.special_event = special_event
        self.re_apply = re_apply
        self.date = date
        self.note = note

    def __dict__(self):
        dict_dwd = {"sales": self.sales.__dict__(),
"parts": self.parts.__dict__(),
"service": self.service.__dict__(),
"holiday": self.holiday,
"special_event": self.special_event,
"re_apply": self.re_apply}
        if not dict_dwd["holiday"]:
            dict_dwd.pop("holiday")
        else:
            dict_dwd.pop("special_event")
        return dict_dwd


class DaySelect:
    EVERY_DAY = "every_day"
    WEEKDAYS = "weekdays"
    WEEKENDS = "weekends"


def get_correct_time_ranges():
    current_time = datetime.datetime.now().time()

    two_hour_before = (datetime.datetime.combine(datetime.datetime.today(), current_time) - datetime.timedelta(hours=2)).time()
    two_hour_after = (datetime.datetime.combine(datetime.datetime.today(), current_time) + datetime.timedelta(hours=2)).time()

    time_range_before_hour = two_hour_before.hour
    time_range_before_minute = random.choice([0, 15, 30, 45])

    time_range_after_hour = two_hour_after.hour
    time_range_after_minute = random.choice([0, 15, 30, 45])

    return str(time_range_before_hour), str(time_range_before_minute), str(time_range_after_hour), str(time_range_after_minute)


def get_incorrect_time_ranges():
    current_time = datetime.datetime.now().time()

    two_hour_after = (datetime.datetime.combine(datetime.datetime.today(), current_time) + datetime.timedelta(hours=2)).time()
    four_hour_after = (datetime.datetime.combine(datetime.datetime.today(), current_time) + datetime.timedelta(hours=4)).time()

    time_range_after_twohrs_hours = two_hour_after.hour
    time_range_after_twohrs_minute = random.choice([0, 15, 30, 45])

    time_range_after_fourhrs_hours = four_hour_after.hour
    time_range_after_fourhrs_minute = random.choice([0, 15, 30, 45])

    return str(time_range_after_twohrs_hours), str(time_range_after_twohrs_minute), str(time_range_after_fourhrs_hours), str(time_range_after_fourhrs_minute)


def get_correct_date_ranges():
    today = datetime.date.today()

    one_month_before = (today - datetime.timedelta(days=30)).strftime('%m/%d/%Y')
    one_month_after = (today + datetime.timedelta(days=30)).strftime('%m/%d/%Y')

    return str(one_month_before), str(one_month_after)


def get_incorrect_date_ranges():

    today = datetime.datetime.today().date()

    one_month_after = (today + datetime.timedelta(days=30)).strftime('%m/%d/%Y')
    two_month_after = (today + datetime.timedelta(days=60)).strftime('%m/%d/%Y')

    return str(one_month_after), str(two_month_after)


def get_reset_date_ranges():

    today = datetime.datetime.today().date()

    one_month_after = (today - datetime.timedelta(days=365)).strftime('%m/%d/%Y')
    two_month_after = (today + datetime.timedelta(days=365)).strftime('%m/%d/%Y')

    return str(one_month_after), str(two_month_after)


class DayTimeConditionEntity:

    def __init__(self, day_type, time_from, time_from_min, time_to, time_to_min):
        self.day_type = day_type
        self.time_from = time_from
        self.time_from_min = time_from_min
        self.time_to = time_to
        self.time_to_min = time_to_min


class DayTimeFactory:

    @classmethod
    def everyday_correct_time(cls):
        return DayTimeConditionEntity(DaySelect.EVERY_DAY, *get_correct_time_ranges())

    @classmethod
    def everyday_incorrect_time(cls):
        return DayTimeConditionEntity(DaySelect.EVERY_DAY, *get_incorrect_time_ranges())

    @classmethod
    def weekdays_incorrect_time(cls):
        return DayTimeConditionEntity(DaySelect.WEEKDAYS, *get_incorrect_time_ranges())

    @classmethod
    def smart_weekday_correct_time(cls):
        day_today = DaySelect.WEEKDAYS if datetime.datetime.today().weekday() in range(0, 5) else DaySelect.WEEKENDS
        return DayTimeConditionEntity(day_today, *get_correct_time_ranges())

    @classmethod
    def smart_weekday_incorrect_time(cls):
        day_today = DaySelect.WEEKDAYS if datetime.datetime.today().weekday() in range(0, 5) else DaySelect.WEEKENDS
        return DayTimeConditionEntity(day_today, *get_incorrect_time_ranges())

    @classmethod
    def smart_weekday_name_correct_time(cls):
        day_today = datetime.datetime.today().strftime('%A').lower()
        return DayTimeConditionEntity(day_today, *get_correct_time_ranges())

    @classmethod
    def smart_incorrect_weekday_name_correct_time(cls):
        day_tomorrow = (datetime.datetime.today() + datetime.timedelta(days=1)).strftime('%A').lower()
        return DayTimeConditionEntity(day_tomorrow, *get_correct_time_ranges())


class DateRangeConditionEntity:

    def __init__(self, date_from, date_to):
        self.date_from = date_from
        self.date_to = date_to


class DateRangeFactory:

    @classmethod
    def correct_date_range(cls):
        return DateRangeConditionEntity(*get_correct_date_ranges())

    @classmethod
    def incorrect_date_range(cls):
        return DateRangeConditionEntity(*get_incorrect_date_ranges())

    @classmethod
    def reset_date_range_value(cls):
        return DateRangeConditionEntity(*get_reset_date_ranges())
