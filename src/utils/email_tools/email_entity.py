class AttachmentEntity:
    def __init__(self, filename=None, size=None, contentDisposition=None, checksum=None, contentType=None,
                 downloadUrl=None):
        self.filename = filename
        self.size = size
        self.contentDisposition = contentDisposition
        self.checksum = checksum
        self.contentType = contentType
        self.downloadUrl = downloadUrl


class EmailEntity:
    def __init__(self, cc=None, date=None, attachments=None, envelope_to=None,
                 subject=None, envelope_from=None, html=None, text_content=None):
        self.cc = cc
        self.date = date
        self.attachments = attachments
        self.envelope_to = envelope_to
        self.subject = subject
        self.envelope_from = envelope_from
        self.html = html
        self.text_content = text_content
