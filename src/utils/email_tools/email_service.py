import time

from kom_framework.src.general import Log
from src.rest_api.services import Service
from src.utils.decorators import step_decorator
from src.utils.email_tools.email_factory import EmailFactory


class EmailService(Service):

    @classmethod
    def get_route(cls) -> str:
        return "https://api.testmail.app/api/json?" + "apikey=0caaa951-5838-4b03-bfb1-47e6e2822755"

    def do_request_email(cls, namespace="tddth", tag="report", timestamp_from_delta=0, timestamp_from=None,
                         wait_time=240, livequery=True, count=1):
        url = cls.get_route() + f'&namespace={namespace}&pretty=true&tag={tag}'
        if timestamp_from_delta > 0:
            from datetime import datetime, timezone, timedelta
            timestamp_from = int((datetime.now(timezone.utc) + timedelta(minutes=timestamp_from_delta)).timestamp() * 1e3)
            url += f"&timestamp_from={timestamp_from}"
        elif timestamp_from:
            url += f"&timestamp_from={timestamp_from}"
        if livequery:
            url += "&livequery=true"
        end_time = time.time() + wait_time
        while True:
            Log.info(f"API - EmailService: Get Email from URL: '{url}'")
            response = cls.send_get_request_no_log(url, timeout=wait_time)
            if response.status_code == 200 and response.json()['count'] >= count:
               return response
            if time.time() > end_time:
                return None
            time.sleep(5)

    @classmethod
    @step_decorator('API - EmailService: Get Email')
    def get_email(cls, namespace="tddth", tag="report", timestamp_from_delta=0, timestamp_from=None, wait_time=240,
                  livequery=True, count=0):
        response = cls.do_request_email(cls, namespace=namespace, tag=tag, timestamp_from_delta=timestamp_from_delta,
                                        timestamp_from=timestamp_from, wait_time=wait_time, livequery=livequery,
                                        count=count)
        json_response = response.json()
        result = [EmailFactory.deserialize(email) for email in json_response['emails']]
        Log.info(f"API - Emails: {result}")
        return result

    @classmethod
    @step_decorator('API - EmailService: Get Email')
    def get_emails_count(cls, namespace="tddth", tag="report", timestamp_from_delta=0, timestamp_from=None,
                         wait_time=240, livequery=True, count=0):
        response = cls.do_request_email(cls, namespace=namespace, tag=tag, timestamp_from_delta=timestamp_from_delta,
                                        timestamp_from=timestamp_from, wait_time=wait_time, livequery=livequery,
                                        count=count)
        json_response = response.json()
        return json_response['count']


