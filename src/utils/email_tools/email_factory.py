from src.utils.email_tools.email_entity import AttachmentEntity, EmailEntity


class AttachmentFactory:

    @classmethod
    def deserialize(cls, json_obj: dict) -> AttachmentEntity:
        return AttachmentEntity(filename=json_obj.get('filename'),
                                size=json_obj.get('size'),
                                contentDisposition=json_obj.get('contentDisposition'),
                                checksum=json_obj.get('checksum'),
                                contentType=json_obj.get('contentType'),
                                downloadUrl=json_obj.get('downloadUrl'))


class EmailFactory:
    @classmethod
    def deserialize(cls, json_obj: dict) -> EmailEntity:
        return EmailEntity(cc=json_obj.get('cc'),
                           date=json_obj.get('date'),
                           attachments=[AttachmentFactory.deserialize(attach) for attach in json_obj.get('attachments')],
                           envelope_to=json_obj.get('envelope_to'),
                           subject=json_obj.get('subject'),
                           envelope_from=json_obj.get('envelope_from'),
                           html=json_obj.get('html'),
                           text_content=json_obj.get('text'))
