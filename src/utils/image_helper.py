import os
import urllib

from PIL import Image, ImageChops

from src.utils.decorators import step_decorator
from test import get_resource_file_path


class ImageHelper:

    @staticmethod
    @step_decorator('UTIL - Compare images {0} and {1}')
    def compare(img_src, exp_file):
        captured = ImageHelper.open_image_from_web(img_src, exp_file, 'RGB')
        expected = Image.open(exp_file).convert('RGB')
        return ImageChops.difference(captured, expected).getbbox()

    @staticmethod
    @step_decorator('UTIL - Compare images {0} and {1} with tolerance')
    def is_equal(image_a, image_b, tolerance=0.0):
        return ImageHelper.image_diff_percent(image_a, image_b) <= tolerance

    @staticmethod
    def open_image_from_web(image_source, image_template, convert=None):
        captured = get_resource_file_path(f"captured{os.path.splitext(image_template)[1]}")
        urllib.request.urlretrieve(image_source, captured)
        opened_image = Image.open(captured).convert(convert) if convert else Image.open(captured)
        os.remove(captured)
        return opened_image

    @staticmethod
    def image_diff_percent(image_a, image_b):
        close_a = False
        close_b = False
        if isinstance(image_a, str):
            image_a = ImageHelper.open_image_from_web(image_a, image_b)
            close_a = True
        if isinstance(image_b, str):
            image_b = Image.open(image_b)
            close_b = True
        try:
            input_images_histogram_diff = ImageHelper.image_diff(image_a, image_b)
            black_reference_image = Image.new('RGB', image_a.size, (0, 0, 0))
            white_reference_image = Image.new('RGB', image_a.size, (255, 255, 255))
            worst_bw_diff = ImageHelper.image_diff(black_reference_image, white_reference_image)
            percentage_histogram_diff = (input_images_histogram_diff / float(worst_bw_diff)) * 100
        finally:
            if close_a:
                image_a.close()
            if close_b:
                image_b.close()
        return percentage_histogram_diff

    @staticmethod
    def image_diff(image_a, image_b):
        return ImageHelper.total_histogram_diff(ImageHelper.pixel_diff(image_a, image_b))

    @staticmethod
    def pixel_diff(image_a, image_b):
        if image_a.size != image_b.size:
            raise ImageCompareException(
                "different image sizes, can only compare same size images: A=" + str(image_a.size) + " B=" + str(
                    image_b.size))
        if image_a.mode != image_b.mode:
            raise ImageCompareException(
                "different image mode, can only compare same mode images: A=" + str(image_a.mode) + " B=" + str(
                    image_b.mode))
        diff = ImageChops.difference(image_a, image_b)
        diff = diff.convert('L')
        return diff

    @staticmethod
    def total_histogram_diff(pixel_diff):
        return sum(i * n for i, n in enumerate(pixel_diff.histogram()))


class ImageCompareException(Exception):
    """
    Custom Exception class for image compare exceptions.
    """
    pass
