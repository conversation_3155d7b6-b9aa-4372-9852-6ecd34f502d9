from kom_framework.src.web import hub_ip, hub_port

from src.rest_api.services import Service
from src.utils.decorators import step_decorator


class BrowserDownloadedFilesService(Service):

    @classmethod
    def get_route(cls) -> str:
        return f"http://{hub_ip}:{hub_port}/download/"

    @classmethod
    @step_decorator('API - BrowserDownloadedFilesService: Get Download')
    def get_download(cls, session_id):
        url = cls.get_route() + f'{session_id}/'
        response = cls.send_get_request(url)
        if response.status_code == 200:
            return response.text
        return None

    @classmethod
    @step_decorator('API - BrowserDownloadedFilesService: Get Download')
    def delete_download(cls, session_id, filename=None):
        url = cls.get_route() + f'{session_id}/{filename}'
        response = cls.send_delete_request(url)
        if response.status_code == 200:
            return response.text
