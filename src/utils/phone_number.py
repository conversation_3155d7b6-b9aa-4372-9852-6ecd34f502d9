import random


class PhoneNumber:

    @staticmethod
    def new_phone():
        return '999' + str(random.randint(10 ** 6, 10 ** 7 - 1))

    @staticmethod
    def new_valid_phone():
        return '900' + "".join([str(random.randint(2, 9)) for i in range(4)]) +\
               "".join([str(random.randint(0, 9)) for i in range(3)])

    @staticmethod
    def format_phone(phone_number):
        return '-'.join([phone_number[:3], phone_number[3:6], phone_number[6:]])

    @staticmethod
    def format_simple_phone(phone_number):
        return phone_number.replace('-', "").replace(' ', "").replace('(', "").replace(')', "")

    @staticmethod
    def format_us_phone(phone_number):
        return f"({phone_number[:3]}) {phone_number[3:6]}-{phone_number[6:]}"
