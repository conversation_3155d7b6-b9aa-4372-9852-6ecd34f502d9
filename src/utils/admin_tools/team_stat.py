import datetime
import os
import re
import sys

import pandas as pd
import matplotlib.pyplot as plt

sys.path.append(os.getcwd())

from kom_framework.utils.testrail.testrail_helper import TestRailHelper
from src.utils.admin_tools import test_rail_url, test_rail_user_name, test_rail_api_key

project_id = 5
test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, project_id)


def get_case_field_config(name):
    for field in test_rail.service.get_case_fields():
        if field['name'] == name:
            matches = re.findall(r"(\d+),\s*([\w .]+)", field['configs'][0]["options"]["items"])
            return dict(matches)

user_list = {u['id']: u['name'] for u in test_rail.service.get_users()}
test_case_type = {case_type['id']: case_type['name'] for case_type in test_rail.service.get_case_types()}
test_case_priorities = {priority['id']: priority['name'] for priority in test_rail.service.get_priorities()}
components_config = get_case_field_config("components")
automation_type = get_case_field_config("automation_type")
device_mode = get_case_field_config("tags")



def parse_refs(str_list):
    return [str(x).strip().upper() if x else x for x in str_list.strip('[]').split(',')]


def parse_time(timestamp):
    dt = datetime.datetime.fromtimestamp(int(timestamp))
    return dt.strftime('%m/%d/%Y')


def parse_components(str_list):
    return [components_config[str(x).strip()] if x else x for x in str_list.strip('[]').split(',')]


def parse_user(user_id):
    return user_list[int(user_id)]


def parse_auto_type(auto_type):
    return automation_type[auto_type]


def parse_device_mode(mode):
    return [device_mode[str(x).strip()] if x else x for x in mode.strip('[]').split(',')]


def parse_case_type(case_type):
    return test_case_type[int(case_type)]


def parse_priority(priority):
    return test_case_priorities[int(priority)]


def parse_automated_on(automated_on):
    return int(automated_on) if automated_on != '' else automated_on


def build_progress_chart(case_created_automated, component_name):
    created_counts = case_created_automated.groupby('created_month').size().reset_index(name='created_count')
    automated_counts = case_created_automated.groupby('automated_month').size().reset_index(name='automated_count')
    merged_created_automated_counts = pd.merge(created_counts, automated_counts,
                                               left_on="created_month", right_on='automated_month', how='outer')
    merged_created_automated_counts['created_month'] = merged_created_automated_counts['created_month'].\
        fillna(merged_created_automated_counts['automated_month'])
    merged_created_automated_counts = merged_created_automated_counts.rename(columns={'created_month': 'date'})
    merged_created_automated_counts = merged_created_automated_counts.drop(columns=['automated_month'])
    merged_created_automated_counts['automated_count'] = merged_created_automated_counts['automated_count'].fillna(0)
    merged_created_automated_counts['automated_count'] = merged_created_automated_counts['automated_count'].astype(int)
    merged_created_automated_counts['created_count'] = merged_created_automated_counts['created_count'].fillna(0)
    merged_created_automated_counts['created_count'] = merged_created_automated_counts['created_count'].astype(int)
    # sort by date
    merged_created_automated_counts = merged_created_automated_counts.sort_values('date')
    merged_created_automated_counts = merged_created_automated_counts.reset_index(drop=True)

    merged_created_automated_counts['created_progress_count'] = merged_created_automated_counts['created_count'].cumsum()
    merged_created_automated_counts['automated_progress_count'] = merged_created_automated_counts['automated_count'].cumsum()
    merged_created_automated_pivot = merged_created_automated_counts[["date", "created_progress_count", "automated_progress_count"]]
    merged_created_automated_pivot.set_index('date', inplace=True)
    ax = merged_created_automated_pivot.plot(kind='area', stacked=False)
    ax.set_xlabel('Date')
    ax.set_ylabel('Test cases Count')
    ax.set_title(f'Test cases and Automated for {component_name}')


def build_priority_chart(group, name):
    priority_chart = pd.pivot(group, index="priority_id", columns="custom_automation_type", values="count")
    ax = priority_chart.plot(kind='bar', stacked=True)
    ax.set_xlabel('Priority')
    ax.set_ylabel('Number of Test IDs')
    ax.set_title(f'Test cases by Type and Priority for Component {name}')


def build_type_priority_spreading(merged_auto_type_component, case_priority):
    merged_priority_auto_type_component = pd.merge(merged_auto_type_component, case_priority, on='id')
    merged_auto_type_component_grouped = merged_priority_auto_type_component.groupby(
        ['custom_components', 'custom_automation_type', 'priority_id']).size().reset_index(name='count')
    result_overall = calculate_overall_auto_type_component(merged_priority_auto_type_component)
    merged_auto_type_component_grouped = pd.concat([merged_auto_type_component_grouped, result_overall], axis=0)

    grouped = merged_auto_type_component_grouped.groupby('custom_components')
    for name, group in grouped:
        build_priority_chart(group, name)


def build_created_automated_progress(case_created_automated, case_component):
    case_created_automated['created_month'] = pd.to_datetime(case_created_automated['created_on'], unit='s').dt.to_period('M')
    case_created_automated['automated_month'] = pd.to_datetime(case_created_automated['automated_on'], unit='s').dt.to_period('M')
    build_progress_chart(case_created_automated, "ALL")
    merged_created_automated_component = pd.merge(case_component, case_created_automated, on='id')

    grouped = merged_created_automated_component.groupby('custom_components')
    for name, group in grouped:
        build_progress_chart(group, name)


def build_coverage_chart(merged_auto_type_component_grouped):
    dfs = pd.pivot(merged_auto_type_component_grouped, index="custom_components",
                   columns="custom_automation_type", values="count")
    ax = dfs.plot(kind='bar', stacked=True)
    ax.set_xlabel('Component')
    ax.set_ylabel('Number of Test IDs')
    ax.set_title(f'Test cases coverage per Component')


def build_last_8_weeks_progress(df):
    df['date'] = pd.to_datetime(df['date'], unit='s').dt.to_period('D')
    df['week_start'] = df['date'] - pd.to_timedelta(df['date'].dt.dayofweek, unit='d')
    weekly_counts = df.groupby(['week_start', 'user_name']).size().unstack(fill_value=0)
    last_8_weeks = pd.date_range(end=df['week_start'].max().to_timestamp(), periods=8, freq='W').strftime('%Y-%m-%d')
    last_week_str = last_8_weeks.min()
    weekly_counts = weekly_counts[weekly_counts.index > last_week_str]
    weekly_counts.plot(kind='line', stacked=False)


def calculate_overall_auto_type_component(merged_priority_auto_type_component):
    result_overall = merged_priority_auto_type_component[~merged_priority_auto_type_component.duplicated(
        subset=['id'], keep="first")]
    result_overall = result_overall.groupby(['priority_id', "custom_automation_type"]).size().reset_index(name='count')
    result_overall['custom_components'] = "ALL"
    return result_overall


def main():
    filename = os.path.join(os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail{os.sep}"),
                            'TestRailCasesData.csv')
    test_rail_cases = pd.read_csv(filename, converters={'custom_components': parse_components,
                                                        "refs": parse_refs,
                                                        "custom_automation_type": parse_auto_type,
                                                        # "created_on": parse_time,
                                                        "created_by": parse_user,
                                                        "updated_by": parse_user,
                                                        "type_id": parse_case_type,
                                                        "priority_id": parse_priority,
                                                        "custom_tags": parse_device_mode,
                                                        "automated_on": parse_automated_on
                                                        })
    if 1:
        case_component = test_rail_cases[["id", "custom_components"]]
        case_component = case_component.explode('custom_components').reset_index(drop=True)
        case_automated_type = test_rail_cases[["id", "custom_automation_type"]]
        case_priority = test_rail_cases[["id", "priority_id"]]

        merged_auto_type_component = pd.merge(case_component, case_automated_type, on='id')
        merged_auto_type_component_grouped = merged_auto_type_component.groupby(
            ['custom_components', 'custom_automation_type']).size().reset_index(name='count')
        build_coverage_chart(merged_auto_type_component_grouped)

        build_type_priority_spreading(merged_auto_type_component, case_priority)

        case_created_automated = test_rail_cases[["id", "created_on", "automated_on"]]
        build_created_automated_progress(case_created_automated, case_component)

    created_progress = test_rail_cases[["id", "created_on", "created_by"]]
    created_progress = created_progress.rename(columns={'created_on': 'date'})
    created_progress = created_progress.rename(columns={'created_by': 'user_name'})
    build_last_8_weeks_progress(created_progress)

    created_progress = test_rail_cases[["id", "updated_on", "updated_by"]]
    created_progress = created_progress.rename(columns={'updated_on': 'date'})
    created_progress = created_progress.rename(columns={'updated_by': 'user_name'})
    build_last_8_weeks_progress(created_progress)

    plt.show()

    case_refs = test_rail_cases[["id", "refs"]]
    case_refs = case_refs.explode('refs').reset_index(drop=True)


if __name__ == '__main__':
    main()
