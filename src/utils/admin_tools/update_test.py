
from kom_framework.utils.testrail.testrail_helper import Test<PERSON><PERSON>Helper

from src.utils.admin_tools import test_rail_url, test_rail_user_name, test_rail_api_key


def main():
    project_id = 5
    suit_id=6
    test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, project_id)

    cases = test_rail.service.get_cases(project_id, suit_id)
    for test in cases:
        if test['section_id'] in [207577, 66516, 149956, 130256, 161430, 66529, 66530, 66531, 66532, 109781, 109783, 141521,
                                  109778, 116757, 120945, 211547, 211548, 228160, 242517, 230828, 109785,   111698, 109782] \
                and test['custom_components'] != [9]:
            test_rail.service.update_case(test['id'], data={"custom_components": [9]})


if __name__ == '__main__':
    main()
