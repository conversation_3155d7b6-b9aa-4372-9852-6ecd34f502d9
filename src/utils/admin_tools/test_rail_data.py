import csv
import os
import sys

sys.path.append(os.getcwd())

from kom_framework.src.general import Log
from kom_framework.utils.testrail.testrail_helper import TestRailHelper, TestRailAutoType
from src.utils.admin_tools import test_rail_api_key, test_rail_url, test_rail_user_name

test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, None)
test_rail_automation_field = 'custom_automation_type'
case_info = ["id", "title", "section_id", "template_id", "type_id", "priority_id", "milestone_id", "refs", "created_by",
             "created_on", "updated_by", "updated_on", "estimate", "estimate_forecast", "suite_id", "display_order",
             "is_deleted", "custom_automation_type", "custom_revision", "custom_preconds", "custom_steps",
             "custom_testrail_bdd_scenario", "custom_expected", "custom_steps_separated", "custom_mission",
             "custom_goals", "custom_tags", "custom_components", "automated_on", "history", "project", "suite"]
project_info = ['id', 'name', 'announcement', 'show_announcement', 'is_completed', 'completed_on', 'suite_mode',
                'default_role_id', 'url', 'users', 'groups']
plan_info = ['id', 'name', 'refs', 'description', 'milestone_id', 'assignedto_id', 'is_completed', 'completed_on',
             'passed_count', 'blocked_count', 'untested_count', 'retest_count', 'failed_count', 'custom_status1_count',
             'custom_status2_count', 'custom_status3_count', 'custom_status4_count', 'custom_status5_count',
             'custom_status6_count', 'custom_status7_count', 'project_id', 'created_on', 'created_by', 'url']
run_info = ['id', 'suite_id', 'name', 'description', 'milestone_id', 'assignedto_id', 'include_all', 'is_completed',
            'completed_on', 'config', 'config_ids', 'passed_count', 'blocked_count', 'untested_count', 'retest_count',
            'failed_count', 'custom_status1_count', 'custom_status2_count', 'custom_status3_count',
            'custom_status4_count', 'custom_status5_count', 'custom_status6_count', 'custom_status7_count',
            'project_id', 'plan_id', 'created_on', 'updated_on', 'refs', 'created_by', 'url', "entry_index", "entry_id"]
result_info = ['id', 'test_id', 'status_id', 'run_id', 'is_completed', 'created_on', 'assignedto_id', 'comment', 'version', 'elapsed',
               'defects', 'created_by', 'custom_step_results', 'custom_testrail_bdd_scenario_results', 'attachment_ids']
test_info = ['id', 'case_id', 'status_id', 'assignedto_id', 'run_id', 'title', 'template_id', 'type_id', 'priority_id',
             'estimate', 'estimate_forecast', 'refs', 'milestone_id', 'custom_automation_type', 'custom_revision',
             'custom_preconds', 'custom_steps', 'custom_testrail_bdd_scenario', 'custom_expected',
             'custom_steps_separated', 'custom_mission', 'custom_goals', 'sections_display_order',
             'cases_display_order', 'case_comments', 'custom_components', 'custom_tags', 'is_completed']

folder_path = os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail")

"""
    Projects -> Plans -> Runs -> Tests -> Results
    Projects -> Suites -> Cases
"""


def get_automated_date_api(history, created_on):
    history_dates = []
    for h in reversed(history):
        if h['changes'] is not None:
            for changes in h['changes']:
                if changes.get('label') and changes['label'] == 'Automation Type' and changes['new_text'] == ' Webdriver':
                    history_dates.append(h['created_on'])
                    break
    if len(history_dates) == 1:
        return history_dates[0]
    elif len(history_dates) > 1:
        return history_dates[-1]
    else:
        return created_on


def collecting_cases(projects):
    """
    Used to collect all test cases from each project
    :return:
        TestRailCasesData
    """
    print("COLLECTING TEST RAIL TEST CASES")
    test_cases = []
    for p in projects:
        suite_ids = [item['id'] for item in test_rail.service.get_suites(p['id'])]
        for s_id in suite_ids:
            cases = test_rail.service.get_cases(p['id'], s_id)
            for c in cases:
                # get history to find date of automation
                if c[test_rail_automation_field] == TestRailAutoType.WEBDRIVER:
                    history = test_rail.service.get_history_for_case(c['id'])
                    c["history"] = history
                    #if c[test_rail_automation_field] == TestRailAutoType.WEBDRIVER:
                    c['automated_on'] = get_automated_date_api(history, c['created_on'])

                c["project"] = p['name']
                c['suite'] = s_id
                test_cases.append(c)

    filename = os.path.join(folder_path, 'TestRailCasesData.csv')
    with open(filename, 'w') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=case_info)
        writer.writeheader()
        writer.writerows(test_cases)

    print("COLLECTING TEST RAIL TEST CASES FINISHED")


def collecting_all_runs():
    """
    Used to collect all projects, plans, runs
    :return:
        TestRailProjectsData
        TestRailPlansData
        TestRailRunsData
    """

    all_projects = test_rail.service.get_projects()
    all_runs = []
    all_plans = []

    for project in all_projects:
        plans = test_rail.service.get_plans(project['id'])
        all_plans.extend(plans)
        runs = test_rail.service.get_runs(project['id'])
        for r in runs:
            r['entry_index'] = None
            r["entry_id"] = None
        all_runs.extend(runs)
        for plan in plans:
            for entries in test_rail.service.get_plan(plan['id'])['entries']:
                all_runs.extend(entries["runs"])
    # Set up path
    filename = os.path.join(folder_path, 'TestRailProjectsData.csv')

    # Make sure the directory exists
    os.makedirs(folder_path, exist_ok=True)

    with open(filename, 'w') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=project_info)
        writer.writeheader()
        writer.writerows(all_projects)

    filename = os.path.join(folder_path, 'TestRailPlansData.csv')
    with open(filename, 'w') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=plan_info)
        writer.writeheader()
        writer.writerows(all_plans)

    filename = os.path.join(folder_path, 'TestRailRunsData.csv')
    with open(filename, 'w') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=run_info)
        writer.writeheader()
        writer.writerows(all_runs)
    return all_projects, all_plans, all_runs


def append_to_file(run_collected_results, run_id, filename, fieldsname, contant):
    Log.info(f"!!! Append run {run_id} with {len(run_collected_results)} {contant}")
    file_exists = os.path.exists(filename)
    with open(filename, 'a') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldsname)
        if not file_exists:
            writer.writeheader()
        writer.writerows(run_collected_results)


def read_from_file(all_runs, filename):
    file_res = []
    completed_run_list = set()

    if not os.path.exists(filename):
        print(f"File '{filename}' not found.")
    return completed_run_list

    csv_file = csv.DictReader(open(filename))
    for row in csv_file:
        if row['is_completed'].lower() == "true" and int(row['run_id']) in all_runs:
            completed_run_list.add(int(row['run_id']))
            file_res.append(dict(row))

    with open(filename, 'w') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=result_info)
        writer.writeheader()
        writer.writerows(file_res)

    return completed_run_list


def collecting_results(all_runs):
    """
    Used to collect all tests and results from each test run
    """
    Log.info(f'Number of Runs {len(all_runs)}')

    all_runs_set = set([run['id'] for run in all_runs])
    filename = os.path.join(folder_path, 'TestRailRunsTests.csv')
    read_from_file(all_runs_set, filename)
    filename = os.path.join(folder_path, 'TestRailRunsResults.csv')
    all_completed_runs = read_from_file(all_runs_set, filename)
    Log.info(f'Number of Runs pulled from file {len(all_completed_runs)}')

    to_analyze = all_runs_set - all_completed_runs
    to_analyze_ordered = [i for i in all_runs if i['id'] in to_analyze and i['is_completed']]
    to_analyze_ordered.extend([i for i in all_runs if i['id'] in to_analyze and not i['is_completed']])
    n_all = len(all_runs_set)
    n = len(to_analyze_ordered)
    Log.info(f'Number of Runs to analyze {n} of {n_all}')

    for run in to_analyze_ordered:
        collected_tests = test_rail.service.get_tests(run['id'])
        updated_collected_tests = [
            {**item, 'is_completed': run['is_completed']}
            for item in collected_tests
        ]
        filename = os.path.join(folder_path, 'TestRailRunsTests.csv')
        append_to_file(updated_collected_tests, run['id'], filename, test_info, "tests")

        collected_results = test_rail.service.get_results_for_run(run['id'])
        updated_collected_results = [
            {**item, 'run_id': run['id'], 'is_completed': run['is_completed']}
            for item in collected_results
        ]
        filename = os.path.join(folder_path, 'TestRailRunsResults.csv')
        append_to_file(updated_collected_results, run['id'], filename, result_info, "test results")

        n -= 1
        Log.info(f'Number of Runs to analyze {n} of {n_all}')
        # run_collected_results = []
        # Log.info(f"!!! Start run {run['id']} with {len(collected_tests)} tests")
        # for test in collected_tests:
        #     collected_results = test_rail.service.get_results(test['id'])
        #
        #     if collected_results is None:
        #         Log.warning(f"Run {run['id']} test case {test['id']} does not have any results")
        #         collected_results = []
        #     for r in collected_results:
        #         r['run_id'] = run['id']
        #         r['is_completed'] = run['is_completed']
        #     run_collected_results.extend(collected_results)
        # if len(run_collected_results) == 0:
        #     run_collected_results.append({'id': -1, 'test_id': -1, 'status_id': -1, 'run_id': run['id'],
        #                                   'is_completed': run['is_completed'],
        #                                   'created_on': -1, 'assignedto_id': -1, 'comment': None, 'version': None,
        #                                   'elapsed': None, 'defects': None, 'created_by': -1,
        #                                   'custom_step_results': None, 'attachment_ids': []})


def main():
    all_projects, all_plans, all_runs = collecting_all_runs()
    collecting_results(all_runs)
    collecting_cases(all_projects)


if __name__ == '__main__':
    main()
