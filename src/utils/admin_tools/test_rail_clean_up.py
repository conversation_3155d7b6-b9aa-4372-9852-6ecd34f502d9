import os

import sys
from time import time

sys.path.append(os.getcwd())

from kom_framework.src.general import Log
from kom_framework.utils.testrail.testrail_helper import TestRailHelper
from src.utils.admin_tools import test_rail_url, test_rail_user_name, test_rail_api_key

test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, None)
line_length = 120


def main():
    print("*" * line_length)
    print("STARTING TEST RAIL CLEANUP")
    time_stamp = int(time() - 13 * 7 * 86400)
    for project_id in test_rail.get_all_projects():
        plans = test_rail.service.get_old_plans(project_id, time_stamp)
        for plan in reversed(plans):
            try:
                test_rail.service.delete_plan(plan['id'])
            except:
                Log.warning(f"Plan {plan['id']} was not deleted")
            else:
                Log.info(f"Plan {plan['id']} deleted")
        runs = test_rail.service.get_old_runs(project_id, time_stamp)
        for run in reversed(runs):
            test_rail.service.delete_run(run['id'])
            Log.info(f"Run {run['id']} deleted")
    print("*" * line_length)
    print("TEST RAIL CLEANUP FINISHED")


if __name__ == '__main__':
    main()
