from enum import Enum


class VSPWidget(Enum):
    DISABLED = "Disabled"
    BUY_ONLINE = "buy"
    BUY_ONLINE_TRADE = "buy-trade"
    BUY_ONLINE_CREDIT = "buy-credit"
    BUY_ONLINE_TRADE_CREDIT = "buy-trade-credit"
    PAYMENTS_ONLY = "payment"
    TRADE_ONLY = "trade"
    CREDIT_ONLY = "credit"
    PAYMENT_TRADE = "payment-trade"
    PAYMENT_CREDIT = "payment-credit"
    PAYMENT_TRADE_CREDIT = "payment-trade-credit"
    UNLOCK_PRICE_PAYMENTS = "payment-unlock"
    UNLOCK_PRICE_TRADE = "unlock-trade"
    UNLOCK_PRICE_CREDIT = "unlock-credit"
    UNLOCK_PRICE_PAYMENTS_TRADE = "payment-unlock-trade"
    UNLOCK_PRICE_PAYMENTS_CREDIT = "payment-unlock-credit"
    UNLOCK_PRICE_PAYMENTS_TRADE_CREDIT = "payment-unlock-trade-credit"
    PAYMENTS_V2_ONLY = "payment2"
    PAYMENT_V2_TRADE = "payment2-trade"
    PAYMENT_V2_TRADE_CREDIT = "payment2-trade-credit"
    ASBURY_CLICKLANE_WIDGET = "buttons-asbury"
