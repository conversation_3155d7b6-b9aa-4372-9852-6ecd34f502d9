# Defining the weight of all items in pounds
kitchen_items = {
    "2 bar stools": 2 * 20,
    "Dreame": 29,
    "Panasonic microwave": 31.5261,
    "Ninja Indoor Grill": 22.44,
    "Philips coffee machine": 3.6,
    "Plates set 12": 44,
    "Cups 12": 11,
    "Instant pot": 18.5,
    "Braun food processor": 2,
    "Cookware Set": 36.7,
    "Pots & Pans": 13,
    "Glasses ±50": 45,
    "Food Storage Container": 35,
    "KitchenAid Stand Mixer": 22
}

dining_items = {
    "Wooden Dining table": (110 + 130) / 2,  # averaging weight range
    "6 Dining chairs": 6 * 14.33
}

master_bedroom_items = {
    "Wooden King bed": 320,
    "King mattress": 96,
    "Wooden dresser": 209,
    "2 wooden nightstands": 2 * 20,
    "Clothing total per family": 350,
    "TV": 45
}

illia_bedroom_items = {
    "Double bed": 110,
    "Double mattress": 60,
    "Computer table": 77,
    "Computer chair": 20,
    "EKET Wall-mounted cabinet combination": 70,
    "Computer": 30,
    "2 Computer screens": 2 * 20,
    "Printer": 15,
    "Bench Seat": 22
}

matthew_bedroom_items = {
    "Toys": 92.8
}

bathroom_items = {
    "Dyson styler": 1.5
}

office_items = {
    "Computer table": 63,
    "Computer chair": 20,
    "Computer": 30,
    "3 Computer screens": 3 * 20,
    "Bookcase": 50.71,
    "Leather Rocking Recliner": 70
}

living_room_items = {
    "Sectional leather sofa": 330,
    "Wooden coffee table": 120,
    "Playstation": 7.05479,
    "Decoration": 77,
    "TV": 60
}

entrance_items = {
    "Console table": 40,
    "Paintings": 21,
    "Mirrors": 40
}

garage_items = {
    "Pressure washer": 22,
    "Snowboards": 35,
    "Fishing boots": 11.90,
    "Fishing equipment": 11,
    "Torin garage cabinets": 120
}

storage_items = {
    "Christmas tree ornaments": 50,
    "Filing cabinet": 112
}

washer_items = {
    "LG washer": 220,
    "LG Dryer": 170,
    "Towels/Sheets": 110
}

# Total weight calculation
total_weight = (
        sum(kitchen_items.values()) +
        sum(dining_items.values()) +
        sum(master_bedroom_items.values()) +
        sum(illia_bedroom_items.values()) +
        sum(matthew_bedroom_items.values()) +
        sum(bathroom_items.values()) +
        sum(office_items.values()) +
        sum(living_room_items.values()) +
        sum(entrance_items.values()) +
        sum(garage_items.values()) +
        sum(storage_items.values()) +
        sum(washer_items.values())
)

print(f"Total weight {total_weight}")
