import os
import sys
from datetime import datetime, timedelta

sys.path.append(os.getcwd())
from src.utils.admin_tools.test_rail_report import coverage
from kom_framework.src.general import Log
from kom_framework.utils.testrail.testrail_helper import TestRailHelper
import csv
from src.utils.admin_tools import test_rail_url, test_rail_user_name, test_rail_api_key

project_id = 5
test_rail_automation_field = 'custom_automation_type'
test_rail_automation_field_value = 1
mode = "M"

now = datetime.now()
monday = now - timedelta(days=now.weekday())
monday = datetime(monday.year, monday.month, monday.day, 0, 0, 0)
monday_timestamp = int(datetime.timestamp(monday))
weeks = [monday_timestamp - i * 7 * 86400 for i in range(13)]

user_list = ["<PERSON><PERSON>", "<PERSON>", "Husnain Shoukat", "<PERSON><PERSON><PERSON>",
             "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
             "<PERSON>", "Dev<PERSON>per<PERSON>"]

workload = {user_name: {week: {"created": 0, "updated": 0, "executed": 0, "ticket_created": 0, "ticket_closed": 0}
                        for week in weeks}
            for user_name in user_list}

test_results = {}

print(test_rail_url, test_rail_user_name, test_rail_api_key)

test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, project_id)


def get_week(date):
    date = int(date)
    for w in range(len(weeks)):
        if (w == 0 and date > weeks[w]) or (w > 0 and weeks[w-1] > date > weeks[w]):
            return weeks[w]


def read_file():
    global test_results, workload
    Log.info(f"!!! Reading")
    filename = os.path.join(os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail{os.sep}"),
                            'TeamStatistic.csv')
    header = None
    with open(filename, "r", newline='') as fd:
        reader = csv.reader(fd)
        for row in reader:
            if reader.line_num >= 20:
                if int(row[2]) in test_results:
                    test_results[int(row[2])]['total'] += 1
                    test_results[int(row[2])][row[5]] += 1
                else:
                    test_results.update({int(row[2]): {"components": "Unknown", "automated": "Unknown", "passed": 0, "blocked": 0, "untested": 0, "retest": 0, "failed": 0, "flaky": 0, "not applicable": 0, "pending":0, "total":1}})
                    test_results[int(row[2])][row[5]] += 1
                if row[5] in ["passed", "blocked", "failed", "flaky", "not applicable", "pending"]:
                    if get_week(row[7]) is None:
                        Log.debug(f"Too late! Test run id {row[0]} test case {row[2]}")
                    else:
                        workload[row[6]][get_week(row[7])]["executed"] += 1
            elif reader.line_num == 1:
                header = row
            else:
                if row[0].isdigit() and int(row[0]) in weeks:
                    for user in range(len(user_list)-1):
                        if row[user*7+1] == '[]':
                            workload[header[user*7]][int(row[0])]['created'] = 0
                        else:
                            workload[header[user*7]][int(row[0])]['created'] = len(row[user*7+1][1:-1].split(","))
                        if row[user*7+2] == '[]':
                            workload[header[user*7]][int(row[0])]['updated'] = 0
                        else:
                            workload[header[user*7]][int(row[0])]['updated'] = len(row[user*7+2][1:-1].split(","))
                        if row[user*7+4] == '[]':
                            workload[header[user*7]][int(row[0])]['ticket_created'] = 0
                        else:
                            workload[header[user*7]][int(row[0])]['ticket_created'] = len(row[user*7+4][1:-1].split(","))

    rows = []
    for table_name in ['created', 'updated', 'executed', 'ticket_created', 'ticket_closed']:
        header = user_list[:]
        header.insert(0, table_name)
        rows.append(header)
        for week_count in range(len(weeks)):
            row = [datetime.fromtimestamp(weeks[week_count]).strftime("%Y-%m-%d")]
            for user_name in user_list:
                row.append(workload[user_name][weeks[week_count]][table_name])
            rows.append(row)
        rows.append([""])

    rows.append(['Test Id', "components", "automated", "passed", "blocked", "untested", "retest", "failed", "flaky", "not applicable", "pending", "total"])

    for t in test_results:
        rows.append([t, test_results[t].get("components", "Unknown"), test_results[t].get("automated", "Unknown"), test_results[t]["passed"], test_results[t]["blocked"], test_results[t]["untested"], test_results[t]["retest"], test_results[t]["failed"], test_results[t]["flaky"], test_results[t]["not applicable"], test_results[t]["pending"], test_results[t]["total"]])

    filename = os.path.join(os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail{os.sep}"),
                                'TeamReport.csv')
    with open(filename, 'w', newline='') as fd:
        writer = csv.writer(fd)
        writer.writerows(rows)


def init_report(project_id):
    global workload, test_results
    suites = {s["id"]: None for s in test_rail.service.get_suites(project_id)}
    for s in suites:
        cases = test_rail.service.get_cases(project_id, str(s))
        test_results.update({c['id']: {"components": ", ".join([coverage[comp]['name'] for comp in c['custom_components']]), "automated": c['custom_automation_type'], "passed": 0, "blocked": 0, "untested": 0, "retest": 0, "failed": 0, "flaky": 0, "not applicable": 0, "pending":0, "total":0} for c in cases})
    return test_results


def main():
    init_report(project_id)
    read_file()
    Log.info("DONE!!!")


if __name__ == '__main__':
    main()
