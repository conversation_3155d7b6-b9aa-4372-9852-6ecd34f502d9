import csv
import datetime
import glob
import os

import time

from kom_framework.utils.testrail.testrail_helper import TestRailHelper
from test import test_rail_api_key, test_rail_url, test_rail_user_name

line_length = 120
priority = {
    1: "Low",
    2: "Medium",
    3: "High",
    4: "Critical"
}

test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, None)


def save_state(suite, project_id):
    print(f"Back up {suite['name']} with  ID {suite['id']}")
    columns = ["id", "title", "section_id", "type_id", "priority_id", "estimate", "suite_id",
               "custom_automation_type", "custom_preconds", "custom_steps", "custom_expected", "custom_steps_separated"]
    cases = test_rail.service.get_cases(project_id, str(suite['id']))
    for i in range(len(cases)):
        test = {}
        for col in columns:
            if col == "priority_id":
                test[col] = priority[cases[i][col]]
            else:
                test[col] = cases[i][col]
        cases[i] = test
    date = datetime.datetime.fromtimestamp(time.time()).strftime("%Y%m%d_%H%M%S%f")
    if not os.path.exists("testrail"):
        os.makedirs(f"testrail{os.sep}P{project_id}")
    if not os.path.exists(f"testrail{os.sep}P{project_id}"):
        os.makedirs(f"testrail{os.sep}P{project_id}")
    name = f'testrail{os.sep}P{project_id}{os.sep}S{str(suite["id"])}_{date}.csv'
    if len(cases) > 0:
        with open(name, mode='w', newline='', encoding='utf-8') as csv_file:
            fieldnames = cases[0].keys()
            writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(cases)
    return name


def compare(new=None, old=None):
    columns = ["id", "title", "section_id", "type_id", "priority_id", "estimate", "suite_id",
               "custom_details", "custom_preconds", "custom_steps", "custom_expected", "custom_steps_separated"]
    cases_new = {}
    cases_old = {}
    if new and old:
        with open(new, newline='', encoding='utf-8') as csvfile_new:
            reader_new = csv.DictReader(csvfile_new)
            for row in reader_new:
                cases_new[row['id']] = row
        with open(old, newline='', encoding='utf-8') as csvfile_old:
            reader_old = csv.DictReader(csvfile_old)
            for row in reader_old:
                cases_old[row['id']] = row
        edited = [k for k in set(cases_new) & set(cases_old) if cases_old[k] != cases_new[k]]
        # edited = {i: {c:(cases_old[i][c], cases_new[i][c])} for i in edited for c in columns if cases_old[i][c] != cases_new[i][c]}
        deleted = set(cases_old) - set(cases_new)
        added = set(cases_new) - set(cases_old)
        if deleted:
            print(f"\t- Following test cases were DELETED: {deleted}")
        if added:
            print(f"\t- Following test cases were ADDED: {added}")
        if edited:
            print("\t- Following test cases were EDDITED:")
            for i in edited:
                for c in columns:
                    if c not in cases_old[i].keys():
                        print(f"\t\t->Test case {i} did not have '{c}' collumn")
                    if c not in cases_new[i].keys():
                        print(f"\t\t->Test case {i} does not have '{c}' collumn")
                    if c in cases_old[i].keys() and c in cases_new[i].keys() and cases_old[i][c] != cases_new[i][c]:
                        print(f"\t\t->Test case {i} collumn '{c}' WAS: {cases_old[i][c]}")
                        print(f"\t\t  Test case {i} collumn '{c}' NOW: {cases_new[i][c]}")
        if not edited and not added and not deleted:
            os.remove(new)


def main():
    print("*" * line_length)
    print("STARTING TEST RAIL BACKUP")

    for project_id in test_rail.get_all_projects():
        suites = test_rail.service.get_suites(project_id)
        for s in suites:
            list_of_files = glob.glob(f'testrail{os.sep}P{project_id}{os.sep}S{str(s["id"])}*.csv')
            old_file = max(list_of_files, key=os.path.getctime) if list_of_files else None
            new_file = save_state(s, project_id)
            compare(new_file, old_file)
    print("*" * line_length)
    print("TEST RAIL BACKUP FINISHED")


if __name__ == '__main__':
    main()
