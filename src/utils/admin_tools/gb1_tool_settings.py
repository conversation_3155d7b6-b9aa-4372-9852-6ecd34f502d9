import time

import requests

from kom_framework.src.general import Log
from src import chat_console_account_id, gb1_user_name, gb1_password
from src.rest_api.services import Service


class Gb1ToolSettings(Service):
    _cookies = None

    @classmethod
    def get_cookies(cls):
        if not cls._cookies:
            result = cls.get_login_page()
            cookies = result.cookies.get_dict()
            cls.login(cookies=cookies)
            time.sleep(10)
            cls._cookies = cookies
        return cls._cookies

    @classmethod
    def get_route(cls) -> str:
        return cls.get_url()

    @classmethod
    def get_login_page(cls):
        return requests.get(cls.get_route())

    @classmethod
    def login(cls, cookies, account_id=99999):
        data = {"account_id": f'{account_id}',
                "username": f"{gb1_user_name}",
                "password": f"{gb1_password}",
                "submit": "Sign In"}
        response = requests.post(f'{cls.get_route()}/auth/user/login', data=data, cookies=cookies)
        assert "logout" in response.text
        return response

    @classmethod
    def reload_inventory(cls, account_id=chat_console_account_id, reload=1, is_priority=1):
        params = {
            'id': f'{account_id}',
            'reload': f'{reload}',
            'is_priority': f'{is_priority}',
        }
        Log.info(f"Reloading inventory for {account_id}")
        response = requests.get(f'{cls.get_route()}/tools/testImport.php', params=params, cookies=cls.get_cookies())
        assert response.status_code == 200 and response.text, f'response code {response.status_code} and text {response.text}'
        time.sleep(1)

    @classmethod
    def showroom_processor_status(cls, wait_time=600):
        end_time = time.time() + wait_time
        while True:
            response = requests.get(f'{cls.get_route()}/cbo/showroom/processor-status', cookies=cls.get_cookies())
            assert response.status_code == 200
            if True not in response.json().values():
                return True
            if time.time() > end_time:
                return False
            time.sleep(1)
