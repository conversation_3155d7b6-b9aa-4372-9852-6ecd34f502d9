from src.utils.admin_tools.reload_inventory import cbo_account_list


def reload_cbo_inventory(account_ids: list[int] = cbo_account_list):
    from src.utils.admin_tools.gb1_tool_settings import Gb1ToolSettings
    for account in account_ids:
        Gb1ToolSettings.reload_inventory(account_id=account)
        Gb1ToolSettings.showroom_processor_status()


def main():
    reload_cbo_inventory()


if __name__ == '__main__':
    main()
