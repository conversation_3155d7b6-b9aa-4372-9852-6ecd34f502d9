name: Execute Automated Tests
description: Opens dialog box
author: <PERSON>
version: 1.2
includes: ^runs/view
excludes:

js:
const formFields = {
    suiteId: { id: 'suite_id', label: 'Suite ID', value: 'xxx', query: 'SUIT_ID', display: false, type:"text2"},
    component: { id: 'component', label: 'Component', value: 'xxx', query: 'COMPONENT', display: false, type:"text2"},
    device: { id: 'device', label: 'Device', value: 'xxx', query: 'DEVICE_MODE_NAME', display: false, type:"text2"},
    runId: { id: 'run_id', label: 'Run ID', value: 'xxx', query: 'RUN_ID', display: false, type:"text2"},
    rerunFailed: { id: 'rerun_failed', label: 'Rerun failed', value: false, query: 'RERUN_FAILED', display: true, type:"checkbox"},
    rerunBlocked: { id: 'rerun_blocked', label: 'Re<PERSON> blocked', value: false, query: 'RERUN_BLOCKED', display: true, type:"checkbox"},
    rerunsNumber: { id: 'reruns_number', label: 'Reruns number', value: '1', query: 'RERUNS_NUMBER', display: true, type:"text2"},
    gliveBranch: { id: 'glive_branch', label: 'Glive Branch', value: 'xxx', query: 'GLIVE_BRANCH', display: true, type:"text2"},
    ccBranch: { id: 'chat_console_branch', label: 'Chat Console Branch', value: 'xxx', query: 'CHAT_CONSOLE_BRANCH', display: true, type:"text2"},
    glivePrBuildUrl: { id: 'glive_pr_build_url', label: 'Glive PR URL', value: 'xxx', query: 'GL_PR_BUILD_URL', display: true, type:"text2"},
    ccPrBuildUrl: { id: 'chat_console_pr_build_url', label: 'Chat Console PR URL', value: 'xxx', query: 'CC_PR_BUILD_URL', display: true, type:"text2"}
}
const GLIVE_BRANCHES = {GLIVE: "resq-desktop/glive-rc", RESQ: "resq-desktop/rc",
                       DEV: "resq-desktop/glive-dev", PR: "resq-desktop/pr-build"}

const CHAT_CONSOLE_BRANCHES = {DEV: "chat_console/develop", STAGING: "chat_console/staging", PR: "chat_console/pr-build"}

$(document).ready(function() {
    const { project } = uiscripts.context
    const PROJECT = { GUBAGOO: 5, GLiveMob : 7, ChatConsole: 10, Consumer: 12, GLive: 13}
    switch (project.id) {
        case PROJECT.GUBAGOO: return handleGubagooProject()
        case PROJECT.GLive: return handleGLiveProject()
        case PROJECT.ChatConsole: return handleChatConsoleProject()
        case PROJECT.Consumer: return handleConsumerProject()
	    case PROJECT.GLiveMob: return PROJECT.GLiveMob
        default: return App.Dialogs.error(`Invalid Project ID: ${project.id}`)
    }
})

function handleConsumerProject() {

    const { suite } = uiscripts.context
    const { run } = uiscripts.context
    const SUITE = { MASTER: 24666}
    const COMPONENTS = {CHATV3: "ChatV3", CONSUMER_V4: "ConsumerV4", AI: "AI"}
    const DEVICES = {ANDROID: "Android", IOS: "iOS", DESKTOP: "Desktop"}

    var component, device, glive_branch;
    if (run.config){
        var $configs = run.config.split(', ')

        $configs.forEach(function(element) {
            if (Object.values(COMPONENTS).includes(element)){
                component = element;}
            if (Object.values(DEVICES).includes(element)){
                device = element;}
            if (Object.values(GLIVE_BRANCHES).includes(element)){
                glive_branch = element;}
        })}

    const isSupportedSuite = Object.values(SUITE).includes(suite.id)
    const isSupportedComponent = Object.values(COMPONENTS).includes(component)
    const isSupportedDevice = Object.values(DEVICES).includes(device)

    if (!isSupportedSuite | !isSupportedComponent | !isSupportedDevice) return;

    const $modal = createModal$()
    const $button = createToolbarButton$(function() { $modal.show() })

    $modal.appendTo('body')
    $('#content-header .content-header-inner').prepend($button);

    switch (component) {
        case COMPONENTS.CHATV3: return handleConsumerSuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.AI: return handleConsumerAISuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.CONSUMER_V4: return handleConsumerV4Suite($modal, component, device, run.id, suite.id)
    }

    function handleConsumerSuite($modal, component_name, device_mode, run_id, suite_id) {
        var job;

        if (device_mode == DEVICES.ANDROID) {
          job = 'e2e-gb1-android';
        } else if (device_mode == DEVICES.IOS) {
          job = 'e2e-gb1-ios';
        } else {
          job = 'e2e-gb1-desktop';
        }
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleConsumerAISuite($modal, component_name, device_mode, run_id, suite_id) {
        var job;

        if (device_mode == DEVICES.ANDROID) {
          job = 'e2e-ai-android';
        } else if (device_mode == DEVICES.IOS) {
          job = 'e2e-ai-ios';
        } else {
          job = 'e2e-ai-desktop';
        }
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleConsumerV4Suite($modal, component_name, device_mode, run_id, suite_id) {
        var job;

        if (device_mode == DEVICES.ANDROID) {
          job = 'e2e-consumer4-android';
        } else if (device_mode == DEVICES.IOS) {
          job = 'e2e-consumer4-ios';
        } else {
          job = 'e2e-consumer4-desktop';
        }
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function appendToModal($modal, $content) {
        $modal.find('.modal-content').append($content)
    }
}

function handleChatConsoleProject() {

    const { suite } = uiscripts.context
    const { run } = uiscripts.context
    const SUITE = { MASTER: 23988}
    const COMPONENTS = { CHAT_CONSOLE: "ChatConsole"}
    const DEVICES = {DESKTOP: "Desktop"}

    var component, device, glive_branch, chat_console_branch, chat_console_pr_build_url;
    chat_console_pr_build_url = ""
    if (run.config){
        var $configs = run.config.split(', ')

        $configs.forEach(function(element) {
            if (Object.values(COMPONENTS).includes(element)){
                component = element;}
            else if  (Object.values(DEVICES).includes(element)){
                device = element;}
            else if  (Object.values(GLIVE_BRANCHES).includes(element)){
                glive_branch = element;}
            else if  (Object.values(CHAT_CONSOLE_BRANCHES).includes(element)){
                chat_console_branch = element;}
            else if  (element.includes("*********************************")){
                chat_console_pr_build_url = element;}
        })}

    const isSupportedSuite = Object.values(SUITE).includes(suite.id)
    const isSupportedComponent = Object.values(COMPONENTS).includes(component)
    const isSupportedDevice = Object.values(DEVICES).includes(device)

    if (!isSupportedSuite | !isSupportedComponent | !isSupportedDevice) return;

    const $modal = createModal$()
    const $button = createToolbarButton$(function() { $modal.show() })

    $modal.appendTo('body')
    $('#content-header .content-header-inner').prepend($button);

    switch (component) {
        case COMPONENTS.CHAT_CONSOLE: return handleChatConsoleSuite($modal, component, device, run.id, suite.id, chat_console_branch, chat_console_pr_build_url)
    }

    function handleChatConsoleSuite($modal, component_name, device_mode, run_id, suite_id, chat_console_branch, chat_console_pr_build_url) {
        var job;

        if (chat_console_branch == CHAT_CONSOLE_BRANCHES.STAGING){
            job = 'e2e-chat-console-staging';
        }
        if (chat_console_branch == CHAT_CONSOLE_BRANCHES.DEV){
            job = 'e2e-chat-console-desktop';
        }
        if (chat_console_branch == CHAT_CONSOLE_BRANCHES.PR){
            job = 'e2e-chat-console-pr-build';
        }

        const { suiteId, component, device, runId, rerunsNumber, ccBranch, ccPrBuildUrl, rerunFailed, rerunBlocked} = formFields;
        ccPrBuildUrl.value = chat_console_pr_build_url;
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id
        ccBranch.value = chat_console_branch

        let fields = [ suiteId, component, device, runId, rerunsNumber, ccBranch, rerunFailed, rerunBlocked]

        if (chat_console_branch == CHAT_CONSOLE_BRANCHES.PR){
            ccPrBuildUrl.value = chat_console_pr_build_url;
            fields = [ suiteId, component, device, runId, rerunsNumber, ccBranch, ccPrBuildUrl, rerunFailed, rerunBlocked]
        }

        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function appendToModal($modal, $content) {
        $modal.find('.modal-content').append($content)
    }
}

function handleGLiveProject() {

    const { suite } = uiscripts.context
    const { run } = uiscripts.context
    const SUITE = { MASTER: 25228}
    const COMPONENTS = { GB1: 'GB1', CBO: "CBO", GLIVE: "GLive", CBO_PROD: "CBOProd", SHOWROOM: "ShowRoom", VDP: "VDP3.0",VDP_4_0: "VDP4.0"}
    const DEVICES = {ANDROID: "Android", IOS: "iOS", DESKTOP: "Desktop"}

    var component, device, glive_branch, chat_console_branch, glive_pr_build_url;
    glive_pr_build_url = ""
    if (run.config){
        var $configs = run.config.split(', ')

        $configs.forEach(function(element) {
            if (Object.values(COMPONENTS).includes(element)){
                component = element;}
            else if  (Object.values(DEVICES).includes(element)){
                device = element;}
            else if  (Object.values(GLIVE_BRANCHES).includes(element)){
                glive_branch = element;}
            else if  (Object.values(CHAT_CONSOLE_BRANCHES).includes(element)){
                chat_console_branch = element;}
            else if  (element.includes("*********************************")){
                glive_pr_build_url = element;}
        })}

    const isSupportedSuite = Object.values(SUITE).includes(suite.id)
    const isSupportedComponent = Object.values(COMPONENTS).includes(component)
    const isSupportedDevice = Object.values(DEVICES).includes(device)

    if (!isSupportedSuite | !isSupportedComponent | !isSupportedDevice) return;

    const $modal = createModal$()
    const $button = createToolbarButton$(function() { $modal.show() })

    $modal.appendTo('body')
    $('#content-header .content-header-inner').prepend($button);

    switch (component) {
        case COMPONENTS.GB1: return handleGb1Suite($modal, component, device, run.id, suite.id)
        case COMPONENTS.GLIVE: return handleGliveSuite($modal, component, device, run.id, suite.id, glive_branch, glive_pr_build_url)
        case COMPONENTS.VDP:
        case COMPONENTS.VDP_4_0:
            return handleVDPSuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.CBO: return handleCboSuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.CBO_PROD: return handleCboProdSuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.SHOWROOM: return handleShowRoomSuite($modal, component, device, run.id, suite.id)
    }

    function handleGb1Suite($modal, component_name, device_mode, run_id, suite_id) {
        var job;

        if (device_mode == DEVICES.ANDROID) {
          job = 'e2e-gb1-android';
        } else if (device_mode == DEVICES.IOS) {
          job = 'e2e-gb1-ios';
        } else {
          job = 'e2e-gb1-desktop';
        }
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleGliveSuite($modal, component_name, device_mode, run_id, suite_id, glive_branch, glive_pr_build_url) {
        var job;

        if (glive_branch == GLIVE_BRANCHES.GLIVE){
            job = 'e2e-glive-desktop';
        }
        if (glive_branch == GLIVE_BRANCHES.RESQ){
            job = 'e2e-resq-desktop';
        }
        if (glive_branch == GLIVE_BRANCHES.DEV){
            job = 'e2e-glive-dev';
        }
        if (glive_branch == GLIVE_BRANCHES.PR){
            job = 'e2e-glive-pr-build';
        }

        const { suiteId, component, device, runId, rerunsNumber, gliveBranch, glivePrBuildUrl, rerunFailed, rerunBlocked} = formFields;
        glivePrBuildUrl.value = glive_pr_build_url
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id
        gliveBranch.value = glive_branch

        let fields = [ suiteId, component, device, runId, rerunsNumber, gliveBranch, rerunFailed, rerunBlocked]

        if (glive_branch == GLIVE_BRANCHES.PR){
            glivePrBuildUrl.value = glive_pr_build_url;
            fields = [ suiteId, component, device, runId, rerunsNumber, gliveBranch, glivePrBuildUrl, rerunFailed, rerunBlocked]
        }

        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleCboSuite($modal, component_name, device_mode, run_id, suite_id) {
        const job = 'e2e-cbo-desktop'
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleCboProdSuite($modal, component_name, device_mode, run_id, suite_id) {
        const job = 'e2e-cbo-prod'
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleShowRoomSuite($modal, component_name, device_mode, run_id, suite_id) {
        const job = 'e2e-showroom-desktop'
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleVDPSuite($modal, component_name, device_mode, run_id, suite_id) {
        var job = component_name == COMPONENTS.VDP_4_0 ? "e2e-vdp4-desktop" : "e2e-vdp-desktop"
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function appendToModal($modal, $content) {
        $modal.find('.modal-content').append($content)
    }
}

function handleGubagooProject() {

    const { suite } = uiscripts.context
    const { run } = uiscripts.context
    const SUITE = { MASTER: 6}
    const COMPONENTS = { GB1: 'GB1', CBO: "CBO", GLIVE: "GLive", CBO_PROD: "CBOProd", SHOWROOM: "ShowRoom",
     VDP: "VDP3.0",VDP_4_0: "VDP4.0", DEALER_MODE: "Dealer Mode"}
    const DEVICES = {ANDROID: "Android", IOS: "iOS", DESKTOP: "Desktop"}

    var component, device, glive_branch, chat_console_branch, glive_pr_build_url;
    glive_pr_build_url = ""
    if (run.config){
        var $configs = run.config.split(', ')

        $configs.forEach(function(element) {
            if (Object.values(COMPONENTS).includes(element)){
                component = element;}
            else if  (Object.values(DEVICES).includes(element)){
                device = element;}
            else if  (Object.values(GLIVE_BRANCHES).includes(element)){
                glive_branch = element;}
            else if  (Object.values(CHAT_CONSOLE_BRANCHES).includes(element)){
                chat_console_branch = element;}
            else if  (element.includes("*********************************")){
                glive_pr_build_url = element;}
        })}

    const isSupportedSuite = Object.values(SUITE).includes(suite.id)
    const isSupportedComponent = Object.values(COMPONENTS).includes(component)
    const isSupportedDevice = Object.values(DEVICES).includes(device)

    if (!isSupportedSuite | !isSupportedComponent | !isSupportedDevice) return;

    const $modal = createModal$()
    const $button = createToolbarButton$(function() { $modal.show() })

    $modal.appendTo('body')
    $('#content-header .content-header-inner').prepend($button);

    switch (component) {
        case COMPONENTS.GB1: return handleGb1Suite($modal, component, device, run.id, suite.id)
        case COMPONENTS.GLIVE: return handleGliveSuite($modal, component, device, run.id, suite.id, glive_branch, glive_pr_build_url)
        case COMPONENTS.VDP:
        case COMPONENTS.VDP_4_0:
            return handleVDPSuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.CBO: return handleCboSuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.CBO_PROD: return handleCboProdSuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.SHOWROOM: return handleShowRoomSuite($modal, component, device, run.id, suite.id)
        case COMPONENTS.DEALER_MODE: return handleDealerModeSuite($modal, component, device, run.id, suite.id)
    }

    function handleGb1Suite($modal, component_name, device_mode, run_id, suite_id) {
        var job;

        if (device_mode == DEVICES.ANDROID) {
          job = 'e2e-gb1-android';
        } else if (device_mode == DEVICES.IOS) {
          job = 'e2e-gb1-ios';
        } else {
          job = 'e2e-gb1-desktop';
        }
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleGliveSuite($modal, component_name, device_mode, run_id, suite_id, glive_branch, glive_pr_build_url) {
        var job;

        if (glive_branch == GLIVE_BRANCHES.GLIVE){
            job = 'e2e-glive-desktop';
        }
        if (glive_branch == GLIVE_BRANCHES.RESQ){
            job = 'e2e-resq-desktop';
        }
        if (glive_branch == GLIVE_BRANCHES.DEV){
            job = 'e2e-glive-dev';
        }
        if (glive_branch == GLIVE_BRANCHES.PR){
            job = 'e2e-glive-pr-build';
        }

        const { suiteId, component, device, runId, rerunsNumber, gliveBranch, glivePrBuildUrl, rerunFailed, rerunBlocked} = formFields;
        glivePrBuildUrl.value = glive_pr_build_url
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id
        gliveBranch.value = glive_branch

        let fields = [ suiteId, component, device, runId, rerunsNumber, gliveBranch, rerunFailed, rerunBlocked]

        if (glive_branch == GLIVE_BRANCHES.PR){
            glivePrBuildUrl.value = glive_pr_build_url;
            fields = [ suiteId, component, device, runId, rerunsNumber, gliveBranch, glivePrBuildUrl, rerunFailed, rerunBlocked]
        }

        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleCboSuite($modal, component_name, device_mode, run_id, suite_id) {
        const job = 'e2e-cbo-desktop'
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleCboProdSuite($modal, component_name, device_mode, run_id, suite_id) {
        const job = 'e2e-cbo-prod'
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleShowRoomSuite($modal, component_name, device_mode, run_id, suite_id) {
        const job = 'e2e-showroom-desktop'
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

     function handleDealerModeSuite($modal, component_name, device_mode, run_id, suite_id) {
        const job = 'e2e-offer-builder'
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function handleVDPSuite($modal, component_name, device_mode, run_id, suite_id) {
        var job = component_name == COMPONENTS.VDP_4_0 ? "e2e-vdp4-desktop" : "e2e-vdp-desktop"
        const { suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked} = formFields
        component.value = component_name
        device.value = device_mode
        runId.value = run_id
        suiteId.value = suite_id

        const fields = [ suiteId, component, device, runId, rerunsNumber, rerunFailed, rerunBlocked]
        const $form = buildFormFor$(job, fields, $modal)
        appendToModal($modal, $form)
    }

    function appendToModal($modal, $content) {
        $modal.find('.modal-content').append($content)
    }
}

function createToolbarButton$(onClickHandler) {
    const $button = $(
        `<div class='toolbar content-header-toolbar'>
            <a class='toolbar-button toolbar-button-last toolbar-button-first content-header-button button-start' href="javascript:void(0)">Run Automated Tests</a>
        </div>`
    )

    $button.click(onClickHandler)
    return $button
}

function createModal$() {
    const $modal = $(
        `<div id="myModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
            </div>
        </div>`
    )

    $modal.find('.close').click(function () { $modal.hide() })
    return $modal
}

function submit(endpoint, $modal) {
    return $.ajax({
        data: null,
        type: 'POST',
        timeout: 15000,
        dataType: 'json',
        xhrFields: { withCredentials: true },
        url: `https://jenkins.gubagoo.dev/${endpoint}`,

        beforeSend: function beforeSend(xhr) {

            // const user = 'jenkins-api'
            // const token = '11eab852d4b9374bc1b204e1287d87f389'
            // xhr.setRequestHeader('Authorization', 'Basic ' + btoa(user + ":" + token))

            xhr.setRequestHeader('content-type', 'application/json'),
            xhr.setRequestHeader('Authorization', 'Basic amVua2lucy1hcGk6MTFlYWI4NTJkNGI5Mzc0YmMxYjIwNGUxMjg3ZDg3ZjM4OQ==')
        },

        error: function(response) {
            switch(response.status) {
              case 404:
                App.Dialogs.error('Invalid job or parameter(s).')
              case 500:
                App.Dialogs.error('Invalid job or parameter(s).')
            default:
                  if ($modal) $modal.hide()

                  App.Dialogs.message(
                      'The tests are being processed in the background and the results are automatically posted back to TestRail.',
                      'Confirmation',
                  )
            }
          },

        success: function() {
            if ($modal) $modal.hide()

            App.Dialogs.message(
                'The tests are being processed in the background and the results are automatically posted back to TestRail.',
                'Confirmation',
            )
        },
    })
}

function buildFormFor$(job, fields, $modal) {
    const $form = $(
        `<form>
            <h2>Run parameters</h2>
            ${formFieldsToMarkup(fields)}
            <button type="submit">Start Execution</button>
        </form>`
    )

    $form.submit(function(event) {
        event.preventDefault()
        const { run } = uiscripts.context

        const base = 'buildByToken/buildWithParameters'
        const token = 'TrJ3n41n3R0nj00B'

        for(var i = 0; i < fields.length; i++) {
            if (fields[i].id == 'glive_branch') {
                if (fields[i].value == GLIVE_BRANCHES.GLIVE){
                    const job = 'e2e-glive-desktop'
                }
                else if (fields[i].value == GLIVE_BRANCHES.RESQ){
                    const job = 'e2e-resq-desktop'
                }
                else if (fields[i].value == GLIVE_BRANCHES.DEV){
                     const job = 'e2e-glive-dev'
                }
                else if (fields[i].value == GLIVE_BRANCHES.PR){
                     const job = 'e2e-glive-pr-build'
                }
                else{
                    App.Dialogs.error('Selected branch is not supported');
		            return false;
                }
                break;
            }
        }
        submit(`${base}?job=${job}&token=${token}&${formFieldsToQuery(fields)}&TRIGGER_RUN_ON_OTHER_PROJECTS=No`, $modal);
    })

    return $form

    function formFieldsToMarkup(fields) {
        return fields.map(toMarkup).join('')

        function toMarkup({ id, label, value, display, type}) {
            if (typeof value === "boolean"){
                return (`<div>
                        <input type="${type}" id="${id}" name="${id}" value="${value}">
                        <label for="${id}">${label}</label>
                        </div>`)
            }
            else {
                return (`<div>
                         <label for="${id}">${label}</label>
                         <input type="${type}" id="${id}" name="${id}" value="${value}">
                          </div>`)
            }
        }
    }

    function formFieldsToQuery(fields) {
        return fields.map(toQuery).join('&')

        function toQuery({id, query, type}) {
            if (type =='checkbox'){
                return `${query}=${document.getElementById(id).checked}`
            }
            else {
                return `${query}=${document.getElementById(id).value}`
            }
        }
    }
}