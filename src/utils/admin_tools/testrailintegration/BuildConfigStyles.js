name: Build Config Styles
description: Styles for the Build Config dialog box
author: Gurock Software
version: 1.0
includes: ^runs/view
excludes:

css:
/* Input group*/
.input_group {
  margin-top: 20px;
}

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0, 0, 0); /* Fallback color */
  background-color: rgba(0, 0, 0, 0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  background-color: #fcfcfc;
  margin: auto;
  padding: 20px;
  border: 3px solid #888;
  width: 400px;
  #height: 50%;
}

/* The Close Button */
#myModal .close {
  position: relative;
  color: #aaaaaa;
  float: right;
  font-size: 30px;
  font-weight: bold;
}

#myModal .close:hover,
#myModal .close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

input[type="text2"],
select {
  width: 100%;
  padding: 5px 5px;
  margin: 5px 0;
  display: inline-block;
  border: 1px solid #ccc;
  box-sizing: border-box;
}

input[type="checkbox"],
select {
  padding: 5px 5px;
  margin: 5px 0;
}

button[type="submit"],
input[type="submit"] {
  width: 100%;
  background-color: #3399ff;
  color: white;
  padding: 10px 20px;
  margin: 8px 0;
  border: none;
  cursor: pointer;
}

input[type="submit"]:hover {
  background-color: #0066ff;
}