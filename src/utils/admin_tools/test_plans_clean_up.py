import os
import re

import sys
from requests import ReadTimeout
from time import sleep

sys.path.append(os.getcwd())

from kom_framework.src.general import Log
from kom_framework.utils.testrail.testrail_helper import TestRailHelper
from src.utils.admin_tools import test_rail_url, test_rail_user_name, test_rail_api_key

test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, None)
line_length = 120
UNTESTED_RATE = 0.20
CREATED_BY = 12
COMPLETED = 0


def clean_plan(plan_id):
    plan = test_rail.service.get_plan(plan_id)
    for entry in plan["entries"]:
        to_delete = False
        for run in entry['runs']:
            all_test = sum([run[k] for k in run.keys() if k.endswith('_count')])
            if all_test != 0 and run['untested_count']/all_test > UNTESTED_RATE:
                Log.info(f"Run {run['id']} will be deleted")
                # test_rail.service.delete_run_from_plan_entry(run['id'])
                to_delete = True
        if to_delete:
            test_rail.service.delete_plan_entry(plan_id=plan_id, entry_id=entry['id'])


def main():
    Log.info("*" * line_length)
    for project_id in test_rail.get_all_projects():
        Log.info(f"STARTING TEST RAIL PLANS CLEANUP FOR PROJECT ID {project_id}")
        all_dev_plans = test_rail.service.get_plans(project_id=project_id,
                                                    additional_args=f'&is_completed={COMPLETED}&created_by={CREATED_BY}')
        comp_plan = {}
        {comp_plan.setdefault(re.sub(' Execution Plan for \d+-\d+-\d+', '', p['name']), []).append(p['id']) for p in all_dev_plans}

        for comp, plans in comp_plan.items():
            if len(plans) > 1:
                plans.remove(max(plans))
                for p in reversed(plans):
                    clean_plan(p)
                    com = None
                    while com is None:
                        try:
                            Log.info(f"Plan {p} will be completed")
                            test_rail.service.close_plan(p)
                            com = 1
                        except ReadTimeout as e:
                            Log.warning(e)
                            com = None
                            sleep(10)

    Log.info("*" * line_length)
    Log.info("TEST RAIL CLEANUP FINISHED")


if __name__ == '__main__':
    main()
