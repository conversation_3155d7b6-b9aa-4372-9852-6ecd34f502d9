import datetime

def parse_holidays(holiday_list):
    holidays = {}
    for line in holiday_list.strip().split("\n"):
        parts = line.rsplit("\t", 1)
        if len(parts) == 2:
            name, date_str = parts
            year, month, day = map(int, date_str.split("-"))
            holidays[datetime.date(year, month, day)] = name
    return holidays

holiday_data = """
Memorial Day	2025-05-26
Juneteenth	2025-06-19
Independence Day	2025-07-04
Veterans Day	2025-11-11
Presidents' Day	2026-02-16
Memorial Day	2026-05-25
Juneteenth	2026-06-19
Independence Day (substitute)	2026-07-03
Independence Day	2026-07-04
Labor Day	2026-09-07
Columbus Day	2026-10-12
Veterans Day	2026-11-11

St. David's Day	2025-03-01
Read Across America Day	2025-03-03
Employee Appreciation Day	2025-03-07
St. Patrick's Day	2025-03-17
National Vietnam War Veterans Day	2025-03-29
Doctors' Day	2025-03-30
National Tartan Day	2025-04-06
National Library Workers' Day	2025-04-08
Tax Day	2025-04-15
Emancipation Day	2025-04-16
Good Friday	2025-04-18
Easter Sunday	2025-04-20
Easter Monday	2025-04-21
Administrative Professionals Day	2025-04-23
Take our Daughters and Sons to Work Day	2025-04-24
Law Day	2025-05-01
National Explosive Ordnance Disposal (EOD) Day	2025-05-03
Cinco de Mayo	2025-05-05
National Nurses Day	2025-05-06
Military Spouse Appreciation Day	2025-05-09
Mother's Day	2025-05-11
Peace Officers Memorial Day	2025-05-15
National Defense Transportation Day	2025-05-16
Armed Forces Day	2025-05-17
Emergency Medical Services for Children Day	2025-05-21
National Maritime Day	2025-05-22
First Day of Pride Month	2025-06-01
D-Day   2025-06-06
Native American Day	2025-06-08
Loving Day	2025-06-12
Army Birthday	2025-06-14
Father's Day	2025-06-15
Juneteenth Freedom Day (substitute)	2025-06-16
Bunker Hill Day	2025-06-17
Juneteenth Day	2025-06-20
Juneteenth	2025-06-21
Bastille Day	2025-07-14
Rural Transit Day	2025-07-16
Parents' Day	2025-07-27
American Family Day	2025-08-03
Coast Guard Birthday	2025-08-04
Purple Heart Day	2025-08-07
National Navajo Code Talkers Day	2025-08-17
National Aviation Day	2025-08-19
Labor Day	2025-09-01
Carl Garner Federal Lands Cleanup Day	2025-09-06
National Grandparents Day	2025-09-07
California Admission Day	2025-09-09
Patriot Day	2025-09-11
Constitution Commemoration Day	2025-09-14
First Day of National Hispanic Heritage Month	2025-09-15
Constitution Day and Citizenship Day	2025-09-17
Air Force Birthday	2025-09-18
National POW/MIA Recognition Day	2025-09-19
Rosh Hashana	2025-09-23
National Public Lands Day	2025-09-27
Gold Star Mother's Day	2025-09-28
Yom Kippur	2025-10-02
Frances Xavier Cabrini Day	2025-10-06
Navy Birthday	2025-10-13
White Cane Safety Day	2025-10-15
Boss's Day	2025-10-16
Sweetest Day	2025-10-18
Halloween	2025-10-31
First Day of Native American Heritage Month	2025-11-01
Marine Corps Birthday	2025-11-10
Thanksgiving Day	2025-11-27
Presidents' Day (Observed)	2025-11-28
Cyber Monday	2025-12-01
Giving Tuesday	2025-12-02
St Nicholas Day	2025-12-06
National Guard Birthday	2025-12-13
Bill of Rights Day	2025-12-15
Pan American Aviation Day	2025-12-17
Christmas Eve	2025-12-24
Christmas Day	2025-12-25
New Year's Eve	2025-12-31
New Year's Day	2026-01-01
Stephen Foster Memorial Day	2026-01-13
Martin Luther King Jr. Day	2026-01-19
Kansas Day	2026-01-29
National Freedom Day	2026-02-01
Groundhog Day	2026-02-02
Rosa Parks Day	2026-02-04
National Wear Red Day	2026-02-06
Valentine's Day	2026-02-14
Lunar New Year	2026-02-17
St. David's Day	2026-03-01
Read Across America Day	2026-03-02
Employee Appreciation Day	2026-03-06
St. Patrick's Day	2026-03-17
National Vietnam War Veterans Day	2026-03-29
Doctors' Day	2026-03-30
Good Friday	2026-04-03
Easter Sunday	2026-04-05
Easter Monday	2026-04-06
National Library Workers' Day	2026-04-14
Tax Day	2026-04-15
Emancipation Day	2026-04-16
Patriots' Day	2026-04-20
Administrative Professionals Day	2026-04-22
Take our Daughters and Sons to Work Day	2026-04-23
Law Day	2026-05-01
National Explosive Ordnance Disposal (EOD) Day	2026-05-02
Cinco de Mayo	2026-05-05
National Nurses Day	2026-05-06
National Day of Prayer	2026-05-07
Native American Day	2026-05-08
Mother's Day	2026-05-10
Peace Officers Memorial Day	2026-05-15
Armed Forces Day	2026-05-16
Emergency Medical Services for Children Day	2026-05-20
National Maritime Day	2026-05-22
First Day of Pride Month	2026-06-01
D-Day   2026-06-06
Native American Day	2026-06-07
Loving Day	2026-06-12
Army Birthday	2026-06-14
Juneteenth Freedom Day (substitute)	2026-06-15
Bunker Hill Day	2026-06-17
Juneteenth Day	2026-06-20
Father's Day	2026-06-21
Bastille Day	2026-07-14
Rural Transit Day	2026-07-16
Parents' Day	2026-07-26
National Korean War Veterans Armistice Day	2026-07-27
American Family Day	2026-08-02
Coast Guard Birthday	2026-08-04
Purple Heart Day	2026-08-07
National Navajo Code Talkers Day	2026-08-16
National Aviation Day	2026-08-19
California Admission Day	2026-09-09
First Responders Day	2026-09-11
Carl Garner Federal Lands Cleanup Day	2026-09-12
National Grandparents Day	2026-09-13
First Day of National Hispanic Heritage Month	2026-09-15
Constitution Day and Citizenship Day	2026-09-17
Air Force Birthday	2026-09-18
Yom Kippur	2026-09-21
Native American Day	2026-09-25
National Public Lands Day	2026-09-26
Gold Star Mother's Day	2026-09-27
Frances Xavier Cabrini Day	2026-10-05
German American Day	2026-10-06
Navy Birthday	2026-10-13
White Cane Safety Day	2026-10-15
Boss's Day	2026-10-16
Sweetest Day	2026-10-17
Halloween	2026-10-31
First Day of Native American Heritage Month	2026-11-01
Marine Corps Birthday	2026-11-10
Thanksgiving Day	2026-11-26
Presidents' Day	2026-11-27
Cyber Monday	2026-11-30
Rosa Parks Day	2026-12-01
St Nicholas Day	2026-12-06
National Guard Birthday	2026-12-13
Bill of Rights Day	2026-12-15
Pan American Aviation Day	2026-12-17
Christmas Eve	2026-12-24
Christmas Day	2026-12-25
New Year's Eve	2026-12-31
"""  # Add the full list of holidays here

holidays = parse_holidays(holiday_data)

# Example: Print all holidays
for date, name in sorted(holidays.items()):
    print(f"datetime.date({date.year}, {date.month}, {date.day}): \"{name}\",")

