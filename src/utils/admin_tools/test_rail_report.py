import ast
import csv
import os
import sys
from datetime import date, datetime, timedelta

import time

sys.path.append(os.getcwd())

from kom_framework.src.general import Log
from kom_framework.utils.testrail.testrail_helper import Test<PERSON><PERSON><PERSON><PERSON>per, TestRailAutoType
from kom_framework.utils.testrail.testrail_service import TestCaseStatuses
from src.utils.admin_tools import test_rail_url, test_rail_user_name, test_rail_api_key, \
    jira_server, jira_api_key, jira_user
from jira import JIRA

project_id = 5
test_rail_automation_field = 'custom_automation_type'
mode = "M"

weeks = None
PROJECT_PRIORITY = {}
coverage = {}
workload = {}

priority = {4: "C",
            3: "H",
            2: "M",
            1: "L"}

auto_type = {
    1: "Manual",
    2: "Webdriver",
    3: "To Automate",
    4: "Unit",
    5: "Cypress"
}


jira = JIRA({'server': jira_server}, basic_auth=(jira_user, jira_api_key))
test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, project_id)
user_list = list(test_rail.users.values())#list(test_rail.get_all_users_dict().values())
folder_path = os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail")


def get_suit_dynamic_monthly(component):
    global coverage
    coverage[component]['dynamic'] = {date: coverage[component]['dynamic'][date] for date in sorted(coverage[component]['dynamic'])}
    previous = None
    for date in coverage[component]['dynamic']:
        if not previous:
            coverage[component]['dynamic'][date]['ALL'] = coverage[component]['dynamic'][date]['CREATED']
            coverage[component]['dynamic'][date]['AUTO'] = coverage[component]['dynamic'][date]['AUTOMATED']
            previous = date
        else:
            coverage[component]['dynamic'][date]['ALL'] = coverage[component]['dynamic'][date]['CREATED'] + coverage[component]['dynamic'][previous]['ALL']
            coverage[component]['dynamic'][date]['AUTO'] = coverage[component]['dynamic'][date]['AUTOMATED'] + coverage[component]['dynamic'][previous]['AUTO']
            previous = date


def get_last_weeks():
    global weeks
    now = datetime.now()
    monday = now - timedelta(days=now.weekday())
    monday = datetime(monday.year, monday.month, monday.day, 0, 0, 0)
    monday_timestamp = int(datetime.timestamp(monday))
    weeks = [monday_timestamp - i * 7 * 86400 for i in range(13)]
    return weeks


def populate_workload_with_jira():
    global workload
    for user in list(workload.keys()):
        user_id = {"Aman Sharma": "5e1278450af0d70e911caf66",
                   "Natalia Kuzovkova": "5ce7038ee826800fcda5fb07",
                   "Husnain Shoukat": "60148db6d04785006870ede5",
                   "Vitaliy Misha": "5f7f6e0dac3a2d006f2f6bcb",
                   "Oleksandr Khomenko": "6042274c1c4423007220a29d",
                   "Manmeet Rai": "5fa57819f7b30b006e29216b",
                   "F16430332638528": "5ee780ea2b9c4c0abfdc021b",
                   "William Radtke": "61239b2c45f753006911c6fe",
                   "Jacob Cohen": "5e6bd11e84dcfc0cf391875f",
                   "Amrinder Bhullar": "60b7e84713c0e90069e69f9e",
                   "Nate Young": "619fc4d3b0b630006ad522b2",
                   'Chris Elles': "61785103a9897100705a1ddc",
                   "Dima Kabak": "557058:24793cf7-ccea-4ba6-9142-7659acd8c1ef-3",
                   "Eddie Sorrell": "61e1d90a9ee70a0068f3685b",
                   "Jon Diamond": "61858e4f062f4c00694b766f",
                   "Joshua Eckerman": "557058:9df1c86e-7c18-40bf-9323-68d2c726cb5f-4",
                   "Justin Christenson": "5fd8f0a734847e006929e4e2",
                   "John Morris": "624dd752fd5e450070486073",
                   "Mark Foster": "61858bfa3e3753006fa78d77",
                   "Simran Kaur": "602b01677fbca20070b39c3e",
                   "Copas Lopez": "625406c9ed2b3e0074fb92b9",
                   "Rajat Lamsal": "6257332513c2c8006a367bff",
                   "Sergei Riabinin": "5e6bd11fbf022f0d8132eb0e",
                   "Jared Eitnier": "5ce7038ee826800fcda5fb07",
                   "Gubagoo Admin": "",
                   "Yang Junhai": "5d8e4168090bd70dc49af290",
                   "Zarif Safiullin": "5b9f42dfe3501337627d2dcf",
                   "Lindsay Millard": "5ce7038ee826800fcda5fb07"
                   }.get(user)
        if user_id:
            for w in workload[user].keys():
                res = jira.search_issues(jql_str=f'reporter in ({user_id}) AND created >= {datetime.fromtimestamp(w).strftime("%Y-%m-%d")} AND created <= {datetime.fromtimestamp(w + 6 * 86400).strftime("%Y-%m-%d")}')
                workload[user][w]['ticket_created'] = [res[i].key for i in range(res.total)]
        else:
            print(f"!!!WARNING!!!: Jira User key for {user} was not found")

    Log.info(workload)


def initialize_workload():
    global workload, user_list
    weeks = get_last_weeks()
    workload = {user_name: {week: {"created": [], "updated": [], "executed": [], "ticket_created": [], "ticket_closed": []}
                            for week in weeks}
                for user_name in user_list}
    return weeks


def initialize_coverage():
    global coverage
    components = test_rail.get_field_opions('components')
    components[0] = "None"
    components[-1] = "All"

    coverage = {int(id): {'name': components[id],
                          'Manual': 0,
                          'To Automate': 0,
                          'Unit': 0,
                          'Cypress': 0,
                          'Webdriver': 0,
                          'Total': 0,
                          'list': [],
                          "dynamic": {},
                          "priority": {
                              "Total": {"C": 0, "H": 0, "M": 0, "L": 0},
                              "Webdriver": {"C": 0, "H": 0, "M": 0, "L": 0},
                              "To Automate": {"C": 0, "H": 0, "M": 0, "L": 0},
                              "Unit": {"C": 0, "H": 0, "M": 0, "L": 0},
                              "Cypress": {"C": 0, "H": 0, "M": 0, "L": 0},
                              "Manual": {"C": 0, "H": 0, "M": 0, "L": 0}}} for id in components}


def get_test_rail_coverage():
    """
    Reads test cases from TestRailCasesData.csv and populates coverage and workload created and updated
    :return:
    """
    global coverage, workload, weeks
    initialize_workload()
    initialize_coverage()
    filename = os.path.join(folder_path, 'TestRailCasesData.csv')
    csv_file = csv.DictReader(open(filename))
    for c in csv_file:  # type: dict[str, Any]
        for fild in ['created_on', 'updated_on', 'created_by', 'updated_by']:
            c[fild] = int(c[fild])
        if c['updated_on'] > weeks[12] or c['created_on'] > weeks[12]:
            for w in range(len(weeks)):
                if w == 0 and c['created_on'] > weeks[w]:
                    workload[test_rail.users[c['created_by']]][weeks[w]]['created'].append(c['id'])
                elif w > 0 and weeks[w-1] > c['created_on'] > weeks[w]:
                    workload[test_rail.users[c['created_by']]][weeks[w]]['created'].append(c['id'])
                if w == 0 and c['updated_on'] > weeks[w]:
                    workload[test_rail.users[c['updated_by']]][weeks[w]]['updated'].append(c['id'])
                elif w > 0 and weeks[w-1] > c['updated_on'] > weeks[w]:
                    workload[test_rail.users[c['updated_by']]][weeks[w]]['updated'].append(c['id'])

        # test rail coverage #
        auto_status = int(c[test_rail_automation_field]) == TestRailAutoType.WEBDRIVER
        date_created = date.fromtimestamp(c["created_on"]).strftime("%Y-%m")
        date_automated = date.fromtimestamp(int(c["automated_on"])).strftime("%Y-%m") if auto_status else None

        populate_component_dynamic(-1, date_created, date_automated, auto_status)
        populate_component_stat(-1, auto_type[int(c.get('custom_automation_type'))], priority[int(c['priority_id'])])

        components = ast.literal_eval(c.get('custom_components')) if c.get('custom_components') is not "" else [0]
        if components is None:
            Log.warning(f"Test case {c['id']} has no component")
            components = []
        for comp in components:
            populate_component_stat(comp, auto_type[int(c.get('custom_automation_type'))], priority[int(c['priority_id'])])
            populate_component_dynamic(comp, date_created, date_automated, auto_status)


def populate_component_stat(comp, auto_type, priority):
    global coverage
    #unnecessary  # coverage[comp]['list'].append(c['id'])
    coverage[comp]['Total'] += 1
    coverage[comp][auto_type] += 1
    coverage[comp]["priority"][auto_type][priority] += 1
    coverage[comp]["priority"]['Total'][priority] += 1


def populate_component_dynamic(comp, date_created, date_automated, auto_status):
    global coverage
    if date_created not in coverage[comp]['dynamic'].keys():
        coverage[comp]['dynamic'][date_created] = {"ALL": 0, "AUTO": 0, "CREATED": 1, "AUTOMATED": 0}
    else:
        coverage[comp]['dynamic'][date_created]["CREATED"] += 1
    if auto_status and date_automated not in coverage[comp]['dynamic'].keys():
        coverage[comp]['dynamic'][date_automated] = {"ALL": 0, "AUTO": 0, "CREATED": 0, "AUTOMATED": 1}
    elif auto_status:
        coverage[comp]['dynamic'][date_automated]["AUTOMATED"] += 1


def save_coverage_in_file():
    """
    Saves coverage in file
    :return:
        GubagooTestStat_
    """
    filename = os.path.join(os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail{os.sep}"),
                            'GubagooTestStat_' + datetime.fromtimestamp(time.time()).strftime("%Y%m%d-%H%M%S") + '.csv')
    with open(filename, 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(["Component", 'Manual', 'To Automate', 'Automated',	'Total'])
        for component in coverage.keys():
            get_suit_dynamic_monthly(component)
            writer.writerow([coverage[component]['name'], coverage[component]['Manual'], coverage[component]['To Automate'], coverage[component]['Webdriver'],	coverage[component]['Total']])
        writer.writerow(["-"*60])

        writer.writerow([c for component in coverage.keys() for c in [coverage[component]['name'], "", "", "", "", ""]])
        writer.writerow([c for component in coverage.keys() for c in ['PRIORITY', 'Total Test cases', 'Webdriver', 'To Automate', 'Manual', ""]])
        writer.writerow([c for component in coverage.keys() for c in ["Critical", coverage[component]['priority']['Total']['C'], coverage[component]['priority']['Webdriver']['C'], coverage[component]['priority']['To Automate']['C'], coverage[component]['priority']['Manual']['C'], ""]])
        writer.writerow([c for component in coverage.keys() for c in ["High", coverage[component]['priority']['Total']['H'], coverage[component]['priority']['Webdriver']['H'], coverage[component]['priority']['To Automate']['H'], coverage[component]['priority']['Manual']['H'], ""]])
        writer.writerow([c for component in coverage.keys() for c in ["Medium", coverage[component]['priority']['Total']['M'], coverage[component]['priority']['Webdriver']['M'], coverage[component]['priority']['To Automate']['M'], coverage[component]['priority']['Manual']['M'], ""]])
        writer.writerow([c for component in coverage.keys() for c in ["Low", coverage[component]['priority']['Total']['L'], coverage[component]['priority']['Webdriver']['L'], coverage[component]['priority']['To Automate']['L'], coverage[component]['priority']['Manual']['L'], ""]])

        [writer.writerow([]) for i in range(17)]
        writer.writerow(["-"*60])
        writer.writerow([c for component in coverage.keys() for c in ["Dynamic", "", "", "", "", ""]])
        writer.writerow([c for component in coverage.keys() for c in ["Data", "Test cases", "Automated", "Created in", "Automated in", ""]])

        max_date = max([len(coverage[component]['dynamic'].keys())for component in coverage.keys()])

        for i in range(max_date):
            row = []
            for component in coverage.keys():
                if i < len(coverage[component]['dynamic'].keys()):
                    day = list(coverage[component]['dynamic'].keys())[i]
                    row.extend([day, coverage[component]['dynamic'][day]['ALL'], coverage[component]['dynamic'][day]['AUTO'], coverage[component]['dynamic'][day]['CREATED'], coverage[component]['dynamic'][day]['AUTOMATED'], ""])
                else:
                    row.extend(["", "", "", "", "", ""])
            writer.writerow(row)


def get_week(res_date):
    res_date = int(res_date)
    for w in range(len(weeks)):
        if (w == 0 and res_date > weeks[w]) or (w > 0 and weeks[w-1] > res_date > weeks[w]):
            return weeks[w]


# def read_update_file(all_runs):
#     global workload, weeks
#     Log.info(f"!!! Reading")
#     rows = []
#     filename = os.path.join(os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail{os.sep}"),
#                             'TeamStatistic.csv')
#     with open(filename, "r", newline='') as fd:
#         reader = csv.reader(fd)
#         for row in reader:
#             if reader.line_num < 20 or (int(row[0]) in all_runs and row[1].lower() == 'true'):
#                 rows.append(row)
#                 if reader.line_num >= 20:
#                     tc_project_id = int(row[9]) if row[9] else project_id
#                     tc_suite_id = list(test_rail.projects[tc_project_id]["suites"].keys())[0]
#
#                     if int(row[2]) in test_rail.projects[tc_project_id]["suites"][tc_suite_id]:
#                         test_rail.projects[tc_project_id]["suites"][tc_suite_id][int(row[2])]['run_stat']['total'] += 1
#                         test_rail.projects[tc_project_id]["suites"][tc_suite_id][int(row[2])]['run_stat'][row[5]] += 1
#                     else:
#                         test_rail.projects[tc_project_id]["suites"][tc_suite_id].update(
#                             {int(row[2]): {"run_stat": {"components": "Unknown",
#                                                         "automated": "Unknown",
#                                                         "passed": 0,
#                                                         "blocked": 0,
#                                                         "untested": 0,
#                                                         "retest": 0,
#                                                         "failed": 0,
#                                                         "flaky": 0,
#                                                         "not applicable": 0,
#                                                         "pending": 0,
#                                                         "total": 1}}})
#                         test_rail.projects[tc_project_id]["suites"][tc_suite_id][int(row[2])]['run_stat'][row[5]] += 1
#                     if row[5] in ["passed", "blocked", "failed", "flaky", "not applicable", "pending"]:
#                         if get_week(row[7]) is None:
#                             Log.debug(f"Too late! Test run id {row[0]} test case {row[2]}")
#                         else:
#                             try:
#                                 workload[row[6]][get_week(row[7])]["executed"].append(row[2])
#                             except KeyError as e:
#                                 Log.warning(f"Workload of {row[6]} was not calculated ")
#
#
#     #replace workload
#
#     rows[0] = [col for user_name in user_list
#                for col in [user_name, 'created', 'updated', 'executed', 'ticket_created', 'ticket_closed', '']]
#     for week_count in range(len(weeks)):
#         rows[week_count+1] = [col for user_name in user_list
#                               for col in [weeks[week_count],
#                                           workload[user_name][weeks[week_count]]["created"],
#                                           workload[user_name][weeks[week_count]]["updated"],
#                                           workload[user_name][weeks[week_count]]["executed"],
#                                           workload[user_name][weeks[week_count]]["ticket_created"],
#                                           workload[user_name][weeks[week_count]]["ticket_closed"],
#                                           '']]
#
#     Log.info(f"!!! Writing")
#     with open(filename, 'w', newline='') as fd:
#         writer = csv.writer(fd)
#         writer.writerows(rows)
#
#     return list(set([int(run[0]) for run in rows[19:]]))


def append_to_file(test_cases):
    Log.info(f"!!! Append run {test_cases[0][0]} with {len(test_cases)} tests")
    filename = os.path.join(os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail{os.sep}"),
                            'TeamStatistic.csv')
    with open(filename, 'a', newline='') as fd:
        writer = csv.writer(fd)
        writer.writerows(test_cases)


def preprocessing_result_data():
    """
    Preprocessing function to create TestRailRunsResults_processed.csv with additional mapped columns:
    - project_id, project_name, suite_id, components_id
    """
    Log.info("Starting data preprocessing...")

    # Check if all required CSV files exist
    required_files = [
        'TestRailCasesData.csv',
        'TestRailRunsData.csv',
        'TestRailRunsTests.csv',
        'TestRailRunsResults.csv'
    ]

    missing_files = []
    for filename in required_files:
        filepath = os.path.join(folder_path, filename)
        if not os.path.exists(filepath):
            missing_files.append(filename)
        else:
            file_size = os.path.getsize(filepath)
            Log.info(f"Found {filename}: {file_size:,} bytes")

    if missing_files:
        Log.error(f"Missing required files: {missing_files}")
        return False

    # Create mapping dictionaries
    Log.info("Creating mapping dictionaries...")

    # 1. Read TestRailRunsTests.csv to map test_id -> case_id, run_id
    test_to_case_map = {}
    test_to_run_map = {}
    tests_filename = os.path.join(folder_path, 'TestRailRunsTests.csv')

    with open(tests_filename, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            try:
                test_id = int(row['id'])
                case_id = int(row['case_id'])
                run_id = int(row['run_id'])
                test_to_case_map[test_id] = case_id
                test_to_run_map[test_id] = run_id
            except (ValueError, KeyError) as e:
                Log.warning(f"Error processing test row {row.get('id', 'unknown')}: {e}")
                continue

    # 2. Read TestRailRunsData.csv to map run_id -> project_id, suite_id
    run_to_project_map = {}
    run_to_suite_map = {}
    runs_filename = os.path.join(folder_path, 'TestRailRunsData.csv')

    with open(runs_filename, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            try:
                run_id = int(row['id'])
                project_id = int(row['project_id'])
                suite_id = int(row['suite_id'])
                run_to_project_map[run_id] = project_id
                run_to_suite_map[run_id] = suite_id
            except (ValueError, KeyError) as e:
                Log.warning(f"Error processing run row {row.get('id', 'unknown')}: {e}")
                continue

    # 3. Read TestRailCasesData.csv to map case_id -> project_name, components_id
    case_to_project_name_map = {}
    case_to_components_map = {}
    cases_filename = os.path.join(folder_path, 'TestRailCasesData.csv')

    with open(cases_filename, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            try:
                case_id = int(row['id'])
                project_name = row['project']
                components = row.get('custom_components', '')
                case_to_project_name_map[case_id] = project_name
                case_to_components_map[case_id] = components
            except (ValueError, KeyError) as e:
                Log.warning(f"Error processing case row {row.get('id', 'unknown')}: {e}")
                continue

    Log.info(f"Created mappings: {len(test_to_case_map)} tests, {len(run_to_project_map)} runs, {len(case_to_project_name_map)} cases")

    # 4. Process TestRailRunsResults.csv and create processed version
    results_filename = os.path.join(folder_path, 'TestRailRunsResults.csv')
    processed_filename = os.path.join(folder_path, 'TestRailRunsResults_processed.csv')

    Log.info("Processing TestRailRunsResults.csv...")

    processed_count = 0
    skipped_count = 0

    with open(results_filename, 'r', newline='') as input_file, \
         open(processed_filename, 'w', newline='') as output_file:

        reader = csv.DictReader(input_file)

        # Add new columns to the fieldnames
        fieldnames = reader.fieldnames + ['project_id', 'project_name', 'suite_id', 'components_id', 'case_id']
        writer = csv.DictWriter(output_file, fieldnames=fieldnames)
        writer.writeheader()

        for row in reader:
            try:
                test_id = int(row['test_id'])

                # Get case_id and run_id from test_id
                case_id = test_to_case_map.get(test_id)
                run_id = test_to_run_map.get(test_id)

                # Get project_id and suite_id from run_id
                project_id = run_to_project_map.get(run_id) if run_id else None
                suite_id = run_to_suite_map.get(run_id) if run_id else None

                # Get project_name and components from case_id
                project_name = case_to_project_name_map.get(case_id) if case_id else None
                components_id = case_to_components_map.get(case_id) if case_id else None

                # Add the new columns
                row['project_id'] = project_id if project_id is not None else ''
                row['project_name'] = project_name if project_name else ''
                row['suite_id'] = suite_id if suite_id is not None else ''
                row['components_id'] = components_id if components_id else ''
                row['case_id'] = case_id if case_id is not None else ''

                writer.writerow(row)
                processed_count += 1

            except (ValueError, KeyError) as e:
                Log.warning(f"Error processing result row {row.get('id', 'unknown')}: {e}")
                skipped_count += 1
                continue

    Log.info(f"Created TestRailRunsResults_processed.csv: {processed_count} rows processed, {skipped_count} rows skipped")
    Log.info("Data preprocessing completed successfully")
    return True


def populate_projects_from_cases():
    """
    Populate projects dictionary from TestRailCasesData.csv
    Returns: dict with structure {project_name: {"suites": {suite_id: {case_id: {"run_stat": {...}}}}, "plans": {}, "runs": {}}}
    """
    projects = {}
    cases_filename = os.path.join(folder_path, 'TestRailCasesData.csv')

    if not os.path.exists(cases_filename):
        Log.error(f"TestRailCasesData.csv not found at {cases_filename}")
        return projects

    Log.info("Populating projects from TestRailCasesData.csv")

    with open(cases_filename, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            try:
                project_name = row['project']
                suite_id = int(row['suite'])
                case_id = int(row['id'])

                # Initialize project if not exists
                if project_name not in projects:
                    projects[project_name] = {
                        "suites": {},
                        "plans": {},
                        "runs": {}
                    }

                # Initialize suite if not exists
                if suite_id not in projects[project_name]["suites"]:
                    projects[project_name]["suites"][suite_id] = {}

                # Initialize case with run_stat
                components = ast.literal_eval(row.get('custom_components')) if row.get('custom_components') is not "" else [0]
                projects[project_name]["suites"][suite_id][case_id] = row
                projects[project_name]["suites"][suite_id][case_id]["run_stat"] = {
                        "components": ", ".join([coverage[comp]['name'] for comp in components]),
                        "automated": row['custom_automation_type'],
                        "passed": 0,
                        "blocked": 0,
                        "untested": 0,
                        "retest": 0,
                        "failed": 0,
                        "flaky": 0,
                        "not applicable": 0,
                        "pending": 0,
                        "total": 0,
                        "failed_with_defect": 0
                    }

            except (ValueError, KeyError) as e:
                Log.warning(f"Error processing case row {row.get('id', 'unknown')}: {e}")
                continue

    # Populate projects["runs"] from TestRailRunsData.csv
    runs_filename = os.path.join(folder_path, 'TestRailRunsData.csv')

    if os.path.exists(runs_filename):
        Log.info("Populating project runs from TestRailRunsData.csv")

        with open(runs_filename, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                try:
                    run_id = int(row['id'])
                    project_id = int(row['project_id'])
                    suite_id = int(row['suite_id'])

                    # Find the project by matching project_id or suite_id with our projects
                    project_name = None
                    for proj_name, proj_data in projects.items():
                        if suite_id in proj_data['suites']:
                            project_name = proj_name
                            break

                    # Add run to the project's runs dictionary
                    if project_name:
                        projects[project_name]["runs"][run_id] = {
                            "name": row.get('name', ''),
                            "description": row.get('description', ''),
                            "suite_id": suite_id,
                            "project_id": project_id,
                            "is_completed": row.get('is_completed', 'False').lower() == 'true',
                            "completed_on": row.get('completed_on', ''),
                            "created_on": row.get('created_on', ''),
                            "created_by": row.get('created_by', ''),
                            "passed_count": int(row.get('passed_count', 0)),
                            "failed_count": int(row.get('failed_count', 0)),
                            "blocked_count": int(row.get('blocked_count', 0)),
                            "untested_count": int(row.get('untested_count', 0)),
                            "retest_count": int(row.get('retest_count', 0)),
                            "plan_id": row.get('plan_id', ''),
                            "url": row.get('url', '')
                        }

                except (ValueError, KeyError) as e:
                    Log.warning(f"Error processing run row {row.get('id', 'unknown')}: {e}")
                    continue

        # Log run statistics
        total_runs = sum(len(proj_data["runs"]) for proj_data in projects.values())
        Log.info(f"Populated {total_runs} runs across all projects")
    else:
        Log.warning(f"TestRailRunsData.csv not found at {runs_filename}")

    Log.info(f"Populated {len(projects)} projects with test cases and runs")
    return projects

def get_results_data():
    """
    Modified to read from pre-collected CSV files instead of making API calls
    Step 0: Preprocessing data validation and create processed file
    Step 1: Populate projects from test cases data
    Step 2: Read TestRailRunsResults_processed and populate workload and run_stat
    """
    # Step 0: Preprocessing data validation and create processed file
    if not preprocessing_result_data():
        Log.error("Data preprocessing failed. Cannot continue.")
        return {}

    # Step 1: Populate projects from test cases data
    projects = populate_projects_from_cases()
    Log.info(f"Step 1 completed: Populated projects structure with {len(projects)} projects")

    # Step 2: Read TestRailRunsResults_processed and populate workload and run_stat
    processed_results_filename = os.path.join(folder_path, 'TestRailRunsResults_processed.csv')

    if not os.path.exists(processed_results_filename):
        Log.error(f"TestRailRunsResults_processed.csv not found at {processed_results_filename}")
        return projects

    Log.info("Step 2: Reading TestRailRunsResults_processed.csv and populating workload and run_stat")

    # Initialize workload if not already done
    if not workload:
        initialize_workload()

    processed_results = 0
    skipped_results = 0
    run_stat_updates = 0

    with open(processed_results_filename, 'r', newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            try:
                # Skip empty or invalid results
                if not row['status_id'] or not row['status_id'].strip():
                    skipped_results += 1
                    continue

                # Extract data from result (now with preprocessed mapping data)
                created_on = int(row['created_on'])
                status_id = int(row['status_id'])
                created_by = int(row['created_by'])
                test_id = int(row['test_id'])
                defects = row.get('defects', '')

                # Get preprocessed mapping data
                project_name = row.get('project_name', '')
                suite_id = int(row['suite_id']) if row.get('suite_id') else None

                # Get week and status
                w = get_week(created_on)
                status = TestCaseStatuses.get_result_status_name(status_id)

                # Populate workload if user exists and week is valid
                if w and created_by in test_rail.users:
                    user_name = test_rail.users[created_by]
                    if user_name in workload and w in workload[user_name]:
                        workload[user_name][w]['executed'].append(int(row['id']))
                        processed_results += 1
                    else:
                        Log.debug(f"User {user_name} or week {w} not found in workload structure")
                        skipped_results += 1
                else:
                    if not w:
                        #Log.debug(f"Week not found for timestamp {created_on}")
                        pass
                    if created_by not in test_rail.users:
                        Log.debug(f"User {created_by} not found in test_rail.users")
                    skipped_results += 1

                # Populate run_stat for the corresponding test case (using preprocessed data)
                if project_name and suite_id and project_name in projects:
                    case_id = int(row['case_id']) if row.get('case_id') else None

                    if case_id and suite_id in projects[project_name]["suites"] and case_id in projects[project_name]["suites"][suite_id]:
                        # Update run_stat counters
                        projects[project_name]["suites"][suite_id][case_id]['run_stat']['total'] += 1
                        projects[project_name]["suites"][suite_id][case_id]['run_stat'][status] += 1

                        # Check for defects
                        if defects and defects.strip():
                            projects[project_name]["suites"][suite_id][case_id]['run_stat']["failed_with_defect"] += 1

                        run_stat_updates += 1

            except (ValueError, KeyError) as e:
                Log.warning(f"Error processing result row {row.get('id', 'unknown')}: {e}")
                skipped_results += 1
                continue

    Log.info(f"Step 2 completed: Processed {processed_results} workload entries, {run_stat_updates} run_stat updates, skipped {skipped_results}")

    return projects


def save_team_report_in_file(projects):
    rows = []
    for table_name in ['created', 'updated', 'executed', 'ticket_created', 'ticket_closed']:
        header = user_list[:]
        header.insert(0, table_name)
        rows.append(header)
        for week_count in range(len(weeks)):
            row = [datetime.fromtimestamp(weeks[week_count]).strftime("%Y-%m-%d")]
            for user_name in user_list:
                if table_name in ['created', 'updated', 'ticket_created', "executed"]: #ticket_closed
                    workload[user_name][weeks[week_count]][table_name] = len(workload[user_name][weeks[week_count]][table_name])
                row.append(workload[user_name][weeks[week_count]][table_name])
            rows.append(row)
        rows.append([""])

    rows.append(['Case Id', "components", "automated", "passed", "blocked", "untested", "retest", "failed", "flaky", "not applicable", "pending", "total", "failed_with_defect"])

    for p in projects:
        for s in projects[p]['suites']:
            for c in projects[p]['suites'][s]:
                if projects[p]['suites'][s][c]['run_stat'].get("failed_with_defect", -100) == -100:
                    Log.error(c)
                rows.append([c,
                             projects[p]['suites'][s][c]['run_stat'].get("components", "Unknown"),
                             projects[p]['suites'][s][c]['run_stat'].get("automated", "Unknown"),
                             projects[p]['suites'][s][c]['run_stat']["passed"],
                             projects[p]['suites'][s][c]['run_stat']["blocked"],
                             projects[p]['suites'][s][c]['run_stat']["untested"],
                             projects[p]['suites'][s][c]['run_stat']["retest"],
                             projects[p]['suites'][s][c]['run_stat']["failed"],
                             projects[p]['suites'][s][c]['run_stat']["flaky"],
                             projects[p]['suites'][s][c]['run_stat']["not applicable"],
                             projects[p]['suites'][s][c]['run_stat']["pending"],
                             projects[p]['suites'][s][c]['run_stat']["total"],
                             projects[p]['suites'][s][c]['run_stat'].get("failed_with_defect", 0)])

    filename = os.path.join(os.path.join(os.getcwd(), f"src{os.sep}utils{os.sep}admin_tools{os.sep}testrail{os.sep}"),
                            'TeamReport.csv')
    with open(filename, 'w', newline='') as fd:
        writer = csv.writer(fd)
        writer.writerows(rows)


def main():
    get_test_rail_coverage()
    save_coverage_in_file()
    #populate_workload_with_jira()
    result = get_results_data()
    save_team_report_in_file(result)
    Log.info("DONE!!!")


if __name__ == '__main__':
    main()


#In TestRailRunsResults.csv we already have id, test_id, and run_id. Let  create a function brushing_result_data() that will create new csv file like TestRailRunsResults_bushed.csv that will have all from TestRailRunsResults plus missing project_id, project_name, suite_id  instead of finding 