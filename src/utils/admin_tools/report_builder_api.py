from src.rest_api.services import Service


class ReportBuilderApi(Service):

    @classmethod
    def get_route(cls) -> str:
        return 'https://reporting-api-test.gubagoo.io/test_report_builder.php'

    @classmethod
    def generate(cls):
        response = cls.send_get_request_no_log(cls.get_route(), timeout=240)
        assert response.status_code == 200 and response.reason.upper() == "OK" and response.text == "done",\
            f"Error happened: {response.text}"
        return response
