import os

from src import env_file_content, account

if account:
    gb1_access_token = env_file_content['gb1'][account].get("access_token", None) if env_file_content['gb1'] else None
    gb1_access_user = env_file_content['gb1'][account].get("access_user", None) if env_file_content['gb1'] else None
device_mode = os.environ.get('DEVICE_MODE_NAME', None)
announcement_content = """
<div class="col-sm-8"><main id="ae-main-content">
<div class="post-5261 page type-page status-publish">
<div class="post-content">
<h1 class="entry-title"><span style="color: #e03e2d;">CUSTOMER HEALTH AND SAFETY</span></h1>
<div class="entry">
<p><span style="color: #141b28;"><img class="aligncenter wp-image-5273 size-large lazyload-loading ae-img" src="https://di-uploads-pod20.dealerinspire.com/vistabmwcoconutcreek/uploads/2020/03/COVID-PREVENTION-02-fixed.jpg" alt="" width="594" height="138"></span></p>
<p><span style="color: #e03e2d;">We are honored in having you as part of our extended family, which is why your safety is our highest priority.&nbsp; As a follow-up to our response to the Coronavirus (COVID-19), we want to more deeply cover some of the precautions we are taking with our facilities and staff to ensure your visit with us remains a safe one.</span></p>
<p><span style="color: #e67e23;"><strong>Initial arrival:</strong>&nbsp; Though our team would typically greet you with a handshake, we have asked our staff to refrain from any physical contact between themselves and our clients as a precautionary measure.</span></p>
<p><span style="color: #f1c40f;"><strong>Cleanliness and hygiene:</strong>&nbsp;Supplementary personnel have been tasked with routinely disinfecting our common areas throughout the day.&nbsp; We have also implemented a series of disinfectant stations in our stores to maintain the highest level of cleanliness possible.&nbsp; We are also employing an additional deep-cleaning every night after hours.</span></p>
<p><span style="color: #2dc26b;"><strong>Staff hygiene:</strong>&nbsp;Our team members are prohibited from reporting to work without a doctor’s certification if they have been exposed to a COVID-19 patient or been in a locale or situation which has been deemed “high-impact”, such as a cruise or certain, recent international travel.</span></p>
<p><span style="color: #3598db;"><strong>Your vehicle:</strong>&nbsp; Our staff will use protective covers throughout your cabin, including covering the seats, flooring and steering wheels.&nbsp; Only when you arrive for pickup will this protection be removed by a gloved team member.</span></p>
<p><span style="color: #3598db;"><strong>Our loaner fleet:&nbsp;</strong>&nbsp;All loaner vehicles are thoroughly sanitized with potent cleaning agents and disinfectants before being allocated to our clients.</span></p>
<p><span style="color: #236fa1;"><strong>Our cafes and food service:</strong>&nbsp;Though our cafes maintain a strict level of hygiene already, we have adjusted our menus to provide primarily pre-packaged foods and drinks.</span></p>
<p><span style="color: #236fa1;">Additionally, we are constantly monitoring the evolution of this virus and will continue to adjust our measures to ensure the safety of everyone.&nbsp; The health of our clients, team members, families and community remains our #1 focus.&nbsp; As always, we will keep you informed about any updates or changes to our circumstances.</span></p>
<p><span style="color: #b96ad9;">We appreciate your trust and confidence in choosing us to service and purchase your vehicle and thank you for your patience, understanding and – most of all – being part of our wonderful community.</span></p>
<p><span style="color: #b96ad9;">Please stay safe, be well and take good care of each other.</span></p>
<p><br><span style="color: #141b28;">Sincerely,&nbsp;</span></p>
<div><span style="color: #7e8c8d;">Jonathan Chariff</span></div>
<div><span style="color: #7e8c8d;">President &amp; CEO</span></div>
<div><span style="color: #7e8c8d;">Vista Motors</span></div>
</div>
</div>
</div>
</main></div>
<div class="col-sm-4">
<div class="textwidget">
<div class="dealer-info">&nbsp;</div>
</div>
</div>
"""
from src import env_file_content

test_rail_api_key = env_file_content['test_rail'].get('api_key', None)
test_rail_url = env_file_content['test_rail'].get('url', None)
test_rail_user_name = env_file_content['test_rail'].get('user_name', None)
test_rail_password = env_file_content['test_rail'].get("password", None)
jira_user = env_file_content['jira']['user']
jira_api_key = env_file_content['jira']['api_key']
jira_server = env_file_content['jira']['server']

all_ai_skills = "generic|hours|employment|service_appt|bounce|availability|vehicle_search|vehicle_price|payment_question|monthly_payment|pii|test_drive|parts|contact|trade_in|map|recall"
