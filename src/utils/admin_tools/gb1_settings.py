import re
import time
import urllib

import dem<PERSON><PERSON>
from allure import step
from requests import ReadTimeout

from kom_framework.src.general import Log
from kom_framework.src.web.drivers import UserAgents
from src import chat_console_account_id, floating_image, right_image, left_image
from src import xtime_id, chat_trigger_id
from src.rest_api.services import Service
from src.utils.admin_tools import gb1_access_token, gb1_access_user, announcement_content, device_mode, all_ai_skills
from src.utils.admin_tools.gb1_settings_enums.cbo_employee_pricing import EmployeePricingFlow
from src.utils.admin_tools.gb1_settings_enums.website_integration import VSPWidget
from src.utils.decorators import step_decorator
from src.web.consumer import ServiceProviderType
from src.web.consumer.desktop.cbo.insurance_component import FIFlow
from src.web.entities.trade_in_entity import TradeInProvider


class Gb1Settings(Service):

    @classmethod
    def get_route(cls) -> str:
        return cls.get_url() + '/admin_tools/qa_tool.php'

    @classmethod
    def auth_header(cls):
        if gb1_access_token and gb1_access_user:
            return gb1_access_user, gb1_access_token

    @classmethod
    def do_request(cls, url, attempts: int = 2):
        while attempts > 0:
            try:
                response = cls.send_get_request_no_log(url, auth=cls.auth_header(), timeout=120)
                break
            except ReadTimeout:
                attempts -= 1
                if attempts == 0:
                    raise
                Log.warning(f"Request {url} failed. {attempts} attempts left")
        assert response.status_code == 200 and response.reason.upper() == "OK", f"Error happened: {response.text}"
        return response

    @classmethod
    def get_loader_settings(cls, account_id=chat_console_account_id):
        url = cls.get_src_url() + f'/toolbars/{account_id}/loader_{account_id}_1.js'
        Log.debug(url)
        response = cls.send_get_request_no_log(url, auth=cls.auth_header())
        json_data = re.search(r"ggConfig\s*?=\s*?\{data:\s*?(.+?})},?\s*ggFramework", response.text).group(1)
        if not json_data.startswith('{"'):
            json_data = json_data.replace("\\", "").replace("!1", "false").replace("!0", "true")
        return demjson.decode(json_data)

    @classmethod
    def get_toolbar_settings(cls, account_id=chat_console_account_id):
        url = cls.get_src_url() + f'/toolbars/{account_id}/toolbar_{"m_" if device_mode in [UserAgents.ANDROID, UserAgents.IOS] else ""}{account_id}_1.js'
        Log.debug(url)
        response = cls.send_get_request_no_log(url, auth=cls.auth_header())
        json_data = re.search(r"ggConfig.extend\((.+?\})\)[,;]", response.text).group(1)
        if not json_data.startswith('{"'):
            json_data = json_data.replace("\\", "").replace("!1", "false").replace("!0", "true")
        return demjson.decode(json_data)

    @classmethod
    @step_decorator('API - Gb1Settings: Loader wait for regeneration by {1}')
    def loader_wait_for_regeneration(cls, setting, value, account_id=chat_console_account_id, wait_time: int = 180):
        end_time = time.time() + wait_time
        while True:
            current_value = cls.get_loader_settings(account_id=account_id).get(setting)
            Log.debug(f"Loader Current value of {setting} is {current_value}!")
            if current_value == value:
                return True
            if time.time() > end_time:
                return False
            time.sleep(5)

    @classmethod
    @step_decorator('API - Gb1Settings: Toolbar wait for regeneration by {1}')
    def toolbar_wait_for_regeneration(cls, setting, value, account_id=chat_console_account_id, wait_time: int = 180):
        end_time = time.time() + wait_time
        while True:
            current_value = cls.get_toolbar_settings().get(setting)
            Log.debug(f"Toolbar Current value of {setting} is {current_value}!")
            if current_value == value:
                return True
            if time.time() > end_time:
                return False
            time.sleep(5)

    @classmethod
    @step_decorator('API - Gb1Settings: Reset impersonation mode ')
    def reset_impersonation(cls, user_id):
        try:
            res = cls.do_request(
                url=f"https://gb1-beta.gubagoo.com/admin_tools/debug/impersonation/reset.php?impersonator_id={user_id}")
            Log.info(f"API - Gb1Settings: Reset impersonation mode result{res.text}")
            return res.text
        except AssertionError as e:
            Log.warning(f"API - Gb1Settings: Error happened during reset impersonation for {user_id} user: {e}")

    @classmethod
    @step_decorator('API - Gb1Settings: Reset')
    def reset(cls, account_id=chat_console_account_id):
        try:
            cls.do_request(url=cls.get_route() + f'?account_id={account_id}&reset=1')
        except AssertionError as e:
            Log.warning(f"API - Gb1Settings: Error happened during reset for {account_id} account {e}")
            step(5)
            cls.do_request(url=cls.get_route() + f'?account_id={account_id}&reset=1')
        time.sleep(3)

    @classmethod
    @step_decorator('API - Gb1Settings: Reset Resq')
    def reset_resq(cls, account_id=chat_console_account_id):
        try:
            cls.do_request(url=cls.get_route() + f'?account_id={account_id}&resq_reset=1')
        except AssertionError as e:
            Log.warning(f"API - Gb1Settings: Error happened during reset for {account_id} account {e}")
            time.sleep(5)
            cls.do_request(url=cls.get_route() + f'?account_id={account_id}&resq_reset=1')

    @classmethod
    @step_decorator('API - Gb1Settings: Chat reset')
    def chat_reset(cls, account_id=chat_console_account_id):
        cls.do_request(url=cls.get_route() + f'?account_id={account_id}&chat_reset=1')
        time.sleep(3)

    @classmethod
    @step_decorator('API - Gb1Settings: Set chat rating setting')
    def set_chat_rating_setting(cls, account_id=chat_console_account_id, rating_value=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_rating]": rating_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("chat_rating", rating_value, account_id), \
            f"{rating_value} chat rating setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set ai skills setting')
    def set_ai_skills_setting(cls, account_id=chat_console_account_id,
                              ai_skills_value=all_ai_skills):
        params = {"account_id": account_id,
                  "settings[ai_skills]": ai_skills_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set employment link')
    def set_employment_link(cls, account_id=chat_console_account_id, link='https://www.google.com'):
        params = {"account_id": account_id,
                  "settings[ai_employment_link]": link}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set QRs setting')
    def set_quick_replies_setting(cls, account_id=chat_console_account_id, skin="default", qr1="", qr1_live_play="0",
                                  qr2="", qr2_live_play="0"):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_skin]": skin,
                  "tbs[toolbar_chat_qr_1_onclick_event_id]": qr1_live_play,
                  "tbs[toolbar_chat_quick_reply_1]": qr1,
                  "tbs[toolbar_chat_qr_2_onclick_event_id]": qr2_live_play,
                  "tbs[toolbar_chat_quick_reply_2]": qr2}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("chat_invite_skin", skin, account_id), \
            f"{skin} greeter skin setting was not applied"
        assert cls.toolbar_wait_for_regeneration("chat_quick_reply_1", qr1, account_id), \
            f"{qr1} greeter quick_reply setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set QRs welcome screen setting')
    def set_quick_replies_welcome_screen_setting(cls, account_id=chat_console_account_id, skin="default",
                                                 qr_in_welcome_screen_value=0,
                                                 qr1="", qr1_live_play="0", qr2="", qr2_live_play="0"):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_skin]": skin,
                  "tbs[toolbar_chat_qr_in_welcome_screen]": qr_in_welcome_screen_value,
                  "tbs[toolbar_chat_qr_1_onclick_event_id]": qr1_live_play,
                  "tbs[toolbar_chat_quick_reply_1]": qr1,
                  "tbs[toolbar_chat_qr_2_onclick_event_id]": qr2_live_play,
                  "tbs[toolbar_chat_quick_reply_2]": qr2}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("chat_invite_skin", skin, account_id), \
            f"{skin} greeter skin setting was not applied"
        assert cls.toolbar_wait_for_regeneration("chat_quick_reply_1", qr1, account_id), \
            f"{qr1} greeter quick_reply setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set X-Time Dealer Code setting')
    def set_xtime_dealer_code_setting(cls, account_id=chat_console_account_id, xtime_dealer_code_value=0):
        params = {"account_id": account_id,
                  "settings[service_scheduling_id]": xtime_dealer_code_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set offer terms setting')
    def set_offer_terms_setting(cls, account_id=chat_console_account_id, offer_id=0, offer_terms_value=0):
        params = {"account_id": account_id,
                  "offer[id]": offer_id,
                  "offer[show_terms]": offer_terms_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set offer enabled setting')
    def set_offer_enabled_setting(cls, account_id=chat_console_account_id, offer_id=0, enabled_value=0):
        params = {"account_id": account_id,
                  "offer[id]": offer_id,
                  "offer[enabled]": enabled_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set service scheduling provider setting')
    def set_service_scheduling_provider_setting(cls, account_id=chat_console_account_id,
                                                service_scheduling_id_value="xts9010",
                                                service_scheduling_provider_value=ServiceProviderType.XTIME.capitalize(),
                                                era_system_number_value="",
                                                service_portal_profile_token_value=""):
        params = {"account_id": account_id,
                  "settings[service_scheduling_id]": service_scheduling_id_value,
                  "settings[service_scheduling_provider]": service_scheduling_provider_value,
                  "settings[era_system_number]": era_system_number_value,
                  "settings[service_portal_profile_token]": service_portal_profile_token_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        time.sleep(3)

    @classmethod
    @step_decorator('API - Gb1Settings: Set offer chat onclick event setting')
    def set_offer_chat_onclick_event_setting(cls, account_id=chat_console_account_id, offer_id=0, event_id=0):
        params = {"account_id": account_id,
                  "offer[id]": offer_id,
                  "offer[chat_onclick_event_id]]": event_id}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set offer form setting')
    def set_offer_form_setting(cls, account_id=chat_console_account_id, offer_id=xtime_id, form_value="xtime",
                               name="Publisher Scheduling Offer with Xtime"):
        params = {"account_id": account_id,
                  "offer[id]": offer_id,
                  "offer[form]": form_value,
                  "offer[name]": name}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        time.sleep(5)

    @classmethod
    @step_decorator('API - Gb1Settings: Set special offer id setting')
    def set_special_offer_id_setting(cls, account_id=chat_console_account_id, offer_id="0"):
        params = {"account_id": account_id,
                  "tbs[special_offer_id]": offer_id}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("special_offer_id", offer_id, account_id), \
            f"{offer_id} special offer id setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Trigger Chat settings')
    def set_trigger_chat_settings(cls, account_id=chat_console_account_id, tr_id=33, enabled_value="0",
                                  chat_fwindow_value='',
                                  chat_fwindow_repeat_value="",
                                  chat_fwindow_skin_value="",
                                  chat_invite_title_value="",
                                  chat_invite_message_value="",
                                  chat_invite_message_personal_value="",
                                  chat_fwindow_position_h_value="",
                                  chat_fwindow_position_v_value="",
                                  chat_fwindow_position_h_px_value="",
                                  chat_fwindow_position_v_px_value="",
                                  chat_autoengage_value="-1",
                                  chat_autoengage_after_value="-1",
                                  chat_welcome_message_value="",
                                  chat_autoengage_score='-1'
                                  ):
        """
            Sets Chat settings values for existed Trigger
            Arguments:
                account_id: an account id to witch trigger belong
                tr_id: a trigger id
                chat_fwindow_value: Float Window setting. Possible values:
                    Use Global Setting - "", Enabled - "enabled", Disabled - "disabled"
                chat_fwindow_repeat_value: Float Window Frequency setting. Possible values:
                    Global - "", disable after 1 close - "1", disable after 2 close - "2", Once Per Session - "ses", Every Time - "ev"
                chat_fwindow_skin_value: Float Window Skin setting. Possible values:
                    Global - "", Default - "default", 3.0 Default Big Text - "default-big-text", Default or Text Us - "or-text-us", Grey - "grey", Small - "small", 3.0 Inventory - "inventory", 3.0 Team with buttons - "team-buttons", 3.0 Small - "small-bar", Chat Now Icon - "now", Greeter with QR - "greeter-qr", Pull Tab - "pull-tab"
                chat_invite_title_value: Greeter title setting
                chat_invite_message_value: Greeter text setting
                chat_invite_message_personal_value: Greeter Text Personalized setting
                chat_fwindow_position_h_value: Float Window Horizontal Position setting. Possible values:
                    Global - "", Auto - "auto", Left - "left", Right - "right"
                chat_fwindow_position_v_value: Float Window Vertical Position setting. Possible values:
                    Global - "", Auto - "auto", Top - "top", Bottom - "bottom"
                chat_fwindow_position_h_px_value: Horizontal Offset setting
                chat_fwindow_position_v_px: Vertical Offset setting
                chat_autoengage_value: Autoengage setting. Possible values:
                    Inherit From Global - "-1", Disabled - "0", Enabled - "1"
                chat_autoengage_after_value: Engage after setting. Possible values:
                    Inherit From Global - "-1", 1-11 second - "1"-"11", 30 second - "30", 60 second - "60", 90 second - "90"
                chat_welcome_message: "Welcome Page" Greeting setting
            Returns:
        """
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][enabled]": enabled_value,
                  f"trigger[{tr_id}][chat_fwindow]": chat_fwindow_value,
                  f"trigger[{tr_id}][chat_fwindow_repeat]": chat_fwindow_repeat_value,
                  f"trigger[{tr_id}][chat_fwindow_skin]": chat_fwindow_skin_value,
                  f"trigger[{tr_id}][chat_invite_title]": chat_invite_title_value,
                  f"trigger[{tr_id}][chat_invite_message]": chat_invite_message_value,
                  f"trigger[{tr_id}][chat_invite_message_personal]": chat_invite_message_personal_value,
                  f"trigger[{tr_id}][chat_fwindow_position_h]": chat_fwindow_position_h_value,
                  f"trigger[{tr_id}][chat_fwindow_position_v]": chat_fwindow_position_v_value,
                  f"trigger[{tr_id}][chat_fwindow_position_h_px]": chat_fwindow_position_h_px_value,
                  f"trigger[{tr_id}][chat_fwindow_position_v_px]": chat_fwindow_position_v_px_value,
                  f"trigger[{tr_id}][chat_autoengage]": chat_autoengage_value,
                  f"trigger[{tr_id}][chat_autoengage_after]": chat_autoengage_after_value,
                  f"trigger[{tr_id}][chat_welcome_message]": chat_welcome_message_value,
                  f"trigger[{tr_id}][chat_autoengage_score_threshold]": chat_autoengage_score
                  }
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        time.sleep(3)

    @classmethod
    @step_decorator('API - Gb1Settings: Set trigger setting')
    def set_greeter_trigger_setting(cls, account_id=chat_console_account_id, tr_id=27, enabled_value="0",
                                    chat_invite_message_value="I'm online! How can I help you with the  {make} {model}? 😀",
                                    condition_value='Vehicle'):
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][condition]": condition_value,
                  f"trigger[{tr_id}][chat_invite_message]": chat_invite_message_value,
                  f"trigger[{tr_id}][enabled]": enabled_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set  setting')
    def set_trigger_setting(cls, account_id=chat_console_account_id, tr_id=28, enabled_value="0",
                            condition_value='Exit',
                            offer_value=1585):
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][condition]": condition_value,
                  f"trigger[{tr_id}][enabled]": enabled_value,
                  f"trigger[{tr_id}][launch_offer]": offer_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Trigger date ranges')
    def set_trigger_date_ranges(cls, account_id=chat_console_account_id, tr_id=33, enabled_value="0",
                                date_range_settings=[]):

        params = {
            "account_id": account_id,
            f"trigger[{tr_id}][enabled]": enabled_value
        }
        for i in range(len(date_range_settings)):
            params[f"trigger[{tr_id}][date_range_conditions][{i}][date_from]"] = date_range_settings[i].date_from
            params[f"trigger[{tr_id}][date_range_conditions][{i}][date_to]"] = date_range_settings[i].date_to

        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Trigger day and time')
    def set_trigger_day_and_time_settings(cls, account_id=chat_console_account_id, tr_id=33, enabled_value="0",
                                          day_time_settings=[]):
        params = {
            "account_id": account_id,
            f"trigger[{tr_id}][enabled]": enabled_value
        }
        for i in range(len(day_time_settings)):
            params[f"trigger[{tr_id}][day_plus_time_conditions][{i}][day_type]"] = day_time_settings[i].day_type
            params[f"trigger[{tr_id}][day_plus_time_conditions][{i}][time_from]"] = day_time_settings[i].time_from
            params[f"trigger[{tr_id}][day_plus_time_conditions][{i}][time_from_min]"] = day_time_settings[
                i].time_from_min
            params[f"trigger[{tr_id}][day_plus_time_conditions][{i}][time_to]"] = day_time_settings[i].time_to
            params[f"trigger[{tr_id}][day_plus_time_conditions][{i}][time_to_min]"] = day_time_settings[i].time_to_min
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: reset day and time')
    def reset_trigger_day_and_time_settings(cls, account_id=chat_console_account_id, tr_id=33, enabled_value='0'):
        params = {
            "account_id": account_id,
            f'trigger[{tr_id}][day_plus_time_conditions]': '',
            f"trigger[{tr_id}][enabled]": enabled_value
        }
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: reset date range')
    def reset_trigger_date_range(cls, account_id=chat_console_account_id, tr_id=33, enabled_value='0'):
        params = {
            "account_id": account_id,
            f'trigger[{tr_id}][date_range_conditions]': '',
            f"trigger[{tr_id}][enabled]": enabled_value
        }
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Trigger setting for mobile page to show "specials" button')
    def set_mobile_offer_trigger_setting(cls, account_id=chat_console_account_id, tr_id=28, enabled_value="0",
                                         condition_value='Exit',
                                         offer_value=1585, mobile_offer_value=""):
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][condition]": condition_value,
                  f"trigger[{tr_id}][enabled]": enabled_value,
                  f"trigger[{tr_id}][launch_offer]": offer_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("mobile_offers", mobile_offer_value, account_id), \
            " mobile offers trigger setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set DB trigger status')
    def set_bd_trigger_status(cls, account_id=chat_console_account_id, tr_id=28, status="0", enabled_value="0"):
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][enabled]": enabled_value,
                  f"trigger[{tr_id}][roller]": status}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set DB trigger status')
    def set_da_trigger_status(cls, account_id=chat_console_account_id, tr_id=28, status="0", enabled_value="0"):
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][enabled]": enabled_value,
                  f"trigger[{tr_id}][autoopen_deals_app]": status}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set DB trigger status')
    def set_bb_trigger_status(cls, account_id=chat_console_account_id, tr_id=28, status="0", enabled_value="0"):
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][enabled]": enabled_value,
                  f"trigger[{tr_id}][single_engage]": status}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set DB trigger status')
    def set_po_trigger_status(cls, account_id=chat_console_account_id, tr_id=28, status="0", enabled_value="0"):
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][enabled]": enabled_value,
                  f"trigger[{tr_id}][po_enabled]": status}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Xtime brand setting')
    def set_xtime_brand_setting(cls, account_id=chat_console_account_id, offer_id=0, brand=""):
        params = {"account_id": account_id,
                  "offer[id]": offer_id,
                  "offer[brand]": brand}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set language setting')
    def set_language_toolbar(cls, account_id=chat_console_account_id, language_value="en"):
        params = {"account_id": account_id,
                  "tb[language]": language_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("language", language_value, account_id), \
            f"{language_value} chat language setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set desktop UI left setting')
    def set_desktop_ui_left_setting(cls, account_id=chat_console_account_id, left_value=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_desktop_ui_left]": left_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("toolbar_chat_desktop_ui_left", left_value, account_id), \
            f"{left_value} desktop UI left setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Mobile state setting')
    def set_mobile_default_state_setting(cls, account_id=chat_console_account_id, mobile_default_state_value=0):
        params = {"account_id": account_id,
                  "tbs[mobile_default_state]": mobile_default_state_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("mobile_state", mobile_default_state_value, account_id), \
            f"{mobile_default_state_value} mobile state setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Floating window image setting')
    def set_floating_image_setting(cls, account_id=chat_console_account_id, floating_image_value=floating_image):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_image]": floating_image_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Display condition setting')
    def set_display_condition_setting(cls, account_id=chat_console_account_id, display_condition_value="ev"):
        """
            Sets Display condition Setting
            Arguments:
                account_id: an account id to witch trigger belong
                display_condition_value: Display condition setting. Possible values:
                    disable after 1 close - "1",
                    disable after 2 close - "2",
                    disable after 3 close - "3",
                    disable after 4 close - "4",
                    disable after 5 close - "5",
                    Once Per Session - "ses",
                    Every Time - "ev"
            Returns:
        """
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_repeat]": display_condition_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        time.sleep(2)

    @classmethod
    @step_decorator('API - Gb1Settings: Set Greeter image left setting')
    def set_greeter_left_image_setting(cls, account_id=chat_console_account_id, left_image_value=1063031):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_image_2]": left_image_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Greeter image right setting')
    def set_greeter_right_image_setting(cls, account_id=chat_console_account_id, right_image_value=1068545):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_image_3]": right_image_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Floating window operator name setting')
    def set_floating_operator_name_setting(cls, account_id=chat_console_account_id,
                                           floating_operator_name_value="John"):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_operator]": floating_operator_name_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("chat_invite_operator_name", floating_operator_name_value,
                                                 account_id), \
            f"{floating_operator_name_value} Floating window operator name setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Show float window operator in chat setting')
    def set_show_float_operator_setting(cls, account_id=chat_console_account_id, show_float_operator_value=1):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_operator_in_chat]": show_float_operator_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO price adjustments setting')
    def set_cbo_fi_price_adjustments(cls, account_id=chat_console_account_id, cbo_fi_price_adjustments_value=""):
        params = {"account_id": account_id,
                  "settings[cbo_fi_price_adjustments]": cbo_fi_price_adjustments_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO display trade-in value range setting')
    def set_cbo_display_trade_in_value_range(cls, account_id=chat_console_account_id, value=1):
        params = {"account_id": account_id,
                  "settings[cbo_display_tradein_value_range]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO Payments Dealer Id setting')
    def set_cbo_payments_dealer_id(cls, account_id=chat_console_account_id,
                                   value="16-01"):
        params = {"account_id": account_id,
                  "settings[cbo_payments_dealer_id]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        # value="9999;449136;https://apis.dealertrack.com/external/production/payment-driver-services/v1?singleWsdl;GUBAGOO;v3r95bzz"):

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO Payments Dealer Id setting')
    def set_cbo_payment_provider(cls, account_id=chat_console_account_id, account="rey_rey"):
        params = {"account_id": account_id,
                  "settings[cbo_payments_provider]": account}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Floating operator settings')
    def set_floating_operator_setting(cls, account_id=chat_console_account_id, floating_operator_name_value="John",
                                      floating_image_value=floating_image, right_image_value=right_image,
                                      left_image_value=left_image):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_operator]": floating_operator_name_value,
                  "tbs[toolbar_chat_fwindow_image]": floating_image_value,
                  "tbs[toolbar_chat_fwindow_image_2]": left_image_value,
                  "tbs[toolbar_chat_fwindow_image_3]": right_image_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("chat_invite_operator_name",
                                                 "Alice" if floating_operator_name_value == ""
                                                 else floating_operator_name_value, account_id), \
            f"{floating_operator_name_value} Floating window operator name setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set use dealer vdp setting')
    def set_use_dealer_vdp_setting(cls, account_id=chat_console_account_id, use_dealer_vdp_value=1):
        params = {"account_id": account_id,
                  "settings[toolbar_use_dealer_vdp]": use_dealer_vdp_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("toolbar_use_dealer_vdp", use_dealer_vdp_value, account_id), \
            f"{use_dealer_vdp_value} use dealer vdp setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Chat terms greeter message setting')
    def set_sms_term_greeter_message_setting(cls, account_id=chat_console_account_id, sms_term_greeter_message_value=1):
        params = {"account_id": account_id,
                  "settings[sms_term_greeter_message]": sms_term_greeter_message_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        # there is no way to check if it was applied, it is in BE

    @classmethod
    @step_decorator('API - Gb1Settings: Set Show Customer Name Field')
    def set_show_sms_customer_name_field(cls, account_id=chat_console_account_id, show_sms_customer_name_field_value=1):
        params = {"account_id": account_id,
                  "settings[show_sms_customer_name_field]": show_sms_customer_name_field_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        # there is no way to check if it was applied, it is in BE

    @classmethod
    @step_decorator('API - Gb1Settings: Set Custom message label')
    def set_custom_message_label(cls, account_id=chat_console_account_id, message_label=""):
        params = {"account_id": account_id,
                  "settings[custom_sms_message_label]": message_label}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set required fields in SMS')
    def set_required_fields_sms(cls, account_id=chat_console_account_id, sms_field_value="phone"):
        params = {"account_id": account_id,
                  "settings[sms_required_fields]": sms_field_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Disable Greeter Msg in Transcript/UI setting')
    def set_chat_disable_transcript_greeter_setting(cls, account_id=chat_console_account_id,
                                                    chat_disable_transcript_greeter_value=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_disable_transcript_greeter]": chat_disable_transcript_greeter_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("toolbar_chat_disable_transcript_greeter",
                                                chat_disable_transcript_greeter_value, account_id), \
            f"{chat_disable_transcript_greeter_value} Disable Greeter Msg in Transcript/UI setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Go mobile setting')
    def set_go_mobile_setting(cls, account_id=chat_console_account_id, go_mobile_value=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_sms]": go_mobile_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("chat_go_mobile", go_mobile_value, account_id), \
            f"{go_mobile_value} Go mobile setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Mobile direct sms chat setting')
    def set_mobile_direct_sms_chat_setting(cls, account_id=chat_console_account_id, mobile_direct_sms_chat_value=0,
                                           go_mobile_value=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_sms]": go_mobile_value,
                  "tbs[toolbar_direct_sms_chat]": mobile_direct_sms_chat_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("direct_sms_chat", mobile_direct_sms_chat_value, account_id), \
            f"{mobile_direct_sms_chat_value} Mobile direct sms chat setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set VDP Flow setting')
    def set_vdp_flow_setting(cls, account_id=chat_console_account_id, vdp_flow_value="vdp"):
        params = {"account_id": account_id,
                  "settings[vdp_flow]": vdp_flow_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("vdp_flow", vdp_flow_value, account_id), \
            f"{vdp_flow_value} VDP Flow setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set VDP version setting')
    def set_vdp_version_setting(cls, account_id=chat_console_account_id, vdp_version_value=3):
        params = {"account_id": account_id,
                  "settings[vdp_version]": vdp_version_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("vdp_version", vdp_version_value, account_id), \
            f"{vdp_version_value} VDP version setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set cbo account package setting')
    def set_cbo_account_package_setting(cls, account_id=chat_console_account_id, vr_value="vr_pro"):
        params = {"account_id": account_id,
                  "settings[cbo_account_package]": vr_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO flow setting')
    def set_cbo_flow_setting(cls, account_id=chat_console_account_id, cbo_flow_value="buy_car"):
        params = {"account_id": account_id,
                  "settings[cbo_flow]": cbo_flow_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set enable showroom setting')
    def set_enable_showroom_setting(cls, account_id=chat_console_account_id, enable_showroom="1"):
        params = {"account_id": account_id,
                  "settings[showroom_enabled]": enable_showroom}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set default lease mileage setting')
    def set_cbo_lease_mileage(cls, account_id=chat_console_account_id, cbo_lease_mileage_value="Select"):
        params = {"account_id": account_id,
                  "settings[cbo_lease_mileage]": cbo_lease_mileage_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Apple Business Chat ID setting')
    def set_apple_biz_id_setting(cls, account_id=chat_console_account_id, apple_biz_id_value=""):
        params = {"account_id": account_id,
                  "settings[apple_biz_id]": apple_biz_id_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("apple_biz_id",
                                                apple_biz_id_value if apple_biz_id_value != "" else False, account_id), \
            f"'{apple_biz_id_value}' Apple Business Chat ID setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Auto Engage setting')
    def set_auto_engage_setting(cls, account_id=chat_console_account_id,
                                autoengage_value=0,
                                autoengage_after='1',
                                autoengage_repeat='0',
                                autoengage_score='0',
                                message="We'd love to help you. Would you like to start a chat?",
                                focus_message=""):
        params = {"account_id": account_id,
                  "tbs[toolbar_autoengage]": autoengage_value,
                  "tbs[toolbar_autoengage_after]": autoengage_after,
                  "tbs[toolbar_autoengage_repeat]": autoengage_repeat,
                  "tbs[toolbar_autoengage_score]": autoengage_score,
                  "tbs[toolbar_chat_request_message]": message,
                  "tbs[toolbar_chat_request_message_personal]": focus_message}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        if autoengage_value == 0:
            autoengage_value = False
        else:
            autoengage_value = '1'
        assert cls.toolbar_wait_for_regeneration('chat_auto_engage', autoengage_value, account_id), \
            f"{autoengage_value} Auto Engage setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: reset cbo settings')
    def reset_cbo_settings(cls, account_id=chat_console_account_id, value=1):
        cls.do_request(url=cls.get_route() + f'?account_id={account_id}&cbo_reset={value}')

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO enabled setting')
    def set_cbo_enabled_setting(cls, account_id=chat_console_account_id, cbo_enabled_value=0):
        params = {"account_id": account_id,
                  "settings[cbo_enabled]": cbo_enabled_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("cbo_enabled", cbo_enabled_value, account_id), \
            f"{cbo_enabled_value} CBO enabled setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Dynamic Credit App Setting')
    def set_dynamic_credit_app(cls, account_id=chat_console_account_id, dynamic_credit_app_value=0):
        params = {"account_id": account_id,
                  "settings[cbo_standalone_credit_app_enabled]": dynamic_credit_app_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO version 2 enabled setting')
    def set_cbo_version_2_enabled_setting(cls, account_id=chat_console_account_id, cbo_enabled_value=0):
        params = {"account_id": account_id,
                  "settings[cbo_version_2_enabled]": cbo_enabled_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set credit check provider setting')
    def set_cbo_credit_check_provider_setting(cls, account_id=chat_console_account_id, value="700Credit"):
        params = {"account_id": account_id,
                  "settings[cbo_credit_check_provider]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set AI Bot Enabled setting')
    def set_ai_bot_enabled_setting(cls, account_id=chat_console_account_id, value=1):
        params = {"account_id": account_id,
                  "settings[ai_enabled]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set cbo lease mileage setting')
    def set_cbo_lease_mileage_setting(cls, account_id=chat_console_account_id, value=''):
        params = {"account_id": account_id,
                  "settings[cbo_lease_mileage]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set cbo trade_in lease enabled setting')
    def set_cbo_trade_in_lease_enabled(cls, account_id=chat_console_account_id, value=0):
        params = {"account_id": account_id,
                  "settings[cbo_trade_in_lease_enabled]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set cbo lease payoff apply method enabled')
    def set_cbo_lease_payoff_apply_method_enabled(cls, account_id=chat_console_account_id, value=1):
        params = {"account_id": account_id,
                  "settings[cbo_lease_payoff_apply_method_enabled]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO version 2 enabled setting')
    def set_cbo_version_2_enabled_setting(cls, account_id=chat_console_account_id, cbo_enabled_value=0):
        params = {"account_id": account_id,
                  "settings[cbo_version_2_enabled]": cbo_enabled_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO employee pricing enabled setting')
    def set_cbo_employee_pricing_enabled(cls, account_id=chat_console_account_id, value=0):
        params = {"account_id": account_id,
                  "settings[cbo_employee_pricing_enabled]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO employee pricing flow setting')
    def set_cbo_employee_pricing_flow(cls, account_id=chat_console_account_id,
                                      employee_pricing_flow: EmployeePricingFlow = EmployeePricingFlow.STEP):
        params = {"account_id": account_id,
                  "settings[cbo_employee_pricing_flow]": employee_pricing_flow.value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Use License Plate Trade-In Flow setting')
    def set_license_plate_trade_in_flow_setting(cls, account_id=chat_console_account_id,
                                                license_plate_trade_in_flow_value=0):
        params = {"account_id": account_id,
                  "settings[ai_tradein_use_plate]": license_plate_trade_in_flow_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set system timezone setting')
    def set_system_timezone_setting(cls, account_id=chat_console_account_id, system_timezone_value='America/New_York'):
        params = {"account_id": account_id,
                  "settings[system_timezone]": system_timezone_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set offers desktop position setting')
    def set_offers_desktop_position(cls, account_id=chat_console_account_id, position=""):
        params = {"account_id": account_id,
                  "tbs[toolbar_offers_desktop_position]": position}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("offers_desktop_position", position, account_id), \
            f"{position} offers desktop position setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set offers mobile position setting')
    def set_offers_mobile_position(cls, account_id=chat_console_account_id, position=""):
        params = {"account_id": account_id,
                  "tbs[toolbar_offers_mobile_position]": position}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO Chat Routing setting')
    def set_cbo_chat_routing_setting(cls, account_id=chat_console_account_id, cbo_chat_routing_value='op'):
        params = {"account_id": account_id,
                  "settings[cbo_chat_routing]": cbo_chat_routing_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Announcement settings')
    def set_announcement_settings(cls, account_id=chat_console_account_id,
                                  position="right",
                                  order=1,
                                  content=announcement_content,
                                  auto_start=0,
                                  button="",
                                  button_label="COVID-19 Safety",
                                  modal_button_label="More info",
                                  modal_button_event="https://www.vistabmw.com/customer-health-and-safety/",
                                  is_bottom_on_mobile=0,
                                  modal_bgcolor="#adc7f6",
                                  modal_btn_bgcolor="#000",
                                  modal_btn_color="#f0ec6b",
                                  desktop_position="gg-is-bottom gg-is-center"):
        params = {"account_id": account_id,
                  f"tbs[toolbar_apps_positions][{position}][Announcement]": order,
                  "tbs[toolbar_announcement_content]": content,
                  "tbs[toolbar_announcement_is_auto_start]": auto_start,
                  "tbs[toolbar_announcement_button]": button,
                  "tbs[toolbar_announcement_button_label]": button_label,
                  "tbs[toolbar_announcement_modal_button_label]": modal_button_label,
                  "tbs[toolbar_announcement_modal_button_event]": modal_button_event,
                  "tbs[toolbar_announcement_is_bottom_on_mobile]": is_bottom_on_mobile,
                  "tbs[toolbar_announcement_modal_bgcolor]": modal_bgcolor,
                  "tbs[toolbar_announcement_modal_btn_bgcolor]": modal_btn_bgcolor,
                  "tbs[toolbar_announcement_modal_btn_color]": modal_btn_color,
                  "tbs[toolbar_announcement_desktop_position]": desktop_position}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("announcement_button_label", button_label, account_id), \
            f"{button_label} announcement button label setting was not applied"
        time.sleep(3)

    @classmethod
    @step_decorator('API - Gb1Settings: Set Company exclusion hours setting')
    def set_exclusion_hours_setting(cls, account_id=chat_console_account_id, exclusion_hours_value=''):
        params = {"account_id": account_id,
                  "settings[company_exclusion_hours]": exclusion_hours_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Show all unassigned and queued chats setting')
    def set_show_all_unassigned_and_queued_chats_setting(cls, account_id=chat_console_account_id,
                                                         resq_self_managed_value=1):
        params = {"account_id": account_id,
                  "settings[resq_self_managed]": resq_self_managed_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator("API - GB1Settings: Set Language settings")
    def set_language_setting(cls, account_id=chat_console_account_id,
                             multilingual_value=0,
                             autotranslate_enabled_value=0,
                             native_language_value='en'):
        params = {"account_id": account_id,
                  "settings[multilingual]": multilingual_value,
                  "settings[autotranslate_enabled]": autotranslate_enabled_value,
                  "settings[native_language]": native_language_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

        assert cls.loader_wait_for_regeneration("multilingual", multilingual_value, account_id), \
            f"{autotranslate_enabled_value} Auto-Detect Chat Language setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Company hours setting')
    def set_company_hours_setting(cls, account_id=chat_console_account_id,
                                  company_hours_value='{"sales":[{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":false,"from":null,"to":null},{"opening":0,"closing":0}],"service":[{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":false,"from":null,"to":null},{"opening":0,"closing":0}],"parts":[{"active":false,"from":null,"to":null},{"active":true,"from":"9:00","to":"18:00"},{"active":false,"from":null,"to":null},{"active":true,"from":"9:00","to":"18:00"},{"active":true,"from":"9:00","to":"18:00"},{"active":false,"from":null,"to":null},{"active":false,"from":null,"to":null},{"opening":0,"closing":0}]}'):
        params = {"account_id": account_id,
                  "settings[company_hours]": company_hours_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set cbo trade in provider setting')
    def set_cbo_trade_in_provider_setting(cls, account_id=chat_console_account_id,
                                          trade_in_provider: TradeInProvider = TradeInProvider.KBB):
        params = {"account_id": account_id,
                  "settings[cbo_trade_in_provider]": trade_in_provider}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set ai trade in provider setting')
    def set_ai_trade_in_provider_setting(cls, account_id=chat_console_account_id,
                                         trade_in_provider: TradeInProvider = TradeInProvider.KBB):
        params = {"account_id": account_id,
                  "settings[ai_trade_in_provider]": trade_in_provider}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set cbo trade in provider dealer id setting')
    def set_cbo_trade_in_provider_dealer_id_setting(cls, account_id=chat_console_account_id,
                                                    cbo_trade_in_provider_dealer_id='ak002'):
        params = {"account_id": account_id,
                  "settings[cbo_trade_in_provider_dealer_id]": cbo_trade_in_provider_dealer_id}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set hide transition messages setting')
    def set_chat_onclick_hide_transition_msgs_setting(cls, account_id=chat_console_account_id,
                                                      chat_onclick_hide_transition_msgs_value=0):
        params = {"account_id": account_id,
                  "settings[chat_onclick_hide_transition_msgs]": chat_onclick_hide_transition_msgs_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Single-Page Application setting')
    def set_single_page_application_setting(cls, account_id=chat_console_account_id,
                                            is_single_page_application=0):
        params = {"account_id": account_id,
                  "tbs[is_single_page_application]": is_single_page_application}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set auto start chat setting')
    def set_auto_start_chat_setting(cls, account_id=chat_console_account_id, toolbar_chat_auto_start_value=0,
                                    message="What can I do for you today?", chat_only_message=""):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_auto_start]": toolbar_chat_auto_start_value,
                  "tbs[toolbar_chat_auto_start_message]": message,
                  "tbs[toolbar_chat_auto_start_msg_personal]": chat_only_message}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("chat_auto_start", toolbar_chat_auto_start_value, account_id), \
            f"{toolbar_chat_auto_start_value} auto start chat setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set pdpa_policy with {2} value and pdpa_policy_link {3} ')
    def set_pdpa_policy(cls, account_id=chat_console_account_id, pdpa_policy=0, pdpa_policy_link=""):
        params = {"account_id": account_id,
                  "settings[pdpa_policy]": pdpa_policy,
                  "settings[pdpa_policy_link]": pdpa_policy_link}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        time.sleep(3)
        assert cls.loader_wait_for_regeneration("pdpa_policy", pdpa_policy, account_id), \
            f"{pdpa_policy} pdpa_policy setting was not applied"
        assert cls.loader_wait_for_regeneration("pdpa_policy_link", pdpa_policy_link, account_id), \
            f"{pdpa_policy_link} pdpa_policy_link setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set WhatsApp enabled setting')
    def set_whats_app_settings(cls, account_id=chat_console_account_id, phone_number=0, whatsapp_value=0):
        params = {"account_id": account_id,
                  "phone": phone_number,
                  "whatsapp": whatsapp_value}
        if cls.get_loader_settings().get("whatsapp_enabled") != bool(whatsapp_value):
            cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
            assert cls.loader_wait_for_regeneration("whatsapp_enabled", bool(whatsapp_value), wait_time=120), \
                f"{whatsapp_value} whatsapp_enabled setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Holiday settings')
    def set_holiday_settings(cls,
                             account_id=chat_console_account_id,
                             position="right",
                             order=1,
                             holiday_type=""):
        params = {"account_id": account_id,
                  f"tbs[toolbar_apps_positions][{position}][Holiday]": order,
                  "tbs[toolbar_holiday_type]": holiday_type}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("holiday_type", holiday_type, account_id), \
            f"{holiday_type} holiday type setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Floating window skin settings')
    def set_floating_window_skin(cls, account_id=chat_console_account_id, toolbar_chat_fwindow_skin_value='default'):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_skin]": toolbar_chat_fwindow_skin_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set no welcome message settings')
    def set_no_welcome_message_settings(cls, account_id=chat_console_account_id, no_welcome_message_value=0):
        params = {"account_id": account_id,
                  "settings[no_welcome_message]": no_welcome_message_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: generate ford token')
    def gen_ford_token(cls, account_id="101487"):
        return cls.do_request(url=cls.get_route() + f'?account_id={account_id}&gen_ford_token=1').json()

    @classmethod
    @step_decorator('API - Gb1Settings: Set cbo F&I flow setting')
    def set_cbo_fi_flow_setting(cls, account_id=chat_console_account_id, fi_flow: FIFlow = FIFlow.PRODUCTS):
        params = {"account_id": account_id, "settings[cbo_fi_flow]": fi_flow}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: set mobile greeter text')
    def set_mobile_greeter_text(cls, account_id=chat_console_account_id, mobile_greeter_text="How may I help you? 😀"):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_invite_mobile_message]": mobile_greeter_text}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.toolbar_wait_for_regeneration("chat_invite_mobile_message", mobile_greeter_text, account_id), \
            f"{mobile_greeter_text} mobile greeter setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set Hide chat on desktop setting')
    def set_hide_chat_desktop_setting(cls, account_id=chat_console_account_id, desktop_value="0"):
        params = {"account_id": account_id,
                  "tbs[hidden]": desktop_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Hide chat on mobile setting')
    def set_hide_chat_mobile_setting(cls, account_id=chat_console_account_id, mobile_value="0"):
        params = {"account_id": account_id,
                  "tbs[hidden_mobile]": mobile_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set Path for inventory settings')
    def set_inventory_path(cls, account_id=chat_console_account_id, path="/qa_vehicles.txt"):
        params = {"account_id": account_id,
                  "settings[inventory_ftp_path]": path}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set value for scoring builder')
    def set_scoring_builder(cls, account_id=chat_console_account_id, page_visit='20', toolbar_click='10',
                            queued_in_chat='10', start_chat='10', new_visit_session='50'):
        ## temporary workaround, replace this function after task [CU-3712] is done
        params = {"account_id": account_id,
                  "settings[scoring_map]": f'a:5:{{s:10:"page_visit";i:{page_visit};s:13:"toolbar_click";i:{toolbar_click};s:14:"queued_in_chat";i:{queued_in_chat};s:10:"start_chat";i:{start_chat};s:17:"new_visit_session";i:{new_visit_session};}}'}

        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: set floating window in toolbar')
    def set_toolbar_floating_window(cls, account_id=chat_console_account_id, value=1):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: set floating window appear after in toolbar')
    def set_toolbar_floating_window_appear_after(cls, account_id=chat_console_account_id, time_in_sec='5'):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_fwindow_time]": time_in_sec}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: set qr in triggers')
    def set_trigger_qrs(cls, account_id=chat_console_account_id, tr_id='33', enabled_value="0", qr_status='Global',
                        qr_1='', qr_1_live_play='Global', qr_2='', qr_2_live_play='Global', qr_3='',
                        qr_3_live_play='Global', qr_4='', qr_4_live_play='Global', greeting_message=''):
        params = {"account_id": account_id,
                  f"trigger[{tr_id}][enabled]": enabled_value,
                  f"trigger[{tr_id}][chat_qr_in_welcome_screen]": qr_status,
                  f"trigger[{tr_id}][chat_quick_reply_1]": qr_1,
                  f"trigger[{tr_id}][chat_qr_1_onclick_event_id]": qr_1_live_play,
                  f"trigger[{tr_id}][chat_quick_reply_2]": qr_2,
                  f"trigger[{tr_id}][chat_qr_2_onclick_event_id]": qr_2_live_play,
                  f"trigger[{tr_id}][quick_reply_option_3]": qr_3,
                  f"trigger[{tr_id}][quick_reply_onclick_event_id_3]": qr_3_live_play,
                  f"trigger[{tr_id}][quick_reply_option_4]": qr_4,
                  f"trigger[{tr_id}][quick_reply_onclick_event_id_4]": qr_4_live_play,
                  f"trigger[{tr_id}][chat_welcome_message]": greeting_message
                  }
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set ghost mode setting')
    def set_ghost_mode_setting(cls, account_id=chat_console_account_id, cbo_enabled_value=0, ghost_enable=0):
        params = {"account_id": account_id,
                  "settings[cbo_enabled]": cbo_enabled_value,
                  "settings[cbo_ghost_widget]": ghost_enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("cbo_enabled", cbo_enabled_value, account_id)

    @classmethod
    @step_decorator('API - Gb1Settings: Set inventory hide price settings')
    def set_inventory_hide_price(cls, account_id=chat_console_account_id, inventory_value='Disabled'):
        params = {"account_id": account_id,
                  f"settings[inventory_hide_prices]": inventory_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set ghost mode setting')
    def set_toolbar_no_chat_sms_only_skin(cls, account_id=chat_console_account_id, chat_value=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_sms_only]": chat_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Disable Publisher Classic')
    def set_disable_publisher_classic(cls, account_id=chat_console_account_id, enable=0):
        params = {"account_id": account_id,
                  "settings[publisher_classic_disabled]": enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Enable Spotlight Book Service CTA')
    def set_enable_new_publisher_schedule_service_cta(cls, account_id=chat_console_account_id, enable=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_publisher_schedule_service_cta]": enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Enable AI Transparency')
    def set_ai_transparency_setting(cls, account_id=chat_console_account_id, enable=0):
        params = {"account_id": account_id,
                  "tbs[ai_transparency_remove_persona_elements]": enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set FOCUS settings')
    def set_focus(cls, account_id=chat_console_account_id, system_id='', group_id='', store_number='', branch_number='', env=''):
        params = {"account_id": account_id,
                  "settings[focus_ppsystem_id]": system_id,
                  "settings[focus_business_group_id]": group_id,
                  "settings[focus_store_number]": store_number,
                  "settings[focus_branch_number]": branch_number,
                  "settings[focus_environment]": env}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set welcome page greeting text personalized')
    def set_welcome_text_greeting_personalized(cls, account_id=chat_console_account_id, text_value=''):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_welcome_text_personal]": text_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set initial reponse personalized (chat only)')
    def set_initial_response(cls, account_id=chat_console_account_id, text_value=''):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_initial_response_personal]": text_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set greeter text personalized (chat only)')
    def set_greeter_text_personalized(cls, account_id=chat_console_account_id, text_value=''):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_invite_message_personal]": text_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set 1st agent join delay message (chat only)')
    def set_first_agent_join_delay_message(cls, account_id=chat_console_account_id, text_value='', time_value=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_agent_join_delay_msg_1_personal]": text_value,
                  "tbs[toolbar_agent_join_notification_delay]": time_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set welcome page greeting message')
    def set_welcome_page_greeting_message(cls, account_id=chat_console_account_id, text_value='Our team is available and happy to help. How can we help you today? 🙌'):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_welcome_text]": text_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set dynamic engagement button settings')
    def set_dynamic_engagement(cls, account_id=chat_console_account_id, engagement_btn_type='classic',
                               engagement_btn_icon='Default', expand_collapse_type='auto_expand', dynamic_label="",
                               primary_label=""):
        params = {"account_id": account_id,
                  "tbs[toolbar_engagement_button_type]": engagement_btn_type,
                  "tbs[toolbar_engagement_button_icon]": engagement_btn_icon,
                  "tbs[toolbar_engagement_expand_collapse_type]": expand_collapse_type,
                  "tbs[toolbar_engagement_button_label]": dynamic_label,
                  "tbs[toolbar_engagement_button_primary_label]": primary_label}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set dynamic engagement button QR settings')
    def set_dynamic_qr(cls, account_id=chat_console_account_id, engagement_btn_type='classic', disable_start_chat=0,
                       dynamic_label="", expand_collapse_type='auto_expand', enable_text_us=0, qr1_lp=4, qr1_lp_name="",
                       qr2_lp=5, qr2_lp_name="", position="right", order=1):
        params = {"account_id": account_id,
                  "tbs[toolbar_engagement_button_type]": engagement_btn_type,
                  "tbs[toolbar_engagement_button_label]": dynamic_label,
                  "tbs[toolbar_engagement_expand_collapse_type]": expand_collapse_type,
                  "tbs[toolbar_disable_engagement_startchat_qr]": disable_start_chat,
                  "tbs[toolbar_enable_engagement_textus_qr]": enable_text_us,
                  "tbs[toolbar_dynamic_qr_onclick_event_id_1]": qr1_lp,
                  "tbs[toolbar_dynamic_qr_option_1]": qr1_lp_name,
                  "tbs[toolbar_dynamic_qr_onclick_event_id_2]": qr2_lp,
                  "tbs[toolbar_dynamic_qr_option_2]": qr2_lp_name,
                  f"tbs[toolbar_apps_positions][{position}][QuickReply]": order}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set mobile UI left setting')
    def set_mobile_ui_left_setting(cls, account_id=chat_console_account_id, left_value=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_mobile_ui_left]": left_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
        assert cls.loader_wait_for_regeneration("toolbar_chat_mobile_ui_left", left_value, account_id), \
            f"{left_value} mobile UI left setting was not applied"

    @classmethod
    @step_decorator('API - Gb1Settings: Set show tab when chat is dismissed on mobile')
    def set_show_tab_chat_dismissed(cls, account_id=chat_console_account_id, enable=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_sidebar_on_mobile]": enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set start chat clarifying question')
    def set_start_chat_clarifying_question_setting(cls, account_id=chat_console_account_id, enable=1):
        params = {"account_id": account_id,
                  "settings[ai_customer_filter]": enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set F&I setting')
    def set_cbo_service_protection_setting(cls, account_id=chat_console_account_id, fi_value="products"):
        params = {"account_id": account_id,
                  "settings[cbo_fi_flow]": fi_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set department picker in chat')
    def set_department_picker_in_chat(cls, account_id=chat_console_account_id, enable=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_dept_picker_enabled]": enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set CBO cbo_vsp_widget setting')
    def set_cbo_vsp_widget_setting(cls, account_id=chat_console_account_id,
                                   vsp_widget_value: VSPWidget = VSPWidget.UNLOCK_PRICE_PAYMENTS_TRADE_CREDIT):
        params = {"account_id": account_id,
                  "settings[cbo_vsp_widget]": vsp_widget_value.value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set blinker animation')
    def set_blinker_animation_in_chat(cls, account_id=chat_console_account_id, enable=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_chat_bubble_blinker]": enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: set chat terms application')
    def set_chat_terms_application(cls, account_id=chat_console_account_id, value='both'):
        params = {"account_id": account_id,
                  "settings[sms_chat_terms_application]": value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set contact us setting')
    def set_contact_us_setting(cls, account_id=chat_console_account_id, position="left", order=1, address="",
                               contact_info=""):
        params = {"account_id": account_id,
                  f"tbs[toolbar_apps_positions][{position}][CallUs]": order,
                  "tbs[toolbar_contact_us_address]": address,
                  "tbs[toolbar_contact_us_info]": contact_info}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: Set spotlight button type')
    def set_spotlight_offer_btn_type(cls, account_id=chat_console_account_id, offer_id=xtime_id,
                                     btn_type=0):
        params = {"account_id": account_id,
                  "offer[id]": offer_id,
                  "offer[metadata]": f'{{"offer_button_type": {btn_type}}}'}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: set AI Location Skills')
    def set_ai_location_skills(cls, account_id=chat_console_account_id,
                               ai_location_detect_value=0,
                               ai_location_detect_setting_distance_value="250"):
        params = {"account_id": account_id,
                  "settings[ai_location_detect_setting]": ai_location_detect_value,
                  "settings[ai_location_detect_setting_distance]": ai_location_detect_setting_distance_value}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))

    @classmethod
    @step_decorator('API - Gb1Settings: toolbar set still with me setting')
    def set_toolbar_still_with_me(cls, account_id=chat_console_account_id, enable=0):
        params = {"account_id": account_id,
                  "tbs[toolbar_disable_still_with_me_message]": enable}
        cls.do_request(url=cls.get_route() + '?' + urllib.parse.urlencode(params))
