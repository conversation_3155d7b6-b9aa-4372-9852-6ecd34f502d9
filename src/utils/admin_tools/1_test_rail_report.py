from datetime import date, datetime

from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_types import Button, Input, TextBlock
from kom_framework.src.web.page_object import PageObject
from kom_framework.src.web.support.page_factory import find_by
from kom_framework.utils.testrail.testrail_helper import TestRailHelper

project_id = 2
test_rail_password = 'Q!123qwe'
test_rail_automation_field = 'custom_execution_type'
test_rail_automation_field_value = 2
test_rail_user_name = "<EMAIL>"
test_rail_url = "https://thirdshelf.testrail.com"
test_rail_api_key = 'Q!123qwe'
mode = "M" #"M" "D"

PROJECT_PRIORITY = {}
TOTAL = {}
PROJECT_DYNAMIC = {}
priority = {4: "C",
            3: "H",
            2: "M",
            1: "L"}

total = {}
total_priority = {"Total": {"C": 0, "H": 0, "M": 0, "L": 0},
                  "Automated": {"C": 0, "H": 0, "M": 0, "L": 0},
                  "Manual": {"C": 0, "H": 0, "M": 0, "L": 0},
                  }

test_rail = TestRailHelper(test_rail_url, test_rail_user_name, test_rail_api_key, project_id)


@find_by(Xpath("//div[@id='history']/../.."))
class TestRailHistoryPage(PageObject):

    def __init__(self, case_id):
        self.case_id = case_id
        self.txt_id = TextBlock(CssSelector(".content-header-id"))
        self.txt_date = TextBlock(Xpath(".//th[text()='Automation Type']/..//ins[contains(text(), ' Webdriver')]/preceding::h3[1]|.//th[text()='Execution type']/..//ins[contains(text(), 'Automated')]/preceding::h3[1]"))
        self.txt_created_date = TextBlock(Xpath(".//*[contains(text(), ' was created')]/preceding::h3[1]"))
        self.btn_show_all = Button(Xpath(".//*[contains(text(), 'Show All')]"))

    def open_actions(self):
        self.get(f"{test_rail_url}/index.php?/cases/history/{self.case_id}")
        if TestRailSignInPage().exists():
            TestRailSignInPage().sign_in()

    def setup_page(self):
        if self.txt_id.text != self.case_id:
            self.get(f"{test_rail_url}/index.php?/cases/history/{self.case_id}")

    def get_automated_date(self):
        if self.btn_show_all.exists():
            self.btn_show_all.click()
        if self.txt_date.exists():
            return self.txt_date.text
        else:
            print(f"Test case {self.case_id} automated_date is undefined")
            return self.txt_created_date.text


@find_by(CssSelector(".loginpage-form"))
class TestRailSignInPage(PageObject):

    def __init__(self):
        self.inp_user_name = Input(CssSelector("input[name='name']"))
        self.inp_password = Input(CssSelector("input[name='password']"))

        self.btn_sign_in = Button(CssSelector("#button_primary"))

    def open_actions(self):
        self.get(f"{test_rail_url}/index.php?/auth/login")

    def sign_in(self, user_name=test_rail_user_name, password=test_rail_password):
        self.inp_user_name.send_keys(user_name)
        self.inp_password.send_keys(password)
        self.btn_sign_in.click()


def get_automated_date(id, test_rail_page):
    print(f"GET AUTOMATED DATE of {id}")
    history_page = TestRailHistoryPage(id)
    history_page.set_session_key(test_rail_page.get_session_key())
    history_page.open()
    date = history_page.get_automated_date()
    return int(datetime.strptime(date, '%A, %B %d, %Y').timestamp())


def get_cases(suit_id, test_rail_page):
    cases = test_rail.service.get_cases(project_id, suit_id)
    for c in cases:
        if c[test_rail_automation_field] == test_rail_automation_field_value:
            c['automated_on'] = get_automated_date(c['id'], test_rail_page)
        else:
            c['automated_on'] = None
    return cases


def get_suit_priority(suite_id, project):
    PROJECT_PRIORITY[str(suite_id)] = {"Total": {"C": 0, "H": 0, "M": 0, "L": 0},
                                       "Automated": {"C": 0, "H": 0, "M": 0, "L": 0},
                                       "Manual": {"C": 0, "H": 0, "M": 0, "L": 0},
                                       }
    cases = project[suite_id]
    for c in cases:
        PROJECT_PRIORITY[str(suite_id)]["Total"][priority[c['priority_id']]] = PROJECT_PRIORITY[str(suite_id)]["Total"][priority[c['priority_id']]]+1
        if c[test_rail_automation_field] == test_rail_automation_field_value:
            PROJECT_PRIORITY[str(suite_id)]["Automated"][priority[c['priority_id']]] = PROJECT_PRIORITY[str(suite_id)]["Automated"][priority[c['priority_id']]]+1
        else:
            PROJECT_PRIORITY[str(suite_id)]["Manual"][priority[c['priority_id']]] = PROJECT_PRIORITY[str(suite_id)]["Manual"][priority[c['priority_id']]]+1


def print_priority():
    for s in PROJECT_PRIORITY.keys():
        print(s)
        for t in ["Total", "Automated", "Manual"]:
            for p in ["C", "H", "M", "L"]:
                total_priority[t][p] = total_priority[t][p]+PROJECT_PRIORITY[s][t][p]
        print(f"TOTAL:\t{PROJECT_PRIORITY[s]['Total']['C']}\t{PROJECT_PRIORITY[s]['Total']['H']}\t{PROJECT_PRIORITY[s]['Total']['M']}\t{PROJECT_PRIORITY[s]['Total']['L']}")
        print(f"Automated:\t{PROJECT_PRIORITY[s]['Automated']['C']}\t{PROJECT_PRIORITY[s]['Automated']['H']}\t{PROJECT_PRIORITY[s]['Automated']['M']}\t{PROJECT_PRIORITY[s]['Automated']['L']}")
        print(f"Manual:\t{PROJECT_PRIORITY[s]['Manual']['C']}\t{PROJECT_PRIORITY[s]['Manual']['H']}\t{PROJECT_PRIORITY[s]['Manual']['M']}\t{PROJECT_PRIORITY[s]['Manual']['L']}")

    print("\n!!!!Total priority")
    print(f"TOTAL:\t{total_priority['Total']['C']}\t{total_priority['Total']['H']}\t{total_priority['Total']['M']}\t{total_priority['Total']['L']}")
    print(f"Automated:\t{total_priority['Automated']['C']}\t{total_priority['Automated']['H']}\t{total_priority['Automated']['M']}\t{total_priority['Automated']['L']}")
    print(f"Manual:\t{total_priority['Manual']['C']}\t{total_priority['Manual']['H']}\t{total_priority['Manual']['M']}\t{total_priority['Manual']['L']}")


def get_suit_dynamic_daily(suite_id, project):
    PROJECT_DYNAMIC[suite_id] = {}
    cases = project[suite_id]
    dates = list(set([c["created_on"] for c in cases]+[c["automated_on"] for c in cases if c["automated_on"] is not None]))
    dates.sort()
    for i in range(len(dates)):
        d = dates[i]
        if not date.fromtimestamp(d) in PROJECT_DYNAMIC[suite_id].keys():
            if i == 0:
                PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)] = {"ALL": 0, "AUTO": 0, "CREATED": 0, "AUTOMATED": 0}
            else:
                PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)] = {"ALL": PROJECT_DYNAMIC[suite_id][date.fromtimestamp(dates[i - 1])]["ALL"], "AUTO": PROJECT_DYNAMIC[suite_id][date.fromtimestamp(dates[i - 1])]["AUTO"], "CREATED": 0, "AUTOMATED": 0}

        for c in cases:
            if c["created_on"] == d:
                PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)]["ALL"] = PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)]["ALL"] + 1
                PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)]["CREATED"] = PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)]["CREATED"] + 1

            if c[test_rail_automation_field] == test_rail_automation_field_value and c["automated_on"] == d:
                PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)]["AUTO"] = PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)]["AUTO"] + 1
                PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)]["AUTOMATED"] = PROJECT_DYNAMIC[suite_id][date.fromtimestamp(d)]["AUTOMATED"] + 1


def get_suit_dynamic_monthly(suite_id, project):
    cases = project[suite_id]
    PROJECT_DYNAMIC[suite_id] = {}
    dates = list(set([date.fromtimestamp(c["created_on"]).strftime("%Y-%m") for c in cases]+[date.fromtimestamp(c["automated_on"]).strftime("%Y-%m") for c in cases if c["automated_on"] is not None]))
    dates.sort()
    for i in range(len(dates)):
        d = dates[i]
        if d not in PROJECT_DYNAMIC[suite_id].keys():
            if i == 0:
                PROJECT_DYNAMIC[suite_id][d] = {"ALL": 0, "AUTO": 0, "CREATED": 0, "AUTOMATED": 0}
            else:
                PROJECT_DYNAMIC[suite_id][d] = {"ALL": PROJECT_DYNAMIC[suite_id][dates[i - 1]]["ALL"], "AUTO": PROJECT_DYNAMIC[suite_id][dates[i - 1]]["AUTO"], "CREATED": 0, "AUTOMATED": 0}

        for c in cases:
            if date.fromtimestamp(c["created_on"]).strftime("%Y-%m") == d:
                PROJECT_DYNAMIC[suite_id][d]["ALL"] = PROJECT_DYNAMIC[suite_id][d]["ALL"] + 1
                PROJECT_DYNAMIC[suite_id][d]["CREATED"] = PROJECT_DYNAMIC[suite_id][d]["CREATED"] + 1

            if c[test_rail_automation_field] == test_rail_automation_field_value and date.fromtimestamp(c["automated_on"]).strftime("%Y-%m") == d:
                PROJECT_DYNAMIC[suite_id][d]["AUTO"] = PROJECT_DYNAMIC[suite_id][d]["AUTO"] + 1
                PROJECT_DYNAMIC[suite_id][d]["AUTOMATED"] = PROJECT_DYNAMIC[suite_id][d]["AUTOMATED"] + 1


def get_total_dynamic_monthly():
    dates = list(set([d for s in PROJECT_DYNAMIC for d in PROJECT_DYNAMIC[s]]))
    dates.sort()

    print("\n!!!!Total dynamic")

    for i in range(len(dates)):
        d = dates[i]
        aut = 0
        cre = 0
        for s in PROJECT_DYNAMIC:
            if d in PROJECT_DYNAMIC[s]:
                cre+=PROJECT_DYNAMIC[s][d]["CREATED"]
                aut+=PROJECT_DYNAMIC[s][d]["AUTOMATED"]
        if i == 0:
            TOTAL[d] =  {"ALL": cre, "AUTO": aut, "CREATED": cre, "AUTOMATED": aut}
            print(f"{d}\t{TOTAL[d]['ALL']}\t{TOTAL[d]['AUTO']}\t{TOTAL[d]['CREATED']}\t{TOTAL[d]['AUTOMATED']}")
        else:
            TOTAL[d] = {"ALL": TOTAL[dates[i-1]]["ALL"]+cre, "AUTO": TOTAL[dates[i-1]]["AUTO"] + aut, "CREATED": cre, "AUTOMATED": aut}
            print(f"{d}\t{TOTAL[d]['ALL']}\t{TOTAL[d]['AUTO']}\t{TOTAL[d]['CREATED']}\t{TOTAL[d]['AUTOMATED']}")


def print_dynamic():
    for s in PROJECT_DYNAMIC.keys():
        print(s)

        for d in PROJECT_DYNAMIC[s].keys():
            print(f"{d}\t{PROJECT_DYNAMIC[s][d]['ALL']}\t{PROJECT_DYNAMIC[s][d]['AUTO']}\t{PROJECT_DYNAMIC[s][d]['CREATED']}\t{PROJECT_DYNAMIC[s][d]['AUTOMATED']}")


def get_test_rail_project(project_id, test_rail_page):
    suites = {s["id"]: None for s in test_rail.service.get_suites(project_id)}
    for s in suites:
        suites[s] = get_cases(str(s), test_rail_page)
    return suites


def main():
    test_rail_page = TestRailSignInPage().open()
    test_rail_page.sign_in()
    project = get_test_rail_project(project_id, test_rail_page)
    test_rail_page.quit()

    for suite_id in project:
        get_suit_priority(suite_id, project)
        if mode == "D":
            get_suit_dynamic_daily(suite_id, project)
        else:
            get_suit_dynamic_monthly(suite_id, project)

    get_total_dynamic_monthly()
    print_dynamic()
    print_priority()


if __name__ == '__main__':
    main()
