import json
import os

account = os.environ.get("ACCOUNT", None)

if os.environ.get("ENV_CONFIG_STRING"):
    env_file_content = json.loads(os.environ.get("ENV_CONFIG_STRING"))
else:
    config_file = 'src/resources/.qa.env.json'
    env_file_content = json.load(open(os.path.abspath(config_file)))

if os.environ.get("ACC_CONFIG_STRING"):
    acc_file_content = json.loads(os.environ.get("ACC_CONFIG_STRING"))
else:
    config_file = 'src/resources/.qa.acc.json'
    acc_file_content = json.load(open(os.path.abspath(config_file)))

if account and env_file_content:
    gb1_sign_in_url = env_file_content['gb1'][account]['url']
    gb1_url = env_file_content['gb1'][account]['url']
    gb1_account_id = env_file_content['gb1'][account]['account_id']
    gb1_user_name = env_file_content['gb1'][account]['user_name']
    gb1_password = env_file_content['gb1'][account]['password']

    floating_image = env_file_content['gb1'][account]['floating_image']
    right_image = env_file_content['gb1'][account]['right_image']
    left_image = env_file_content['gb1'][account]['left_image']
    notification_url = env_file_content['gb1'][account].get('notification_url')
    notification_access_token = env_file_content['gb1'][account].get("notification_access_token", None)
    whatsapp_phone_number = env_file_content["gb1"][account].get("whatsapp_phone_number", None)

    database_configuration = env_file_content["gb1"][account].get("database_configuration", None)

if account and acc_file_content:

    consumer_url = acc_file_content[account]["consumer"]["url"]
    consumer_src_url = acc_file_content[account]["consumer"]["src"]
    cbo_url = acc_file_content[account]["consumer"]["cbo_url"]
    if os.environ.get("CBO_PR_BUILD_URL"):
        cbo_url = f"{cbo_url}#{os.environ.get('CBO_PR_BUILD_URL')}"

    chat_console_sign_in_url = acc_file_content[account]['chat_console']['url']
    chat_console_url = acc_file_content[account]['chat_console']['url']

    if os.environ.get("CHAT_CONSOLE_BRANCH") == "chat_console/pr-build" and os.environ.get("CC_PR_BUILD_URL"):
        chat_console_url[os.environ.get("CHAT_CONSOLE_BRANCH")] = os.environ.get("CC_PR_BUILD_URL")

    chat_console_account_id = acc_file_content[account]['chat_console']['account_id']
    chat_console_manager_user_name = acc_file_content[account]['chat_console']['users']['manager']['user_name']
    chat_console_operator_user_name = acc_file_content[account]['chat_console']['users']['operator']['user_name']
    chat_console_admin_user_name = acc_file_content[account]['chat_console']['users']['admin']['user_name']
    chat_console_vr_specialist_user_name = acc_file_content[account]['chat_console']['users']['vr_specialist'][
        'user_name']
    chat_console_password = acc_file_content[account]['chat_console']['users']['password']
    chat_console_manager_id = acc_file_content[account]['chat_console']['users']['manager']['user_id']
    chat_console_operator_id = acc_file_content[account]['chat_console']['users']['operator']['user_id']
    chat_console_admin_id = acc_file_content[account]['chat_console']['users']['admin']['user_id']
    chat_console_vr_specialist_id = acc_file_content[account]['chat_console']['users']['vr_specialist']['user_id']

    gb1_token = acc_file_content[account]['chat_console']['token']
    offer_id = acc_file_content[account]['chat_console']['offer_id']
    xtime_id = acc_file_content[account]['chat_console']['xtime_id']
    da_trigger_id = acc_file_content[account]['chat_console'].get("da_trigger_id", None)
    db_trigger_id = acc_file_content[account]['chat_console'].get("db_trigger_id", None)
    bb_trigger_id = acc_file_content[account]['chat_console'].get("bb_trigger_id", None)
    po_trigger_id = acc_file_content[account]['chat_console'].get("po_trigger_id", None)
    dynamic_greeter_trigger_id = acc_file_content[account]['chat_console'].get("dynamic_greeter_trigger_id", None)
    chat_trigger_id = acc_file_content[account]['chat_console'].get("chat_trigger_id", None)
    e_price_id = acc_file_content[account]['chat_console'].get("e_price_id", None)
    chat_console_account_name = acc_file_content[account]['chat_console']['account_name']

    resq_sign_in_url = acc_file_content[account].get("resq").get("url", None) if acc_file_content[account].get(
        "resq") else None
    resq_api_url = acc_file_content[account].get("resq").get("api", None) if acc_file_content[account].get(
        "resq") else None

    resq_sales_user_name = acc_file_content[account]["resq"]["users"]["sales"]["user_name"] if acc_file_content[
        account].get("resq") else None
    resq_manager_user_name = acc_file_content[account]["resq"]["users"]["manager"]["user_name"] if acc_file_content[
        account].get("resq") else None
    resq_service_user_name = acc_file_content[account]["resq"]["users"]["service"]["user_name"] if acc_file_content[
        account].get("resq") else None
    resq_admin_user_name = acc_file_content[account]["resq"]["users"]["admin"]["user_name"] if acc_file_content[
        account].get("resq") else None
    resq_non_gubagoo_user_name = acc_file_content[account]["resq"]["users"]["non_gubagoo"]["user_name"] if \
        acc_file_content[
        account].get("resq", None).get("users", None).get("non_gubagoo") else None

    resq_sales_uuid = acc_file_content[account]["resq"]["users"]["sales"]["uuid"] if acc_file_content[account].get(
        "resq") else None
    resq_manager_uuid = acc_file_content[account]["resq"]["users"]["manager"]["uuid"] if acc_file_content[account].get(
        "resq") else None
    resq_service_uuid = acc_file_content[account]["resq"]["users"]["service"]["uuid"] if acc_file_content[account].get(
        "resq") else None
    resq_admin_uuid = acc_file_content[account]["resq"]["users"]["admin"]["uuid"] if acc_file_content[account].get(
        "resq") else None
    resq_non_gubagoo_uuid = acc_file_content[account]["resq"]["users"]["non_gubagoo"]["uuid"] if acc_file_content[
        account].get("resq", None).get("users", None).get("non_gubagoo") else None

    resq_sales_id = acc_file_content[account]["resq"]["users"]["sales"]["id"] if acc_file_content[account].get(
        "resq") else None
    resq_manager_id = acc_file_content[account]["resq"]["users"]["manager"]["id"] if acc_file_content[account].get(
        "resq") else None
    resq_service_id = acc_file_content[account]["resq"]["users"]["service"]["id"] if acc_file_content[account].get(
        "resq") else None
    resq_admin_id = acc_file_content[account]["resq"]["users"]["admin"]["id"] if acc_file_content[account].get(
        "resq") else None
    resq_non_gubagoo_id = acc_file_content[account]["resq"]["users"]["non_gubagoo"]["id"] if acc_file_content[
        account].get("resq", None).get("users", None).get("non_gubagoo") else None

    resq_report_sales_user_name = acc_file_content["report"]["resq"]["users"]["sales"][
        "user_name"] if acc_file_content.get("report", None) else None
    resq_report_manager_user_name = acc_file_content["report"]["resq"]["users"]["manager"][
        "user_name"] if acc_file_content.get("report", None) else None
    resq_report_inactive_user_name = acc_file_content["report"]["resq"]["users"]["service"][
        "user_name"] if acc_file_content.get("report", None) else None
    resq_report_admin_user_name = acc_file_content["report"]["resq"]["users"]["admin"][
        "user_name"] if acc_file_content.get("report", None) else None

    resq_report_non_gubagoo_user_name = "<EMAIL>" #need to add to config file

    resq_report_password = acc_file_content["report"]["resq"]["password"] if acc_file_content.get("report",
                                                                                                  None) else None
    resq_report_account = acc_file_content["report"]["resq"]["dealer_account_name"] if acc_file_content.get("report",
                                                                                                            None) else None
    resq_report_sub_account = acc_file_content["report"]["resq"]["sub_dealer"][
        "dealer_account_name"] if acc_file_content.get("report", None) else None
    resq_report_account_id = int(acc_file_content["report"]["resq"]["chat_console_account_id"]) if acc_file_content.get(
        "report", None) else None
    resq_report_sub_account_id = int(
        acc_file_content["report"]["resq"]["sub_dealer"]["chat_console_account_id"]) if acc_file_content.get("report",
                                                                                                             None) else None

    resq_password = acc_file_content[account]["resq"]["password"] if acc_file_content[account].get("resq") else None
    resq_url = acc_file_content[account]['resq']['url'] if acc_file_content[account].get("resq") else None

    if os.environ.get("GLIVE_BRANCH") == "resq-desktop/pr-build" and os.environ.get("GL_PR_BUILD_URL"):
        resq_url[os.environ.get("GLIVE_BRANCH")] = os.environ.get("GL_PR_BUILD_URL")

    resq_dealer_name = acc_file_content[account]['resq']['dealer_name'] if acc_file_content[account].get(
        "resq") else None
    resq_dealer_account_name = acc_file_content[account]['resq']['dealer_account_name'] if acc_file_content[
        account].get("resq") else None
    resq_account_id = acc_file_content[account]['resq']['resq_account_id'] if acc_file_content[account].get(
        "resq") else None

    consumer_url_dealer_a = acc_file_content[account]["resq"]["sub_dealer"]["consumer_url"]
    resq_account_id_dealer_a = acc_file_content[account]["resq"]["sub_dealer"]["resq_account_id"]
    chat_console_account_id_dealer_a = acc_file_content[account]["resq"]["sub_dealer"]["chat_console_account_id"]
