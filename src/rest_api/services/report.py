from kom_framework.src.general import Log
from src import resq_report_manager_user_name, resq_report_password
from src.rest_api.entities.report.report_factory import ReportFactory
from src.rest_api.services import Service
from src.utils.decorators import step_decorator


class Report(Service):

    @classmethod
    def get_route(cls) -> str:
        return "https://reporting-api-test.gubagoo.io/report-builder"

    @classmethod
    def auth_header(cls):
        url = f'https://users-api.gubagoo.dev/sessions'
        response = cls.send_post_request(url, json={"username": resq_report_manager_user_name,
                                                   "password": resq_report_password})
        assert response.status_code == 201
        json_response = response.json()
        if json_response.get("access_token"):
            return {"Authorization": f"Bearer {json_response['access_token']}"}
        elif json_response.get("token"):
            return {"Authorization": f"RESQ-TOKEN {json_response['token']}"}

    @classmethod
    @step_decorator('API - Report: Delete schedule')
    def delete_schedule(cls, id):
        url = cls.get_route() + f'/schedule/{id}'
        Log.info(f"API - Report: Delete schedule: '{url}'")
        response = cls.send_delete_request(url, headers=cls.auth_header())
        return response.status_code == 204

    @classmethod
    @step_decorator('API - Report: Get schedule')
    def get_schedule(cls, id):
        url = cls.get_route() + f'/schedule/{id}'
        Log.info(f"API - Report: Get schedule: '{url}'")
        response = cls.send_get_request(url, headers=cls.auth_header())
        if response.status_code == 200:
            json_response = response.json()
            return ReportFactory.deserialize(json_response)
        return None

    @classmethod
    @step_decorator('API - Report: Get recurring reports')
    def get_schedules(cls):
        url = cls.get_route() + f'/schedule'
        Log.info(f"API - Report: Get recurring reports: '{url}'")
        response = cls.send_get_request(url, headers=cls.auth_header())
        if response.status_code == 200:
            json_response = response.json()
            return [ReportFactory.deserialize(report) for report in json_response]
        return None

    @classmethod
    @step_decorator('API - Report: Create schedule')
    def create_schedule(cls, data):
        url = cls.get_route() + f'/schedule'
        response = cls.send_post_request(url, json=data.__dict__, headers=cls.auth_header())
        if response.status_code == 201:
            return response.json()["id"]
