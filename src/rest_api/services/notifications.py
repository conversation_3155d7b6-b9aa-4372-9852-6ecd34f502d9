from kom_framework.src.general import Log
from src import notification_url, notification_access_token
from src.rest_api.entities.notification.notification_factory import NotificationFactory

from src.rest_api.services import Service
from src.utils.decorators import step_decorator


class Notification(Service):

    @classmethod
    def get_route(cls) -> str:
        return notification_url + "/notification"

    @classmethod
    def auth_header(cls):
        if notification_access_token:
            return {notification_access_token: "1"}

    @classmethod
    @step_decorator('API - Notification: Get Notifications')
    def get_notifications(cls, uu_id, days=1):
        url = cls.get_route() + f'?recipient={uu_id}&days={days}&sort=DESC'
        Log.info(f"API - Notification: Get Notifications from URL: '{url}'")
        response = cls.send_get_request(url, headers=cls.auth_header())
        if response.status_code == 200:
            json_response = response.json()
            result = [NotificationFactory.deserialize(notification) for notification in json_response['notifications']]
            Log.info(f"API - Notification: {result}")
            return result
        return None
