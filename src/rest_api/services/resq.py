from kom_framework.src.general import Log
from src import resq_api_url, resq_manager_user_name, resq_password
from src.rest_api.entities.resq.user_factory import UserFactory
from src.rest_api.services import Service
from src.utils.decorators import step_decorator


class Resq(Service):

    @classmethod
    def get_route(cls) -> str:
        return resq_api_url

    @classmethod
    def auth_header(cls):
        if resq_manager_user_name and resq_password:
            return resq_manager_user_name, resq_password

    @classmethod
    @step_decorator('API - Resq: Get User')
    def get_user_info(cls, uuid):
        url = cls.get_route() + f'/users/{uuid}.json'
        Log.info(f"API - Resq: Get User: '{url}'")
        response = cls.send_get_request(url, auth=cls.auth_header())
        if response.status_code == 200:
            json_response = response.json()
            return UserFactory.deserialize(json_response)
        return None
