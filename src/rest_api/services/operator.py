from typing import Optional

from kom_framework.src.general import Log
from src.rest_api.entities.operator.operator_entity import OperatorEntity
from src.rest_api.entities.operator.operator_factory import OperatorFactory
from src.rest_api.services import Service
from src.utils.admin_tools import gb1_access_token, gb1_access_user
from src.utils.decorators import step_decorator


class Operator(Service):

    @classmethod
    def auth_header(cls, account_id, user_name, password, mfa_type):
        url = f'https://gb1-rc.gubagoo.com/rest_api/operators/login'
        response = cls.send_post_request(url, json={"account_id": account_id,
                                                    "password": password,
                                                    "username": user_name,
                                                    "mfa_type": mfa_type})
        assert response.status_code == 200
        json_response = response.json()
        return {"api-token": json_response.get("token")}

    @classmethod
    def get_url(cls):
        return "https://operators-api.gubagoo.dev"

    @classmethod
    def get_route(cls) -> str:
        return cls.get_url() + '/v1/users/'

    @classmethod
    @step_decorator('API - Operator: Get {1} Operator Info')
    def get_operator_info(cls, operator_id, account_id, user_name, password) -> Optional[OperatorEntity]:
        url = cls.get_route() + f'{operator_id}'
        Log.info(f"API - Operator: Get Operator Info from URL: '{url}'")
        response = cls.send_get_request(url, headers=cls.auth_header(account_id, user_name, password, "mail"))
        if response.status_code == 200:
            json_response = response.json()
            return OperatorFactory.deserialize(json_response)
        return None

    @classmethod
    @step_decorator('API - Operator: Get {1} Operator Info')
    def restore_quick_responses(cls, account_id, user_name, password):
        url = cls.get_url() + f'/v1/quick-responses/{account_id}/restore-default'
        Log.info(f"API - Operator: Get Operator Info from URL: '{url}'")
        response = cls.send_post_request(url, headers=cls.auth_header(account_id, user_name, password, "mail"))
        if response.status_code == 200:
            json_response = response.json()
        return None
