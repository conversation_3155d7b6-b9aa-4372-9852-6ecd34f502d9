import os
import socket
from abc import abstractmethod
from urllib.parse import urlparse

from kom_framework.src.utils.http import HTTP


class Service:
    __backend_link = None

    @classmethod
    @abstractmethod
    def get_route(cls, *args):
        pass

    @classmethod
    def send_get_request(cls, url: str, headers: dict = None, auth=None):
        return HTTP.send_get_request(url, headers=headers, auth=auth)

    @classmethod
    def send_get_request_no_log(cls, url: str, headers: dict = None, auth=None, timeout=60, params=None):
        return HTTP.send_get_request_no_log(url, headers=headers, auth=auth, timeout=timeout, params=params)

    @classmethod
    def send_delete_request(cls, url: str, headers: dict = None, auth=None):
        return HTTP.send_delete_request(url, headers=headers, auth=auth)

    @classmethod
    def send_put_request(cls, url: str, data: dict = (), json: dict = (), headers: dict = None, auth=None):
        return HTTP.send_put_request(url, data=data, json=json, headers=headers, auth=auth)

    @classmethod
    def send_post_request(cls, url, data: dict = (), json: dict = (), headers: dict = (), auth=None, **kwargs):
        return HTTP.send_post_request(url, data=data, json=json, headers=headers, auth=auth, **kwargs)

    @classmethod
    def get_url(cls):
        from src import gb1_url
        if not cls.__backend_link:
            cls.__backend_link = os.getenv('backend_link', gb1_url)
        return cls.__backend_link

    @classmethod
    def get_src_url(cls):
        from src import consumer_src_url
        return os.getenv('backend_link', consumer_src_url)

    @classmethod
    def get_host_name(cls):
        return urlparse(cls.get_url()).hostname

    @classmethod
    def is_local_env(cls):
        return cls.get_host_name() == socket.gethostbyname(cls.get_host_name())
