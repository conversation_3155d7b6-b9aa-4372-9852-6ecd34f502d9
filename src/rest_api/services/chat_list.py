from typing import Optional

from kom_framework.src.general import Log
from src import chat_console_account_id, gb1_token
from src.rest_api.entities.chats.chat_entity import ChatEntity
from src.rest_api.entities.chats.chat_factory import ChatFactory
from src.rest_api.services import Service
from src.utils.decorators import step_decorator


class ChatList(Service):

    @classmethod
    def get_route(cls) -> str:
        return cls.get_url() + '/api/chat_list'

    @classmethod
    @step_decorator('API - ChatList: Get Chats')
    def get_chats(cls, account_id=chat_console_account_id, token=gb1_token, from_date=None, to_date=None) \
            -> Optional[ChatEntity]:
        date = f'"from": "{from_date}", "to": "{to_date}", ' if from_date and to_date else ""
        url = cls.get_route() + f'?gg_request={{"authentication":{{"account_id": {account_id}, "token":"{token}"}}, "request": {{{date}"transcript":1}}}}'
        Log.info(f"API - ChatList: Get Chats from URL: '{url}'")
        response = cls.send_get_request(url)
        if response.status_code == 200:
            json_response = response.json()
            return [ChatFactory.deserialize(chat) for chat in json_response['data']]
        return None

    @classmethod
    @step_decorator('API - ChatList: Get Chat transcript')
    def get_chat_transcript(cls, id):
        chats = cls.get_chats(account_id=chat_console_account_id)
        for chat in chats:
            if chat.id == int(id):
                return chat.transcript
