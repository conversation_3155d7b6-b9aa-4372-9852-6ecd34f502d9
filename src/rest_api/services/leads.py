from datetime import timedelta, date, datetime
from typing import Optional
import time

import allure
from allure import step
from allure_commons.types import LinkType
from time import sleep

from kom_framework.src.general import Log
from src import chat_console_account_id, gb1_token
from src.rest_api.entities.leads.lead_entity import LeadEntity
from src.rest_api.entities.leads.lead_factory import LeadFactory
from src.rest_api.services import Service
from src.utils.decorators import step_decorator
from src.web.consumer import ServiceProviderType
from src.web.entities.lead_entity import WebLeadEntity


class Leads(Service):

    @classmethod
    def get_route(cls) -> str:
        return cls.get_url() + '/api/lead_list'

    @classmethod
    @step_decorator('API - Leads: Get Leads')
    def get_leads(cls, account_id=chat_console_account_id, token=gb1_token, from_date=None, to_date=None) \
            -> Optional[LeadEntity]:
        if from_date and to_date:
            url = cls.get_route() + f'?gg_request={{"authentication":{{"account_id": {account_id}, "token":"{token}"}}, "request": {{"from": "{from_date}", "to": "{to_date}", "extended":1}}}}'
        else:
            url = cls.get_route() + f'?gg_request={{"authentication":{{"account_id": {account_id}, "token":"{token}"}}, "request": {{"extended":1}}}}'
        Log.info(f"API - Leads: Get Leads from URL: '{url}'")
        response = cls.send_get_request_no_log(url)
        if response.status_code == 200:
            json_response = response.json()
            return [LeadFactory.deserialize(lead) for lead in json_response['data'][:10]]
        return None

    @classmethod
    @step_decorator('API - Leads: Get Last Day Leads')
    def get_last_day_leads(cls):
        yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
        today = date.today().strftime("%Y-%m-%d")
        sleep(3)  # need to wait while lead is settled
        return cls.get_leads(from_date=yesterday, to_date=today)

    @classmethod
    @step_decorator('API - Leads: Find XTime lead')
    def find_service_appointment_lead(cls, lead_info: WebLeadEntity,
                                      service_appointment_type=ServiceProviderType.XTIME):
        from src.web.entities.lead_entity import WebLeadFactory
        leads = cls.get_last_day_leads()
        lead = leads[leads.index(lead_info)]
        with step(f'API - Leads: ID {lead.id} lead found'):
            Log.info(f'API - Leads: ID {lead.id} lead found')
            allure.dynamic.link(f"https://desktop-rc.resq.rocks/leads/{chat_console_account_id}/{lead.id}",
                                link_type=LinkType.LINK, name=f"Lead Id - {lead.id}")
            if service_appointment_type == ServiceProviderType.XTIME:
                lead = WebLeadFactory.deserialize_to_xtime(lead)
            elif service_appointment_type == ServiceProviderType.GM:
                lead = WebLeadFactory.deserialize_to_gm(lead)
            elif service_appointment_type == ServiceProviderType.SERVICEPORTAL:
                lead = WebLeadFactory.deserialize_to_service_portal(lead)
        assert lead_info == lead, f"Leads are not matched. Difference: {lead_info - lead} does not mach {lead - lead_info}"
        return lead

    @classmethod
    @step_decorator('API - Leads: Find web lead')
    def find_web_lead(cls, lead_info: WebLeadEntity):
        from src.web.entities.lead_entity import WebLeadFactory
        leads = cls.get_last_day_leads()
        lead = leads[leads.index(lead_info)]
        with step(f'API - Leads: ID {lead.id} lead found'):
            Log.info(f'API - Leads: ID {lead.id} lead found')
            allure.dynamic.link(f"https://desktop-rc.resq.rocks/leads/{chat_console_account_id}/{lead.id}",
                                link_type=LinkType.LINK, name=f"Lead Id - {lead.id}")
            lead = WebLeadFactory.deserialize(lead)
        assert lead_info == lead, f"Leads are not matched. Difference: expected {lead_info - lead} does not mach {lead - lead_info}"

    @classmethod
    @step_decorator('API - Leads: Verify lead was not found')
    def verify_lead_not_found(cls, lead_info: WebLeadEntity):
        leads = cls.get_last_day_leads()
        try:
            lead_index = leads.index(lead_info)
        except ValueError as e:
            lead_index = -1

        assert lead_index < 0, f"Lead {leads[lead_index].id} was not expected to be found"

    @classmethod
    @step_decorator('API - Leads: Get Lead ID')
    def get_lead_id(cls, lead_info: WebLeadEntity):
        Log.info("API - Leads: Get Lead ID")
        leads = cls.get_last_day_leads()
        lead = leads[leads.index(lead_info)]
        with step(f'API - Leads: ID {lead.id} lead found'):
            Log.info(f'API - Leads: ID {lead.id} lead found')
            return lead.id
