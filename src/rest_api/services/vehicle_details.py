from functools import cache
from typing import Optional

from kom_framework.src.general import Log
from src import chat_console_account_id, gb1_token
from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.entities.vehicle.vehicle_factory import VehicleFactory
from src.rest_api.services import Service


class VehicleDetails(Service):

    @classmethod
    def get_route(cls) -> str:
        return cls.get_url() + '/api/chat_vehicle_details'

    @classmethod
    @cache
    def get_vehicle_details(cls, account_id=chat_console_account_id, token=gb1_token, vin=None) \
            -> Optional[VehicleEntity]:
        url = cls.get_route() + f'?gg_request={{"authentication":{{"account_id":{account_id}, "token":"{token}"}},"request":{{"vin":"{vin}","json":true,"vehicle_only":true}}}}'
        Log.info(f"API - VehicleDetails: Get Vehicle Details from URL: '{url}'")
        response = cls.send_get_request(url)
        if response.status_code == 200:
            try:
                json_response = response.json()
            except:
                Log.error(url)
                Log.error(response)
            if (len(json_response['data']) > 2):
                vehicle = VehicleFactory.deserialize(json_response['data'])
                Log.info(f"VehicleDetails: {vehicle}")
                return VehicleFactory.deserialize(json_response['data'])
        Log.info(f"vehicle was not found")
        return None
