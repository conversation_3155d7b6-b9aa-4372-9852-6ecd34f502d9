from src.rest_api.entities.notification.notification_entity import PayloadEntity, DataEntity, NotificationEntity


class NotificationFactory:

    @classmethod
    def create_lead(cls, id=None, sent_at=None, account_id=None, viewed_at=None, status=None, recipient=None,
                    data: DataEntity = None) -> NotificationEntity:
        return NotificationEntity(id=id, sent_at=sent_at, account_id=account_id, viewed_at=viewed_at, status=status,
                                  recipient=recipient, data=data)

    @classmethod
    def deserialize(cls, json_obj: dict) -> NotificationEntity:
        return NotificationEntity(id=json_obj.get('id'),
                                  sent_at=json_obj.get('sent_at'),
                                  account_id=json_obj.get('account_id'),
                                  viewed_at=json_obj.get('viewed_at'),
                                  status=json_obj.get('status'),
                                  recipient=json_obj.get('recipient'),
                                  data=DataFactory.deserialize(json_obj.get('data')))


class PayloadFactory:
    @classmethod
    def create_meta(cls, url=None, body=None, entity=None, subject=None, category=None,
                    language=None, external_id=None):
        return PayloadEntity(url=url, body=body, entity=entity, subject=subject, category=category,
                             language=language, external_id=external_id)

    @classmethod
    def deserialize(cls, json_obj: dict) -> PayloadEntity:
        return PayloadEntity(url=json_obj.get('url', None),
                             body=json_obj.get('body', None),
                             entity=json_obj.get('entity', None),
                             subject=json_obj.get('subject', None),
                             category=json_obj.get('category', None),
                             language=json_obj.get('language', None),
                             external_id=json_obj.get('external_id', None))


class DataFactory:
    @classmethod
    def create_meta(cls, id=None, payload: PayloadEntity = None, medium=None, entity_ref=None, category_id=None,
                    entity_type=None, category=None, super_category=None):
        return DataEntity(id=id, payload=payload, medium=medium, entity_ref=entity_ref, category_id=category_id,
                          entity_type=entity_type, category=category, super_category=super_category)

    @classmethod
    def deserialize(cls, json_obj: dict) -> PayloadEntity:
        return DataEntity(id=json_obj.get('id', None),
                          payload=PayloadFactory.deserialize(json_obj.get('payload')),
                          medium=json_obj.get('medium', None),
                          entity_ref=json_obj.get('entity_ref', None),
                          category_id=json_obj.get('category_id', None),
                          entity_type=json_obj.get('entity_type', None),
                          category=json_obj.get('category', None),
                          super_category=json_obj.get('super_category', None))
