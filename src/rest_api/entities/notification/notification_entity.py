class PayloadEntity:
    def __init__(self, url=None, body=None, entity=None, subject=None, category=None,
                 language=None, external_id=None):
        self.url = url
        self.body = body
        self.entity = entity
        self.subject = subject
        self.category = category
        self.language = language
        self.external_id = external_id

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)


class DataEntity:
    def __init__(self, id=None, payload: PayloadEntity = None, medium=None, entity_ref=None, category_id=None,
                 entity_type=None, category=None, super_category=None):
        self.id = id
        self.payload = payload
        self.medium = medium
        self.entity_ref = entity_ref
        self.category_id = category_id
        self.entity_type = entity_type
        self.category = category
        self.super_category = super_category

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)


class NotificationEntity:
    def __init__(self, id=None, sent_at=None, account_id=None, viewed_at=None, status=None, recipient=None,
                 data: DataEntity = None):
        self.id = id
        self.sent_at = sent_at
        self.account_id = account_id
        self.viewed_at = viewed_at
        self.status = status
        self.recipient = recipient
        self.data = data

    def __eq__(self, other):
        """Overrides the default implementation"""
        if isinstance(other, str):
            return self.data.payload.external_id == int(other)
        return False

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str({"notification_id": self.data.id, "chat_id": self.data.payload.external_id})
