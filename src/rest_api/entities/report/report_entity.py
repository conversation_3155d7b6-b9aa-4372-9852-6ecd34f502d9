from dataclasses import dataclass


@dataclass
class DeliveryEntity:
    status: str
    sent_at: str


class ReportEntity:
    def __init__(self,
                 id=None,
                 frequency_period=None,
                 period_delivery_offset=None,
                 account_ids=None,
                 channel=None,
                 products=None,
                 channel_config=None,
                 is_active=None,
                 created_by=None,
                 last_delivery: DeliveryEntity = None):
        self.id = id
        self.frequency_period = frequency_period
        self.period_delivery_offset = period_delivery_offset
        self.account_ids = account_ids
        self.channel = channel
        self.products = products
        self.channel_config = channel_config
        self.is_active = is_active
        self.created_by = created_by
        self.last_delivery = last_delivery

    def __str__(self):
        return str(self.__dict__)
