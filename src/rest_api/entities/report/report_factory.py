from src.rest_api.entities.report.report_entity import ReportEntity, DeliveryEntity


class DeliveryFactory:
    @classmethod
    def deserialize(cls, json_obj: dict) -> ReportEntity:
        return DeliveryEntity(
            status=json_obj.get('status'),
            sent_at=json_obj.get('sent_at'))


class ReportFactory:

    @classmethod
    def create_report(cls, id=None,
                      frequency_period=None,
                      period_delivery_offset=None,
                      account_ids=None,
                      channel=None,
                      products=None,
                      channel_config=None,
                      is_active=None,
                      created_by=None,
                      last_delivery=None) -> ReportEntity:
        return ReportEntity(id=id,
                            frequency_period=frequency_period,
                            period_delivery_offset=period_delivery_offset,
                            account_ids=account_ids,
                            channel=channel,
                            products=products,
                            channel_config=channel_config,
                            is_active=is_active,
                            created_by=created_by,
                            last_delivery=last_delivery)

    @classmethod
    def deserialize(cls, json_obj: dict) -> ReportEntity:
        return ReportEntity(
            id=json_obj.get('id'),
            frequency_period=json_obj.get('frequency_period'),
            period_delivery_offset=json_obj.get('period_delivery_offset'),
            account_ids=json_obj.get('account_ids'),
            channel=json_obj.get('channel'),
            products=json_obj.get('products'),
            channel_config=json_obj.get('channel_config'),
            is_active=json_obj.get('is_active'),
            created_by=json_obj.get('created_by'),
            last_delivery=DeliveryFactory.deserialize(json_obj.get('last_delivery')))
