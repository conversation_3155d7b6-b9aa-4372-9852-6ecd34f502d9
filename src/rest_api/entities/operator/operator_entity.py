class OperatorEntity:
    def __init__(self,
                 account_id=None,
                 auto_accept=None,
                 break_id=None,
                 display_name=None,
                 first_name=None,
                 group=None,
                 id=None,
                 last_name=None,
                 location_id=None,
                 new_ui_photo_url=None,
                 role_id=None,
                 status=None,
                 username=None,
                 ):
        self.id = id
        self.account_id = account_id
        self.break_id = break_id
        self.location_id = location_id
        self.username = username
        self.display_name = display_name
        self.first_name = first_name
        self.last_name = last_name
        self.status = status
        self.group = group
        self.auto_accept = auto_accept
        self.new_ui_photo_url = new_ui_photo_url
        self.role_id = role_id

    def __str__(self):
        return str(self.__dict__)
