from src.rest_api.entities.operator.operator_entity import OperatorEntity


class OperatorFactory:

    @classmethod
    def create_operator(cls, account_id=None,
                        auto_accept=None,
                        break_id=None,
                        display_name=None,
                        first_name=None,
                        group=None,
                        id=None,
                        last_name=None,
                        location_id=None,
                        new_ui_photo_url=None,
                        role_id=None,
                        status=None,
                        username=None) -> OperatorEntity:
        return OperatorEntity(id=id,
                              account_id=account_id,
                              break_id=break_id,
                              location_id=location_id,
                              username=username,
                              display_name=display_name,
                              first_name=first_name,
                              last_name=last_name,
                              status=status,
                              group=group,
                              auto_accept=auto_accept,
                              new_ui_photo_url=new_ui_photo_url,
                              role_id=role_id)

    @classmethod
    def deserialize(cls, json_obj: dict) -> OperatorEntity:
        return OperatorEntity(
            account_id=json_obj.get('account_id'),
            auto_accept=json_obj.get('auto_accept'),
            break_id=json_obj.get('break_id'),
            display_name=json_obj.get('display_name'),
            first_name=json_obj.get('first_name'),
            group=json_obj.get('group'),
            id=json_obj.get('id'),
            last_name=json_obj.get('last_name'),
            location_id=json_obj.get('location_id'),
            new_ui_photo_url=json_obj.get('new_ui_photo_url'),
            role_id=json_obj.get('role_id'),
            status=json_obj.get('status'),
            username=json_obj.get('username'))
