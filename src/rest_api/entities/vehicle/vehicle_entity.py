from typing import List


class Info:
    def __init__(self,
                 engine: str = None,
                 transmission: str = None,
                 body: str = None,
                 mileage: str = None,
                 mpg: str = None,
                 drivetrain: str = None):
        self.engine = engine
        self.transmission = transmission
        self.body = body
        self.mileage = mileage
        self.mpg = mpg
        self.drivetrain = drivetrain

    def __eq__(self, other):
        """Overrides the default implementation"""
        return self.__dict__ == other.__dict__

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)


class VehicleEntity:
    def __init__(self,
                 type: str = None,
                 make: str = None,
                 model: str = None,
                 year: str = None,
                 trim: str = None,
                 name: str = None,
                 stock: str = None,
                 price: int = None,
                 price_formatted: str = None,
                 msrp_price: str = None,
                 dealer_final_price: int = None,
                 currency: str = None,
                 fuel: str = None,
                 vin: str = None,
                 tag: str = None,
                 highlights: List[str] = None,
                 number_of_pictures: int = None,
                 info: Info = None,
                 interior_color: str = None,
                 exterior_hex: str = None,
                 interior_hex: str = None,
                 exterior_color: str = None,
                 features: str = None,
                 description: str = None,
                 doors: str = None
                 ):
        self.type = type
        self.make = make
        self.model = model
        self.year = year
        self.trim = trim
        self.fuel = fuel
        self.name = name
        self.stock = stock
        self.price = price
        self.price_formatted = price_formatted
        self.msrp_price = msrp_price
        self.dealer_final_price = dealer_final_price
        self.currency = currency
        self.vin = vin
        self.highlights = highlights
        self.tag = tag
        self.number_of_pictures = number_of_pictures
        self.info = info
        self.interior_color = interior_color
        self.exterior_color = exterior_color
        self.exterior_hex = exterior_hex
        self.interior_hex = interior_hex
        self.features = features
        self.description = description
        self.doors = doors

    def __str__(self):
        return " ".join([i for i in [self.name,
                                     self.stock,
                                     self.vin] if i])

    def __repr__(self):
        return " ".join([i for i in [self.name,
                                     self.stock,
                                     self.vin] if i])
