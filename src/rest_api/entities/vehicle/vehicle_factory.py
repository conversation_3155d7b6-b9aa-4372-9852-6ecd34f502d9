from typing import List

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity, Info


class VehicleFactory:

    @classmethod
    def create_vehicle(cls, type: str = None,
                       make: str = None,
                       model: str = None,
                       year: str = None,
                       trim: str = None,
                       name: str = None,
                       stock: str = None,
                       price: int = None,
                       price_formatted: str = None,
                       msrp_price: str = None,
                       dealer_final_price: int = None,
                       currency: str = None,
                       fuel: str = None,
                       vin: str = None,
                       tag: str = None,
                       highlights: List[str] = None,
                       number_of_pictures: int = None,
                       info: Info = None,
                       interior_color: str = None,
                       exterior_color: str = None,
                       exterior_hex: str = None,
                       interior_hex: str = None,
                       features: str = None,
                       description: str = None,
                       doors: str = None) -> VehicleEntity:
        return VehicleEntity(type=type,
                             make=make,
                             model=model,
                             year=year,
                             trim=trim,
                             name=name,
                             stock=stock,
                             price=price,
                             price_formatted=price_formatted,
                             msrp_price=msrp_price,
                             dealer_final_price=dealer_final_price,
                             currency=currency,
                             fuel=fuel,
                             vin=vin,
                             tag=tag,
                             highlights=highlights,
                             number_of_pictures=number_of_pictures,
                             info=info,
                             interior_color=interior_color,
                             exterior_color=exterior_color,
                             exterior_hex=exterior_hex,
                             interior_hex=interior_hex,
                             features=features,
                             description=description,
                             doors=doors)

    @classmethod
    def deserialize(cls, json_obj: dict) -> VehicleEntity:
        return VehicleEntity(type=json_obj.get('type'),
                             make=json_obj.get('make'),
                             model=json_obj.get('model'),
                             year=json_obj.get('year'),
                             trim=json_obj.get('trim'),
                             name=" ".join([json_obj.get('year'), json_obj.get('make'), json_obj.get('model')]),
                             stock=json_obj.get('stock_number'),
                             price=json_obj.get('price'),
                             price_formatted=json_obj.get('price_formatted'),
                             msrp_price=json_obj.get('msrp'),
                             dealer_final_price=json_obj.get('dealer_final_price'),
                             currency=json_obj.get('currency'),
                             fuel=json_obj.get('meta').get('fuel', None) if json_obj.get('meta') else None,
                             vin=json_obj.get('vin'),
                             tag=None,
                             highlights=json_obj.get('highlights').values(),
                             number_of_pictures=len(json_obj.get('images')),
                             info=Info(engine=json_obj.get('engine'),
                                       transmission=json_obj.get('transmission'),
                                       body=json_obj.get('body'),
                                       mileage=json_obj.get('mileage'),
                                       mpg=json_obj.get('mpg'),
                                       drivetrain=json_obj.get('drivetrain')),
                             interior_color=json_obj.get('interior_color'),
                             exterior_color=json_obj.get('exterior_color'),
                             exterior_hex=json_obj.get('exterior_hex'),
                             interior_hex=json_obj.get('interior_hex'),
                             features=json_obj.get('features'),
                             description=json_obj.get('description'),
                             doors=json_obj.get('doors')
                             )
