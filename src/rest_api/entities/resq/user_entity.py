class UserEntity:
    def __init__(self, uuid=None, username=None, email=None, fullname=None, firstname=None, lastname=None, title=None,
                 status=None, phone_number=None, picture=None, call_notifications=None, chat_notifications=None,
                 sound_notifications=None, reply_sounds=None, show_unassigned=None):
        self.uuid = uuid
        self.username = username
        self.email = email
        self.fullname = fullname
        self.firstname = firstname
        self.lastname = lastname
        self.title = title
        self.status = status
        self.phone_number = phone_number
        self.picture = picture
        self.call_notifications = call_notifications
        self.chat_notifications = chat_notifications
        self.sound_notifications = sound_notifications
        self.reply_sounds = reply_sounds
        self.show_unassigned = show_unassigned

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)
