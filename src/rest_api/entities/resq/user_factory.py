from src.rest_api.entities.resq.user_entity import UserEntity


class UserFactory:

    @classmethod
    def create_user(cls, uuid=None, username=None, email=None, fullname=None, firstname=None, lastname=None, title=None,
                    status=None, phone_number=None, picture=None, call_notifications=None, chat_notifications=None,
                    sound_notifications=None, reply_sounds=None, show_unassigned=None) -> UserEntity:
        return UserEntity(uuid=uuid, username=username, email=email, fullname=fullname, firstname=firstname,
                          lastname=lastname, title=title, status=status, phone_number=phone_number, picture=picture,
                          call_notifications=call_notifications, chat_notifications=chat_notifications,
                          sound_notifications=sound_notifications, reply_sounds=reply_sounds,
                          show_unassigned=show_unassigned)

    @classmethod
    def deserialize(cls, json_obj: dict) -> UserEntity:
        return UserEntity(uuid=json_obj.get('uuid'),
                          username=json_obj.get('username'),
                          email=json_obj.get('email'),
                          fullname=json_obj.get('fullname'),
                          firstname=json_obj.get('firstname'),
                          lastname=json_obj.get('lastname'),
                          title=json_obj.get('title'),
                          status=json_obj.get('status'),
                          phone_number=json_obj.get('phone_number'),
                          picture=json_obj.get('picture'),
                          call_notifications=json_obj.get('call_notifications'),
                          chat_notifications=json_obj.get('chat_notifications'),
                          sound_notifications=json_obj.get('sound_notifications'),
                          reply_sounds=json_obj.get('reply_sounds'),
                          show_unassigned=json_obj.get('show_unassigned'))
