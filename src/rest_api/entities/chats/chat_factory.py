from src.rest_api.entities.chats.chat_entity import ChatEntity


class ChatFactory:

    @classmethod
    def create_chat(cls, id=None, created_at=None, referrer=None, started_at=None, platform=None, leads=None,
                    transcript=None) -> ChatEntity:
        return ChatEntity(id, created_at, referrer, started_at, platform, leads, transcript)

    @classmethod
    def deserialize(cls, json_obj: dict) -> ChatEntity:
        return ChatEntity(id=json_obj.get('id'),
                          created_at=json_obj.get('created_at'),
                          referrer=json_obj.get('referrer'),
                          started_at=json_obj.get('started_at'),
                          platform=json_obj.get('platform'),
                          leads=json_obj.get('leads'),
                          transcript=json_obj.get('transcript'))
