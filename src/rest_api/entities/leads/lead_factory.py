from src.rest_api.entities.leads.lead_entity import LeadEntity, MetaLeadEntity


class LeadFactory:

    @classmethod
    def create_lead(cls, id=None, created_at=None, score=None, ip=None, first_name=None, last_name=None, city=None,
                    state=None, zip=None, phone=None, phone_to_send=None, email=None, email_to_send=None, type=None,
                    mobile=None, offer=None, department=None,
                    entry_page=None, referrer=None, transcript=None, meta: MetaLeadEntity = None) -> LeadEntity:
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        return LeadEntity(id=id,
                          created_at=created_at,
                          score=score,
                          ip=ip,
                          first_name=first_name,
                          last_name=last_name,
                          city=city,
                          state=state,
                          zip=zip,
                          phone=phone,
                          phone_to_send=phone_to_send,
                          email=email,
                          email_to_send=email_to_send,
                          type=type,
                          mobile=mobile,
                          offer=offer,
                          department=department,
                          entry_page=entry_page,
                          referrer=referrer,
                          transcript=transcript,
                          meta=meta)

    @classmethod
    def deserialize(cls, json_obj: dict) -> LeadEntity:
        return LeadEntity(id=json_obj.get('id'),
                          created_at=json_obj.get('created_at'),
                          score=json_obj.get('score'),
                          ip=json_obj.get('ip'),
                          first_name=json_obj.get('first_name'),
                          last_name=json_obj.get('last_name'),
                          city=json_obj.get('city'),
                          state=json_obj.get('state'),
                          zip=json_obj.get('zip'),
                          phone=json_obj.get('phone'),
                          email=json_obj.get('email'),
                          type=json_obj.get('type'),
                          mobile=json_obj.get('mobile'),
                          offer=json_obj.get('offer'),
                          department=json_obj.get('department'),
                          entry_page=json_obj.get('entry_page'),
                          referrer=json_obj.get('referrer'),
                          transcript=json_obj.get('transcript'),
                          meta=MetaLeadFactory.deserialize(json_obj.get('meta'))
                          )


class MetaLeadFactory:
    @classmethod
    def create_meta(cls, vehicle_make=None, vehicle_model=None, vehicle_year=None, vehicle_type=None, vehicle_vin=None,
                    stock_number=None, lead_type=None, appointment_time=None, appointment_date=None,
                    appointment_services=None, notes=None, marketing_phone=None, marketing_email=None,
                    marketing_sms=None, marketing_mail=None, postal_code_from_geoip=None, appointment=None,
                    appointment_transport=None, appointment_id=None, trade_in=None):
        return MetaLeadEntity(vehicle_make=vehicle_make,
                              vehicle_model=vehicle_model,
                              vehicle_year=vehicle_year,
                              vehicle_type=vehicle_type,
                              vehicle_vin=vehicle_vin,
                              stock_number=stock_number,
                              lead_type=lead_type,
                              appointment_time=appointment_time,
                              appointment_date=appointment_date,
                              appointment_services=appointment_services,
                              notes=notes,
                              marketing_phone=marketing_phone,
                              marketing_email=marketing_email,
                              marketing_sms=marketing_sms,
                              marketing_mail=marketing_mail,
                              postal_code_from_geoip=postal_code_from_geoip,
                              appointment=appointment,
                              appointment_transport=appointment_transport,
                              appointment_id=appointment_id,
                              trade_in=trade_in)

    @classmethod
    def deserialize(cls, json_obj: dict) -> MetaLeadEntity:
        return MetaLeadEntity(vehicle_make=json_obj.get('vehicle_make', None),
                              vehicle_model=json_obj.get('vehicle_model', None),
                              vehicle_year=json_obj.get('vehicle_year', None),
                              vehicle_type=json_obj.get('vehicle_type', None),
                              vehicle_vin=json_obj.get('vehicle_vin', None),
                              stock_number=json_obj.get('stock_number', None),
                              lead_type=json_obj.get('lead_type', None),
                              appointment_time=json_obj.get('appointment_time', None),
                              appointment_date=json_obj.get('appointment_date', None),
                              appointment_services=json_obj.get('appointment_services', None),
                              notes=json_obj.get('notes', None),
                              marketing_phone=json_obj.get('marketing_phone', None),
                              marketing_email=json_obj.get('marketing_email', None),
                              marketing_sms=json_obj.get('marketing_sms', None),
                              marketing_mail=json_obj.get('marketing_mail', None),
                              postal_code_from_geoip=json_obj.get('postal_code_from_geoip', None),
                              appointment=json_obj.get('appointment', None),
                              appointment_transport=json_obj.get('appointment_transport', None),
                              appointment_id=json_obj.get('appointment_id', None),
                              appointment_timestamp=json_obj.get('appointment_timestamp', None),
                              trade_in=json_obj.get('trade_in', None),
                              recall_number=json_obj.get('recall_number', None))
