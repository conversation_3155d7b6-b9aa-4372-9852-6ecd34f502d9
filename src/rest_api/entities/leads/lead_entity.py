class MetaLeadEntity:
    def __init__(self, vehicle_make=None, vehicle_model=None, vehicle_year=None, vehicle_type=None, vehicle_vin=None,
                 stock_number=None,
                 lead_type=None, appointment_time=None, appointment_date=None, appointment_services=None, notes=None,
                 marketing_phone=None, marketing_email=None, marketing_sms=None, marketing_mail=None,
                 postal_code_from_geoip=None, appointment=None, appointment_transport=None, appointment_id=None,
                 appointment_timestamp=None, trade_in=None, recall_number=None):
        self.vehicle_make = vehicle_make
        self.vehicle_model = vehicle_model
        self.vehicle_year = vehicle_year
        self.vehicle_type = vehicle_type
        self.vehicle_vin = vehicle_vin
        self.stock_number = stock_number
        self.lead_type = lead_type
        self.appointment_time = appointment_time
        self.appointment_timestamp = appointment_timestamp
        self.appointment = appointment
        self.appointment_date = appointment_date
        self.appointment_services = appointment_services
        self.appointment_transport = appointment_transport
        self.notes = notes
        self.marketing_phone = marketing_phone
        self.marketing_email = marketing_email
        self.marketing_sms = marketing_sms
        self.marketing_mail = marketing_mail
        self.postal_code_from_geoip = postal_code_from_geoip
        self.appointment_id = appointment_id
        self.trade_in = trade_in
        self.recall_number = recall_number


class LeadEntity:
    def __init__(self, id=None, created_at=None, score=None, ip=None, first_name=None, last_name=None, city=None,
                 state=None, zip=None, phone=None, phone_to_send=None, email=None, type=None, mobile=None, offer=None,
                 department=None,
                 entry_page=None, referrer=None, transcript=None, meta: MetaLeadEntity = None,
                 email_to_send=None):
        self.id = id
        self.created_at = created_at
        self.score = score
        self.ip = ip
        self.first_name = first_name
        self.last_name = last_name
        self.city = city
        self.state = state
        self.zip = zip
        self.phone = phone
        self.phone_to_send = phone_to_send
        self.email = email.lower()
        self.email_to_send = email_to_send
        self.type = type
        self.mobile = mobile
        self.offer = offer
        self.department = department
        self.entry_page = entry_page
        self.referrer = referrer
        self.transcript = transcript
        self.meta = meta

    def __eq__(self, other):
        """Overrides the default implementation"""
        from src.web.entities.lead_entity import WebLeadEntity
        if isinstance(other, (self.__class__, WebLeadEntity)):
            compare = ["first_name", "last_name", "phone", "email"]
            this = {k: self.__dict__[k] for k in self.__dict__ if k in compare}
            another = {k: other.__dict__[k] for k in other.__dict__ if k in compare}
            this["phone"] = this["phone"].replace(" ", "").replace("-", "")
            another["phone"] = another["phone"].replace(" ", "").replace("-", "")
            if this == another:
                return this == another
            else:
                if isinstance(other, self.__class__) \
                        and hasattr(self.meta, "appointment_id") \
                        and hasattr(other.meta, "appointment_id"):
                    return other.meta.appointment_id == self.meta.appointment_id
                elif isinstance(other, WebLeadEntity) and hasattr(self.meta, "appointment_id"):
                    return other.appointment_id == self.meta.appointment_id
        return False

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)

    @property
    def phone_formatted(self):
        return f"({self.phone[0:3]}) {self.phone[3:6]}-{self.phone[6:]}"
