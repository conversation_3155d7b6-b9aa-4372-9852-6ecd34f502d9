from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_types import Text<PERSON>lock, Button, Input
from kom_framework.src.web.page_object import PageObject

from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(CssSelector('.modal-feedback__form'))
class FeedBackComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_title = TextBlock(CssSelector('.modal-feedback__title'))
        self.btn_zendesk = Button(CssSelector("a.modal-feedback__zendeskLink"))
        self.inp_message = Input(CssSelector('.modal-feedback__textarea'))
        self.btn_send = Button(CssSelector('button[type="submit"]'))
        self.help_center = HelpCenterPage(self.ancestor)

    def open_actions(self):
        self.ancestor.btn_feedback.click()

    @step_decorator('WEB - FeedBackComponent: Close')
    def close(self):
        return self.ancestor.btn_updates.click()

    @step_decorator('WEB - FeedBackComponent: Close Gubagoo help center')
    def close_zendesk(self):
        self.ancestor.close_last_tab()


@find_by(CssSelector('.layout'))
class HelpCenterPage(PageObject):
    def __init__(self, glive):
        self.txt_name = TextBlock(CssSelector('.dropdown-toggle--user span'))
        self.glive = glive

    def open_actions(self):
        self.glive.feedback.btn_zendesk.click()
        assert len(self.glive.window_handles()) > 1
        self.glive.switch_to_last_tab()
        self.glive.wait_for.url_contains("help.gubagoo.com")
        self.set_session_key(self.glive.get_session_key())

    @step_decorator('WEB - HelpCenterPage: Close last tab')
    def close(self):
        self.glive.close_last_tab()
