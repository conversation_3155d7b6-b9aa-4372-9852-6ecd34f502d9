import time

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath
from kom_framework.src.web.data_types.element_types import But<PERSON>, TextArea
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(Xpath(".//div[@data-testid='delete-deal-confirmation-modal']"))
class DeleteConfirmationComponent(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.title = TextArea(Xpath(".//h2[text()='Delete Payment?']"))
        self.btn_cancel = Button(Xpath(".//button[text()='Cancel']"))
        self.btn_delete = Button(Xpath(".//button[text()='Delete']"))
        self.btn_exit = Button(Xpath(".//button[@aria-label='Exit modal']"))

    def open_actions(self):
        pass

    @step_decorator('Web - DeleteConfirmationComponent: Delete one payment')
    def delete_payment(self):
        time.sleep(1)
        self.btn_delete.click()
        assert self.wait_for.invisibility_of_element_located(wait_time=3), "The modal wasn't closed"

    @step_decorator('Web - DeleteConfirmationComponent: Cancel delete action')
    def cancel_delete(self):
        time.sleep(1)
        self.btn_cancel.click()
        assert self.wait_for.invisibility_of_element_located(wait_time=3), "The modal wasn't closed"


