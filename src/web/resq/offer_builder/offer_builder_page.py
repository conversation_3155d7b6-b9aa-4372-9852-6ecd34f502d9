import re

from pytest_check import check_func

from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_types import Button, TextBlock, Input, Image
from kom_framework.src.web.page_object import PageObject
from kom_framework.src.web.support.page_factory import find_by
from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.utils.converter import Converter
from src.utils.decorators import step_decorator
from src.web.consumer.desktop.vdp_page import VDP3Page
from src.web.entities.payment_grid.finance_payment_grid_entity import FinancePaymentGridEntity
from src.web.resq.offer_builder.cash_payment_grid_component import CashPaymentGridComponent
from src.web.resq.offer_builder.finance_payment_grid_component import FinancePaymentGridComponent
from src.web.resq.offer_builder.lease_payment_grid_component import LeasePaymentGridComponent
from src.web.resq.offer_builder.payment_widget_component import PaymentsWidgetComponent
from src.web.resq.offer_builder.pii_form_component import PIIFormComponent
from src.web.resq.offer_builder.price_widget_component import PriceWidgetComponent
from src.web.resq.offer_builder.program_browser_component import ProgramBrowserComponent
from src.web.resq.offer_builder.send_payment_options_component import SendPaymentOptionsComponent
from src.web.resq.offer_builder.service_protection_widget_component import ServiceProtectionWidgetComponent
from src.web.resq.offer_builder.taxes_widget_component import TaxesWidgetComponent
from src.web.resq.offer_builder.trade_in_component import TradeInWidgetComponent


@find_by(Xpath("//h1[text()='Offer Builder']/../../.."))
class OfferBuilderPage(PageObject):

    def __init__(self, resq=None):
        self.resq = resq
        self.program_browser = ProgramBrowserComponent(self)
        self.lease_payment_grid = LeasePaymentGridComponent(self)
        self.finance_payment_grid = FinancePaymentGridComponent(self)
        self.cash_payment_grid = CashPaymentGridComponent(self)
        self.payment_widget = PaymentsWidgetComponent(self)
        self.price_widget = PriceWidgetComponent(self)
        self.taxes_widget = TaxesWidgetComponent(self)
        self.service_protection_widget = ServiceProtectionWidgetComponent(self)
        self.trade_in_widget = TradeInWidgetComponent(self)
        self.send_payment_options = SendPaymentOptionsComponent(self)
        self.pii_form = PIIFormComponent(self)
        self.btn_finance = Button(Xpath(".//button[@id='tab_0']"))
        self.btn_lease = Button(Xpath(".//button[@id='tab_1']"))
        self.btn_cash = Button(Xpath(".//button[@id='tab_2']"))
        self.txt_number_of_finance_payments = TextBlock(Xpath(".//div[@data-testid='deals-total-finance']"))
        self.txt_number_of_lease_payments = TextBlock(Xpath(".//div[@data-testid='deals-total-lease']"))
        self.txt_number_of_cash_payments = TextBlock(Xpath(".//div[@data-testid='deals-total-cash']"))
        self.txt_vehicle_info = TextBlock(Xpath(".//a[@data-testid='vehicle-info-name']"))
        self.txt_stock_number = TextBlock(Xpath(".//p[@data-testid='vehicle-info-stock']"))
        self.txt_vin_number = TextBlock(Xpath(".//p[@data-testid='vehicle-info-vin']"))
        self.txt_selling_price = TextBlock(Xpath(".//p[@data-testid='vehicle-info-price']"))
        self.btn_close = Button(CssSelector("[aria-label='Close application']"))
        self.btn_add_payment = Button(Xpath(".//button[@data-testid='add-payments-button']"))
        self.btn_next = Button(Xpath(".//button[@data-testid='ob-send-button']"))
        self.btn_credit_score = Button(Xpath(".//button[@id='credit-score-filter-dropdown']"))
        self.btn_finance_down_payment = Button(Xpath(".//div[@data-testid='finance-deal-container']//button[@id='down-payment-filter']"))
        self.btn_lease_down_payment = Button(Xpath(".//div[@data-testid='lease-deal-container']//button[@id='down-payment-filter']/span"))
        self.btn_lease_mileage = Button(Xpath(".//button[@data-testid='ob-mileage-filter-trigger']"))
        self.txt_finance_monthly_payment = TextBlock(Xpath(".//div[@data-testid='finance-deal-container']//p//span"))
        self.btn_copy = Button(Xpath(".//button[text()='Copy']"))
        self.btn_delete = Button(Xpath(".//button[text()='Delete']"))
        self.inp_down_payment = Input(Xpath(".//input[@id='down-payment-slider-value']"))
        self.btn_customer_pii = Button(Xpath(".//button[@data-testid='customer-pii-button']"))
        # Customer Selected Deal
        self.btn_close_customer_deal_modal = Button(Xpath(".//button[@data-testid='modal-close-button']"))
        self.btn_view_payment = Button(Xpath(".//button[text()='View ']"))
        self.txt_selected_deal_monthly_payment = TextBlock(Xpath(".//span[@data-testid='deal-monthly-payment']"))
        self.txt_selected_deal_term = TextBlock(Xpath(".//span[@data-testid='deal-term']"))
        self.txt_selected_deal_apr = TextBlock(Xpath(".//span[@data-testid='deal-apr']"))
        self.img_selected_deal_lender = Image(Xpath(".//div[@data-testid='current-deal-lender-image']"))
        self.txt_selected_deal_down_payment = TextBlock(Xpath(".//dl[@data-testid='summary-list-down-payment']//dd"))

    @step_decorator('WEB - OBPage: Open Offer Builder Page')
    def open_actions(self, lead_info, vehicle: VehicleEntity, share_menu=False):
        if self.driver:
            self.switch_to.default_content()
        add_customer_form = self.resq.vr.add_customer.open()
        add_customer_form.create_customer(lead_info)
        if share_menu:
            self.resq.vr.customer_deals.open_suggest_new_car_share()
        else:
            self.resq.vr.customer_deals.open_suggest_new_car()
        add_customer_form.select_vehicle(vehicle, search_param='stock', column_name='description')
        add_customer_form.open_ob_create_deal()
        self.resq.iframe_offer_builder.switch_to()
        self.exists(5)
        assert self.txt_vehicle_info.wait_for.presence_of_element_located(5), \
            "Vehicle info wasn't loaded on Offer Builder page"

    @step_decorator("WEB - OfferBuilderPage: Close Offer Builder")
    def close_offer_builder(self):
        self.btn_close.click()
        self.driver.switch_to.default_content()
        assert self.resq.vr.customer_deals.exists(wait_time=5), \
            "Offer Builder page wasn't closed or customer deal page is not displayed"

    @property
    @step_decorator("WEB - OfferBuilderPage: Get stock")
    def get_stock(self):
        return self.txt_stock_number.text.split(" ")[1]

    @property
    @step_decorator("WEB - OfferBuilderPage: Get VIN")
    def get_vin(self):
        return self.txt_vin_number.text.split(" ")[1]

    @property
    @step_decorator("WEB - OfferBuilderPage: Get selling price")
    def get_selling_price(self):
        selling_price = Converter.get_int_value_rounded(self.txt_selling_price.text.split(" ")[1])
        return selling_price

    @property
    @step_decorator("WEB - OfferBuilderPage: Get vehicle type, make, model, trim and year")
    def get_vehicle_info(self):
        return self.txt_vehicle_info.text

    @step_decorator("WEB - OfferBuilderPage: Open VDP")
    def open_vdp_from_ob_and_get_vin(self):
        self.txt_vehicle_info.click()
        self.switch_to_last_tab()
        vdp_page = VDP3Page()
        vdp_page.set_session_key(self.get_session_key())
        assert vdp_page.exists(), "VDP page wasn't open"
        vdp_vin_number = vdp_page.get_vin()
        self.close_last_tab()
        self.resq.iframe_offer_builder.switch_to()
        return vdp_vin_number

    @step_decorator("WEB - OfferBuilderPage: Create Customer via create link flow")
    def send_deal_create_link_flow(self):
        self.send_payment_options.open()
        self.send_payment_options.send_deal()
        self.send_payment_options.close_updates_sent()

    @step_decorator("WEB - OfferBuilderPage: Get selling price")
    def get_credit_score(self):
        return self.btn_credit_score.text.replace(' Credit Score', '')

    @step_decorator("WEB - OfferBuilderPage: open offer builder - Update deal flow")
    def open_offer_builder_update_deal(self, consumer_name):
        customer = self.resq.vr.get_customer_by_full_name(consumer_name)
        customer.txt_vehicle_name.click()
        self.resq.vr.customer_deals.btn_update_deal.wait_for.presence_of_element_located(8)
        self.resq.vr.customer_deals.btn_update_deal.click()
        self.resq.iframe_offer_builder.switch_to()
        self.exists(5)
        assert self.txt_vehicle_info.wait_for.presence_of_element_located(10), \
            "Vehicle info wasn't loaded on Offer Builder page"
        return self

    @step_decorator("WEB - OfferBuilderPage: Get  selected payment values")
    def get_selected_payment_values(self):
        values = []
        entity = FinancePaymentGridEntity(
            monthly_payment=float(re.sub(r"\D", '',self.txt_selected_deal_monthly_payment.text)),
            term=int(self.txt_selected_deal_term.text.replace(" mo", "")),
            apr=float(self.txt_selected_deal_apr.text.replace("% APR", "")),
            lender=self.img_selected_deal_lender.get_attribute('data-lender'), down_payment=re.sub(r"\D", "", self.txt_selected_deal_down_payment.text))
        values.append(entity)
        return values

    @step_decorator("WEB - OfferBuilderPage: Open customer selected deal info")
    def open_customer_selected_deal(self, expected_monthly_payment):
        self.btn_view_payment.wait_for.presence_of_element_located(5)
        self.btn_view_payment.click()
        format_monthly_payment = expected_monthly_payment.replace("/mo", "")
        (self.txt_selected_deal_monthly_payment.wait_for.text_to_be_present_in_element(text=format_monthly_payment, wait_time=5),
         "Monthly Payment is not the same as ot was selected in the consumer VR")
        return self.get_selected_payment_values()

    @step_decorator("WEB - OfferBuilderPage: Close customer selected deal")
    def close_customer_selected_deal(self):
        self.btn_close_customer_deal_modal.exists(3)
        self.btn_close_customer_deal_modal.click()
        assert not self.btn_close_customer_deal_modal.exists(3), "The customer deal modal wasn't closed"

    @check_func
    @step_decorator("WEB - OfferBuilderPage: Check that payments widget does not exist")
    def check_payments_widget_exists(self):
        assert not self.payment_widget.exists(), "Payment widget is present on the page"



