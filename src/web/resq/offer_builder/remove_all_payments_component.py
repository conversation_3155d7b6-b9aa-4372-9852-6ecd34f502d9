import time

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath
from kom_framework.src.web.data_types.element_types import Button, TextArea
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.resq.offer_builder import PaymentType


@find_by(Xpath("//h1[contains(@class, 'ClearAllButton')]/../.."))
class RemoveAllPaymentsComponent(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.title = TextArea(Xpath(".//h1"))
        self.btn_cancel = Button(Xpath(".//button[text()='Cancel']"))
        self.btn_delete = Button(Xpath(".//button[text()='Delete']"))
        self.btn_exit = Button(Xpath("//button[@aria-label='Exit modal']"))

    def open_actions(self):
        self.ancestor.btn_clear_all.click()
        assert self.title.exists(wait_time=3), "Clear all modal didn't appear"

    @step_decorator('Web - RemoveAllPaymentsComponent: Clear all payments')
    def clear_all(self, payment_type: PaymentType):
        if payment_type == PaymentType.FINANCE:
            payment_tab = self.ancestor.ancestor.txt_number_of_finance_payments
        elif payment_type == PaymentType.LEASE:
            payment_tab = self.ancestor.ancestor.txt_number_of_lease_payments
        elif payment_type == PaymentType.CASH:
            payment_tab = self.ancestor.ancestor.txt_number_of_cash_payments
        else:
            raise NotImplementedError
        payment_size = payment_tab.text
        self.open()
        self.btn_delete.wait_for.element_to_be_clickable(wait_time=5)
        time.sleep(1)
        self.btn_delete.click()
        assert payment_tab.wait_for.text_to_be_present_in_element(text=payment_size, wait_time=5, until=False), \
            "Amount of payments wasn't changed"

    @step_decorator('Web - RemoveAllPaymentsComponent: Cancel delete action')
    def cancel_delete(self, payment_type: PaymentType):
        if payment_type == PaymentType.FINANCE:
            payment_tab = self.ancestor.ancestor.txt_number_of_finance_payments
        elif payment_type == PaymentType.LEASE:
            payment_tab = self.ancestor.ancestor.txt_number_of_lease_payments
        elif payment_type == PaymentType.CASH:
            payment_tab = self.ancestor.ancestor.txt_number_of_cash_payments
        else:
            raise NotImplementedError
        payment_size = payment_tab.text
        self.open()
        self.btn_delete.wait_for.element_to_be_clickable(wait_time=5)
        time.sleep(1)
        self.btn_cancel.click()
        assert payment_tab.wait_for.text_to_be_present_in_element(text=payment_size, wait_time=5),\
            "Amount of payments was changed"
