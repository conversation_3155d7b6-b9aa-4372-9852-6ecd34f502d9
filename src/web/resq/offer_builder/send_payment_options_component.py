import time

from pytest_check import check_func

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath
from kom_framework.src.web.data_types.element_types import Button, Input, TextArea, CheckBox, Image
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(Xpath(".//div[@aria-label='Send Offer Modal']"))
class SendPaymentOptionsComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        # Buttons
        self.btn_spo_next = Button(Xpath(".//button[@data-testid='spo-next-button']"))
        self.btn_spo_added_payment = Button(Xpath("//div[@data-testid='finance-radio-icon']/ancestor::div[3]"))
        self.btn_spo_send = Button(Xpath(".//button[@data-testid='spo-send-button']"))
        self.btn_close_send_flow = Button(Xpath(".//button[@data-testid='send-flow-close-button']"))
        self.btn_close_x = Button(Xpath(".//button[@aria-label='Exit modal']"))
        self.btn_cancel = Button(Xpath(".//button[contains(text(), 'Cancel')]"))
        # Inputs
        self.inp_first_name = Input(Xpath(".//input[@aria-label='First name']"))
        self.inp_last_name = Input(Xpath(".//input[@aria-label='Last name']"))
        self.inp_email = Input(Xpath(".//input[@aria-label='Email']"))
        self.inp_phone = Input(Xpath(".//input[@aria-label='Phone']"))
        self.inp_dealer_message = Input(Xpath(".//textarea[@id='dealer-message-input']"))
        # Checkboxes
        self.chk_chat = CheckBox(Xpath(".//label[@for='send-chat-checkbox']//div"))
        self.chk_email = CheckBox(Xpath(".//label[@for='send-email-checkbox']//div"))
        self.chk_text = CheckBox(Xpath(".//label[@for='send-sms-checkbox']//div"))
        self.chk_link = CheckBox(Xpath(".//input[@id='send-link-checkbox']"))
        self.chk_send_lead_crm = CheckBox(Xpath(".//label[@for='sendCrm']//div"))
        # Image
        self.img_chat = Image(Xpath(".//div[contains(@class, 'ChatPreview')]"))
        self.img_email = Image(Xpath(".//div[contains(@class, 'EmailPreview')]"))
        self.img_sms = Image(Xpath(".//div[contains(@class, 'SmsPreview')]"))
        # Text
        self.txt_header = TextArea(Xpath(".//h1[contains(@class, 'BaseText')]"))

    @step_decorator('WEB Offer Builder - Open Send Payment Options')
    def open_actions(self):
        self.ancestor.btn_next.click()
        self.btn_spo_added_payment.wait_for.presence_of_element_located(5)
        self.btn_spo_next.wait_for.presence_of_element_located(5), "Button 'Next' is not displayed"
        self.btn_spo_next.js.click()

    @step_decorator('WEB - Offer Builder: Close Send Payment Options')
    def close_modal(self):
        self.btn_close_x.click()
        self.inp_first_name.exists(wait_time=2, until=False)
        assert self.ancestor.txt_vehicle_info.wait_for.presence_of_element_located(2), "The modal wasn't close"

    @check_func
    @step_decorator('WEB - Offer Builder, SPO: Check customer lead information')
    def check_customer_lead_information(self, first_name: str, last_name: str, emai: str, phone: str = None):
        assert not self.inp_first_name.is_enabled(), "First name input is enabled"
        assert self.inp_first_name.get_attribute('value') == first_name, "First name is not the same"
        assert not self.inp_last_name.is_enabled(), "Last name input is enabled"
        assert self.inp_last_name.get_attribute('value') == last_name, "Last name is not the same"
        assert not self.inp_email.is_enabled(), "Email input is enabled"
        assert self.inp_email.get_attribute('value') == emai, "Email is not the same"
        assert not self.inp_phone.is_enabled(), "Phone input is enabled"
        assert self.inp_phone.get_attribute('value').translate(str.maketrans('', '', '()- ')) == phone, \
            "Phone is not the same"

    @check_func
    @step_decorator('WEB - Offer Builder, SPO: Check chat image is present when chat checkbox is hovered')
    def check_chat_image(self):
        self.chk_chat.action_chains.move_to_element().perform()
        assert self.img_chat.wait_for.presence_of_element_located(wait_time=2), "Chat preview is not displayed"

    @check_func
    @step_decorator('WEB - Offer Builder, SPO: Check email image is present when email checkbox is hovered')
    def check_email_image(self):
        self.chk_email.action_chains.move_to_element().perform()
        assert self.img_email.wait_for.presence_of_element_located(wait_time=2), "Email preview is not displayed"

    @check_func
    @step_decorator('WEB - Offer Builder, SPO: Check email image is present when email checkbox is hovered')
    def check_sms_image(self):
        self.chk_text.action_chains.move_to_element().perform()
        assert self.img_sms.wait_for.presence_of_element_located(wait_time=2), "Sms preview is not displayed"

    @check_func
    @step_decorator('WEB - Offer Builder, SPO: Check that link checkbox is checked & disabled')
    def check_link_checkbox(self):
        assert self.chk_link.get_attribute('aria-checked'), "Link checkbox is not checked"
        assert not self.chk_link.is_enabled(), "Link checkbox is enabled"

    @step_decorator('WEB - Offer Builder Sand Payment Option: Get Button text')
    def get_button_text(self):
        return self.btn_spo_send.text

    @step_decorator('WEB - Offer Builder Sand Payment Option: Get dealer message')
    def get_dealer_message(self):
        return self.inp_dealer_message.get_attribute('value')

    @step_decorator('WEB - Offer Builder Sand Payment Option: Select checkbox option')
    def select_option_checkbox(self, chat: bool = False, email: bool = False, text: bool = False):
        if chat:
            self.chk_chat.click()
            self.img_chat.wait_for.presence_of_element_located(wait_time=2), "Chat preview is not displayed"
        if email:
            self.inp_first_name.action_chains.move_to_element().perform()
            self.chk_email.click()
            self.img_email.wait_for.presence_of_element_located(wait_time=2), "Email preview is not displayed"
        if text:
            self.chk_text.click()
            assert self.img_sms.wait_for.presence_of_element_located(wait_time=2), "Sms preview is not displayed"

    @check_func
    @step_decorator('WEB - Offer Builder Sand Payment Option: Check - Cancel action')
    def check_cancel_acton(self):
        self.btn_cancel.click()
        self.inp_first_name.exists(wait_time=2, until=False), "The SPO modal wasn't close"
        assert self.ancestor.txt_selling_price.wait_for.presence_of_element_located(wait_time=2), \
            "Selling price is not displayed when SPO modal was closed"

    @step_decorator('WEB - Offer Builder Sand Payment Option: Close - Updates sent modal')
    def send_deal(self):
        self.btn_spo_send.wait_for.presence_of_element_located(3)
        self.btn_spo_send.js.click()

    @step_decorator('WEB - Offer Builder Sand Payment Option: Close - Updates sent modal')
    def close_updates_sent(self, live_chat: bool = False):
        if live_chat:
            self.txt_header.wait_for.text_equal(text="Updates Sent", wait_time=10)
        self.btn_close_send_flow.wait_for.presence_of_element_located(3), "Close button is not displayed"
        self.btn_close_send_flow.js.click()
        self.ancestor.driver.switch_to.default_content()
        assert self.ancestor.resq.vr.customer_deals.exists(wait_time=5), "The customer deals page wasn't defined"

    @step_decorator('WEB - Offer Builder Sand Payment Option: Enter new message ')
    def delete_and_enter_test_message(self):
        self.inp_dealer_message.exists(3)
        self.inp_dealer_message.clear()
        self.inp_dealer_message.type_keys("This is a test message!")
