from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON>ss<PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_list_types import Menu
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextBlock
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.resq.conversations import quick_replies_btn_close, quick_replies_current_category


@find_by(Xpath(".//div[@class='quick-replies__container']/.."))
class QuickRepliesComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_close = Button(quick_replies_btn_close)
        self.btn_back = Button(CssSelector(".icon-arrow-left"))
        self.mn_categories = Menu(CssSelector(".ol-list__category"))
        self.mn_messages = Menu(CssSelector(".ol-list>li>div"))
        self.current_category = TextBlock(quick_replies_current_category)

    @step_decorator('WEB - QuickRepliesComponent: Open "Quick Replies" Component')
    def open_actions(self):
        self.ancestor.btn_quick_replies.js.click()

    @step_decorator('WEB - QuickRepliesComponent: Close "Quick Replies" Component')
    def close(self):
        self.btn_close.click()

    @step_decorator('WEB - QuickRepliesComponent: Get quick replies list from {1} category')
    def get_quick_replies_list(self, category):
        self.ancestor.ancestor.close_notification_windows()
        self.select_category(category)
        return self.mn_messages.elements_texts

    @step_decorator('WEB - QuickRepliesComponent: Select quick reply {2} from {1} category')
    def select_reply(self, category, reply):
        self.ancestor.ancestor.close_notification_windows()
        self.select_category(category)
        self.mn_messages.select_menu_section_by_pattern(reply)

    @step_decorator('WEB - QuickRepliesComponent: Select quick replies {1}')
    def select_replies(self, replies):
        self.open()
        for reply in replies:
            self.select_reply(reply.category, reply.message)

    @step_decorator('WEB - QuickRepliesComponent: Select quick reply {1} category')
    def select_category(self, category):
        if category not in self.current_category.text:
            if "Quick Replies" not in self.current_category.text \
                    and "Saved Responses" not in self.current_category.text:
                self.btn_back.click()
            self.mn_categories.select_menu_section_by_pattern(category)
