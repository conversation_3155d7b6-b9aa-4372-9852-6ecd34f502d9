from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_types import But<PERSON>, Input
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(CssSelector('.file-upload-modal'))
class FileAttachComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_close = Button(CssSelector("button.close"))
        self.btn_cancel = Button(CssSelector("button.btn-secondary"))
        self.btn_send = Button(CssSelector("button.btn-primary"))
        self.inp_file = Input(CssSelector("input[type='file']"))

    @step_decorator('WEB - FileAttachComponent: Open Component')
    def open_actions(self):
        self.ancestor.conversations.btn_attach.js.click()
        if not self.exists(2):
            self.ancestor.conversations.btn_attach.js.click()

    @step_decorator('WEB - FileAttachComponent: Close Component')
    def close(self):
        self.btn_close.click()

    @step_decorator('WEB - FileAttachComponent: Attach file')
    def attach(self, path):
        self.inp_file.send_keys_to_invisible_field(path)
        self.btn_send.click()
