from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextBlock, Input
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(Xpath('//*[contains(@class,"updateMember")]/../../../../../..'))
class UpdateMemberComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_reactivate = Button(CssSelector('[data-testid="members-reactivate"]'))
        self.txt_status = TextBlock(Xpath('.//*[contains(@class,"info")]/../span'))
        self.btn_cancel = Button(Xpath('.//*[contains(@class,"modal-footer")]/button[text()="Cancel"]'))
        self.btn_save = Button(Xpath('.//*[contains(@class,"modal-footer")]//button[@data-testid="members-save"]'))
        self.inp_first_name = Input(CssSelector('#edit_user_form_first_name'))
        self.inp_last_name = Input(CssSelector('#edit_user_form_last_name'))
        self.inp_email = Input(CssSelector('#edit_user_form_email'))
        self.inp_title = Input(CssSelector('#edit_user_form_title'))

    @step_decorator('WEB - UpdateMemberComponent: Open "Update member" Component')
    def open_actions(self, first_name, last_name):
        member = self.ancestor.members.search_member(first_name, last_name)
        member.more.click()
        member.update.click()

    def setup_component(self, first_name, last_name):
        if first_name not in self.inp_first_name.get_content() or last_name not in self.inp_last_name.get_content():
            self.btn_cancel.click()
            self.open(first_name, last_name)

    @step_decorator("WEB - UpdateMemberComponent: Save")
    def save(self):
        self.btn_save.click()

    @step_decorator("WEB - UpdateMemberComponent: Get status")
    def get_status(self):
        return self.txt_status.text

    def close(self):
        self.btn_cancel.click()
        self.wait_for.visibility_of_element_located(until=False)
