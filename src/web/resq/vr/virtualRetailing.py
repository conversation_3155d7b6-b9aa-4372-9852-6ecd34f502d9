from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON><PERSON>ctor, ClassName, Xpath
from kom_framework.src.web.data_types.element_types import <PERSON>Extended, TextBlock, AnyType, Image, Spinner, Button
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.entities.lead_entity import WebLeadEntity
from src.web.resq.vr.add_customer_component import AddCustomerForm
from src.web.resq.vr.customer_detail_component import CustomerDealsComponent


class CustomersStructure:
    def __init__(self):
        self.txt_date = TextBlock(ClassName("row-id__date"))
        self.txt_time = TextBlock(ClassName("row-id__time"))
        self.ant_avatar = AnyType(ClassName("avatar"))
        self.txt_fullname = TextBlock(Xpath(".//div[contains(@class,'us-name')]/div[contains(@class,'fullname')]"))
        self.txt_city = TextBlock(ClassName("city"))
        self.img_vehicle = Image(ClassName("row-vehicle__img"))
        self.txt_vehicle_name = TextBlock(ClassName("row-vehicle__text--name"))
        self.txt_vehicle_price = TextBlock(ClassName("row-vehicle__text--price"))
        self.txt_payments_period = TextBlock(ClassName("row-payments__period"))
        self.txt_progress_time = TextBlock(ClassName("row-progress__time"))
        self.txt_progress_status = TextBlock(ClassName("row-progress__status"))
        self.txt_tel = TextBlock(ClassName("row-contact__tel"))
        self.txt_email = TextBlock(ClassName("row-contact__email"))
        self.txt_chat = TextBlock(ClassName("row-virtual"))


@find_by(ClassName("deals"))
class VirtualRetailing(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.customer_deals = CustomerDealsComponent(self)
        self.add_customer = AddCustomerForm(self)
        self.btn_add_customer = Button(CssSelector('[data-testid="glw-add-customer-button"]'))
        self.dll_leads_list = SelectExtended(CssSelector(".account-select"), CssSelector(".account-select__option"),
                                             search_input_locator=CssSelector(".account-select .Select-input input"))
        self.tbl_customers = Table(ClassName("table-custom--vfi__row"), CustomersStructure)
        self.spn_loader = Spinner(CssSelector(".loading"))

    def open_actions(self):
        self.ancestor.open()
        if not self.ancestor.current_url().endswith('/virtual-retailing/'):
            self.ancestor.btn_virtualRetailing.click()
        self.spn_loader.wait_for_appear_and_disappear(wait_until_appears=2)

    @step_decorator('WEB - VirtualRetailing: select {1} dealer')
    def select_dealer(self, dealer_name):
        if self.dll_leads_list.text != dealer_name:
            self.dll_leads_list.select_item_by_text(dealer_name)
            self.spn_loader.wait_for_appear_and_disappear(wait_until_appears=2)

    @step_decorator('WEB - VirtualRetailing: get {1} customer')
    def get_customer(self, lead_info: WebLeadEntity):
        return self.tbl_customers.get_row_by_column_value("txt_email", lead_info.email)

    @step_decorator('WEB - VirtualRetailing: get {1} customer by full name')
    def get_customer_by_full_name(self, customer_name: str):
        return self.tbl_customers.get_row_by_column_text_content("txt_fullname", customer_name, wait_time=10)

    @step_decorator('WEB - VirtualRetailing: open deals for a customer')
    def select_specific_customer_deal(self, lead_info: WebLeadEntity):
        self.get_customer(lead_info).txt_fullname.click()

