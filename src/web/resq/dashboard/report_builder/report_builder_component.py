import time

from kom_framework.src.general import File
from kom_framework.src.web import remote_execution
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextBlock
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.entities.report_entity import ReportEntity, ReportType
from src.web.resq.dashboard.report_builder.edit_recurring_report_component import EditReportReportComponent
from src.web.resq.dashboard.report_builder.recurring_report import RecurringReportComponent, \
    RecurringReportTimePeriodComponent, RecurringReportDeliveryChannelComponent
from src.web.resq.dashboard.report_builder.single_report import SingleReportComponent, SingleReportTimePeriodComponent, \
    SingleReportDeliveryChannelComponent
from test.web_tests.resq import CONST_PATH_FOR_DOWNLOAD


class RecurringReportsStructure:
    def __init__(self):
        self.txt_report = TextBlock(CssSelector('[data-testid="report_name"]>span'))
        self.txt_report_detail = TextBlock(CssSelector('[data-testid="report_name"]>div'))
        self.txt_sent = TextBlock(CssSelector('[data-testid="report_sent"]'))
        self.txt_next_send = TextBlock(CssSelector('[data-testid="report_next"]'))
        self.txt_created_by = TextBlock(CssSelector('[data-testid="report_createdby"]'))
        self.btn_edit = Button(CssSelector('[data-testid="report_edit"]'))
        self.btn_activate = Button(Xpath('.//*[@data-testid="report_activity"]/../span'))
        self.txt_status = TextBlock(Xpath('.//*[@data-testid="report_activity"]/../../../span'))
        self.btn_more = Button(CssSelector('[data-testid="report_moreactions"]'))
        self.btn_ddl_edit = Button(Xpath(".//button[contains(@class, 'dropdown-item') and text()='Edit']"))
        self.btn_ddl_view_log = Button(Xpath(".//button[contains(@class, 'dropdown-item') and text()='View delivery log']"))
        self.btn_ddl_delete = Button(Xpath(".//button[contains(@class, 'dropdown-item') and text()='Delete']"))


@find_by(Xpath(".//h1[contains(text(), 'Report')]/../../..")) #TODO: Change
class ReportBuilderComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_single_report = Button(Xpath(".//button[text()='Single report']"))
        self.btn_recurring_report = Button(Xpath(".//button[text()='Schedule recurring report']"))
        self.tbl_recurring_reports = Table(Xpath('.//*[@data-testid="report_detail"]//*[@data-testid="report_name"]/../..'),
                                           RecurringReportsStructure)
        self.single_report = SingleReportComponent(self)
        self.single_report_time = SingleReportTimePeriodComponent(self)
        self.single_report_delivery = SingleReportDeliveryChannelComponent(self)
        self.recurring_report = RecurringReportComponent(self)
        self.recurring_report_time = RecurringReportTimePeriodComponent(self)
        self.recurring_report_delivery = RecurringReportDeliveryChannelComponent(self)
        self.confirm_deletion = ConfirmComponent(self.ancestor.ancestor)
        self.edit_report = EditReportReportComponent(self.ancestor.ancestor)

    @step_decorator('WEB - ReportBuilderComponent: Open Component')
    def open_actions(self):
        self.ancestor.open()
        self.ancestor.btn_report_builder.click()

    def get_latest_report_id(self):
        a = self.exists(2)
        if self.tbl_recurring_reports.exists(10):
            s = self.tbl_recurring_reports.wait_for.number_of_elements(0, wait_time=10, until=False)
        if self.tbl_recurring_reports.exists() and self.tbl_recurring_reports.size >= 1:
            name = self.tbl_recurring_reports.get_row_by_index(self.tbl_recurring_reports.size-1).txt_report.text
            return name[name.find("#")+1:]

    def get_report_by_id(self, report_id, wait_time=5):
        report = None
        end_time = time.time() + wait_time
        while report is None and time.time() < end_time:
            report = self.tbl_recurring_reports.get_row_by_column_pattern("txt_report", f"#{report_id}", wait_time=5)
        return report

    def get_status_by_id(self, report_id):
        report = self.get_report_by_id(report_id)
        return report.txt_status.text

    def get_report_details_by_id(self, report_id):
        report_row = self.get_report_by_id(report_id)
        report = report_row.txt_report.text.split()
        report.extend(report_row.txt_report_detail.text.split(" on "))
        dict_result = dict(zip(["report_type", "delivery", "id", 'report_timing', "accounts"], report))
        dict_result['accounts'] = int(dict_result['accounts'].split()[0])
        dict_result["status"] = report_row.txt_status.text
        return dict_result

    @step_decorator('WEB - ReportBuilderComponent: Create report')
    def create_report(self, report_configs: ReportEntity):
        const_download_wait_time = 15
        if report_configs.report_type == ReportType.SINGLE:
            file_name = None
            self.single_report_delivery.open(accounts=report_configs.accounts,
                                             time_period=report_configs.report_timing)
            if report_configs.emails:
                self.single_report_delivery.send_email(report_configs.emails)
                self.ancestor.ancestor.notification.wait_for.visibility_of_element_located(5)
            if report_configs.download:
                self.single_report_delivery.download()
                self.ancestor.ancestor.notification.wait_for.visibility_of_element_located(5)
                file_patt = r"Gubagoo_report_\d{4}-\d{2}-\d{2}-\d{4}-\d{2}-\d{2}( \(\d\))?.csv"
                if remote_execution:
                    file_name = File.wait_until_file_download_finish_remote(self.ancestor.ancestor.driver.session_id,
                                                                            regex=file_patt,
                                                                            wait_time=const_download_wait_time)
                else:
                    file_name = File.wait_until_file_download_finish(directory=CONST_PATH_FOR_DOWNLOAD, regex=file_patt,
                                                                     wait_time=const_download_wait_time)
            return file_name
        else:
            last_report_id = self.get_latest_report_id()
            report_count = self.tbl_recurring_reports.size
            self.recurring_report_delivery.open(accounts=report_configs.accounts,
                                                time_period=report_configs.report_timing,
                                                every=report_configs.send_on)
            if report_configs.emails:
                self.recurring_report_delivery.send_email(report_configs.emails)
                self.ancestor.ancestor.notification.wait_for.visibility_of_element_located(5)
            if report_configs.ftp:
                self.recurring_report_delivery.ftp(host=report_configs.ftp.host,
                                                   user=report_configs.ftp.user_name,
                                                   password=report_configs.ftp.password,
                                                   protocol=report_configs.ftp.protocol)
                self.ancestor.ancestor.notification.wait_for.visibility_of_element_located(5)
            new_report_count = report_count + 1
            self.tbl_recurring_reports.wait_for.number_of_elements(new_report_count, wait_time=5)
            new_report_id = self.get_latest_report_id()
            assert (last_report_id and int(new_report_id) >= int(last_report_id)+1) or self.tbl_recurring_reports.size == 1, \
                f"New report id {new_report_id} is not correct, previous was {last_report_id}"
            return new_report_id

    def inactivate_report(self, report_id):
        report = self.get_report_by_id(report_id)
        if report.txt_status.text == 'ACTIVE':
            report.btn_activate.click()
        report.txt_status.wait_for.text_equal('INACTIVE', wait_time=5)

    def activate_report(self, report_id):
        report = self.get_report_by_id(report_id)
        if report.txt_status.text == 'INACTIVE':
            report.btn_activate.click()
        report.txt_status.wait_for.text_equal('ACTIVE', wait_time=5)

    def delete_report(self, report_id):
        self.confirm_deletion.open(report_id=report_id)
        return self.confirm_deletion.confirm()

    def edit(self, report_id, report_configs: ReportEntity):
        self.edit_report.open(report_id=report_id)
        return self.edit_report.edit(report_configs)


@find_by(CssSelector(".confirmation-modal"))
class ConfirmComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.report_id = None
        self.txt_message = TextBlock(CssSelector(".modal-body div"))
        self.btn_cancel = Button(CssSelector('.btn-secondary'))
        self.btn_confirm = Button(CssSelector('.btn-primary'))

    def open_actions(self, report_id):
        self.report_id = report_id
        report_row = self.ancestor.dashboard.report_builder.get_report_by_id(report_id)
        report_row.btn_more.click()
        report_row.btn_ddl_delete.click()

    def setup_component(self, report_id):
        self.report_id = report_id
        if report_id not in self.txt_message.text:
            self.open(report_id)

    def confirm(self):
        self.btn_confirm.click()
        self.wait_for.presence_of_element_located(until=False)
        self.ancestor.notification.wait_for.visibility_of_element_located(5)
        return self.ancestor.notification.txt_message.text
