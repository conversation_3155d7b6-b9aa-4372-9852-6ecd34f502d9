from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON>path, CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, CheckBox, TextBlock, Input
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(Xpath(".//h2[text()='Select accounts you want to include in your single report']/../.."))
class SingleReportComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_back = Button(Xpath(".//button[contains(@class, 'StyledXLButton-XLBackButton')]"))
        self.btn_next = Button(Xpath(".//button[text()='Next']"))

    @step_decorator('WEB: Open SingleReportComponent')
    def open_actions(self):
        self.ancestor.btn_single_report.click()

    @step_decorator('WEB - SingleReportComponent: select accounts {1}')
    def select_accounts(self, account_list):
        for acc in account_list:
            check_box = CheckBox(Xpath(f".//div[text()='{acc}']/div[@role='checkbox']"))
            check_box.ancestor = self
            check_box.check()
        self.btn_next.click()


@find_by(Xpath(".//h2[text()='Select the time-period of your single report']/../.."))
class SingleReportTimePeriodComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.accounts = None
        self.btn_next = Button(Xpath(".//button[text()='Next']"))
        self.btn_back = Button(Xpath(".//button[contains(@class, 'StyledXLButton-XLBackButton')]"))

    @step_decorator('WEB: Open SingleReportTimePeriodComponent')
    def open_actions(self, accounts):
        self.accounts = accounts
        self.ancestor.single_report.open()
        self.ancestor.single_report.select_accounts(self.accounts)

    def select_time_period(self, time_period):
        btn_period = Button(Xpath(f".//button/div[text()='{time_period}']/.."))
        btn_period.ancestor = self
        btn_period.click()
        self.btn_next.click()


class EmailListStructure:
    def __init__(self):
        self.email = TextBlock(CssSelector('div'))
        self.delete = Button(CssSelector("button"))


@find_by(Xpath(".//div[contains(@class, 'DeliveryChannel')]/.."))
class SingleReportDeliveryChannelComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.accounts = []
        self.time_period = None
        self.btn_download = Button(Xpath(".//div[text() = 'Download']"))
        self.btn_email = Button(Xpath(".//div[text() = 'Email']"))
        self.inp_email = Input(CssSelector('input[placeholder="Add Email Address"]'))
        self.btn_add_email = Button(Xpath(".//button[text() = 'Add Email']"))
        self.email_list = Table(Xpath(".//div[contains(@class, 'EmailsList')]/div"), EmailListStructure)
        self.btn_back = Button(Xpath(".//button[contains(@class, 'StyledXLButton-XLBackButton')]"))
        self.btn_send = Button(Xpath(".//button[text()='Send']"))
        self.btn_close = Button(Xpath(".//button[text()='Close']"))

    @step_decorator('WEB: Open SingleReportDeliveryChannelComponent')
    def open_actions(self, accounts, time_period=None):
        self.time_period = time_period
        self.accounts = accounts
        self.ancestor.single_report_time.open(self.accounts)
        self.ancestor.single_report_time.select_time_period(self.time_period)

    def close(self):
        self.btn_close.click()

    def download(self):
        self.btn_download.click()
        self.btn_close.click()

    def send_email(self, email_list):
        for email in email_list:
            self.inp_email.send_keys(email)
            self.btn_add_email.click()
        self.btn_send.click()
        self.btn_close.exists(wait_time=10)
        self.btn_close.click()
