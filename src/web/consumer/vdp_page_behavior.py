import re

import names
from pytest_check import check_func
from selenium.common.exceptions import TimeoutException

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON>elector, Xpath, Id
from kom_framework.src.web.data_types.element_list_types import <PERSON><PERSON><PERSON>
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>lock, Button, SelectExtended, Input, Image, Spinner
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.rest_api.entities.vehicle.vehicle_entity import Info, VehicleEntity
from src.utils.decorators import step_decorator
from src.web.consumer import OwnershipType, default_phone, VDPVersion, get_vdp_version
from src.web.consumer.desktop.cbo.sign_up import SignUp
from src.web.consumer.desktop.vdp.contact_us import ContactUs
from src.web.consumer.desktop.vdp.images import Images, SlidesFullScreen
from src.web.consumer.desktop.vdp.request_test_drive_component import TestDriveForm
from src.web.consumer.desktop.vdp.unlock_best_price_PII_component import UnlockBestPricePII
from src.web.entities.lead_entity import WebLeadEntity
from src.web.entities.trade_in_entity import TradeInEntity


class OpenVDPFrom:
    SHOWROOM = 1
    CHAT = 2
    CBO = 3


class SimilarVehicleStructure:
    def __init__(self):
        self.image = Image(Xpath('.//p[contains(@class,"VehicleImage")]'))
        self.model = TextBlock(Xpath(".//p[contains(@class,'VehicleName')]"))
        self.description = TextBlock(CssSelector('.vdp-similar-cars__slider-desc'))
        self.txt_title = TextBlock(CssSelector("[data-testid='showroom-vehicle-card__year']"))
        self.txt_locker_price = TextBlock(CssSelector("[data-testid='showroom-vehicle-card__price-locked']"))
        self.btn_get_best_price = Button(Xpath(".//button[text()='Get best price']"))
        self.txt_call_for_price = TextBlock(
            Xpath(".//div[contains(@data-testid,'showroom-price')]/div | .//span[contains(text(),'Call for price')]"))

    @step_decorator("WEB - SimilarVehicleStructure: get price")
    def get_price(self):
        return int(self.txt_locker_price.text.replace("$", "").replace(",", "")) \
            if self.txt_locker_price.exists() else self.txt_call_for_price.text

    @step_decorator("WEB - SimilarVehicleStructure: get vehicle year")
    def get_year(self):
        return int(self.txt_title.text.split(" ")[0])

    @step_decorator("WEB - SimilarVehicleStructure: get vehicle make")
    def get_make(self):
        return self.txt_title.text.split(" ")[1]


class VDP3PageBehaviour:

    def __init__(self, consumer_name=None, stock=None, consumer=None, open_from=None):
        self.version = get_vdp_version()
        if self.version == VDPVersion.V4:
            self.txt_miles = TextBlock(Xpath(".//p[contains(normalize-space(),'miles')]"))
            self.txt_payment_widget = Button(Xpath(".//div[contains(@class,'PriceWidgetv2')]//div[@role='tooltip']"))
            self.anl_features_tabs_body = AnyList(Xpath(".//h2[text()='Standard Features']/../div/ul"))
            self.txt_trade_in = TextBlock(Xpath(".//div[contains(@class,'TradeInCard__InnerWrapper')]"))
        else:
            self.txt_payment_widget = TextBlock(Xpath(".//div[contains(@class,'PriceWidget__UnlockedContainer')]/p"))
            self.txt_miles = TextBlock(Xpath(".//h4[contains(normalize-space(),'miles')]"))
            self.anl_features_tabs_body = AnyList(Xpath("//div[contains(@class,'StandardFeaturesV3__Tab')]/ul"))
            self.txt_trade_in = TextBlock(Xpath(".//div[contains(@class,'TradeInStatus__StatusContainer')]"))

        self.open_from = open_from
        self.consumer_name = consumer_name
        self.stock = stock
        self.consumer = consumer
        self.trade_in = TradeInComponent(self)
        self.payments = Payments(self)
        self.trade_in_report = TradeInReportComponent(self)
        self.modal = VDPModal(self)
        self.gallery = VDPGallery(self)
        self.mini_bar = VDPMiniBar(self)
        self.unlock_price_pii = UnlockBestPricePII(self)
        self.images = Images(self)
        self.slides_full_screen = SlidesFullScreen(self)
        self.test_drive_form = TestDriveForm(self)
        self.contact_us_form = ContactUs(self)
        self.sign_up = SignUp(self)
        self.txt_exterior = TextBlock(CssSelector(".about-vehicle__row-color:nth-child(1)"))
        self.txt_interior = TextBlock(CssSelector(".about-vehicle__row-color:nth-child(2)"))
        self.txt_exterior_color = TextBlock(CssSelector(".about-vehicle__row-color:nth-child(1) span"))
        self.txt_interior_color = TextBlock(CssSelector(".about-vehicle__row-color:nth-child(2) span"))
        self.txt_title = TextBlock(CssSelector('.about-vehicle__row-title'))
        self.img_carfax = Image(CssSelector("img[alt='carfax logo']"))
        self.txt_stock = TextBlock(Xpath(".//span[contains(text(),'Stock')]/.."))
        self.txt_vin = TextBlock(Xpath(".//span[contains(text(),'VIN')]/../span[last()]"))
        self.lst_images = AnyList(CssSelector('.vdp-gallery__item'))
        self.lst_features = AnyList(CssSelector('.vdp-features__item'))
        self.btn_view_about = Button(Xpath(".//button[text()='Read More']"))
        self.btn_view_features = Button(CssSelector(".vdp-features__button"))
        self.txt_about = TextBlock(CssSelector('.vdp-description__col p'))
        self.txt_body = TextBlock(CssSelector('[data-testid="key-feature--Body"] p:nth-child(2)'))
        self.txt_drivetrain = TextBlock(CssSelector('[data-testid="key-feature--Drivetrain"] p:nth-child(2)'))
        self.txt_transmission = TextBlock(CssSelector('[data-testid="key-feature--Transmission"] p:nth-child(2)'))
        self.txt_engine = TextBlock(CssSelector('[data-testid="key-feature--Engine"] p:nth-child(2)'))
        self.txt_mpg = TextBlock(CssSelector('[data-testid="key-feature--Miles-Per-Gallon"] p:nth-child(2)'))
        self.btn_add_trade_in = Button(Xpath(".//button[contains(text(),'Add Trade-In')]"))
        self.btn_apply_trade_in = Button(Xpath(".//button[text()='Apply Trade-In']"))
        self.tbl_similar_vehicle = Table(CssSelector("[data-testid='showroom-vehicle-card']"), SimilarVehicleStructure)
        self.txt_price = TextBlock(Xpath(".//div[contains(@class,'PriceCard__Price-')]"))
        self.txt_locked_price = TextBlock(Xpath(".//span[contains(@class,'Price__BasePrice-')]"))
        self.spn_preloader = Spinner(CssSelector("[data-testid='spinner']"))
        self.anl_features_tabs = AnyList(
            Xpath('.//div[contains(@class,"CategoryInput")]'))
        self.btn_unlock_price = Button(Xpath(".//button[contains(text(),'Unlock Best Price')]"))
        self.txt_unlocked_price = TextBlock(Xpath(".//div[contains(@class,'PriceCard__Price')]"))
        self.btn_view_next_button = Button(
            Xpath(".//div[contains(@class,'SimilarVehiclesContainer')]//button[@aria-label='View Next']"))
        self.btn_view_previous_button = Button(
            Xpath(".//div[contains(@class,'SimilarVehiclesContainer')]//button[@aria-label='View Previous']"))
        self.btn_buy_now = Button(Xpath(".//button[text()='Buy Now']"))
        self.btn_explore_payments = Button(Xpath(".//button[text()='Explore Payments']"))
        self.btn_contact_us = Button(Xpath(".//button[text()='Contact Us']"))
        self.btn_view_result = Button(Xpath(".//button[text()='View Results']"))
        self.btn_get_directions = Button(Xpath(".//a[text()='Get Directions']"))
        self.btn_test_drive = Button(Xpath(".//button[contains(@class,'TestDriveTrigger')]"))
        self.btn_full_screen = Button(Xpath(".//button[text()='Show Full Screen']"))
        self.ddl_shop = SelectExtended(link_locator=Id("select-vehicle-type-filter"),
                                       option_list_locator=CssSelector("#select-vehicle-type-filter-menu button"))
        self.btn_add_favorites = Button(CssSelector('[data-testid="showroom-vehicle-card__button--favorite"]'))
        self.btn_saved_cars = Button(Xpath("//button[contains(@class,'SavedCars')]"))
        self.btn_close = Button(CssSelector(".close-btn"))
        self.tooltip = Button(Xpath("//div[contains(@class,'Tooltip__ContentWrapper')]"))
        self.btn_profile_name = Button(Xpath(".//button[@aria-label='Toggle profile menu']/span"))
        self.btn_profile_details = Button(
            Xpath(".//button[@data-analytics='menu-interaction:menu-showroom-profile-details-button']"))
        self.txt_trade_in_status = TextBlock(Xpath(".//div[contains(@class,'TradeInStatus__StatusContainer')]"))

    @step_decorator("WEB - NewVDPPage: Get title")
    def get_title(self):
        return self.txt_title.text

    @step_decorator('WEB - NewVDPPage: Get tooltip text')
    def get_tooltip_text(self):
        if self.version == VDPVersion.V4:
            self.action_chains.move_to_element(self.txt_payment_widget.find()).perform()
            return self.tooltip.text
        else:
            return self.txt_payment_widget.text

    @step_decorator("WEB - NewVDPPage: Get miles")
    def get_miles(self):
        if self.version == VDPVersion.V4:
            return self.txt_miles.text.split("/")[2].strip().split(" ")[0]
        else:
            return self.txt_miles.text.split(" ")[0]

    @step_decorator("WEB - NewVDPPage: Get payment widget text")
    def get_payment_widget_text(self):
        if self.version == VDPVersion.V4:
            return self.txt_payment_widget.get_attribute("aria-label")
        else:
            return self.txt_payment_widget.text

    @step_decorator("WEB - NewVDPPage: Check CarFax label")
    def carfax_present(self):
        return self.img_carfax.exists(2)

    @step_decorator("WEB - NewVDPPage: Get info")
    def get_info(self):
        info = Info()
        if self.txt_body.exists():
            info.body = self.txt_body.text
        if self.txt_engine.exists():
            info.engine = self.txt_engine.text
        if self.txt_mpg.exists():
            info.mpg = self.txt_mpg.text.replace(" MPG", "")
        if self.txt_drivetrain.exists():
            info.drivetrain = self.txt_drivetrain.text
        if self.txt_transmission.exists():
            info.transmission = self.txt_transmission.text
        return info

    @step_decorator("WEB - NewVDPPage: Apply Trade-in")
    def apply_trade_in(self, vehicle: TradeInEntity):
        self.trade_in.open()
        self.trade_in.apply_trade_in(vehicle)
        self.payments.unlock_payments()
        self.trade_in_report.btn_apply_trade_in.js.scroll_into_view()
        self.trade_in_report.btn_apply_trade_in.click()
        self.spn_preloader.wait_for_appear_and_disappear()

    @step_decorator("WEB - NewVDPPage: Get stock")
    def get_stock(self):
        return self.txt_stock.text.split(" ")[1]

    @step_decorator("WEB - NewVDPPage: Get vin")
    def get_vin(self):
        return self.txt_vin.text

    @step_decorator("WEB - NewVDPPage: Get similar vehicles list")
    def get_similar_vehicles(self):
        similar_vehicles = []
        if self.tbl_similar_vehicle.exists(wait_time=5):
            for i in range(self.tbl_similar_vehicle.size):
                vehicle = self.tbl_similar_vehicle.get_row_by_index(i)
                if not vehicle.model.is_displayed():
                    self.btn_view_next_button.click()
                    self.btn_view_previous_button.wait_for.visibility_of_element_located(wait_time=5)
                similar_vehicles.append({"year": vehicle.get_year(),
                                         'model': vehicle.model.text,
                                         'make': vehicle.get_make(),
                                         "price": vehicle.get_price()})
        return similar_vehicles

    @step_decorator("WEB - NewVDPPage: Check similar vehicles")
    @check_func
    def check_similar_vehicles(self, vehicle: VehicleEntity):
        error = []
        similar_vehicles = self.get_similar_vehicles()
        assert similar_vehicles, "No similar vehicles were found"
        for v in similar_vehicles:
            if v["year"] < int(vehicle.year) - 1:
                error.append(f"Year of {v['make']} is lower then {vehicle.year} - 1")
            if vehicle.make not in v['make']:
                error.append(f"{vehicle.make} is not matching {v['make']}")
            if vehicle.model not in v['model']:
                error.append(f"{vehicle.model} is not matching {v['model']}")
            # if not (vehicle.price * 0.8 <= v['price'] <= vehicle.price * 1.2): # price is not included based Sandeep comment
            #     error.append(f"Price {v['price']} is not in {vehicle.price * 0.8} - {vehicle.price * 1.2} range")
        assert not error, error

    @step_decorator("WEB - NewVDPPage: View details of random similar vehicle")
    def open_similar_vehicle(self):
        import random
        index = random.randrange(self.tbl_similar_vehicle.size)
        vehicle = self.tbl_similar_vehicle.get_row_by_index(index)
        info = {"name": vehicle.model.text_content,
                "price": int(vehicle.description.text_content.split(" • ")[0].replace("$", "").replace(",", ""))}
        vehicle.model.js.scroll_into_view()
        vehicle.model.js.click()
        self.wait_while_text_exists("Preparing your vehicle.")
        try:
            self.exists(3)
        except TimeoutException:
            Log.warning("New VDP Page was not found")
        return info

    @step_decorator("WEB - NewVDPPage: Open About pop up, get content and close")
    def view_vehicle_details(self):
        self.btn_view_about.click()
        assert self.modal.exists(3)
        self.modal.txt_title.wait_for.text_to_be_present_in_element("From Gubagoo Inc.")
        content = self.modal.txt_content.text
        self.modal.close()
        return content

    @step_decorator("WEB - NewVDPPage: Open Features pop up, get content and close")
    def view_all_features(self):
        self.btn_view_features.click()
        assert self.modal.exists(3)
        self.modal.txt_title.wait_for.text_to_be_present_in_element("All Features")
        content = self.modal.txt_content.text
        self.modal.close()
        return content

    @step_decorator("WEB - NewVDPPage: unlock best price")
    def unlock_best_price(self, lead_info: WebLeadEntity):
        pii_form = self.unlock_price_pii.open()
        pii_form.get_best_price(lead_info)

    @step_decorator("WEB - NewVDPPage: open VR")
    def open_vr(self):
        if self.btn_buy_now.exists():
            self.btn_buy_now.click()
        else:
            self.btn_explore_payments.click()
        self.consumer.cbo.exists(wait_time=10)
        from src.web.consumer.blur_component import BlurPiiCountryComponent
        BlurPiiCountryComponent(self.consumer).next()
        self.consumer.cbo.payment.exists(wait_time=10)
        self.consumer.cbo.payment.spn_skeleton.wait_for_appear_and_disappear(wait_until_appears=2)
        return self.consumer.cbo

    @step_decorator("WEB - NewVDPPage: remove trade-in")
    def remove_trade_in(self):
        self.btn_view_result.click()
        if self.consumer.cbo.trade_in.final_report.btn_cont_without_trade_in.exists():
            self.consumer.cbo.trade_in.final_report.btn_cont_without_trade_in.click(wait_time=3)
        else:
            self.consumer.cbo.trade_in.final_report.btn_remove.click(wait_time=3)
        self.txt_trade_in_status.wait_for.presence_of_element_located(wait_time=3, until=False)

    @step_decorator("WEB - NewVDPPage: remove trade-in")
    def add_to_favorites(self):
        if not eval(self.btn_add_favorites.get_attribute("aria-pressed").title()):
            self.btn_add_favorites.click()

    @step_decorator('WEB - ConsumerPage: Send a {1} message')
    def send_message(self, message):
        self.switch_to.default_content()
        self.consumer.chat_component.expand()
        self.consumer.chat_component.send_message(message)
        self.consumer.iframe_new_vdp.switch_to()

    @step_decorator("WEB - ShowRoomComponent: Open PII form")
    def open_pii(self):
        self.btn_profile_name.js.scroll_into_view()
        self.btn_profile_name.js.scroll_into_view('false')
        self.btn_profile_name.click()
        self.btn_profile_details.exists(3)
        self.btn_profile_details.click()
        assert self.sign_up.exists(3), "PII modal wasn't opened"


@find_by(CssSelector(".vdp-mini-top-bar--show"))
class VDPMiniBar(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_title = TextBlock(CssSelector(".vdp-mini-top-bar__title--name"))
        self.txt_desc = TextBlock(CssSelector(".vdp-mini-top-bar__title--desc"))
        self.txt_tag = TextBlock(CssSelector(".vdp-mini-top-bar__title--name-type"))

    def open_actions(self):
        self.ancestor.tbl_similar_vehicle.get_row_by_index(0).description.js.scroll_into_view()
        self.ancestor.js.scroll_down()

    @step_decorator("WEB - VDPMiniBar: Get tag")
    def get_tag(self):
        return self.txt_tag.text

    @step_decorator("WEB - VDPMiniBar: Get title")
    def get_title(self):
        return self.txt_title.text


@find_by(CssSelector('[aria-label="Additional information from Gubagoo Inc."]'))
class VDPModal(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_title = TextBlock(CssSelector("h2"))
        self.btn_close = Button(CssSelector("button"))
        self.txt_content = TextBlock(CssSelector("p"))

    def open_actions(self):
        pass

    @step_decorator("WEB - VDPModal: Get title")
    def get_title(self):
        return self.txt_title.text

    @step_decorator("WEB - VDPModal: Close")
    def close(self):
        self.btn_close.click()


@find_by(Xpath(".//div[@class='vdp-gallery-carousel']/.."))
class VDPGallery(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_close = Button(CssSelector("i.modal__close"))
        self.lst_thumbnails = AnyList(CssSelector('.vdp-gallery-carousel__thumbnails img'))
        self.lst_images = AnyList(CssSelector('.vdp-gallery-carousel__images img'))

    def open_actions(self):
        self.ancestor.lst_images()

    @step_decorator("WEB - VDPGallery: Close")
    def close(self):
        self.btn_close.click()


@find_by(Xpath("//div[contains(@class,'StandaloneTradeIn__Container')]"))
class TradeInComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.ddl_year = SelectExtended(link_locator=CssSelector("input[placeholder='Select year']"),
                                       option_list_locator=Xpath(".//ul/li"),
                                       search_input_locator=CssSelector("input[placeholder='Select year']"))
        self.ddl_make = SelectExtended(link_locator=CssSelector("input[placeholder='Select make']"),
                                       option_list_locator=Xpath(".//ul/li"),
                                       search_input_locator=CssSelector("input[placeholder='Select make']"))
        self.ddl_model = SelectExtended(link_locator=CssSelector("input[placeholder='Select model']"),
                                        option_list_locator=Xpath(".//ul/li"),
                                        search_input_locator=CssSelector("input[placeholder='Select model']"))
        self.ddl_trim = SelectExtended(link_locator=CssSelector("input[placeholder='Select trim']"),
                                       option_list_locator=Xpath(".//ul/li"),
                                       search_input_locator=CssSelector("input[placeholder='Select trim']"))
        self.ddl_lender = SelectExtended(link_locator=CssSelector("[name='lenderSelect']"),
                                         option_list_locator=Xpath(".//input[@name='lenderSelect']/../../ul/li"),
                                         search_input_locator=CssSelector("[name='lenderSelect']"))
        self.inp_mileage = Input(Xpath(".//input[@placeholder='Mileage']"))
        self.btn_financed = Button(Xpath(".//div[contains(@class,'btn-select') and text()='Financed']"))
        self.inp_loan = Input(CssSelector("#estimateInput"))
        self.btn_see_trade_in_value = Button(CssSelector("[data-testid='questions-screen-next-button']"))
        self.btn_close = Button(CssSelector('.modal__close'))
        self.btn_make_model = Button(Xpath(".//div[contains(text(),'Make/Model')]"))
        self.btn_find_vehicle = Button(CssSelector("[data-testid='find-vehicle-btn']"))
        self.btn_making_payments_yes = Button(Xpath(".//label[@for='makingPayments-yes-id']/.."))
        self.btn_making_payments_no = Button(Xpath(".//label[@for='makingPayments-no-id']/.."))

    def open_actions(self):
        self.ancestor.switch_to.default_content()
        self.ancestor.consumer.iframe_new_vdp.switch_to()
        self.ancestor.btn_add_trade_in.click()

    @step_decorator("WEB - TradeInComponent: Click OK button")
    def click_ok(self):
        self.btn_ok.click()

    @step_decorator("WEB - TradeInComponent: Wait for loading")
    def wait_for_loading(self):
        self.ancestor.wait_for_text_exists("Loading", wait_time=3)
        self.ancestor.wait_while_text_exists("Loading", wait_time=60)

    @step_decorator('WEB - TradeInComponent:  Apply Trade-in')
    def apply_trade_in(self, vehicle: TradeInEntity):
        self.btn_make_model.click()
        if vehicle.year:
            self.ddl_year.select_item_by_text(vehicle.year)
        if vehicle.make:
            self.ddl_make.select_item_by_text(vehicle.make)
        if vehicle.model:
            self.ddl_model.select_item_by_text(vehicle.model)
        if vehicle.trim:
            self.ddl_trim.select_item_by_text(vehicle.trim)
        self.btn_find_vehicle.click()
        if vehicle.mileage:
            self.inp_mileage.wait_for.presence_of_element_located(wait_time=5).send_keys(vehicle.mileage)
        self.provide_accident_condition(vehicle)
        self.provide_mechanical_condition(vehicle)
        if vehicle.ownership:
            if vehicle.ownership == OwnershipType.OWNED:
                self.btn_making_payments_no.click()
            else:
                self.btn_making_payments_yes.click()
                if vehicle.loan:
                    self.inp_loan.clear_and_send_keys(vehicle.loan)
                elif vehicle.lender:
                    self.ddl_lender.select_item_by_text(vehicle.lender.lender)
                    if vehicle.lender.ssn:
                        self.inp_ssn.send_keys(vehicle.lender.ssn)
                else:
                    btn_lease = Button(Xpath("//div[contains(text(),'Lease')]"))
                    btn_lease.ancestor = self
                    btn_lease.click()
        self.btn_see_trade_in_value.js.click()

    @step_decorator('WEB - TradeInComponent: Provide accident condition')
    def provide_accident_condition(self, vehicle: TradeInEntity):
        locator = "Yes" if vehicle.accidents else "No"
        btn_accidents = Button(Xpath(
            f".//p[@data-testid='has_had_accidents']/..//label[contains(text(),'{locator}')]"))
        btn_accidents.ancestor = self
        btn_accidents.js.click()
        if vehicle.accidents:
            if vehicle.accidents.airbags_deployed:
                self.btn_airbags_deployed.click()
            if vehicle.accidents.repair_costs:
                self.inp_repair_costs.clear_and_send_keys(vehicle.accidents.repair_costs)

    @step_decorator('WEB - TradeInComponent: Provide mechanical condition')
    def provide_mechanical_condition(self, vehicle: TradeInEntity):
        locator = "Yes" if vehicle.mechanical_issues else "No"
        btn_mechanical_issues = Button(Xpath(
            f".//p[@data-testid='has_had_mechanical_issues']/..//label[contains(text(),'{locator}')]"))
        btn_mechanical_issues.ancestor = self
        btn_mechanical_issues.js.click()
        if vehicle.mechanical_issues:
            for issue in vehicle.mechanical_issues:
                btn_issue = Button(Xpath(f'.//label[contains(text(),"{issue}")]'))
                btn_issue.ancestor = self
                btn_issue.click()


@find_by(Xpath('//h2[text()="Your Trade-in value is ready! Unlock it now"]/../../../..'))
class Payments(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.last_name = names.get_last_name()
        self.phone = default_phone
        self.email = f"{ancestor.consumer_name}.{self.last_name}-<EMAIL>"
        self.inp_full_name = Input(CssSelector('[placeholder="Enter name"]'))
        self.inp_phone = Input(CssSelector('[placeholder="Enter phone"]'))
        self.inp_email = Input(CssSelector('[placeholder="Enter email"]'))
        self.inp_submit = Button(CssSelector('[type="submit"]'))

    def open_actions(self):
        pass

    @step_decorator('WEB - Payments: unlock payments')
    def unlock_payments(self):
        self.inp_full_name.send_keys(f"{self.ancestor.consumer_name} {self.last_name}")
        self.inp_phone.send_keys(self.phone)
        self.inp_email.send_keys(self.email)
        self.inp_submit.click()
        self.ancestor.spn_preloader.wait_for_appear_and_disappear()


@find_by(Xpath('.//h1[text()="Your vehicle\'s trade-in estimate"]/../../../../../..'))
class TradeInReportComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_price_range = TextBlock(CssSelector("[data-testid='trade-in-calculation']"))
        self.txt_price = TextBlock(CssSelector('[data-testid="trade-in-infobox"]'))
        self.txt_middle_price = TextBlock(Xpath('.//p[contains(text(),"Trade-In Value")]'))
        self.txt_price_calc = TextBlock(CssSelector('.trade-in-report__net-calc'))
        self.btn_remove = Button(CssSelector(".vdp-trade-in__remove"))
        self.btn_edit = Button(CssSelector('[data-testid="trade-in-edit-appraisal"]'))
        self.btn_apply_trade_in = Button(Xpath(".//button[text()='Apply Trade-In']"))
        self.btn_continue_with_out_trade_in = Button(Xpath(".//button[text()='Continue without trade-in']"))

    def open_actions(self):
        pass

    @step_decorator('WEB - TradeInReportComponent: Get Trade-in value')
    def get_trade_in_value(self):
        # self.wait_for.presence_of_element_located(wait_time=60)
        assert self.txt_price.exists()
        return self.txt_price.text

    @step_decorator('WEB - TradeInReportComponent: Remove Trade-in')
    def remove(self):
        self.wait_for.presence_of_element_located(wait_time=60)
        self.btn_remove.js.click()

    @step_decorator('WEB - TradeInReportComponent: Get Trade-in values')
    def get_trade_in_values(self):
        self.wait_for.presence_of_element_located(wait_time=60)
        assert self.txt_price_range.exists(5)
        prices = self.txt_price_range.text.split(" — ")
        assert prices[0].startswith("$") and prices[1].startswith("$"), "$ sing is missing"
        prices = [int(p[1:].replace(",", "")) for p in prices]
        return prices

    @step_decorator('WEB - TradeInReportComponent: Get Trade-in middle price')
    def get_trade_in_middle_price(self):
        self.wait_for.presence_of_element_located(wait_time=60)
        assert self.txt_middle_price.exists()
        return int(self.txt_middle_price.text.split("Trade-In Value$")[1].replace(",", ""))

    @step_decorator('WEB - TradeInReportComponent: Get price calculation')
    def get_price_calc(self):
        if self.txt_price_calc.exists():
            match = re.search(r"\$(?P<trade_in>[\w,]*) Trade-In – \$(?P<payoff>[\w,]*) Payoff",
                              self.txt_price_calc.text)
            return float(match.group('trade_in').replace(",", "")), float(match.group('payoff').replace(",", ""))
