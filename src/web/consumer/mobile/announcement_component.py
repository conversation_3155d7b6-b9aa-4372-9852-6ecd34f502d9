from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON>ton, TextBlock
from kom_framework.src.web.support.page_factory import find_by

from src.utils.decorators import step_decorator


@find_by(CssSelector("div.gg-announcement__top"))
class AnnouncementComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_close = Button(CssSelector("div.gg-announcement__top--close"))
        self.btn_modal = Button(CssSelector("a.gg-announcement__top--cta"))
        self.txt_content = TextBlock(CssSelector("div.gg-announcement__top--content"))

    @step_decorator('WEB - AnnouncementComponent: Open')
    def open_actions(self):
        self.ancestor.btn_announcement.click()

    @step_decorator('WEB - AnnouncementComponent: Get content html')
    def get_content_html(self):
        return self.txt_content.get_attribute('innerHTML')

    @step_decorator('WEB - AnnouncementComponent: Get modal button label')
    def get_button_label(self):
        if self.btn_modal.exists():
            return self.btn_modal.text

    @step_decorator('WEB - AnnouncementComponent: Close')
    def close(self):
        self.btn_close.click()

    @step_decorator('WEB - AnnouncementComponent: Click modal button')
    def modal_button_click(self):
        self.btn_modal.click()

    @step_decorator('WEB - AnnouncementComponent: Is scrollable vertical')
    def is_scrollable_vertical(self):
        return int(self.find().get_attribute("scrollHeight")) > int(self.find().get_attribute("offsetHeight"))

    @step_decorator('WEB - AnnouncementComponent: Is scrollable horizontal')
    def is_scrollable_horizontal(self):
        return int(self.find().get_attribute("scrollWidth")) > int(self.find().get_attribute("clientWidth"))
