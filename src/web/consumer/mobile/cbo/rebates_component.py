from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_types import Button
from kom_framework.src.web.support.page_factory import find_by


@find_by(CssSelector('[data-testid="current-step-rebates"]'))
class RebatesComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)

        self.btn_back = Button(CssSelector("button.btn-grey"))
        self.btn_next = Button(CssSelector('[data-testid="cbo-nav-bottom"]'))

    def open_actions(self):
        payment = self.ancestor.payment.open()
        if not any((payment.selected_lease, payment.selected_finance, payment.selected_cash)):
            payment.select_finance_option(term="72")
        payment.btn_next.click()
