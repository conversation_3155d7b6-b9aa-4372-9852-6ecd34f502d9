from selenium.common.exceptions import TimeoutException

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Css<PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_list_types import <PERSON><PERSON>ist
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>lock, Button, SelectExtended, Input, Check<PERSON>ox, Spinner, \
    IFrame, Link
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.consumer.blur_component import BlurDownPaymentComponent, BlurPiiSinglePage
from src.web.consumer.desktop.cbo.auto_hub_widget_component import AutoHubWidget
from src.web.consumer.desktop.cbo.employee_pricing_component import EmployeePricing
from src.web.consumer.desktop.cbo.kbb_ico_widget_component import KBBICOWidget
from src.web.consumer.get_pre_qualified_component import GetPreQualified, CreditScale
from src.web.entities.cbo_payment_option_entity import CBOPaymentOptionEntity
from src.web.entities.pii_entity import PIIEntity
from src.web.entities.trade_in_entity import TradeInProvider


class CboCTAButton:
    CBO_UNLOCK_PAYMENTS = ".cbo-button.cbo-unlock[data-flow='payments']"
    CBO_PRIMARY_PAYMENTS = ".cbo-button.cbo-primary[data-flow='payments']"
    CBO_TRADE_IN = ".cbo-button[data-flow='trade_in']"
    CBO_CREDIT_CHECK = ".cbo-button[data-flow='credit_check']"
    BUY_ONLINE = ".cbo-button.cbo-primary[data-flow='buy_online']"


class CBOPageBehaviour:

    def __init__(self, consumer_name=None, vin=None, consumer=None, pii_entity: PIIEntity = None,
                 trade_in_provider: TradeInProvider = None, open_how: CboCTAButton = None):
        self.consumer_name = consumer_name
        self.vin = vin
        self.consumer = consumer
        self.pii_entity = pii_entity
        self.trade_in_provider = trade_in_provider
        self.open_how = open_how
        self.get_pre_qualified = GetPreQualified(self)
        self.credit_scale = CreditScale(self)
        self.start_blur = BlurDownPaymentComponent(self)
        self.single_blur = BlurPiiSinglePage(self)
        self.appointment = AppointmentComponent(self)
        self.auto_hub_widget = AutoHubWidget(consumer=self)
        self.kbb_ico_widget = KBBICOWidget(consumer=self)
        self.employee_pricing = EmployeePricing(self)

        self.btn_close = Button(CssSelector(".close-btn"))
        self.spn_preloader = Spinner(CssSelector('[data-testid="spinner"]'))
        self.btn_garage = Button(CssSelector(".garage-button"))

        self.btn_start_with_payment = Button(Xpath(
            ".//button[contains(@class, 'step-selector__item-button') and contains(text(), 'Start with payments')]"))
        self.btn_start_with_credit = Button(Xpath(
            ".//button[contains(@class, 'step-selector__item-button') and contains(text(), 'Start with credit')]"))
        self.btn_start_with_trade_in = Button(Xpath(
            ".//button[contains(@class, 'step-selector__item-button') and contains(text(), 'Start with trade-in')]"))

        self.lst_error = AnyList(CssSelector(".notification.notification--error span"))

        self.txt_payment_tab_value = TextBlock(
            Xpath(".//div[contains(@class, 'cbo-nav__tab-title') and contains(text(), 'Payment')]/span[2]"))
        self.txt_notification_error = TextBlock(CssSelector(".notification--error"))
        self.iframe_autohub_widget = IFrame(CssSelector('#autohubWidget iframe'))
        self.iframe_kbb_ico_widget = IFrame(CssSelector('#ico'))
        self.lnk_vdp_link = Link(Xpath("//div[contains(@class,'HomePage__Wrapper')]/h1/a"))

    @step_decorator('WEB - CBOPage: Set payment')
    def set_payment(self, downpayment=None, term=None, payment_type=None):
        Log.info(f'WEB - CBOPage: Set payment: downpayment {downpayment}, term {term}, payment_type {payment_type}')
        self.downpayment = downpayment
        self.term = term
        self.payment_type = payment_type

    @step_decorator('WEB - CBOPage: Close CBO')
    def close_cbo(self):
        Log.info('WEB - CBOPage: Close CBO')
        self.btn_close.exists(3)
        self.btn_close.click()

    @step_decorator('WEB - CBOPage: submit get pre-qualified form')
    def submit_get_pre_qualified_form(self, pii_entity: PIIEntity):
        self.get_pre_qualified.fill_form(pii_entity=pii_entity)
        self.credit_scale.submit()

    @step_decorator("WEB - CBOPage: Get VIN")
    def get_vin(self):
        return self.consumer.cbo.vin

    def get_cta_button(self, open_how: CboCTAButton = CboCTAButton.CBO_UNLOCK_PAYMENTS):
        cbo_button = Button(CssSelector(f"{open_how}[data-vin='{self.vin}']"))
        cbo_button.ancestor = self.consumer
        return cbo_button

    def get_uuid(self):
        return self.consumer.iframe_new_vdp.get_attribute("src").split("uuid=")[1].split("&")[0]

    @step_decorator('WEB - CBOPage: open payment page')
    def open_payment(self, cbo_payment_option_entity: CBOPaymentOptionEntity = None):
        self.payment.set_cbo_payment_option_entity(cbo_payment_option_entity)
        payment = self.payment.open()
        if cbo_payment_option_entity:
            payment.select_payment_options()
        return payment


@find_by(CssSelector(".modal-credit"))
class StartCreditComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)

        self.inp_first_name = Input(CssSelector("input[name='first_name']"))
        self.inp_last_name = Input(CssSelector("input[name='last_name']"))
        self.inp_street = Input(CssSelector("input[name='street']"))
        self.inp_city = Input(CssSelector("input[name='city']"))
        self.inp_phone = Input(CssSelector("input[name='phone']"))
        self.ddl_state = SelectExtended(CssSelector("select[name='state']"))
        self.inp_zip = Input(CssSelector("input[name='zip']"))
        self.inp_email = Input(CssSelector("input[name='email']"))

        self.chk_term_and_condition = CheckBox(CssSelector("input[name='terms_and_conditions']"),
                                               attribute="checked",
                                               checked_value="true")

        self.btn_back = Button(CssSelector("button.btn-grey"))
        self.btn_unlock_payment = Button(CssSelector("button.btn-blue"))

    def open_actions(self):
        pass


@find_by(CssSelector(".credit-app__success"))
class CreditSuccessComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)

        self.btn_edit = Button(CssSelector("button.ca-success__edit"))
        self.btn_back = Button(CssSelector("button.btn-grey"))
        self.btn_next = Button(CssSelector("button.btn-blue"))

    def open_actions(self):
        pass


@find_by(Xpath("//div[contains(text(), 'Appointment')]/../../../.."))
class AppointmentComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_yes = Button(Xpath(".//div[@class='decision__text' and contains(text(), 'Yes')]"))
        self.btn_no = Button(Xpath(".//div[@class='decision__text' and contains(text(), 'No')]"))

    def open_actions(self):
        self.ancestor.time_saving.open()
        self.ancestor.time_saving.btn_next.click()
        try:
            self.ancestor.spn_preloader.wait_for_appear_and_disappear()
        except TimeoutException:
            Log.warning("Spinner for loading Credit application did not appear")
