import datetime
import random
import re

import dateutil
from allure import step
from pytz.reference import Eastern

from kom_framework.src.general import Log, non_zero_padded
from src.rest_api.services.leads import Leads
from src.utils.decorators import step_decorator
from src.web.chat_console import QRResponses
from src.web.consumer import TransportType, ai_wait_time, AIBotResponse, EPriceDecisionQRType
from src.web.entities.message_entity import MessageFactory


class AIBehaviour:

    def __init__(self, consumer_behaviour: 'ConsumerPageBehaviour'):
        self.consumer_behaviour = consumer_behaviour

    @step_decorator("WEB - ConsumerPage: Process lead")
    def process_lead(self, lead_info, response=AIBotResponse.VEHICLE_LEAD_FOLLOWUP()):
        Log.info("WEB - ConsumerPage: Process lead")
        self.consumer_behaviour.provide_full_name(lead_info.first_name, lead_info.last_name)
        self.consumer_behaviour.provide_phone_number(lead_info.phone if not lead_info.phone_to_send
                                                     else lead_info.phone_to_send)
        self.consumer_behaviour.provide_email(lead_info.email if lead_info.email_to_send is None
                                              else lead_info.email_to_send)
        self.consumer_behaviour.chat_component.wait_for_message(response, wait_time=ai_wait_time)
        self.consumer_behaviour.ai_close_chat(name=lead_info.first_name)
        with step("Check if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @step_decorator("WEB - ConsumerPage: Provide full PII")
    def provide_full_pii(self, lead_info, question=AIBotResponse.PII_REQUEST_NAME):
        Log.info(f"WEB - ConsumerPage: Provide full PII {lead_info.first_name} {lead_info.last_name}"
                 f" {lead_info.phone} {lead_info.email}")
        self.consumer_behaviour.chat_component.wait_for_message(question, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(
            f"{lead_info.first_name} {lead_info.last_name} {lead_info.phone} {lead_info.email}")

    @step_decorator("WEB - ConsumerPage: Provide full name {1} {2}")
    def provide_full_name(self, first_name, last_name):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.PII_REQUEST_NAME, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(f"{first_name} {last_name}")

    @step_decorator("WEB - ConsumerPage: Provide first name only {1}")
    def provide_first_name(self, first_name):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.PII_REQUEST_NAME, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(first_name)

    @step_decorator("WEB - ConsumerPage: Provide last name {1}")
    def provide_last_name(self, last_name, message=AIBotResponse.PII_REQUEST_LASTNAME):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(last_name)

    @step_decorator("WEB - ConsumerPage: Provide recall number {1}")
    def provide_recall_number(self, recall_number="",
                             question=AIBotResponse.RECALL_RECALL_NUMBER):
        self.consumer_behaviour.chat_component.wait_for_message(question, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(recall_number)

    @step_decorator("WEB - ConsumerPage: Provide vin number {1}")
    def provide_recall_vin(self, vin):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.RECALL_VIN_NUMBER, wait_time=ai_wait_time, typing=True)
        self.consumer_behaviour.send_message(vin)

    def ai_wait_for_recall_followup(self):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.RECALL_FOLLOWUP, wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Provide vehicle info {1}, {2}, {3}")
    def provide_vehicle_info(self, vehicle_year, vehicle_make, vehicle_model,
                             question=AIBotResponse.VEHICLES_YOUR_VEHICLE_INFO_REQUEST):
        self.consumer_behaviour.chat_component.wait_for_message(question, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(f"{vehicle_year} {vehicle_make} {vehicle_model}")

    @step_decorator("WEB - ConsumerPage: Provide vehicle info for service look up")
    def provide_vehicle_for_service_look_up(self, vehicles_found, source_input, vehicle):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_CUSTOMER_VEHICLES_FOUND_TEXT(vehicles_found, source_input), wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_CUSTOMER_VEHICLES_CHOOSE_TEXT, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(vehicle)

    @step_decorator("WEB - ConsumerPage: Select vehicle isn't listed option")
    def select_vehicle_not_listed(self, qr=True, question=AIBotResponse.VEHICLES_CUSTOMER_VEHICLES_CHOOSE_TEXT):
        self.consumer_behaviour.chat_component.wait_for_message(question, wait_time=ai_wait_time)
        if qr:
            self.consumer_behaviour.chat_component.select_quick_reply(qr_text="My vehicle isn't listed")
        else:
            self.consumer_behaviour.send_message("My vehicle isn't listed")

    @step_decorator("WEB - ConsumerPage: Provide vehicle model {1}")
    def provide_vehicle_model(self, vehicle_model, vehicle_make, qr=True, question=AIBotResponse.VEHICLE_MODEL_REQUEST):
        self.consumer_behaviour.chat_component.wait_for_message(question(vehicle_make), wait_time=ai_wait_time)
        if qr:
            self.consumer_behaviour.chat_component.select_quick_reply(vehicle_model)
        else:
            self.consumer_behaviour.send_message(vehicle_model)

    @step_decorator("WEB - ConsumerPage: Provide vehicle year {1}")
    def provide_vehicle_year_with_qr(self, vehicle_year, vehicle_make_model,
                                     question=AIBotResponse.VEHICLES_VEHICLE_YEAR_REQUEST):
        self.consumer_behaviour.chat_component.wait_for_message(question(vehicle_make_model), wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(vehicle_year)

    @step_decorator("WEB - ConsumerPage: Provide vehicle year {1}")
    def provide_vehicle_year(self, vehicle_year, vehicle_make_model,
                             question=AIBotResponse.VEHICLES_VEHICLE_YEAR_REQUEST):
        self.consumer_behaviour.chat_component.wait_for_message(question(vehicle_make_model), wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(vehicle_year)

    @step_decorator("WEB - ConsumerPage: Provide vehicle make {1}")
    def provide_vehicle_make(self, vehicle_make, vehicle_year):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_VEHICLE_YEAR_REQUEST(vehicle_year), wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(vehicle_make)

    @step_decorator("WEB - ConsumerPage: Provide phone number {1}")
    def provide_phone_number(self, phone, message=AIBotResponse.PII_REQUEST_PHONE):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time, last_only=True)
        self.consumer_behaviour.send_message(phone)

    @step_decorator("WEB - ConsumerPage: Provide phone number again {1}")
    def provide_phone_number_again(self, phone):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.PII_NEED_FULL_PII_PHONE, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(phone)

    @step_decorator("WEB - ConsumerPage: Provide correct phone number {1}")
    def provide_correct_phone_number(self, phone):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.PII_INVALID_PHONE, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(phone)

    @step_decorator("WEB - ConsumerPage: Provide email {1}")
    def provide_email(self, email, message=AIBotResponse.PII_REQUEST_EMAIL):
        assert self.consumer_behaviour.chat_component.get_message(message, wait_time=ai_wait_time) \
               or self.consumer_behaviour.chat_component.get_message(
            AIBotResponse.PII_REQUEST_EMAIL_NO_PHONE, wait_time=ai_wait_time, typing=True)
        self.consumer_behaviour.send_message(email)

    @step_decorator("WEB - ConsumerPage: Wait for e-price {1}")
    def wait_for_e_price(self, msrp_price, price):
        discount = msrp_price - price
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.E_PRICE(f'{price:,}', f'{discount:,}'), wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for e-price ai response related to MSRP and dealers price")
    def wait_for_msrp_dealer_price_statement(self, msrp_price, price):
        if price != 0 and msrp_price > price:
            discount = msrp_price - price
            self.consumer_behaviour.chat_component.wait_for_message(
                AIBotResponse.E_PRICE(f'{price:,}', f'{discount:,}'), wait_time=ai_wait_time)
        elif msrp_price == price:
            self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.E_price_conditional(f'{price:,}'), wait_time=ai_wait_time)
        elif msrp_price < price:
            self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.E_price_conditional(f'{price:,}'),
                                                                    wait_time=ai_wait_time)
        elif price == 0:
            self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.E_price_null(), wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Provide email again {1}")
    def provide_email_again(self, email):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.PII_NEED_FULL_PII_EMAIL, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(email)

    @step_decorator("WEB - ConsumerPage: Provide correct email {1}")
    def provide_correct_email(self, email):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.PII_INVALID_EMAIL, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(email)

    @step_decorator("WEB - ConsumerPage: Provide waiting option {1}")
    def provide_waiting_option(self, transport_type):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.XTIME_DROPOFF_QUESTION, wait_time=ai_wait_time)
        if transport_type == TransportType.WAITER:
            self.consumer_behaviour.send_message("I'll wait")
        elif transport_type == TransportType.DROPOFF:
            self.consumer_behaviour.send_message("Drop off your vehicle")
        elif transport_type == TransportType.RENTAL:
            self.consumer_behaviour.send_message("Rental")

    @step_decorator("WEB - ConsumerPage: Provide random waiting option with QRs")
    def provide_random_waiting_option_with_qrs(self):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.XTIME_DROPOFF_QUESTION, wait_time=ai_wait_time)
        transport_type = random.choice(self.consumer_behaviour.chat_component.get_quick_reply_options())
        self.consumer_behaviour.chat_component.select_quick_reply(transport_type)
        transport_type = "Waiter" if transport_type == 'Waiting' else transport_type
        return transport_type

    @step_decorator("WEB - ConsumerPage: Provide appointment services {1}")
    def provide_appointment_services(self, appointment_services, message=AIBotResponse.XTIME_SERVICE_INQUIRY):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        Log.debug(f"!!! provide_appointment_services {appointment_services}")
        self.consumer_behaviour.send_message(appointment_services[0] if isinstance(appointment_services, tuple)
                                             else appointment_services)

    @step_decorator("WEB - ConsumerPage: Provide random appointment service with qrs")
    def provide_random_appointment_service_with_qr(self, message=AIBotResponse.XTIME_PICK_SERVICE):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        service = random.choice(self.consumer_behaviour.chat_component.get_quick_reply_options())
        self.consumer_behaviour.chat_component.select_quick_reply(service)
        return service

    @step_decorator("WEB - ConsumerPage: Provide vin or stock {1}")
    def provide_vin_stock(self, vin):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_MODEL_OR_VIN_REQUEST, wait_time=ai_wait_time, typing=True)
        self.consumer_behaviour.send_message(vin)

    @step_decorator("WEB - ConsumerPage: Provide model {1}")
    def provide_model(self, model):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.ASK_MODEL, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(model)

    @step_decorator("WEB - ConsumerPage: Confirm vehicle")
    def confirm_vehicle(self, response="yeah", message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP_MANY):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(response)

    @step_decorator("WEB - ConsumerPage: Confirm E-price decision with QRs")
    def e_price_decision_qr(self, message=AIBotResponse.VEHICLES_E_PRICE_DECISION,
                            response=EPriceDecisionQRType.BOOK_TEST_DRIVE, response_text=None):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        if response_text is None:
            self.consumer_behaviour.chat_component.select_quick_reply(response)
        else:
            self.consumer_behaviour.send_message(response_text)

    @step_decorator("WEB - ConsumerPage: Confirm vehicle with QRs")
    def confirm_vehicle_qr(self, response="Yes", message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP_MANY):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(response)

    @step_decorator("WEB - ConsumerPage: Provide finance or lease with QRs")
    def provide_finance_or_lease(self, response=QRResponses.FINANCE,
                                 message=AIBotResponse.VEHICLES_VEHICLE_MATCH_FINANCE_LEASE):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(response)

    @step_decorator("WEB - ConsumerPage: Provide zip code")
    def provide_zip_code(self, zip_code, message=AIBotResponse.VEHICLES_ZIP_REQUEST, too_far=None, wrong_zip=None):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.send_message(zip_code)
        if too_far:
            self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.VEHICLES_ZIP_TOO_FAR, wait_time=ai_wait_time)
        elif wrong_zip:
            self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.VEHICLES_ZIP_WRONG, wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Provide which of the above")
    def provide_vehicle_which_one(self, which_one=None, message=AIBotResponse.VEHICLES_VEHICLE_WHICH_ONE):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time, last_only=True)
        if which_one:
            self.consumer_behaviour.send_message(which_one)

    @step_decorator("WEB - ConsumerPage: Agree with available opening")
    def agree_with_available_opening(self):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.XTIME_DATE_QUESTION, wait_time=ai_wait_time)
        date_to_select = random.choice(self.consumer_behaviour.chat_component.get_quick_reply_options())
        self.consumer_behaviour.chat_component.select_quick_reply(date_to_select)

        if datetime.datetime.strptime(date_to_select, '%a %b %d').month < datetime.datetime.today().month:
            year = str(datetime.datetime.today().year + 1)
        else:
            year = str(datetime.datetime.today().year)
        date_to_select = datetime.datetime.strptime(date_to_select + " " + year, '%a %b %d %Y')

        if (datetime.datetime.today() + datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0,
                                                                            microsecond=0) == date_to_select:
            day_selected = "tomorrow"
            date_to_select = " tomorrow"
        elif datetime.datetime.today().replace(hour=0, minute=0, second=0, microsecond=0) == date_to_select:
            day_selected = "today"
            date_to_select = " today"
        elif (datetime.datetime.today() + datetime.timedelta(days=14)).replace(hour=0, minute=0, second=0,
                                                                               microsecond=0) < date_to_select:
            day_selected = f"on {date_to_select.strftime('%B %-d')}"
            date_to_select = f" on {date_to_select.strftime('%A (%b %d)')}"
        else:
            day_selected = f"on {date_to_select.strftime('%A')}"
            date_to_select = f" on {date_to_select.strftime('%A (%b %d)')}"
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.XTIME_TIME_PREFERENCES(day_selected), wait_time=ai_wait_time)
        time_to_select = random.choice(self.consumer_behaviour.chat_component.get_quick_reply_options())
        self.consumer_behaviour.chat_component.select_quick_reply(time_to_select)
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.XTIME_WE_HAVE_AN_OPENING, wait_time=ai_wait_time, typing=True)
        return time_to_select + date_to_select

    @step_decorator("WEB - ConsumerPage: Propose appointment time {1}")
    def propose_your_time(self, appointment_time, question=AIBotResponse.XTIME_DATE_QUESTION):
        self.consumer_behaviour.chat_component.wait_for_message(question, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(appointment_time)

    @step_decorator("WEB - ConsumerPage: Get confirmation code and close chat")
    def get_confirmation_code(self):
        code = self.consumer_behaviour.chat_component.get_calendar_confirmation_code()
        assert code, "Successful message was not appeared"
        return code

    @step_decorator("WEB - ConsumerPage: Get looked up appointment")
    def check_look_up_appointment(self, lead):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.APPOINTMEMT_APPT_LOOKUP_FOUND_PREAMBLE(lead.first_name), wait_time=ai_wait_time)
        template = r"(?P<first_name>[\S]+) (?P<last_name>[\S]+)\nPhone: (?P<phone>\S+)\nEmail: (?P<email>\S+)\nVehicle: (?P<vehicle>.+)\nAppointment #: (?P<appointment>\S+)\nServices: (?P<service>.+)\nAdvisor Name: (?P<advisor_name>[\S ]+)\nScheduled for: (?P<date>[\S ]+)$"
        confirmation = self.consumer_behaviour.chat_component.get_message_by_regex(MessageFactory.text(template))
        match = re.search(template, confirmation.body.text).groupdict()
        results = []
        if match.get("first_name") != lead.first_name:
            results.append(f"First name {match.get('first_name')} does not match {lead.first_name}")
        if match.get("last_name") != lead.last_name:
            results.append(f"Last name {match.get('last_name')} does not match {lead.last_name}")
        if match.get("phone") != lead.phone:
            results.append(f"Phone number {match.get('phone')} does not match {lead.phone}")
        if match.get("email") != lead.email:
            results.append(f"Email {match.get('email')} does not match {lead.email}")
        if match.get("vehicle") != f"{lead.vehicle_year} {lead.vehicle_make} {lead.vehicle_model}".upper():
            results.append(f"Vehicle info {match.get('vehicle')} does not match {f'{lead.vehicle_year} {lead.vehicle_make} {lead.vehicle_model}'.upper()}")
        if match.get("service") != lead.appointment_services:
            results.append(f"Service {match.get('service')} does not match {lead.appointment_services}")
        if match.get("date") != lead.appointment:
            results.append(f"Date {match.get('date')} does not match {lead.appointment}")
        assert results == [], "\n".join(results)

    @step_decorator("WEB - ConsumerPage: Provide appointment looked up cancel answer")
    def provide_appointment_look_up_cancel(self, respond="No"):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.APPOINTMEMT_APPT_LOOKUP_CANCEL, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(respond)

    @step_decorator("WEB - ConsumerPage: Get trade in estimation")
    def get_trade_in_estimation_trade_pending(self):
        message = self.consumer_behaviour.chat_component.get_message_by_regex(
            AIBotResponse.TRADE_IN_ESTIMATION, wait_time=ai_wait_time)
        assert message, "Estimation message was not appeared"
        estimation = re.search(AIBotResponse.TRADE_IN_ESTIMATION.text, message.body.text).groupdict()
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.TRADE_IN_REPORT, wait_time=ai_wait_time)
        return estimation.get("estimate_low"), estimation.get("estimate_high")

    @step_decorator("WEB - ConsumerPage: Get trade in estimation")
    def get_trade_in_estimation_kbb(self):
        message = self.consumer_behaviour.chat_component.get_message_by_regex(
            AIBotResponse.TRADE_IN_KBB_ESTIMATION, wait_time=ai_wait_time)
        assert message, "Estimation message was not appeared"
        estimation = re.search(AIBotResponse.TRADE_IN_KBB_ESTIMATION.text, message.body.text).groupdict()
        return estimation.get("estimate_low"), estimation.get("estimate_high")

    @step_decorator("WEB - ConsumerPage: Check confirmation message")
    def check_confirmation_message(self, appointment):
        confirmation = self.consumer_behaviour.chat_component.get_message(
            AIBotResponse.TEST_DRIVE_BOOKING_SUCCESS, wait_time=ai_wait_time, typing=True)
        assert confirmation, "Successful message was not appeared"
        app = re.search(r"see you at (?P<time>\S+) on (?P<week_day>\S+) (?P<month>\S+) (?P<date>\d+)",
                        confirmation.body.text).groupdict()
        assert datetime.datetime.strptime(appointment, '%Y-%m-%d %I:%M%p').strftime('%b') == app.get("month")
        assert datetime.datetime.strptime(appointment, '%Y-%m-%d %I:%M%p').strftime(f'%{non_zero_padded}d') == app.get("date")
        assert datetime.datetime.strptime(appointment, '%Y-%m-%d %I:%M%p').strftime('%A') == app.get("week_day")
        assert datetime.datetime.strptime(appointment, '%Y-%m-%d %I:%M%p').strftime(f'%{non_zero_padded}I:%M%p').lower() == app.get("time")

    @step_decorator("WEB - ConsumerPage: Check confirmation message")
    def check_non_xtime_confirmation_message(self, name):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.NON_XTIME_BOOKING_SUCCESS(name), wait_time=ai_wait_time, typing=True)

    @step_decorator("WEB - ConsumerPage: Agree with available opening")
    def agree_with_available_opening_non_xtime(self, answer="ok"):
        time = self.consumer_behaviour.chat_component.get_message(
            AIBotResponse.NO_48_HOURS_BOOK, wait_time=ai_wait_time)
        assert time, "Date question was not appeared"
        appointment = re.search(f"{AIBotResponse.XTIME_CLOSEST_TIME.text} (.+). {AIBotResponse.CLOSEST_TIME_END.text}",
                                time.body.text).group(1)

        if datetime.datetime.strptime(appointment, '%I:%M%p on %A (%b %d)').month < datetime.datetime.today().month:
            year = str(datetime.datetime.today().year + 1)
        else:
            year = str(datetime.datetime.today().year)
        appointment = datetime.datetime.strptime(year + appointment, '%Y%I:%M%p on %A (%b %d)')
        assert datetime.timedelta(hours=48) < appointment - datetime.datetime.now(), \
            "Time selected is not a proper time window"
        appointment = appointment.strftime(f'%Y-%m-%d %{non_zero_padded}I:%M%p').lower()
        self.consumer_behaviour.send_message(answer)
        return appointment

    @step_decorator("WEB - ConsumerPage: AI close chat")
    def ai_close_chat(self, qr=False, name=None):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.ANYTHING_ELSE, wait_time=ai_wait_time)
        if qr:
            self.consumer_behaviour.chat_component.select_quick_reply("No, thanks")
        else:
            self.consumer_behaviour.send_message("no")
        if not name:
            self.consumer_behaviour.ai_wait_for_farewell()
        else:
            self.consumer_behaviour.ai_wait_for_farewell_with_name(name)
        self.consumer_behaviour.closed_chat.wait_for.visibility_of_element_located(5)

    @step_decorator("WEB - ConsumerPage: AI continue chat with qr")
    def ai_continue_chat(self):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.ANYTHING_ELSE, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply("Yes!")
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.GENERIC_WHAT_ELSE, wait_time=3)

    @step_decorator("WEB - ConsumerPage: Confirm appointment information")
    def confirm_appointment_info(self):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.XTIME_CONFIRMATION, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message("yes")

    @step_decorator("WEB - ConsumerPage: Confirm appointment information")
    def confirm_appointment_info_qr(self, respond="Yes"):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.XTIME_CONFIRMATION, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(respond)

    @step_decorator("WEB - ConsumerPage: Wait for AI greeting")
    def ai_wait_for_greeting(self, greeting=AIBotResponse.GREETING):
        self.consumer_behaviour.chat_component.wait_for_message(greeting, wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for AI farewell")
    def ai_wait_for_farewell(self):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.THANK_YOU_FOR_VISITING, wait_time=ai_wait_time)
        assert self.consumer_behaviour.closed_chat.exists(wait_time=30), "Chat was not closed"

    @step_decorator("WEB - ConsumerPage: Wait for AI farewell")
    def ai_wait_for_farewell_with_name(self, name):
        message = self.consumer_behaviour.chat_component.get_message_by_regex(
            AIBotResponse.HAVE_A_GREAT_DAY, wait_time=ai_wait_time)
        assert message, "Farewell message was not appeared"
        message_text = message.body.text
        found_name = re.search(AIBotResponse.HAVE_A_GREAT_DAY.text, message_text).groupdict().get("name")
        assert name == found_name, f"Name {name} was not found in {message_text}"
        assert self.consumer_behaviour.closed_chat.exists(wait_time=30), "Chat was not closed"

    @step_decorator("WEB - ConsumerPage: Wait for AI carfax information")
    def ai_wait_for_carfax(self, vehicle=None, found=1):
        if found == 1:
            self.consumer_behaviour.chat_component.wait_for_message(
                AIBotResponse.VEHICLES_CARFAX_ONE_OWNER(vehicle), wait_time=ai_wait_time)
        else:
            self.consumer_behaviour.chat_component.wait_for_message(
                AIBotResponse.VEHICLES_FULL_CARFAX(found), wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: check AI no carfax matching")
    def ai_no_carfax_matching(self):
        assert not self.consumer_behaviour.chat_component.get_message(AIBotResponse.CARFAX_MATCHING, wait_time=2)

    @step_decorator("WEB - ConsumerPage: Wait for AI multiple matching")
    def ai_wait_for_multiple_matching(self, found):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_MATCHING(found), wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for AI no matching")
    def ai_wait_for_no_match(self, model):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLE_NO_MATCH(model), wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for vr link")
    def ai_wait_for_vr_link(self, lease_or_finance, vehicle, vin):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_PROVIDE_VR_LINK_PREAMBLE(lease_or_finance, vehicle), wait_time=ai_wait_time)
        return self.consumer_behaviour.chat_component.tbl_operator_last_messages.get_row_by_column_value(
            "monthly_payment", "See My Payments", wait_time=10)

    def ai_open_vr_from_link(self, lease_or_finance, vehicle, vin):
        self.consumer_behaviour.cbo.set_session_key(self.consumer_behaviour.get_session_key())
        message = self.consumer_behaviour.ai_wait_for_vr_link(lease_or_finance, vehicle, vin)
        assert message, 'Message with vr link was not found'
        message.monthly_payment.click()
        self.consumer_behaviour.cbo.exists()

    @step_decorator("WEB - ConsumerPage: Wait for AI carfax url")
    def ai_wait_for_carfax_urls(self, vin):
        # self.consumer_behaviour.chat_component.wait_while_typing(ai_wait_time / 2)
        messages = self.consumer_behaviour.chat_component.tbl_chat_content.get_rows_by_attribute_pattern(
            "body", "textContent", "www.carfax.com", wait_time=ai_wait_time, reversed_order=False)

        assert messages and set([re.search('vin=(.*)', m.body.text).group(1) for m in messages]) == set(vin), \
            f"CarFax report message was not displayed or {vin} do not match to {[m.body.text.lower() for m in messages]}"

    @step_decorator("WEB - ConsumerPage: Wait for AI single vehicle available")
    def ai_wait_for_single_vehicle_available(self, stock, message=AIBotResponse.VEHICLES_SINGLE_VEHICLE_AVAIL):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        assert self.consumer_behaviour.chat_component.get_multiple_vehicle_slider(
            stock), f"Vehicle card for {stock} stock# was not displayed"

    @step_decorator("WEB - ConsumerPage: Wait for AI {1} vehicles push")
    def ai_wait_for_multiple_vehicles_available(self, stocks, message=AIBotResponse.VEHICLES_MULTIPLE_VEHICLES_AVAIL):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        displayed = self.consumer_behaviour.chat_component.get_all_stocks_from_slider(stocks[0])
        assert set(stocks) == set(displayed), f"Expected to see cards for {stocks} stocks, but {displayed} displayed"

    @step_decorator("WEB - ConsumerPage: Wait for AI many vehicles push")
    def ai_wait_for_many_vehicles_push(self, stocks, message=AIBotResponse.VEHICLES_MANY_VEHICLES_PUSH):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        displayed = self.consumer_behaviour.chat_component.get_all_stocks_from_slider(stocks[0])
        assert set(stocks) == set(displayed), f"Expected to see cards for {stocks} stocks, but {displayed} displayed"

    @step_decorator("WEB - ConsumerPage: Wait for AI specific vehicles push")
    def ai_wait_for_specific_vehicles_push(self, stocks):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_MULTIPLE_VEHICLES_FOUND, wait_time=ai_wait_time, typing=True)
        displayed = self.consumer_behaviour.chat_component.get_all_stocks_from_slider(stocks[0])
        assert set(stocks) == set(displayed), f"Expected to see cards for {stocks} stocks, but {displayed} displayed"

    @step_decorator("WEB - ConsumerPage: Wait for AI vehicle lead followup")
    def ai_wait_for_vehicle_lead_followup(self, model=None):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLE_LEAD_FOLLOWUP(model), wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for AI part lead followup")
    def ai_wait_for_part_lead_followup(self, part=None):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.PARTS_BOOKING_SUCCESS(part), wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for AI vehicle lead followup")
    def ai_wait_for_trade_in_vehicle_lead_followup(self, year=None, model=None, trim=""):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.TRADE_IN_VEHICLE_LEAD_FOLLOWUP(year, model, trim), wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for AI vehicle choice")
    def ai_wait_for_vehicle_choice(self):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.VEHICLE_MATCH, wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for AI vehicle not available")
    def ai_wait_for_vehicle_not_available(self, message=AIBotResponse.VEHICLES_NO_VEHICLE_FOUND):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Provide vehicle info for test drive {1}")
    def provide_vehicle_for_test_drive(self, vehicle):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.TEST_DRIVE_WHAT_VEHICLE, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(vehicle)

    @step_decorator("WEB - ConsumerPage: Provide test drive time and PII for VIN flow")
    def provide_test_drive_time_and_pii(self, appointment_time, lead_info):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.TEST_DRIVE_RANGE_FOR_DEPARTMENT_OPEN,
                                                                wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.TEST_DRIVE_DATE_QUESTION,
                                                                wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(appointment_time)
        self.provide_full_pii(lead_info)

    @step_decorator("WEB - ConsumerPage: Provide test drive time {1}")
    def provide_test_drive_time(self, appointment_time):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.TEST_DRIVE_RANGE_FOR_DEPARTMENT_OPEN, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.TEST_DRIVE_DATE_QUESTION, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(appointment_time)
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.TEST_DRIVE_BOOKING_SUCCESS, wait_time=ai_wait_time, typing=True)

    @step_decorator("WEB - ConsumerPage: Provide test drive time with quick replies")
    def provide_test_drive_time_with_qr(self, showroom_open_skip=False):
        if not showroom_open_skip:
            self.consumer_behaviour.chat_component.wait_for_message(
                AIBotResponse.TEST_DRIVE_RANGE_FOR_DEPARTMENT_OPEN, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.TEST_DRIVE_DATE_QUESTION, wait_time=ai_wait_time)
        date_to_select = random.choice(self.consumer_behaviour.chat_component.get_quick_reply_options())
        self.consumer_behaviour.chat_component.select_quick_reply(date_to_select)
        year = str(datetime.datetime.today().year)
        if date_to_select.lower() in ["today", "tomorrow"]:
            day_selected = date_to_select.lower()
            if date_to_select.lower() == "tomorrow":
                date_to_select = (datetime.date.today() + dateutil.relativedelta.relativedelta(days=1)).strftime(
                    '%a %b %d')
                year = str((datetime.date.today() + dateutil.relativedelta.relativedelta(days=1)).year)
            elif date_to_select.lower() == "today":
                date_to_select = datetime.date.today().strftime('%a %b %d')
                year = str(datetime.datetime.today().year)
        else:
            if datetime.datetime.strptime(date_to_select, '%a %b %d').month < datetime.datetime.today().month:
                year = str(datetime.datetime.today().year + 1)
            else:
                year = str(datetime.datetime.today().year)
            day_selected = f"on {datetime.datetime.strptime(date_to_select + ' ' + year, '%a %b %d %Y').strftime('%A')}"
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.XTIME_TIME_PREFERENCES(day_selected), wait_time=ai_wait_time)
        time_to_select = random.choice(self.consumer_behaviour.chat_component.get_quick_reply_options())
        self.consumer_behaviour.chat_component.select_quick_reply(time_to_select)

        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.TEST_DRIVE_BOOKING_SUCCESS, wait_time=ai_wait_time, typing=True)
        assert datetime.timedelta(hours=0.5) < datetime.datetime.strptime(
            date_to_select + " " + year + " " + time_to_select,
            '%a %b %d %Y %I:%M%p').replace(tzinfo=Eastern) - datetime.datetime.now(tz=Eastern),\
            "Time selected is not a proper time window"
        return datetime.datetime.strptime(date_to_select + " " + year + " " + time_to_select,
                                          '%a %b %d %Y %I:%M%p').strftime(f'%Y-%m-%d %{non_zero_padded}I:%M%p').lower()

    @step_decorator("WEB - ConsumerPage: Provide trim {1}")
    def provide_trim(self, trim, year, model):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_VEHICLE_TRIM_REQUEST(year, model), wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(trim)

    @step_decorator("WEB - ConsumerPage: Provide license plate {1}")
    def provide_license_plate(self, license_plate, message=AIBotResponse.VEHICLES_VEHICLE_LICENSE_PLATE_REQUEST):
        self.consumer_behaviour.chat_component.wait_for_message(message, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.send_message(license_plate)

    @step_decorator("WEB - ConsumerPage: Provide vehicle registration {1}")
    def provide_vehicle_registration(self, state):
        if state in self.consumer_behaviour.chat_component.wait_for_message(
                AIBotResponse.VEHICLE_VEHICLE_STATE_CONFIRMATION, wait_time=ai_wait_time).body.text:
            self.consumer_behaviour.chat_component.select_quick_reply('Yes')
        else:
            self.consumer_behaviour.chat_component.select_quick_reply('No')
            self.consumer_behaviour.chat_component.wait_for_message(
                AIBotResponse.VEHICLE_VEHICLE_STATE_REGISTERED_REQUEST, wait_time=ai_wait_time)
            self.consumer_behaviour.chat_component.send_message(state)

    @step_decorator("WEB - ConsumerPage: Provide body type {1}")
    def provide_body_type(self, body_type, year, model, question=AIBotResponse.VEHICLES_VEHICLE_BODY_REQUEST):
        self.consumer_behaviour.chat_component.wait_for_message(question(year, model), wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(body_type)

    @step_decorator("WEB - ConsumerPage: Provide engine {1}")
    def provide_engine(self, engine, year, model):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_VEHICLE_ENGINE_REQUEST(year, model), wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(engine)

    @step_decorator("WEB - ConsumerPage: Provide drivetrain {1}")
    def provide_drivetrain(self, drivetrain, year, model):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_VEHICLE_DRIVETRAIN_REQUEST(year, model), wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(drivetrain)

    @step_decorator("WEB - ConsumerPage: Provide mileage {1}")
    def provide_mileage(self, miles, year, model):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_VEHICLE_MILEAGE_REQUEST(year, model), wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(miles)

    @step_decorator("WEB - ConsumerPage: Provide condition {1}")
    def provide_condition(self, condition, year, model, respond=AIBotResponse.VEHICLES_TRADE_IN_PII_TRANSITION_KBB):
        self.consumer_behaviour.chat_component.wait_for_message(
            AIBotResponse.VEHICLES_VEHICLE_CONDITION_REQUEST(year, model),
            wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.select_quick_reply(condition)
        if respond:
            self.consumer_behaviour.chat_component.wait_for_message(respond, wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Send a message connecting to operator {1}")
    def send_dump_message(self, message):
        self.consumer_behaviour.send_message(message)
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.GENERIC_TRANSFER_TO_OP,
                                                                wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: Wait for connecting to operator")
    def wait_for_operator(self):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.GENERIC_TRANSFER_TO_OP,
                                                                wait_time=ai_wait_time)


    @step_decorator("WEB - ConsumerPage: AI send post lead message")
    def send_post_lead_message(self, message, response):
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.ANYTHING_ELSE, wait_time=ai_wait_time)
        self.consumer_behaviour.send_message(message)
        self.consumer_behaviour.chat_component.wait_for_message(response, wait_time=ai_wait_time)
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.ANYTHING_ELSE, wait_time=ai_wait_time)

    @step_decorator("WEB - ConsumerPage: AI send lease questions")
    def send_lease_inquiry(self, lease_question, lease_question_2):
        self.consumer_behaviour.send_message(lease_question)
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.AI_LEASING_TERM_MILEAGE, wait_time=20)
        self.consumer_behaviour.send_message(lease_question_2)

    @step_decorator("WEB - ConsumerPage: Provide decision on switch live plays")
    def provide_decision_on_switching_live_plays(self, decision=AIBotResponse.DECISION_START_NEW_CHAT,
                                                 # DECISION_CONTINUE
                                                 system_message=AIBotResponse.SYSTEM_MESSAGE_INTERESTED_IN_TEST_DRIVE):
        Log.info(f"WEB - ConsumerPage: Provide decision on switch live plays: {decision}")
        self.consumer_behaviour.chat_component.wait_for_message(system_message, wait_time=5, typing=False)
        self.consumer_behaviour.chat_component.wait_for_message(AIBotResponse.AI_START_NEW_CHAT, wait_time=5)
        self.consumer_behaviour.chat_component.select_quick_reply(decision)
