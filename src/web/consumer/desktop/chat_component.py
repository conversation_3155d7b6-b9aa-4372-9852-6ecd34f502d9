import re

from selenium.webdriver.common.keys import Keys

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_list_types import AnyList
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, Input, Spinner, TextBlock, Image, SelectExtended
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.entities.message_entity import MessageEntity


class SeveralVehiclesStructure:
    def __init__(self):
        self.title = TextBlock(CssSelector('strong[data-testid="vehicle-title"]'))
        self.price = TextBlock(CssSelector('span[data-testid="vehicle-price"]'))
        self.stock_hidden = TextBlock(Xpath('.//div[contains(@data-testid, "vehicle-stock-")]'))
        # self.transmission = TextBlock(Xpath('.//div[contains(text(), "Transmission")]/../span'))
        # self.engine = TextBlock(Xpath('.//div[contains(text(), "Engine")]/../span'))
        self.mileage = TextBlock(CssSelector('span[data-testid="vehicle-mileage"]'))
        self.view_details = Button(CssSelector('[aria-label="View vehicle details"]'))
        self.btn_explore_payments = Button(CssSelector('[aria-label="View available payment options"]'))
        self.vehicle_type = TextBlock(Xpath(".//p[contains(@class,'Typography__DefaultParagraphStyle')]/small"))

    def get_stock(self):
        return self.stock_hidden.get_attribute('data-testid').split("vehicle-stock-")[-1]

    @step_decorator('WEB - SeveralVehiclesStructure: Get title text')
    def get_title_text(self):
        return self.title.text.split("\n")[0]

    @step_decorator('WEB - SeveralVehiclesStructure: Get Price')
    def get_price_from_vehicle_card(self):
        return self.price.text

    @step_decorator('WEB - SeveralVehicleStructure: Get VehicleType')
    def get_vehicle_type(self):
        return self.vehicle_type.text


class DealershipCardStructure:
    def __init__(self):
        self.title = TextBlock(Xpath("./div[contains(@class, 'Dealership__Title')]"))
        self.phone_number = TextBlock(Xpath(".//a[contains(@class, 'Dealership__PhoneNumber')]"))
        self.card_content = TextBlock(Xpath(".//div[contains(@class, 'Dealership__Row')][2]/.."))
        self.locate_map = Image(Xpath(".//div[contains(@class, 'Dealership__MapAddress')]/.."))


class ContactCardStructure:
    def __init__(self):
        self.name = TextBlock(CssSelector("div[type='name'] p"))
        self.phone_number = TextBlock(Xpath(".//div[contains(@class, 'StyledComponents__ContactCardInfoWrapper')]//div[contains(@class, 'StyledComponents__ContactCardContentWrapper')]/p[1]/a"))
        self.email = TextBlock(Xpath(".//div[contains(@class, 'StyledComponents__ContactCardInfoWrapper')]//div[contains(@class, 'StyledComponents__ContactCardContentWrapper')]/p[2]/a"))
        self.title = TextBlock(CssSelector("div[type='subHeading'] p"))
        self.avatar = Image(Xpath(".//div[contains(@class, 'CardHeaderImage')]"))

    def get_avatar_src(self):
        return self.avatar.value_of_css_property('background-image').replace("\")", "").replace("url(\"", "")


class DealerEnterStructure:
    def __init__(self):
        self.avatar = Image(CssSelector("img"))
        self.author = TextBlock(Xpath(".//p[contains(@class, 'StyledComponents__StyledOperatorText')]"))
        self.title = TextBlock(Xpath(".//span[contains(@class, 'StyledComponents__StyledOperatorText')][1]"))
        self.message = TextBlock(Xpath(".//span[contains(@class, 'StyledComponents__StyledOperatorText')][2]"))


class DepartmentStructure:
    def __init__(self):
        self.new_vehicles = Button(Xpath('.//div/button[contains(text(), "New Vehicles")]'))
        self.pre_owned_vehicles = Button(Xpath('.//div/button[contains(text(), "Pre-Owned Vehicles")]'))
        self.service = Button(Xpath('.//div/button[contains(text(), "Service")]'))
        self.parts = Button(Xpath('.//div/button[contains(text(), "Parts")]'))
        self.call_me = Button(Xpath(".//div/p/a/strong[contains(text(),'call me back')]"))

    def call_me(self):
        self.call_me.click()

    def select_new_vehicles(self):
        self.new_vehicles.click()

    def select_pre_owned_vehicles(self):
        self.pre_owned_vehicles.click()

    def select_service(self):
        self.service.click()

    def select_parts(self):
        self.parts.click()


class MessageStructure:
    def __init__(self):
        ##self.operator_avatar = Image(CssSelector(".gg-message div.gg-message__img img"))
        self.body = TextBlock(Xpath(".//div[contains(@class, 'StyledComponents__ChatBubble')]|//div[contains(@data-testid, 'quick-replies-picker')]/p"))
        self.author = TextBlock(CssSelector("p[data-testid='timestamp']"))
        self.time = TextBlock(CssSelector("p[data-testid='timestamp']"))

        self.dealer_card = TextBlock(CssSelector(".gg-message-locate"))
        self.contact_card = Table(CssSelector("div[data-testid='contact-card']"), ContactCardStructure)
        self.dealership_card = Table(CssSelector("div[data-testid='dealership']"), DealershipCardStructure)
        self.image = Image(CssSelector("div[data-testid='image'] img"))
        self.video = Image(CssSelector("div[data-testid='video']"))
        self.link = Button(Xpath(".//a[contains(@class, 'Button__LinkButton')]"))
        self.email = TextBlock(Xpath('.//a[contains(@href, "mailto:")]'))

        self.link_with_preview = Button(Xpath(".//div/a[contains(@class,'Button__LinkButton')]"))
        self.file = Button(CssSelector('[aria-label="Download Now"]'))
        self.offer_title = TextBlock(Xpath(".//div[contains(@class, 'StyledComponents__OfferTitle')]"))
        self.offer_get = Button(Xpath(".//button[@aria-label='View Offer']"))
        self.vehicles = Table(Xpath(".//div[contains(@class, 'StyledComponents__VehicleWrapper')]"), SeveralVehiclesStructure)
        self.monthly_payment_img = Image(Xpath('.//div[contains(@class, "StyledComponents__VehicleImageWrapper")]/img'))
        self.monthly_payment = Button(Xpath('.//div[contains(@class, "StyledComponents__CBOButtonWrapper")]/button'))
        self.left = Button(CssSelector('.gg-msg-group__btn--prev'), action_element=True)
        self.right = Button(CssSelector('.gg-msg-group__btn--next'), action_element=True)
        self.active_vehicle_title = TextBlock(CssSelector('.gg-vehicle--active .gg-vehicle__title'))
        self.active_vehicle_stock = TextBlock(
            Xpath('.//div[contains(@class, "gg-vehicle--active")]//div[contains(text(), "Stock #")]/../span'))
        self.confirmation_code = TextBlock(CssSelector("p[data-testid='appointment-reference-number']"))
        self.calendar_card = TextBlock(CssSelector("div[data-testid='service-appointment']"))
        self.google = Button(Xpath(".//a[text()='Google']"))
        self.outlook = Button(Xpath(".//a[text()='Outlook']"))

    @step_decorator('WEB - MessageStructure: Click next button')
    def next(self):
        self.right.js.click()
        self.active_vehicle_stock.wait_for.text_equal(text="", wait_time=5, until=False)

    @step_decorator('WEB - MessageStructure: Click previous button')
    def previous(self):
        self.left.js.click()
        self.active_vehicle_stock.wait_for.text_equal(text="", wait_time=5, until=False)


@find_by(Xpath(".//div[contains(@class, 'StyledComponents__ChatContainerContent')]/../.."))
class ChatComponent(Component):
    def __init__(self, ancestor, initial_message=None):
        super().__init__(ancestor)
        # Page elements
        self.btn_close_icon = Button(CssSelector("button[aria-label='Close']"))
        self.btn_send = Button(CssSelector("button[aria-label='Send Message']"))
        self.btn_close = Button(CssSelector("button[aria-label='Toggle Chat Window']"))
        self.inp_message = Input(CssSelector("[aria-label='Type your message']"))

        self.img_operator_avatar = Image(CssSelector("div[aria-label='Operator Image']"))
        self.txt_operator_name = TextBlock(CssSelector("div[aria-label='Operator Name'] p"))
        self.txt_operator_title = TextBlock(CssSelector("div[aria-label='Operator Title'] p"))
        self.lst_video = AnyList(CssSelector("div[data-testid='video']"))
        ##self.lst_operator_avatars = AnyList(CssSelector(".gg-message--operator div.gg-message__img img"))

        self.tbl_chat_content = Table(Xpath(".//div[contains(@class, 'StyledComponents__MessengerMessagesWrapper')]/div"),
                                      MessageStructure)
        self.tbl_operator_last_messages = Table(
            Xpath('.//div[@data-testid="default-consumer"][last()]/following-sibling::div[contains(@class, "StyledComponents__ChatBubbleRow")]'),
            MessageStructure)
        self.lst_visitor_messages = AnyList(CssSelector("div[data-testid='default-consumer']"))
        self.initial_message = initial_message
        self.spn_typing = Spinner(CssSelector("div[data-testid='typing']"))
        self.ddl_quick_replies = SelectExtended(
            Xpath(".//div[@data-testid='quick-replies']"),
            Xpath(".//div[@data-testid='quick-replies']/button"),
            extent_list_by_click_on_field=False)
        self.ddl_quick_replies_picker = SelectExtended(
            Xpath('.//div[@data-testid="quick-replies-picker"]//div[contains(@class, "StyledComponents__PickerWrapper")]'),
            Xpath(".//div[contains(@class, 'StyledComponents__PickerOptionsWrapper')]/span"),
            extent_list_by_click_on_field=True)
        self.txt_quick_replies_question = TextBlock(Xpath('.//div[@data-testid="quick-replies-picker"]//p'))
        self.btn_submit_quick_replies_picker = Button(
            Xpath('.//div[@data-testid="quick-replies-picker"]//button[text()="Submit"]'))
        self.dealer_enter = Table(CssSelector('div[data-testid=operator-entered]'), DealerEnterStructure)
        self.img_holiday_flag = Image(Xpath(".//div[contains(@class, 'HolidayTopperWrapper')]"))
        self.btn_sms = Button(Xpath(".//button[@aria-label='Chat with SMS']//*[@aria-label='Text']"))
        self.btn_terms = Button(Xpath(".//a[contains(text(), 'Terms')]"))
        self.btn_gubagoo = Button(Xpath(".//a[contains(text(), 'Gubagoo')]"))
        self.btn_trade_in_conditions = Button(Xpath(".//div[@aria-label='View Explanation of Conditions']"))
        self.txt_trade_in_conditions = TextBlock(Xpath(".//div[@class='KBBConditions__KBBWrapper-lqfx1o-1 fwpuV']/div[@aria-label='View Explanation of Conditions']"))
        self.tbl_department_picker = Table(CssSelector("div[data-testid='department-picker']"), DepartmentStructure)

    @step_decorator('WEB - ChatComponent: Open "Chat" Component sending a message')
    def open_actions(self):
        self.open_from_bubble(self.initial_message)

    @step_decorator('WEB - ChatComponent: Close "Chat" Component')
    def close(self):
        self.btn_close.js.click()
        assert not self.wait_for.visibility_of_element_located(wait_time=3, until=False)

    @step_decorator('WEB - ChatComponent: Reopen "Chat" Component')
    def open_from_bubble(self, message):
        self.ancestor.welcome.open()
        self.ancestor.welcome.send_message(message)
        assert self.exists(5), "Component ChatComponent was not found"
        return self

    @step_decorator('WEB - ChatComponent: Expand "Chat" Component')
    def expand(self):
        if not self.exists(1):
            self.ancestor.btn_chat_bubble.click()
            assert self.exists(5)
        return self

    @step_decorator('WEB - ChatComponent: Type a {1} message')
    def type_message(self, message):
        for text in re.split(r'(\n)', message):
            if text == "\n":
                self.inp_message.action_chains.send_keys(Keys.END).key_down(Keys.SHIFT).send_keys(Keys.ENTER).key_up(
                    Keys.SHIFT).perform()
            else:
                self.inp_message.type_keys(text)

    @step_decorator('WEB - ChatComponent: Clear input field')
    def clear_message(self):
        self.inp_message.clean_with_backspace()

    @step_decorator('WEB - ChatComponent: Send a {1} message')
    def send_message(self, message):
        if isinstance(message, MessageEntity):
            message = message.text
        visitor_messages_quantity = self.lst_visitor_messages.size
        self.type_message(message)
        self.btn_send.click()
        self.lst_visitor_messages.wait_for.number_of_elements(elements_count=visitor_messages_quantity,
                                                              wait_time=2, until=False)

    @step_decorator('WEB - ChatComponent: Send a {1} message')
    def send_message_quick(self, message):
        visitor_messages_quantity = self.lst_visitor_messages.size
        for text in re.split(r'(\n)', message):
            if text == "\n":
                self.inp_message.action_chains.send_keys(Keys.END).key_down(Keys.SHIFT).send_keys(Keys.ENTER).key_up(
                    Keys.SHIFT).perform()
            else:
                self.inp_message.send_keys(text)
        self.btn_send.click()
        self.lst_visitor_messages.wait_for.number_of_elements(elements_count=visitor_messages_quantity,
                                                              wait_time=2, until=False)

    @step_decorator('WEB - ChatComponent: Wait while typing')
    def wait_while_typing(self, wait_time):
        from selenium.common.exceptions import TimeoutException
        try:
            self.spn_typing.wait_for_appear_and_disappear(wait_time)
        except TimeoutException:
            pass

    @step_decorator('WEB - ChatComponent: Wait for typing')
    def wait_for_typing(self, wait_time):
        self.spn_typing.wait_for.visibility_of_element_located(wait_time)

    @step_decorator('WEB - ChatComponent: Get contract card by {1} text')
    def get_contact_card(self, text):
        card_in_chat = self.tbl_chat_content.get_rows_by_attribute_pattern("contact_card", "textContent", text,
                                                                           wait_time=15, reversed_order=True)
        assert len(card_in_chat) == 1
        return card_in_chat[0].contact_card.get_row_by_index(0)

    @step_decorator('WEB - ChatComponent: Get contract card avatar source')
    def get_contact_card_avatar(self, text):
        card = self.get_contact_card(text)
        return card.avatar.value_of_css_property('background-image').replace("\")", "").replace("url(\"", "")

    @step_decorator('WEB - ChatComponent: Get dealership card by {1} text')
    def get_dealership_card(self, text):
        card_in_chat = self.tbl_chat_content.get_rows_by_attribute_pattern("dealership_card", "textContent", text,
                                                                           wait_time=15, reversed_order=True)
        actual_list = [card.dealership_card.get_row_by_index(0) for card in card_in_chat if
                       card.dealership_card.get_row_by_index(0).title.text == text]
        assert len(actual_list) >= 1
        return actual_list[0]

    @step_decorator('WEB - ChatComponent:  Wait for {1} message')
    def wait_for_message(self, message, wait_time=10, typing=None, last_only=True):
        actual_message = self.get_message(message, wait_time, typing, last_only=last_only)
        assert actual_message, \
            f"Message {message} was not found in {self.get_messages_text(last_only=last_only)}"
        return actual_message

    @step_decorator('WEB - ChatComponent:  Get {1} message')
    def get_message(self, message, wait_time=10, typing=None, last_only=True): ##to-do: remove typing from here and just use ancestor.typing
        text = ""
        if message.text:
            text += message.text
        if message.quick_replies:
            text += " ".join([message.message for message in message.quick_replies])
        if typing or self.ancestor.typing:
            self.wait_while_typing(5)
        if not last_only or not self.tbl_operator_last_messages.exists(5):
            found_message = self.tbl_chat_content.get_row_by_column_pattern("body", text, wait_time=wait_time,
                                                                            reversed_order=True)
        else:
            found_message = self.tbl_operator_last_messages.get_row_by_column_pattern("body", text, wait_time=wait_time,
                                                                                      reversed_order=False)
        # if found_message is None and text in self.txt_quick_replies_question.text:
        #     found_message = self.txt_quick_replies_question
        return found_message

    @step_decorator('WEB - ChatComponent: Get timestamp of {1} message')
    def get_timestamp(self, message):
        self.action_chains.move_to_element(message.body.find()).perform()
        message.time.wait_for.visibility_of_element_located(wait_time=5)
        return message.time.text

    @step_decorator('WEB - ChatComponent: Get all messages with timestamp')
    def get_messages_with_timestamp(self):
        res = {}
        chat_content = self.tbl_chat_content.get_content()
        for row in chat_content:
            res[row.body.text] = self.get_timestamp(row)
        return res

    @step_decorator('WEB - ChatComponent: Input "Space" and click on send button')
    def space_and_click(self):
        self.inp_message.send_keys(Keys.SPACE)
        self.btn_send.click(wait_time=2)

    @step_decorator('WEB - ChatComponent:  Wait for message by regular expression {1}')
    def wait_for_message_by_regex(self, message, wait_time=5, typing=None, last_only=True):
        assert self.get_message_by_regex(message, wait_time, typing, last_only=last_only), \
            f"Message {message} was not found in {self.get_messages_text(last_only=last_only)}"

    @step_decorator('WEB - ChatComponent:  Wait for message by regular expression {1}')
    def get_message_by_regex(self, message, wait_time=5, typing=None, last_only=True):
        text = ""
        if message.text:
            text += message.text
        if message.quick_replies:
            text += " ".join([message.message for message in message.quick_replies])
        # if typing:
        #     self.wait_while_typing(5)
        if not last_only or not self.tbl_operator_last_messages.exists(3):
            return self.tbl_chat_content.get_row_by_column_regex("body", text, wait_time=wait_time, reversed_order=True)
        else:
            return self.tbl_operator_last_messages.get_row_by_column_regex("body", text, wait_time=wait_time,
                                                                           reversed_order=False)

    @step_decorator('WEB - ChatComponent: Get messages text')
    def get_messages_text(self, wait_time=10, last_only=False):
        # self.wait_while_typing(wait_time)
        if not last_only or not self.tbl_operator_last_messages.exists(3):
            messages = self.tbl_chat_content.get_column_values('body')
        else:
            messages = self.tbl_operator_last_messages.get_column_values('body')
        return [text.replace("\n", "").replace("\t", "") if text else text for text in messages if text is not None]

    @step_decorator('WEB - ChatComponent: Click on last video')
    def click_on_last_video(self):
        assert self.lst_video.exists(3)
        self.lst_video.find()[-1].click()

    @step_decorator('WEB - ChatComponent: Get Offer from chat by name {1}')
    def get_offer_from_chat(self, offer_name, wait_time=5):
        return self.tbl_chat_content.get_row_by_column_value("offer_title", offer_name, wait_time=wait_time,
                                                             reversed_order=True)

    @step_decorator('WEB - ChatComponent: Selecting {1} from QRs')
    def select_quick_reply(self, qr_text):
        visitor_messages_quantity = self.lst_visitor_messages.size
        assert self.ddl_quick_replies_picker.exists(3) or self.ddl_quick_replies.exists(3),\
            "Quick replies are not displayed"
        if self.ddl_quick_replies_picker.exists():
            self.ddl_quick_replies_picker.select_item_by_text(qr_text)
            self.btn_submit_quick_replies_picker.click()
        else:
            self.ddl_quick_replies.select_item_by_text(qr_text, offset=2)
        self.lst_visitor_messages.wait_for.number_of_elements(elements_count=visitor_messages_quantity,
                                                              wait_time=2, until=False)
        # assert not self.ddl_quick_replies_picker.exists() and not self.ddl_quick_replies.exists(),\
        #     "Quick replies are still displayed after selection"

    @step_decorator('WEB - ChatComponent: Get QRs options')
    def get_quick_reply_options(self):
        assert self.ddl_quick_replies_picker.exists(3) or self.ddl_quick_replies.exists(3),\
            "Quick replies are not displayed"
        if self.ddl_quick_replies_picker.exists():
            return self.ddl_quick_replies_picker.get_options_list()
        else:
            return self.ddl_quick_replies.get_options_list()

    @step_decorator('WEB - ChatComponent: Get entered dealer card')
    def get_entered_dealer_card(self, index=0):
        if self.dealer_enter.exists(3):
            return self.dealer_enter.get_row_by_index(index)

    @step_decorator("WEB - ChatComponent: Get vehicle from chat by {1} text")
    def get_vehicle(self, text):
        return self.get_multiple_vehicle_slider(text).vehicles.get_row_by_index(0)

    @step_decorator("WEB - ChatComponent: Get multiple vehicle slider from chat by {1} stock")
    def get_multiple_vehicle_slider(self, text):
        if not self.tbl_chat_content.exists(3):
            self.ancestor.iframe_new_vdp.switch_to_parent_frame()
        sliders = self.tbl_chat_content.get_rows_by_attribute_pattern("vehicles", "data-testid", text, wait_time=10,
                                                                      reversed_order=True,
                                                                      inner_column_name="stock_hidden")
        if sliders:
            return sliders[0]

    @step_decorator("WEB - ChatComponent: Get last vehicle slider from chat by {1} stock")
    def get_last_vehicle_in_chat(self, text):
        if not self.tbl_chat_content.exists(3):
            self.ancestor.iframe_new_vdp.switch_to_parent_frame()
        sliders = self.tbl_operator_last_messages.get_rows_by_attribute_pattern("vehicles", "data-testid", text, wait_time=10,
                                                                      reversed_order=True,
                                                                      inner_column_name="stock_hidden")
        if sliders:
            return sliders[0]

    @step_decorator("WEB - ChatComponent: Get all vehicle stocks from slider by {1} text")
    def get_all_stocks_from_slider(self, text):
        slider = self.get_multiple_vehicle_slider(text)
        if slider:
            return [vehicle.get_stock() for vehicle in slider.vehicles.get_content()]
        else:
            return []

    @step_decorator('WEB - ChatComponent: Get operator avatar')
    def get_operator_avatar(self):
        return self.img_operator_avatar.value_of_css_property('background-image').replace("\")", "").replace("url(\"", "")

    @step_decorator('WEB - ChatComponent: Get operator name from header')
    def get_header_operator_name(self):
        return self.txt_operator_name.text

    @step_decorator('WEB - ChatComponent: Get operator title from header')
    def get_header_operator_title(self):
        return self.txt_operator_title.text

    @step_decorator("WEB - ChatComponent: View vehicle details of {1} stock")
    def view_vehicle_details(self, stock, last_only=False):
        if not self.exists():
            self.expand()
        if last_only:
            slider_row = self.get_last_vehicle_in_chat(stock)
        else:
            slider_row = self.get_multiple_vehicle_slider(stock)
        vehicle = slider_row.vehicles.get_rows_by_attribute_pattern("stock_hidden", "data-testid", stock)[0]
        vehicle.title.js.scroll_into_view()
        vehicle.view_details.js.click()

    @step_decorator("WEB - ChatComponent: View explore payments of {1} stock")
    def view_explore_payments(self, stock, last_only=False):
        if not self.exists():
            self.expand()
        if last_only:
            slider_row = self.get_last_vehicle_in_chat(stock)
        else:
            slider_row = self.get_multiple_vehicle_slider(stock)
        vehicle = slider_row.vehicles.get_rows_by_attribute_pattern("stock_hidden", "data-testid", stock)[0]
        vehicle.title.js.scroll_into_view()
        vehicle.btn_explore_payments.js.click()

    @step_decorator('WEB - ChatComponent: Get operator avatar src from header')
    def get_header_avatar_src(self):
        return self.img_operator_avatar.value_of_css_property('background-image').replace("\")", "").replace("url(\"", "")

    @step_decorator('WEB - ChatComponent: Verify holiday theme were applied')
    def has_holiday(self, name):
        return self.img_holiday_flag.exists() and self.img_holiday_flag.get_attribute("data-testid") == name

    @step_decorator('WEB - ChatComponent: get calendar card code')
    def get_calendar_confirmation_code(self):
        calendar = self.tbl_chat_content.get_row_by_column_pattern("calendar_card", "Your appointment has been scheduled", wait_time=15, reversed_order=True)
        reference = calendar.confirmation_code.text.split("# ")[1]
        return reference

    @step_decorator('WEB - ChatComponent: get calendar card qr name')
    def get_calendar_card(self):
        calendar = self.tbl_chat_content.get_row_by_column_pattern("calendar_card", "Your appointment has been scheduled", wait_time=15, reversed_order=True)
        return calendar

    @step_decorator('WEB - ChatComponent: get trade in conditions')
    def get_trade_in_conditions_text(self):
        self.btn_trade_in_conditions.exists(2)
        self.btn_trade_in_conditions.click()
        assert self.txt_trade_in_conditions.exists(3), 'Trade-in condition page did not display'
        return self.txt_trade_in_conditions.text

    @step_decorator('WEB - ChatComponent: close chat using close icon')
    def close_using_icon(self):
        self.btn_close_icon.click()
        assert not self.wait_for.visibility_of_element_located(wait_time=3, until=False)

    @step_decorator('WEB - ChatComponent: get department picker card')
    def select_new_vehicles_department(self):
        self.tbl_department_picker.exists(3)
        self.tbl_department_picker.get_content()[0].select_new_vehicles()

    @step_decorator('WEB - ChatComponent: click employment link')
    def select_employment_link(self, url):
        txt_employment_link = TextBlock(Xpath(f".//div/a[@href='{url}']"))
        txt_employment_link.ancestor = self
        txt_employment_link.click()
        self.ancestor.switch_to_last_tab()
