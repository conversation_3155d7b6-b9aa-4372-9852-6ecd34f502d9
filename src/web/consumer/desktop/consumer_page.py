import os

from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON><PERSON>ctor, Xpath
from kom_framework.src.web.data_types.element_list_types import AnyList
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AnyType
from kom_framework.src.web.page_object import PageObject
from kom_framework.src.web.support.page_factory import find_by
from src import consumer_url
from src.utils.decorators import step_decorator
from src.web.consumer import ServiceProviderType
from src.web.consumer.consumer_page_behaviour import ConsumerPageBehaviour
from src.web.consumer.desktop.announcement_component import AnnouncementComponent
from src.web.consumer.desktop.cbo.cbo_page import CBOPage
from src.web.consumer.desktop.cbo.in_store_welcome_page import InStoreWelcomePage
from src.web.consumer.desktop.cbo.show_room_component import ShowRoom
from src.web.consumer.desktop.offer_component import OfferComponent
from src.web.consumer.desktop.service_scheduling_component import ServiceSchedulingComponent
from src.web.consumer.desktop.social_toolbar_component import Facebook<PERSON>omponent, YoutubeComponent, Twitter<PERSON>omponent, \
    InstagramComponent, GoogleReviewComponent, ContactUsComponent
from src.web.consumer.desktop.spotlight_component import SpotlightComponent
from src.web.consumer.desktop.vdp_page import VDP3Page
from src.web.consumer.desktop.pdpa_pop_up import PDPAPopUp
from src.web.consumer.desktop.chat_component import ChatComponent
from src.web.consumer.desktop.chat_greeter_component import ChatGreeterComponent
from src.web.consumer.desktop.closed_chat_component import ClosedChatComponent
from src.web.consumer.desktop.welcome_view_component import WelcomeViewComponent
from src.web.consumer.desktop.sms_component import SmsComponent
from src.web.consumer.desktop.chat_notification_component import ChatNotificationComponent
from src.web.consumer.desktop.chat_greeter_component import AutoEngageGreeterComponent


@find_by(CssSelector(".guba-toolbar-loaded"))
class DesktopConsumerPage(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, initial_message=None, url_query=None, url_extension=None,
                 url=consumer_url, typing=False):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=initial_message,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.url_query = url_query if isinstance(url_query, dict) else {}
        self.url_extension = url_extension
        self.typing = typing
        if not self.typing:
            self.url_query["gb1e2e"] = "1"
        self.url_query["disableCacheApi"] = "true"
        if self.url_query:
            url = url + f"?{'&'.join(map('='.join, self.url_query.items()))}"
        if self.url_extension:
            url = url + self.url_extension
        self.consumer_url = url

        self.btn_chat_bubble = Button(CssSelector("button[aria-label='Toggle Chat Window']"))
        self.js_session_id = "return ggToolbar.apps.chat.getSession().id"
        self.js_session_status = "return ggToolbar.apps.chat.getSession().status"
        self.js_session_forward_status = "return ggToolbar.apps.chat.getSession().forward_status"
        self.div_video = AnyType(Xpath(".//div[contains(@class, 'StyledComponents__VideoModal')]//video"))
        self.blurred = AnyType(Xpath(".//div[contains(@class, 'PDPAConsent')]"))

        self.auto_engage_greeter = AutoEngageGreeterComponent(self)
        self.chat_component = ChatComponent(self, self.initial_message)
        self.closed_chat = ClosedChatComponent(self)
        self.welcome = WelcomeViewComponent(self)
        self.chat_greeter = ChatGreeterComponent(self)
        self.notification_component = ChatNotificationComponent(self)
        self.cbo = CBOPage(self.consumer_name, consumer=self)
        self.cbo.set_session_key(self.get_session_key())
        self.show_room = ShowRoom(self.consumer_name, consumer=self)
        self.show_room.set_session_key(self.get_session_key())
        self.in_store = InStoreWelcomePage(self)
        self.in_store.set_session_key(self.get_session_key())
        self.vdp3 = VDP3Page(self.consumer_name, consumer=self)
        self.vdp3.set_session_key(self.get_session_key())
        self.offer = OfferComponent(self)
        self.xtime = ServiceSchedulingComponent(self)
        self.gm = ServiceSchedulingComponent(self, scheduling_provider=ServiceProviderType.GM)
        self.autoloop = ServiceSchedulingComponent(self, scheduling_provider=ServiceProviderType.AUTOLOOP)
        self.service_portal = ServiceSchedulingComponent(self, scheduling_provider=ServiceProviderType.SERVICEPORTAL)
        self.spotlight = SpotlightComponent(self)

        self.facebook = FacebookComponent(self)
        self.youtube = YoutubeComponent(self)
        self.twitter = TwitterComponent(self)
        self.instagram = InstagramComponent(self)
        self.google_review = GoogleReviewComponent(self)
        self.contact_us = ContactUsComponent(self)

        self.sms = SmsComponent(self)
        self.announcement = AnnouncementComponent(self)
        # Page elements
        self.btn_special = Button(CssSelector('div.gg-btn-specials'))
        self.btn_spotlight_engagement = Button(Xpath(".//div[@id='spotlight-offer-engagement-sliding-unlock']"))
        self.btn_see_offer = Button(Xpath(".//button[@aria-label='See Offer']"))
        self.btn_dynamic_engagement = Button(CssSelector("#gg-dynamic-chat-button"))
        self.btn_youtube = Button(CssSelector('div.gg-social__item--video'))
        self.btn_facebook = Button(CssSelector('div.gg-social__item--facebook'))
        self.btn_bb_trigger = Button(CssSelector("button.gg-bb__btn"))
        self.btn_db_trigger = Button(CssSelector("div.gg-db-item"))  # TODO: extend to table
        self.btn_da_trigger = Button(CssSelector("div.gg-da-slider__item"))  # TODO: extend to table

        self.btn_announcement = Button(CssSelector('div.gg-announcements a'))
        self.btn_announcement_position = Button(CssSelector('div.gg-announcements'))
        self.btn_announcement_close = Button(CssSelector('div.gg-announcements__item--close'))
        self.iframe_facebook = IFrame(Xpath(".//div[@class='gg-facebook gg-content']/iframe"))
        self.facebook_authors = AnyList(Xpath(".//div[@class='_2_79 _50f4 _50f7']"))

        self.btn_twitter = Button(CssSelector('div.gg-social__item--twitter'))
        self.iframe_twitter = IFrame(Xpath(".//iframe[contains(@id, 'twitter-widget')]"))
        self.twitter_authors = AnyList(Xpath(".//span[@class='TweetAuthor-name Identity-name customisable-highlight']"))

        self.btn_contact_us = Button(CssSelector("div.gg-social__item--call_us"))
        self.btn_instagram = Button(CssSelector('div.gg-social__item--instagram'))
        self.iframe_instagram = IFrame(Xpath(".//div[@class='gg-instagram gg-content']/iframe"))
        self.instagram_authors = TextBlock(Xpath(".//header//h1"))
        self.btn_google_review = Button(CssSelector('div.gg-social__item--google_review'))
        self.spn_cbo = Spinner(Xpath(".//*[@class='cbo-spinner']"))
        self.btn_cbo_buy_new = Button(Xpath(".//span[@class='cbo-button' and contains(text(), 'Buy New')]"))
        self.btn_cbo_buy_used = Button(Xpath(".//span[@class='cbo-button' and contains(text(), 'Buy Used')]"))

        self.pdpa_pop_up = PDPAPopUp(self)
        self.spn_pre_loader = Spinner(Xpath("//*[contains(@class, 'Preloader')]"))
        self.blinker = Button(CssSelector("div[aria-label='An operator is currently online']"))

        self.btn_icon_default = Button(CssSelector(".gg-chat-icon__bubble"))
        self.btn_icon_smile = Button(CssSelector(".gg-chat-icon--smile"))
        self.btn_icon_heart = Button(CssSelector(".gg-heart-icon-wrapper"))

    @step_decorator('WEB - ConsumerPage: Open Browser and enter Consumer Page URL')
    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - ConsumerPage: View offer from special')
    def view_offer_from_special(self, offer_name=None):
        self.specials_offer.open()
        self.specials_offer.get_offer(offer_name)
        self.offer.offer_name = offer_name
        assert self.offer.exists(5)

    def setup_page(self):
        pass

    @step_decorator('WEB - ConsumerPage: Get invite message text')
    def get_invite_message_text(self):
        return self.chat_greeter.inp_message.get_attribute("placeholder")

    @step_decorator('WEB - ConsumerPage: Get greeter avatar src')
    def get_greeter_avatar_src(self):
        self.chat_greeter.exists(5)
        return self.chat_greeter.get_operator_avatar()

    @step_decorator('WEB - ConsumerPage: Click on PO trigger button with trigger id {1}')
    def po_trigger(self, trigger_id):
        po_trigger_button = Button(CssSelector(f"a.gg-po-tab[data-id='{trigger_id}']"))
        po_trigger_button.ancestor = self
        assert po_trigger_button.exists(3), "PO trigger button is not displayed"
        po_trigger_button.click()

    @step_decorator('WEB - ConsumerPage: Click on BB trigger button')
    def bb_trigger(self):
        assert self.btn_bb_trigger.exists(8), "BB trigger button is not displayed"
        self.btn_bb_trigger.click()

    @step_decorator('WEB - ConsumerPage: Click on DB trigger button with name')
    def db_trigger(self):
        assert self.btn_db_trigger.exists(8), "DB trigger button is not displayed"
        self.btn_db_trigger.click()

    @step_decorator('WEB - ConsumerPage: Click on DA trigger button with name')
    def da_trigger(self):
        assert self.btn_da_trigger.exists(8), "DA trigger button is not displayed"
        self.btn_da_trigger.click()

    @step_decorator('WEB - ConsumerPage: Check If Chat position is left')
    def is_ui_position_left(self):
        if "guba-desktop-ui-left" in self.find().get_attribute("class"):
            return True
        else:
            return False

    @step_decorator('WEB - ConsumerPage: Check for blinker')
    def blinker_status(self):
        if self.blinker.exists(3):
            return True
        else:
            return False
