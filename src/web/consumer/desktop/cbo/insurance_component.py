import re

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON>, CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextBlock, Image, SelectExtended
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.consumer import PaymentOptionType
from src.web.entities.insurance_entity import InsuranceEntity, BundleEntity


class FIFlow:
    DISABLED = "disabled"
    PRODUCTS = "products"
    BUNDLES = "bundles"


@find_by(Xpath('.//*[@data-testid="current-step-insurance"] | //section[contains(@class,"BundleList__Wrapper")]'))
class InsuranceComponent(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.coverage_options = CoverageOptionsComponent(self.ancestor)
        self.select_coverage = SelectCoverageComponent(self.ancestor)
        self.insurance_details = InsuranceItemComponent(self.ancestor)
        self.bundle_details = BundleDetailsComponent(self.ancestor)
        self.build_your_own = BuildYourOwnComponent(self.ancestor)

    def open_actions(self):
        self.ancestor.trade_in.open()
        self.ancestor.trade_in.skip_trade_in()
        if self.ancestor.payment.selected_lease:
            self.select_coverage.wait_for.presence_of_element_located(wait_time=10)
        else:
            self.coverage_options.btn_continue.wait_for.presence_of_element_located(wait_time=10)

    @step_decorator('Web - InsuranceComponent: Open F&I step from Offer Builder Flow')
    def open_fi_ob_flow(self):
        self.ancestor.trade_in.open()
        self.ancestor.trade_in.skip_trade_in()
        self.ancestor.insurance.select_coverage.wait_for.presence_of_element_located(wait_time=10)

    @step_decorator('WEB - InsuranceComponent: refresh page')
    def refresh_page(self):
        self.ancestor.refresh()
        self.ancestor.exists(wait_time=5)
        self.ancestor.switch_to.default_content()
        self.ancestor.consumer.iframe_new_vdp.switch_to()
        self.ancestor.insurance.wait_for.presence_of_element_located(wait_time=10)
        return self.ancestor.insurance

    @step_decorator('WEB - InsuranceComponent: select {insurance_item_name} insurance')
    def select_insurance_item(self, insurance_item_name: str):
        if self.ancestor.payment.cbo_payment_option_entity.payment_type != PaymentOptionType.LEASE:
            self.coverage_options.btn_continue.js.click()
        before = self.ancestor.txt_payment_amount.text
        self.select_coverage.add_insurance_item(insurance_item_name)
        self.ancestor.txt_payment_amount.wait_for.text_to_be_present_in_element(text=before, until=False, wait_time=5)

    @step_decorator('WEB - InsuranceComponent: select {bundle_name} bundle')
    def select_bundle(self, bundle_name: str):
        if self.ancestor.payment.cbo_payment_option_entity.payment_type != PaymentOptionType.LEASE:
            self.coverage_options.btn_continue.js.click()
        before = self.ancestor.txt_payment_amount.text
        self.select_coverage.add_bundle(bundle_name)
        self.ancestor.txt_payment_amount.wait_for.text_to_be_present_in_element(text=before, until=False, wait_time=10)

    @step_decorator('WEB - InsuranceComponent: get {insurance_item_name} insurance')
    def get_insurance_item(self, insurance_item_name: str):
        return self.select_coverage.get_insurance_item(insurance_item_name)

    @step_decorator('WEB - InsuranceComponent: apply insurance item')
    def apply_insurance_item(self):
        text = self.ancestor.txt_service_protection.text
        self.select_coverage.btn_next.click()
        self.ancestor.txt_service_protection.wait_for.text_to_be_present_in_element(text=text, until=False, wait_time=5)

    @step_decorator('WEB - InsuranceComponent: apply bundle')
    def apply_bundle(self):
        text = self.ancestor.txt_service_protection.text
        self.select_coverage.btn_save_and_continue.click()
        self.ancestor.txt_service_protection.wait_for.text_to_be_present_in_element(text=text, until=False, wait_time=5)


@find_by(Xpath('.//h1[text()="Let’s prepare your coverage options"]/../..'))
class CoverageOptionsComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_continue = Button(CssSelector('[data-testid="cbo-nav-bottom"] button'))
        self.btn_back = Button(CssSelector('aria-label="step back button"]'))

    def open_actions(self):
        pass

    @step_decorator('WEB - CoverageOptionsComponent: select {miles} miles')
    def select_miles(self, miles: str = None):
        button = Button(Xpath(f".//div[contains(text(),'{miles}')]"))
        button.ancestor = self
        button.click()

    @step_decorator('WEB - CoverageOptionsComponent: select {years} years')
    def select_years(self, years: str = None):
        button = Button(Xpath(f"//div[contains(text(),'{years}')]"))
        button.ancestor = self
        button.click()

    def apply_coverage_options(self, miles: str = None, years: str = None):
        if miles:
            self.select_miles(miles)
        if years:
            self.select_years(years)
        self.btn_continue.click()
        self.wait_for.presence_of_element_located(wait_time=10, until=False)


@find_by(Xpath(
    ".//h1[text()=\"Now, let's select your coverage\"]/../.. | //section[contains(@class,'BundleList__Wrapper')]"))
class SelectCoverageComponent(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.build_own_coverage_package = BuildOwnCoveragePackageComponent(self.ancestor)
        self.tbl_cards = Table(Xpath(".//div[contains(@class,'Item__Card')]"), Card)
        self.tbl_insurance_items = Table(Xpath('.//div[contains(@data-testid,"insurance-item")]'), InsuranceItem)
        self.tbl_bundles = Table(Xpath('.//section[@data-testid="bundle-card"]'), Bundle)
        self.btn_not_interested = Button(Xpath(".//div[contains(@class,'NotInterested__Wrapper')]"))
        self.btn_back = Button(CssSelector('[aria-label="step back button"]'))
        self.txt_description = TextBlock(Xpath(".//section[contains(@class,'BundleList__Desctiption')]"))
        self.btn_next = Button(CssSelector('[data-testid="cbo-nav-bottom"] button'))
        self.btn_save_and_continue = Button(Xpath(".//button[contains(text(),'Save and Continue')]"))
        self.btn_continue_without_coverage = Button(
            Xpath(".//button[contains(text(),'Continue without any coverage')]"))
        self.txt_error_message = TextBlock(Xpath("./p"))

    def open_actions(self):
        pass

    @step_decorator('WEB - SelectCoverage: select {insurance_items_name} insurance')
    def add_insurance_item(self, insurance_items_name: str):
        self.tbl_insurance_items.wait_for.presence_of_all_elements_located(wait_time=10)
        insurance = self.get_insurance_item(insurance_items_name)
        text = insurance.btn_add.text
        insurance.btn_add.click()
        insurance.btn_add.wait_for.text_equal(text, wait_time=5, until=False)

    @step_decorator('WEB - SelectCoverage: select {bundle_name} bundle')
    def add_bundle(self, bundle_name: str):
        self.tbl_bundles.wait_for.presence_of_all_elements_located(wait_time=10)
        bundle = self.get_bundle(bundle_name)
        bundle.btn_select.click()

    @step_decorator('WEB - InsuranceComponent: Get Selected insurance item')
    def get_selected_item(self):
        fi_data = []
        self.tbl_insurance_items.wait_for.presence_of_all_elements_located(wait_time=5)
        content = self.tbl_insurance_items.get_rows_by_attribute_value(column_name="btn_add", attribute_name="textContent"
                                                                       , attribute_value="Added")
        for selected in content:
            entity = InsuranceEntity(title=selected.txt_title.text, description=selected.txt_description.text,
                                     monthly_price=int(selected.txt_price_per_month.text),
                                     total_price=int(re.sub(r"[^\d]", "", selected.txt_total_price.text)))
            fi_data.append(entity)
        return fi_data

    @step_decorator('WEB - InsuranceComponent: Get Selected bundle option')
    def get_selected_bundle(self):
        fi_data = []
        self.tbl_bundles.wait_for.presence_of_all_elements_located(wait_time=5)
        content = self.tbl_bundles.get_rows_by_attribute_value(column_name="btn_select", attribute_name="textContent",
                                                               attribute_value="Selected")
        for selected in content:
            entity = BundleEntity(title=selected.txt_title.text, description=selected.txt_description.text,
                                  monthly_price=int(selected.txt_monthly_price.text),
                                  products_list=selected.txt_products_list.text)
            fi_data.append(entity)
        return fi_data

    @step_decorator('WEB - SelectCoverage: get {insurance_items_name} insurance')
    def get_insurance_item(self, insurance_items_name: str):
        self.tbl_insurance_items.wait_for.presence_of_all_elements_located(wait_time=10)
        insurance = self.tbl_insurance_items.get_row_by_column_value(column_name="txt_title",
                                                                     value=insurance_items_name)
        if not insurance:
            raise AssertionError(f"Unable to find {insurance_items_name} insurance")
        return insurance

    @step_decorator('WEB - SelectCoverage: get {bundle_name} bundle')
    def get_bundle(self, bundle_name: str):
        self.tbl_bundles.wait_for.presence_of_all_elements_located(wait_time=10)
        insurance = self.tbl_bundles.get_row_by_column_value(column_name="txt_title", value=bundle_name)
        if not insurance:
            raise AssertionError(f"Unable to find {bundle_name} bundle")
        return insurance

    @step_decorator('WEB - SelectCoverage: continue without any coverage')
    def continue_without_coverage(self):
        self.btn_continue_without_coverage.click()
        self.wait_for.presence_of_element_located(wait_time=10, until=False)


class Card:
    def __init__(self):
        self.txt_title = TextBlock(Xpath(".//div[contains(@class,'Item__Title')]"))
        self.txt_description = TextBlock(Xpath(".//div[contains(@class,'Item__Description')]"))
        self.img_parent = Image(Xpath(".//div[contains(@class,'Image__Parent')]/img"))
        self.btn_add = Button(CssSelector('[aria-label="Add"]'))
        self.btn_details = Button(CssSelector('[aria-label="Details"]'))
        self.txt_price = TextBlock(Xpath(".//sup/.."))
        self.txt_total_price = TextBlock(Xpath(".//div[contains(@class,'Item__TotalPrice')]"))


class InsuranceItem:
    def __init__(self):
        self.txt_title = TextBlock(Xpath(".//div[contains(@class,'Item__Title')]"))
        self.txt_description = TextBlock(Xpath(".//div[contains(@class,'Item__Description')]"))
        self.txt_price_per_month = TextBlock(Xpath(".//div[contains(@class,'Item__PriceWrap')]/div/div"))
        self.txt_total_price = TextBlock(Xpath(".//div[contains(@class,'Item__TotalPrice')]"))
        self.btn_add = Button(CssSelector('[aria-label="Add"]'))
        self.btn_details = Button(CssSelector('[aria-label="Details"]'))


class Bundle:
    def __init__(self):
        self.txt_title = TextBlock(Xpath(".//div[contains(@class,'BundleCard__Title')]"))
        self.txt_description = TextBlock(Xpath(".//div[contains(@class,'BundleCard__Description')]"))
        self.txt_products_list = TextBlock(Xpath(".//ul[contains(@class,'BundleCard__ProductsList')]"))
        self.txt_monthly_price = TextBlock(Xpath(".//div[@data-testid='product-month-price']"))
        self.btn_view_details = TextBlock(Xpath(".//button[contains(@class,'ViewDetails')]"))
        self.btn_select = TextBlock(CssSelector('[aria-label="Add"]'))


@find_by(Xpath(".//div[contains(@class,'Modal__ModalContent')]"))
class InsuranceItemComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_title = TextBlock(Xpath("./div/h1"))
        self.btn_close = Button(CssSelector('[data-testid="modal-close-button"]'))
        self.btn_add = Button(Xpath(".//button[contains(text(),'Add')]"))

    def open_actions(self):
        pass

    @step_decorator('WEB - BundleDetailsComponent: close window')
    def close_window(self):
        self.btn_close.click()
        self.wait_for.presence_of_element_located(wait_time=5, until=False)


@find_by(Xpath(".//section[contains(@class,'BundleDetails__Wrapper')]"))
class BundleDetailsComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_title = TextBlock(Xpath("./h1"))
        self.btn_close = Button(CssSelector('[aria-label="step back button"]'))
        self.btn_continue = Button(Xpath(".//button[contains(text(),'Continue')]"))

    def open_actions(self):
        pass

    @step_decorator('WEB - BundleDetailsComponent: close window')
    def close_window(self):
        self.btn_close.click()
        self.wait_for.presence_of_element_located(wait_time=5, until=False)


@find_by(Xpath(".//div[text()='Want to build own coverage package?']/.."))
class BuildOwnCoveragePackageComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_start = Button(CssSelector('[aria-label="Start"]'))

    def open_actions(self):
        pass


@find_by(Xpath(".//h1[text()='Build Your Own']/.."))
class BuildYourOwnComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.product_details_component = ProductDetails(self.ancestor)
        self.tbl_products = Table(Xpath(".//section[contains(@class,'CardWrapper__StyledWrapp')]"), OwnProduct)
        self.btn_back = Button(CssSelector('[aria-label="step back button"]'))

    def open_actions(self):
        pass

    @step_decorator('WEB - BuildYourOwnComponent: open {product_title} product details')
    def open_product_details(self, product_title: str):
        product = self.tbl_products.get_row_by_column_value("txt_title", product_title)
        assert product, f"Unable to find product by {product_title} name"
        product.view_details_button.click()
        return product


class OwnProduct:
    def __init__(self):
        self.txt_title = TextBlock(Xpath(".//div[contains(@class,'ProductCard__Title')]"))
        self.view_details_button = Button(Xpath(".//button[contains(@class,'ViewDetails__Wrapper')]"))
        self.add_button = Button(CssSelector('[aria-label="Add"]'))


@find_by(Xpath(".//div[contains(@class,'ProductDetails__StyledBackButton')]/../../.."))
class ProductDetails(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.dll_coverage_option = SelectExtended(
            CssSelector('[aria-label="Select coverage option"]'), CssSelector("[role='option']"))
        self.btn_back = Button(Xpath("//div[contains(@class,'ProductDetails__StyledBackButton')]"))
