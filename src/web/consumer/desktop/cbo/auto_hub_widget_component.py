from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.webdriver.support import expected_conditions

from kom_framework.src.general import Log
from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_types import Button, SelectExtended, Input
from kom_framework.src.web.page_object import PageObject
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
# from src.utils.twilio_tools.twilio_service import TwilioService
from src.web.entities.lead_entity import WebLeadEntity
from src.web.entities.trade_in_entity import TradeInEntity


@find_by(Xpath("//title[text()='AutoHub Online Offer']/../.."))
class AutoHubWidget(PageObject):
    def __init__(self, consumer=None):
        self.consumer = consumer
        self.btn_year_modal = Button(Xpath(".//span[text()='Year/Model']"))
        self.dll_select_year = SelectExtended(link_locator=CssSelector("#year"))
        self.dll_select_vehicle = SelectExtended(link_locator=CssSelector('[placeholder="Search model..."]'),
                                                 option_list_locator=CssSelector(".MuiAutocomplete-option"))
        self.inp_mileage = Input(CssSelector("#mileage"))
        self.inp_name = Input(CssSelector('input[placeholder="John Doe"]'))
        self.inp_tel = Input(CssSelector("input[type='tel']"))
        self.inp_verification_code = Input(CssSelector('[name="Verification Code"]'))
        self.btn_continue = Button(CssSelector(".MuiButton-containedPrimary"))
        self.btn_done = Button(Xpath(".//button[text()='Done']"))
        self.btn_vehicle_history = Button(Xpath(".//span[text()='Yes, all good!']"))
        self.btn_cosmetic_condition = Button(CssSelector('[for="cosmetic condition:-:rh:"]'))
        self.btn_mechanical_condition = Button(
            CssSelector('[for="mechanical condition:-:rs:"]'))  # "[name='mechanical condition:'][value='7']"
        self.btn_safety_condition = Button(
            CssSelector('[for="safety and drivability:-:r17:"]'))  # "[name='safety and drivability:'][value='7']"

    def open_actions(self):
        if not self.exist():
            if self.consumer.trade_in.btn_yes.exists():
                self.consumer.trade_in.btn_yes.js.click()
            self.consumer.iframe_autohub_widget.exists(15)
            self.consumer.iframe_autohub_widget.switch_to()
            self.dll_select_year.wait_for.presence_of_element_located(wait_time=5)

    def exist(self, wait_time: int = 0) -> bool:
        Log.debug(f"Page {self.page_name} existence verification. Wait time = {wait_time}")
        if self.driver:
            try:
                self.wait_for.condition(wait_time, expected_conditions.visibility_of_element_located(self.locator))
                return True
            except (NoSuchElementException, TimeoutException):
                Log.debug(f"Page {self.page_name} was not found")
        return False

    def exists(self, wait_time: int = 0) -> bool:
        if not self.driver:
            self.set_session_key(self.consumer.get_session_key())
        if self.consumer.iframe_autohub_widget.exists():
            self.consumer.iframe_autohub_widget.switch_to()
        try:
            return self.exist()
        except TimeoutException:
            Log.warning("AutoHub widget was not found")

    @step_decorator('WEB - AutoHubWidget: trade in')
    def trade_in(self, vehicle: TradeInEntity, lead_info: WebLeadEntity = None):
        self.open()
        # Vehicle section
        self.dll_select_year.select_item_by_value(vehicle.year)
        self.dll_select_vehicle.select_item_by_text(f"{vehicle.make} {vehicle.model} {vehicle.trim}", type_keys=True)
        self.inp_mileage.send_keys(vehicle.mileage)
        self.btn_continue.click()
        # Condition section
        self.btn_cosmetic_condition.click()
        self.btn_mechanical_condition.click()
        self.btn_safety_condition.click()
        self.btn_continue.click()
        # History section
        self.btn_vehicle_history.click()
        self.btn_continue.click()
        # Your Offer section
        self.btn_done.wait_for.presence_of_element_located(wait_time=10)
        if self.inp_name.exists(wait_time=2):
            self.inp_name.send_keys(f"{lead_info.first_name} {lead_info.last_name}")
            self.inp_tel.send_keys(lead_info.phone)
        from time import sleep
        sleep(1)  # added sleep to avoid issue with AutoHub widget
        self.btn_done.click()
        # if self.inp_verification_code.exists(wait_time=2):
        # TwilioService.read_sms()
        self.switch_to.default_content()
        if self.consumer.consumer.iframe_new_vdp.exists():
            self.consumer.consumer.iframe_new_vdp.switch_to()
