from src.web.entities.trade_in_entity import TradeInProvider


class GreeterMessages:
    MAIN = "Welcome! I'm your retail specialist, ready to guide you through your online buying process. Pick a step above to begin."
    PAYMENTS = "Questions on your lease or finance payment options? I'm here to help."
    TRADE_IN = "You can trade in your current vehicle to lower your monthly payment."
    REBATES = "Select the rebates that you best qualify for. If you have any questions, I'm here standing by."
    SERVICE = "A majority of our customers add a few protection packages for their vehicle. I'll be happy to walk you through them."
    SUMMARY = "Double-check that everything looks good. Remember, taxes and fees are included in your monthly payment."
    TIME_SAVING = "You can typically save 25 mins by submitting your financing online!"
    CREDIT = "Great choice! Filling out your credit application now will save you time at our dealership."
    APPOINTMENT = "What time works best for you to come in?"


class SortingType:
    LOWEST_PRICE = "Lowest Price"
    HIGHEST_PRICE = "Highest Price"
    NEWEST = "Newest"
    OLDEST = "Oldest"
    LOWEST_MILEAGE = "Lowest Mileage"
    HIGHEST_MILEAGE = "Highest Mileage"


get_grid_view = {
    (("Make", ("Subaru",)),): {'2019 Impreza': '15 in stock', '2018 Forester': '3 in stock',
                               '2019 Legacy': '15 in stock', '2019 Forester': '10 in stock',
                               '2019 Crosstrek': '9 in stock', '2019 Outback': '32 in stock', '2019 BRZ': '1 in stock',
                               '2019 WRX': '1 in stock', '2018 Outback': '1 in stock', '2019 Ascent': '22 in stock'},
    (('Make', ('Subaru', 'Dodge')),): {'2019 Impreza': '15 in stock', '2018 Forester': '3 in stock',
                                       '2019 Legacy': '15 in stock', '2019 Forester': '10 in stock',
                                       '2019 Crosstrek': '9 in stock', '2019 Outback': '32 in stock',
                                       '2021 Challenger': '2 in stock', '2019 BRZ': '1 in stock',
                                       '2021 Charger': '1 in stock', '2019 WRX': '1 in stock',
                                       '2018 Outback': '1 in stock', '2019 Ascent': '22 in stock'},
    (('Make', ('Honda',)),): ['5J6RM3H38EL043027', '1HGCS22828A002488', '2HGFB2F58CH557632', '1HGCR2F5XDA235332',
                              '5FNRL5H32GB062053', '2HKRM3H70CH513741', '5FNRL5H63FB121460', '2HKRM4H38FH604277',
                              '1HGCR2F54FA266739', '1HGCV1F12KA171710', '2HGFC2F50JH516437', '5FNYF5H51HB001220',
                              '2HGFC2F74HH556340', '5FNYF5H57JB032347', '5J6RW6H31KL002620', '1HGCV1F49JA201536',
                              '1HGCV1F39JA170537', '2HGFE2F20NH545314', '7FARW1H88JE049833', '19XZE4F5XNE010343',
                              '1HGCV1F17LA057171', '2HGFE2F50NH552144', '19XFC1F38ME206530', '5FPYK3F51HB018187',
                              '7FARW2H82JE100894', '19XFL2H87NE002933', '5FNRL6H73LB071072', '1HGCV1F31NA027510',
                              '5FNYF5H22NB026919', '5FNYF5H33LB021069', '2HGFE2F51PH510942', '1HGCV1F34NA052501',
                              '7FARW2H75ME029346', '1HGCV2F92LA023098', '5FNRL6H72KB054634', '2HGFE2F52PH500341',
                              '1HGCV2F91MA016368', '5FPYK3F54PB005058', '5FNRL6H71KB094087', '5FNYF5H39NB007342',
                              '5FNRL6H56MB002098', '7FART6H94ME038167', '5FNYF5H27NB031209', '2HKRS3H48RH324291',
                              '5FNYF5H29NB023208', '5FNRL6H89NB023023', '5FNRL6H63PB045388', 'SHHFK8G79LU200370',
                              '5FNYG1H8XSB031954'],
    (('Make', ('Lexus', 'Toyota')),): ['4T1BE46K89U350245', 'JTEZU14RX78074639', '5TDKY5G1XAS027511',
                                       'JTJBM7FX7B5029301', '4T1BK1EB7FU145472', '4T1B11HK2JU585073',
                                       '5YFEPMAE1MP206426', 'JTMYFREV8GJ069534', '2T2ZK1BAXFC174326',
                                       'JTHBK1GG9E2113824', '5YFEPMAE0NP345383', 'JTHBK1GG7E2100585',
                                       'JTHBK1GG2G2214674', '4T1G11AK5MU605997', 'JTJBZMCA6H2018980',
                                       '4T1K61AK1LU397496', 'JTHBA1D27J5068712', 'JTJDARBZ7M2191101',
                                       '2T2ZZMCAXJC109769', '5TDYZ3DC1KS983236', '3TYAX5GN7MT021868',
                                       'JTM16RFV4ND060101', 'JTMRWRFV8ND158275', '3TMAZ5CN0NM193723',
                                       '5TFLA5DB4NX051342', 'JTJHY7AX7H4234279', '5TFNA5DB6PX102030',
                                       '5TDERKECXRS180866'],
    (('Make', ('Chevrolet',)),): ['1G1PA5SHXE7152421', '1G1BE5SM4G7255994', 'KL7CJLSB3GB570810', '3GNAL4EK2ES578525',
                                  '2G1WC5E37E1186741', '1G1BE5SM8H7245437', '1G1BE5SM7H7244148', '1G1BE5SM6H7246036',
                                  '1G1BE5SM1H7247062', '1G1BE5SM0H7237509', '1G1ZE5STXHF246276', '1G1ZE5ST5HF227795',
                                  '1G1ZE5ST3HF257863', '2G1105S30H9175741', '3GCUKTEC8HG422525', '2GNALCEK7G1138244',
                                  '2G1105S30H9175982', 'KL7CJPSB1JB666563', '1G1ZD5ST2JF215189', '1G1105S34HU202197',
                                  '1G1105S35HU201611', '2G1105S33H9166127', '2GNFLFEK0H6237578', '1G1FA1RXXH0130286',
                                  '1GNKRGKD2HJ118474', '2GNAXUEVXK6117243', '1G1FB1RS3H0124301', '3GCPWCED5KG103388',
                                  '1GCVKREC2FZ438806', '1GCGTCEN3J1131107', '1GCGTDE37G1215668', '1GCVKREC3GZ126074',
                                  '1GCVKPEC2HZ277817', '3GCUKREC5GG176650', '1GCVKREC9GZ233968', '1GC2KVEG2FZ103219',
                                  '1GCGTEEN4H1289560', '1G1YA2D75F5104846', '1GNSKCKCXFR126616', '1GNSKCKC0GR471302',
                                  '1G1YT2D67J5600712'],
    (('Make', ('Kia', 'Subaru')),): ['KNAFX4A82G5475967', 'KNDJN2A26G7249521', 'KNDJN2A28G7293679', '3KPA24AB7JE085014',
                                     'KNDJP3A54G7283290', '5XYKT4A61FG655104', '5XXGT4L34GG026726', '5XXGT4L34GG046099',
                                     '3KPFL4A79JE273628', 'KNAGT4L30G5071227', 'KNDPBCAC1F7749134', '5XXGT4L34GG033580',
                                     'KNDPC3AC1G7845277', '5XYKU4A71FG608354', '5XYPG4A31GG050145', '4S3BNAB6XH3021451',
                                     'KNDPM3AC1H7060613', 'KNDPCCA63G7871910', '4S4BRCCC5E3292543', '4S3GKAD60J3623261',
                                     'JF2SJAGC2GH534058', '5XXGU4L35GG077987', '5XYPGDA56GG168483', 'JF2GPABC7HH270252',
                                     '5XYPHDA16GG034020', 'JF2GTAAC9JH332785', 'JF2SJAHCXGH494813', '5XYPKDA55GG151611',
                                     '5XYPKDA59GG130048', '5XYPKDA54GG119832', '5XYPKDA57GG173075',
                                     '4S4BSANC0H3219204'],
    (('Model', ('Accord Hybrid',)),): ['1HGCY2F58RA096263', '1HGCY2F59RA096434', '1HGCY2F52RA094931',
                                       '1HGCY2F52RA093701', '1HGCY2F52RA068698', '1HGCY2F50RA095365',
                                       '1HGCY2F6XRA097536', '1HGCY2F69RA095793', '1HGCY2F64RA087889',
                                       '1HGCY2F62RA095764', '1HGCY2F62RA083453', '1HGCY2F79RA070997',
                                       '1HGCY2F78RA081070', '1HGCY2F76RA074036', '1HGCY2F73RA081039',
                                       '1HGCY2F68RA083070', '1HGCY2F67RA093427', '1HGCY2F66RA096528',
                                       '1HGCY2F66RA059544', '1HGCY2F65RA084354', '1HGCY2F65RA081373',
                                       '1HGCY2F62RA095392', '1HGCY2F60RA096654', '1HGCY2F7XRA075173',
                                       '1HGCY2F79RA090098', '1HGCY2F78RA093915', '1HGCY2F77RA079097',
                                       '1HGCY2F74RA087092', '1HGCY2F73RA091585', '1HGCY2F72RA094235',
                                       '1HGCY2F72RA080951', '1HGCY2F70RA092404', '1HGCY2F70RA082066',
                                       '1HGCY2F70RA082035', '1HGCY2F70RA071746', '1HGCY2F59SA006477',
                                       '1HGCY2F59SA003322', '1HGCY2F57SA010480', '1HGCY2F5XSA008402',
                                       '1HGCY2F5XSA004253', '1HGCY2F57SA001858', '1HGCY2F66SA003965',
                                       '1HGCY2F65SA009482', '1HGCY2F75SA009717', '1HGCY2F6XSA011681',
                                       '1HGCY2F65SA004590', '1HGCY2F64SA002488', '1HGCY2F74SA008073',
                                       '1HGCY2F73SA010641', '1HGCY2F73SA008520', '1HGCY2F8XRA091737',
                                       '1HGCY2F87RA083711', '1HGCY2F87RA078959', '1HGCY2F85RA091256',
                                       '1HGCY2F85RA088664', '1HGCY2F85RA083321', '1HGCY2F83RA083169',
                                       '1HGCY2F80RA096039', '1HGCY2F8XRA093570', '1HGCY2F87RA087998',
                                       '1HGCY2F85RA079253', '1HGCY2F81RA083025', '1HGCY2F80RA084425',
                                       '1HGCY2F80SA013053', '1HGCY2F88RA057702', '1HGCY2F83SA013905',
                                       '1HGCY2F82SA014186', '1HGCY2F7XSA015187', '1HGCY2F7XSA013276',
                                       '1HGCY2F72RA068184', '1HGCY2F6XRA077089', '1HGCY2F65SA012866',
                                       '1HGCY2F63SA013482', '1HGCY2F58SA014540', '1HGCY2F57SA015081',
                                       '1HGCY2F57SA014142'],
    (('Model', ("Accord", "HR-V")),): ['3CZRZ1H38SM725065', '3CZRZ1H39SM719646', '3CZRZ1H34SM718839',
                                       '3CZRZ1H38SM726166', '3CZRZ1H38SM723770', '3CZRZ1H33SM723787',
                                       '3CZRZ1H32SM729368', '3CZRZ1H30SM724413', '3CZRZ1H59SM720779',
                                       '3CZRZ1H59SM718949', '3CZRZ1H53SM721927', '3CZRZ2H5XSM742683',
                                       '3CZRZ1H79SM722484', '3CZRZ1H76SM730302', '3CZRZ1H75SM727701',
                                       '3CZRZ1H73SM718687', '3CZRZ1H7XSM726883', '3CZRZ1H72SM724660',
                                       '3CZRZ1H70SM724964', '3CZRZ2H79SM744104', '3CZRZ2H75SM751423',
                                       '3CZRZ2H58SM752239', '3CZRZ1H77SM729997', '3CZRZ1H76SM730655',
                                       '3CZRZ1H74SM732422', '3CZRZ1H73SM731858', '3CZRZ1H70SM730943',
                                       '3CZRZ1H57SM731599', '3CZRZ1H50SM730116', '3CZRZ1H50SM726079',
                                       '3CZRZ1H3XSM733586', '3CZRZ1H39SM732591', '3CZRZ1H38SM731741',
                                       '3CZRZ1H33SM729895', '3CZRZ1H32SM731704', '1HGCY1F4XSA005744',
                                       '1HGCY1F48SA008030', '1HGCY1F48SA007007', '1HGCY1F48SA005967',
                                       '1HGCY1F48SA003829', '1HGCY1F48SA003314', '1HGCY1F47SA005376',
                                       '1HGCY1F46SA007541', '1HGCY1F46SA001822', '1HGCY1F46SA001366',
                                       '1HGCY1F45SA007899', '1HGCY1F43SA007853', '1HGCY1F43SA001731',
                                       '1HGCY1F41SA007303', '1HGCY1F40SA010256', '1HGCY1F40SA007549',
                                       '1HGCY1F40SA003825', '1HGCY1F36RA087067', '1HGCY1F2XSA003944',
                                       '1HGCY1F29SA002896', '1HGCY1F29SA002588', '1HGCY1F29SA001201',
                                       '1HGCY1F28SA010617', '1HGCY1F28SA005191', '1HGCY1F27RA090180',
                                       '1HGCY1F27RA075128', '1HGCY1F27RA065201', '1HGCY1F26RA088825',
                                       '1HGCY1F26RA083222', '1HGCY1F26RA072446', '1HGCY1F24RA083056',
                                       '1HGCY1F22SA009284', '1HGCY1F22SA007275', '1HGCY1F22SA004909',
                                       '1HGCY1F21RA089834', '1HGCY1F21RA089235', '1HGCY1F20SA006383',
                                       '1HGCY1F20SA000597'],
    (('Model', ('CR-V',)),): ['5J6RM3H38EL043027', '2HKRM3H70CH513741', '2HKRM4H38FH604277', '5J6RW6H31KL002620',
                              '7FARW1H88JE049833', '7FARW2H82JE100894', '7FARW2H75ME029346', '2HKRS3H48RH324291'],
    (('Model', ('CR-V', 'Wrangler')),): ['5J6RM3H38EL043027', '2HKRM3H70CH513741', '2HKRM4H38FH604277',
                                         '1C4BJWDG7CL229830', '5J6RW6H31KL002620', '1C4BJWDG1GL123119',
                                         '7FARW1H88JE049833', '7FARW2H82JE100894', '1C4HJXFG2JW258659',
                                         '7FARW2H75ME029346', '1C4HJXDG6LW229185', '2HKRS3H48RH324291'],
    (('Model', ('Civic',)),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '2HGFE2F51PH510942'],
    (('Model', ('Odyssey', 'Civic')),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '2HGFE2F51PH510942',
                                         '5FNRL6H71KB094087', '5FNRL6H89NB023023'],
    (('Model', ('Pilot',)),): ['5FNYG2H35SB013369', '5FNYG1H39SB048300', '5FNYG2H32SB018075', '5FNYG2H32SB015709',
                               '5FNYG2H47SB008120', '5FNYG1H45SB027498', '5FNYG2H44SB018099', '5FNYG2H40SB018746',
                               '5FNYG2H40SB014258', '5FNYG2H44SB012321', '5FNYG1H42SB005393', '5FNYG1H4XSB044376',
                               '5FNYG1H49SB017623', '5FNYG1H47SB055920', '5FNYG1H45SB055933', '5FNYG2H41SB007108',
                               '5FNYG2H47SB018436', '5FNYG2H40SB018911', '5FNYG1H44SB056426', '5FNYG1H49SB041534',
                               '5FNYG1H48SB034719', '5FNYG1H41SB030348', '5FNYG1H44SB054966', '5FNYG2H48SB009972',
                               '5FNYG2H43SB015890', '5FNYG2H40SB008816', '5FNYG2H45SB016605', '5FNYG1H63SB002259',
                               '5FNYG1H40SB070727', '5FNYG1H45SB056953', '5FNYG1H60SB006172', '5FNYG1H40SB063485',
                               '5FNYG1H44SB066146', '5FNYG1H48SB063766', '5FNYG2H76SB004618', '5FNYG1H79SB006566',
                               '5FNYG1H40SB072851', '5FNYG1H69SB024847', '5FNYG1H63SB041045', '5FNYG2H71SB002212',
                               '5FNYG2H79SB015743', '5FNYG1H6XSB031788', '5FNYG1H69SB036691', '5FNYG1H64SB017014',
                               '5FNYG1H63SB030420', '5FNYG1H61SB044865', '5FNYG1H61SB028648', '5FNYG1H61SB054764',
                               '5FNYG1H65SB025946', '5FNYG2H7XSB017081', '5FNYG2H78SB017046', '5FNYG2H76SB018292',
                               '5FNYG2H76SB018289', '5FNYG2H71SB017535', '5FNYG2H71SB014456', '5FNYG1H68SB056608',
                               '5FNYG1H62RB034288', '5FNYG1H79SB048655', '5FNYG1H75SB025289', '5FNYG1H4XSB067852',
                               '5FNYG1H72SB041773', '5FNYG1H73SB059764', '5FNYG1H69SB075958', '5FNYG1H60SB069062',
                               '5FNYG1H64SB025498', '5FNYG1H62SB061173', '5FNYG1H79SB072003', '5FNYG1H79SB071563',
                               '5FNYG1H76SB076008', '5FNYG1H72SB071209', '5FNYG1H69SB063552', '5FNYG1H68SB074400',
                               '5FNYG1H6XSB040409', '5FNYG1H67SB023681', '5FNYG1H6XSB000234', '5FNYG1H68SB072081',
                               '5FNYG2H7XSB017579', '5FNYG1H88SB056089', '5FNYG1H95SB044801', '5FNYG1H85SB071634',
                               '5FNYG1H81SB075227', '5FNYG1H89SB078568', '5FNYG1H82SB076533', '5FNYG1H84SB039614',
                               '5FNYG1H82SB030216', '5FNYG1H99SB064579', '5FNYG1H90SB058783', '5FNYG2H79SB018996',
                               '5FNYG2H77SB016972', '5FNYG2H73SB019237', '5FNYG2H72SB016779', '5FNYG2H4XSB020021',
                               '5FNYG2H4XSB019225', '5FNYG2H49SB019636', '5FNYG2H40SB019119', '5FNYG1H95SB072677',
                               '5FNYG1H93SB075433', '5FNYG1H90SB077365', '5FNYG1H8XSB085111', '5FNYG1H8XSB036765',
                               '5FNYG1H88SB080604', '5FNYG1H88SB036411', '5FNYG1H85SB080513', '5FNYG1H83SB083684',
                               '5FNYG1H79SB079775', '5FNYG1H78SB073269', '5FNYG1H76SB038844', '5FNYG1H74SB072345',
                               '5FNYG1H71SB080841', '5FNYG1H6XSB035081', '5FNYG1H64SB073390', '5FNYG1H61SB085254',
                               '5FNYG1H48SB072144', '5FNYG1H46SB063247', '5FNYG1H41SB084264', '5FNYG1H41SB081333',
                               '5FNYG1H41SB081252'],
    (('Year', ('2025',)),): {'2025 Civic': '48 in stock', '2025 HR-V': '35 in stock', '2025 Accord': '28 in stock',
                             '2025 CR-V': '86 in stock', '2025 Accord Hybrid': '25 in stock',
                             '2025 CR-V Hybrid': '104 in stock', '2025 Pilot': '116 in stock',
                             '2025 Odyssey': '61 in stock', '2025 Ridgeline': '22 in stock',
                             '2025 Passport': '17 in stock'},
    (('Year', ("2024", "2025")),): {'2025 Civic': '48 in stock', '2025 HR-V': '35 in stock', '2024 Civic': '2 in stock',
                                    '2024 Accord': '10 in stock', '2025 Accord': '28 in stock',
                                    '2025 CR-V': '86 in stock', '2024 Accord Hybrid': '51 in stock',
                                    '2024 CR-V': '1 in stock', '2025 Accord Hybrid': '25 in stock',
                                    '2025 CR-V Hybrid': '104 in stock', '2024 Ridgeline': '14 in stock',
                                    '2025 Pilot': '116 in stock', '2024 Odyssey': '1 in stock',
                                    '2025 Odyssey': '61 in stock', '2025 Ridgeline': '22 in stock',
                                    '2025 Passport': '17 in stock', '2024 Passport': '4 in stock',
                                    '2024 Prologue': '72 in stock', '2024 Pilot': '1 in stock'},
    (('Year', ('2022',)),): ['5YFEPMAE0NP345383', '2HGFE2F20NH545314', '19XZE4F5XNE010343', '2HGFE2F50NH552144',
                             '19XFL2H87NE002933', '1HGCV1F31NA027510', '5FNYF5H22NB026919', '1HGCV1F34NA052501',
                             '5FNYF5H39NB007342', '7SAYGAEEXNF359991', '5FNYF5H27NB031209', 'JTM16RFV4ND060101',
                             'JTMRWRFV8ND158275', '3TMAZ5CN0NM193723', '5FNYF5H29NB023208', '5FNRL6H89NB023023',
                             '2C3CDZFJ3NH223484', '5TFLA5DB4NX051342', '3MW5U7J09N8C34682'],
    (('Year', ('2017', '2018')),): ['1FADP5EU5HL111486', '3C4NJDBB6HT679649', '1FMJU1HT6HEA51526', 'KNDPM3ACXH7248062',
                                    'ZACCJABB1HPG02006', '4T1B11HK2JU585073', '5N1DR2MN3HC658958', '2HGFC2F50JH516437',
                                    '1FM5K7D8XJGA88858', '5FNYF5H51HB001220', '2HGFC2F74HH556340', '5GAERBKW7JJ257100',
                                    '5FNYF5H57JB032347', 'JN8AZ2NF4J9663527', '1FM5K7FH0JGB88674', 'WA1BCCFS2JR017660',
                                    '1HGCV1F49JA201536', '1HGCV1F39JA170537', '7FARW1H88JE049833', 'JTJBZMCA6H2018980',
                                    '5FPYK3F51HB018187', '7FARW2H82JE100894', '1FA6P8CF0H5312604', '5GAERCKW4JJ225646',
                                    '1C4HJXFG2JW258659', '1GYS3CKJXHR396687', '1GNSCCKC3JR246554', '3GTU2NEC6JG277454',
                                    'JTHBA1D27J5068712', '3GCUKREC4JG398913', '2T2ZZMCAXJC109769', '1GYS3BKJ1HR388021',
                                    'JTJHY7AX7H4234279'],
    (('Year', ('2020',)),): ['5FNYF5H33LB021069', '1HGCV2F92LA023098'],
    (('Year', ('2020', '2022')),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '5FNYF5H33LB021069', '1HGCV2F92LA023098',
                                    '5FNYF5H39NB007342', '5FNYF5H27NB031209', '5FNRL6H89NB023023'],
    (('Year', ('2024',)),): {'2024 Civic': '2 in stock', '2024 Accord': '10 in stock',
                             '2024 Accord Hybrid': '51 in stock', '2024 CR-V': '1 in stock',
                             '2024 Ridgeline': '14 in stock', '2024 Odyssey': '1 in stock',
                             '2024 Passport': '4 in stock', '2024 Prologue': '72 in stock',
                             '2024 Pilot': '1 in stock'},
    (('Exterior Color', ('Red',)),): {'2025 Civic': '8 in stock', '2024 Accord': '4 in stock',
                                      '2025 HR-V': '3 in stock', '2025 Accord': '2 in stock',
                                      '2025 CR-V': '8 in stock', '2024 Civic': '1 in stock',
                                      '2024 Accord Hybrid': '5 in stock', '2025 CR-V Hybrid': '13 in stock',
                                      '2025 Accord Hybrid': '2 in stock', '2025 Pilot': '11 in stock',
                                      '2024 Odyssey': '1 in stock', '2024 Ridgeline': '1 in stock',
                                      '2025 Odyssey': '6 in stock', '2024 Passport': '1 in stock',
                                      '2025 Ridgeline': '4 in stock', '2025 Passport': '1 in stock',
                                      '2024 Prologue': '11 in stock'},
    (('Exterior Color', ('Blue', 'Silver')),): {'2025 HR-V': '3 in stock', '2025 Civic': '12 in stock',
                                                '2025 Accord': '9 in stock', '2025 CR-V': '22 in stock',
                                                '2024 Accord Hybrid': '8 in stock', '2025 Accord Hybrid': '7 in stock',
                                                '2025 CR-V Hybrid': '40 in stock', '2025 Pilot': '33 in stock',
                                                '2024 Passport': '2 in stock', '2024 Ridgeline': '1 in stock',
                                                '2025 Odyssey': '11 in stock', '2025 Passport': '3 in stock',
                                                '2025 Ridgeline': '1 in stock', '2024 Prologue': '30 in stock',
                                                '2024 Pilot': '1 in stock'},
    (('Exterior Color', ('Silver',)),): ['1FADP5EU5HL111486', '3C4NJDBB6HT679649', '5NPE34AF7KH759418',
                                         '1HGCV1F12KA171710', '5YFEPMAE1MP206426', '5FNYF5H51HB001220',
                                         '7FARW2H82JE100894', '1HGCV1F31NA027510', '5FNYF5H33LB021069',
                                         '1GYS3CKJXHR396687', '1HGCV2F91MA016368', '1GYKPCRS4LZ158894',
                                         'JTM16RFV4ND060101', '4JGDM2DB4PA020751'],
    (('Exterior Color', ('Blue', 'Red')),): ['1G1PA5SH4E7432609', 'JM1GJ1W61E1132621', '2HKRM4H38FH604277',
                                             '3GYFNBE39FS517481', '5UXWX9C56G0D64484', '4T1B11HK2JU585073',
                                             '1FM5K7F80GGC57727', 'JTMYFREV8GJ069534', '5FNYF5H57JB032347',
                                             '1C4BJWDG1GL123119', 'WA1BCCFS2JR017660', '1HGCV1F49JA201536',
                                             'JTHBK1GG7E2100585', '4T1G11AK5MU605997', 'JTJBZMCA6H2018980',
                                             '2HGFE2F50NH552144', 'JM3KFADM4M0383626', '4T1K61AK1LU397496',
                                             '5FPYK3F54PB005058', '5FNRL6H71KB094087', '2HKRS3H48RH324291',
                                             '1GNSCCKJ8KR317599', '1FMJK1HT2MEA86368', '5TFLA5DB4NX051342',
                                             '5TFNA5DB6PX102030'],
    (('Exterior Color', ('Black',)),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '2HGFE2F51PH510942',
                                        '5FNYF5H39NB007342', '5FNYF5H27NB031209'],
    (('Exterior Color', ('Black', 'Gray')),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '2HGFE2F51PH510942',
                                               '1HGCV2F92LA023098', '5FNYF5H39NB007342', '5FNYF5H27NB031209',
                                               '5FNRL6H89NB023023'],
    (('Exterior Color', ('Gray',)),): {'2025 HR-V': '6 in stock', '2025 Civic': '11 in stock',
                                       '2025 Accord': '11 in stock', '2025 CR-V': '20 in stock',
                                       '2024 Accord Hybrid': '18 in stock', '2024 CR-V': '1 in stock',
                                       '2025 Accord Hybrid': '3 in stock', '2025 CR-V Hybrid': '23 in stock',
                                       '2024 Ridgeline': '4 in stock', '2025 Odyssey': '20 in stock',
                                       '2025 Ridgeline': '7 in stock', '2025 Pilot': '14 in stock',
                                       '2025 Passport': '4 in stock', '2024 Prologue': '5 in stock'},
    (('Interior Color', ('Brown',)),): {'2024 Prologue': '2 in stock', '2024 Ridgeline': '3 in stock',
                                        '2025 Odyssey': '1 in stock', '2025 Passport': '1 in stock',
                                        '2025 Pilot': '2 in stock', '2025 Ridgeline': '4 in stock'},
    (('Interior Color', ('Brown', 'Gray')),): {'2025 HR-V': '14 in stock', '2024 Accord': '7 in stock',
                                               '2025 Accord': '8 in stock', '2025 Civic': '2 in stock',
                                               '2025 CR-V': '22 in stock', '2024 Accord Hybrid': '5 in stock',
                                               '2025 Accord Hybrid': '1 in stock', '2024 Ridgeline': '4 in stock',
                                               '2025 Odyssey': '22 in stock', '2025 CR-V Hybrid': '5 in stock',
                                               '2025 Passport': '3 in stock', '2025 Pilot': '36 in stock',
                                               '2025 Ridgeline': '4 in stock', '2024 Prologue': '20 in stock'},
    (('Interior Color', ('Gray',)),): ['5J6RM3H38EL043027', '5NPE34AB1FH126710', '5FNRL5H32GB062053',
                                       '2HKRM3H70CH513741', '2HKRM4H38FH604277', '5NPE34AF7KH759418',
                                       '5FNYF5H57JB032347', '1HGCV1F49JA201536', '1HGCV1F17LA057171',
                                       '1C6RR7GT8KS656886', '5FNYF5H33LB021069', '1HGCV2F92LA023098',
                                       '5FNRL6H72KB054634', '5FNRL6H56MB002098', '4JGDM2DB4PA020751'],
    (('Interior Color', ('Gray', 'Ash')),): ['5J6RM3H38EL043027', '5NPE34AB1FH126710', '5FNRL5H32GB062053',
                                             '2HKRM3H70CH513741', '2HKRM4H38FH604277', '5NPE34AF7KH759418',
                                             'JTMYFREV8GJ069534', '5FNYF5H57JB032347', '1HGCV1F49JA201536',
                                             '1HGCV1F17LA057171', '1C6RR7GT8KS656886', '5FNYF5H33LB021069',
                                             '1HGCV2F92LA023098', '5FNRL6H72KB054634', '5FNRL6H56MB002098',
                                             '5TDYZ3DC1KS983236', '4JGDM2DB4PA020751'],
    (('Interior Color', ('Beige',)),): ['5FNRL6H71KB094087'],
    (('Interior Color', ('Black', 'Beige')),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '2HGFE2F51PH510942',
                                                '1HGCV2F91MA016368', '5FNRL6H71KB094087', '5FNYF5H39NB007342',
                                                '5FNYF5H27NB031209', '2HKRS3H48RH324291', '5FNRL6H89NB023023'],
    (('Interior Color', ('Black',)),): {'2025 Civic': '43 in stock', '2025 HR-V': '21 in stock',
                                        '2024 Civic': '2 in stock', '2024 Accord': '3 in stock',
                                        '2025 Accord': '20 in stock', '2025 CR-V': '64 in stock',
                                        '2024 Accord Hybrid': '46 in stock', '2024 CR-V': '1 in stock',
                                        '2025 Accord Hybrid': '24 in stock', '2025 CR-V Hybrid': '99 in stock',
                                        '2024 Ridgeline': '10 in stock', '2025 Pilot': '80 in stock',
                                        '2024 Odyssey': '1 in stock', '2025 Odyssey': '39 in stock',
                                        '2025 Ridgeline': '18 in stock', '2025 Passport': '14 in stock',
                                        '2024 Passport': '4 in stock', '2024 Prologue': '52 in stock',
                                        '2024 Pilot': '1 in stock'},
    (('Highlights', ('Panoramic Roof',)),): {'2024 Pilot': '1 in stock', '2024 Prologue': '54 in stock',
                                             '2025 Pilot': '73 in stock'},
    (("Highlights", ("Heated Mirrors", "Rear Climate Control")),): {'2024 Odyssey': '1 in stock',
                                                                    '2024 Passport': '4 in stock',
                                                                    '2024 Pilot': '1 in stock',
                                                                    '2024 Ridgeline': '13 in stock',
                                                                    '2025 Odyssey': '61 in stock',
                                                                    '2025 Passport': '17 in stock',
                                                                    '2025 Pilot': '116 in stock',
                                                                    '2025 Ridgeline': '20 in stock'},
    (('Highlights', ('AUX',)),): ['1FADP5EU5HL111486', '1G1PA5SH4E7432609', 'WA1LKAFP3AA045148', '5J6RM3H38EL043027',
                                  '1HGCS22828A002488', '1GNALBEK5DZ104445', '4T1BE46K89U350245', '5UXWX9C55D0D04644',
                                  '3C4NJDBB6HT679649', 'JTEZU14RX78074639', '1FMJU1HT6HEA51526', '2HGFB2F58CH557632',
                                  '3VW2K7AJ6DM383011', '5NPE34AB1FH126710', 'WA1LGAFE5DD017032', 'KNDPM3ACXH7248062',
                                  '1HGCR2F5XDA235332', '5FNRL5H32GB062053', '5TDKY5G1XAS027511', 'JM1GJ1W61E1132621',
                                  'JTJBM7FX7B5029301', '2HKRM3H70CH513741', '5FNRL5H63FB121460', 'JN1CV6FE6DM770662',
                                  'YV440MDK4F2674537', 'ZACCJABB1HPG02006', '4T1BK1EB7FU145472', '2HKRM4H38FH604277',
                                  '1HGCR2F54FA266739', '3GYFNBE39FS517481', '5UXWX9C56G0D64484', '3FA6P0LU6KR255667',
                                  '1C4BJWDG7CL229830', '4T1B11HK2JU585073', '5N1DR2MN3HC658958', '5NPE34AF7KH759418',
                                  '1C4RJEAG6KC534540', '1HGCV1F12KA171710', '1FM5K7F80GGC57727', '2HGFC2F50JH516437',
                                  '5YFEPMAE1MP206426', '1FM5K7D8XJGA88858', '5FNYF5H51HB001220', 'JTMYFREV8GJ069534',
                                  '2HGFC2F74HH556340', '2T2ZK1BAXFC174326', '5GAERBKW7JJ257100', 'JTHBK1GG9E2113824',
                                  '5FNYF5H57JB032347', '2C3CDXBG9MH508588', 'JN8AZ2NF4J9663527', '3VV0B7AX3MM156696',
                                  '1FM5K7FH0JGB88674', '5J6RW6H31KL002620', '1C4BJWDG1GL123119', 'WA1BCCFS2JR017660',
                                  '5YFEPMAE0NP345383', '1HGCV1F49JA201536', 'JTHBK1GG7E2100585', 'JTHBK1GG2G2214674',
                                  '1FMJK1HTXKEA58993', '1HGCV1F39JA170537', '4S4WMALDXM3423008', '4T1G11AK5MU605997',
                                  '2HGFE2F20NH545314', '1GNERGKW5LJ117987', '7FARW1H88JE049833', '19XZE4F5XNE010343',
                                  '1HGCV1F17LA057171', 'JTJBZMCA6H2018980', '2HGFE2F50NH552144', '19XFC1F38ME206530',
                                  'JM3KFADM4M0383626', '5FPYK3F51HB018187', '7FARW2H82JE100894', '5N1DL0MN7LC532214',
                                  '1FA6P8TH5K5134472', '19XFL2H87NE002933', '5FNRL6H73LB071072', '1GNERJKW5MJ218526',
                                  '1HGCV1F31NA027510', '1FA6P8CF0H5312604', '1C6RR7GT8KS656886', '5GAERCKW4JJ225646',
                                  '5FNYF5H22NB026919', '5FNYF5H33LB021069', '1C4HJXFG2JW258659', '2HGFE2F51PH510942',
                                  '1HGCV1F34NA052501', '7FARW2H75ME029346', '2C4RC1EG3LR239594', '4T1K61AK1LU397496',
                                  '1GYS3CKJXHR396687', '1HGCV2F92LA023098', '5FNRL6H72KB054634', '1C4RJECG0LC152938',
                                  '1C4HJXDG6LW229185', '1GNSCCKC3JR246554', '2HGFE2F52PH500341', '3GTU2NEC6JG277454',
                                  'JTHBA1D27J5068712', '1HGCV2F91MA016368', '1GKS1CKJXGR220771', '5FPYK3F54PB005058',
                                  '5FNRL6H71KB094087', '5FNYF5H39NB007342', 'JTJDARBZ7M2191101', '2FMPK4K90MBA15626',
                                  '5FNRL6H56MB002098', '3GCUKREC4JG398913', '1GYKPCRS4LZ158894', 'WA1CNAFY1K2069184',
                                  '2T2ZZMCAXJC109769', '7SAYGAEEXNF359991', '5TDYZ3DC1KS983236', '3TYAX5GN7MT021868',
                                  '7FART6H94ME038167', '5FNYF5H27NB031209', 'JTM16RFV4ND060101', '1FTEW1C83PFA34503',
                                  '1GYS3BKJ1HR388021', '1N6AA1FB2MN517371', '2HKRS3H48RH324291', '3GKALVEG1RL323167',
                                  '1C4RJKBG4M8210506', '4S4BTADCXS3120394', 'JTMRWRFV8ND158275', '1GNSCCKJ8KR317599',
                                  '3TMAZ5CN0NM193723', '5FNYF5H29NB023208', '5FNRL6H89NB023023', '5FNRL6H63PB045388',
                                  '1C6SRFLT4KN692607', '2C3CDZFJ3NH223484', 'SHHFK8G79LU200370', '7SAYGDED4RF045509',
                                  '1FMJK1HT2MEA86368', '5TFLA5DB4NX051342', '1FMDE5CH5PLA90834', 'JTJHY7AX7H4234279',
                                  '3MW5U7J09N8C34682', '5FNYG1H8XSB031954', '5TFNA5DB6PX102030', '4JGDM2DB4PA020751',
                                  '5TDERKECXRS180866', '1GKS2JKL0PR504011', '1GKS2JKL9PR466410'],
    (('Highlights', ('AUX', 'Parking Sensors')),): ['1FMJU1HT6HEA51526', '5NPE34AB1FH126710', '3FA6P0LU6KR255667',
                                                    '5N1DR2MN3HC658958', '1C4RJEAG6KC534540', '1FM5K7F80GGC57727',
                                                    '1FM5K7D8XJGA88858', 'JTMYFREV8GJ069534', '5GAERBKW7JJ257100',
                                                    '2C3CDXBG9MH508588', 'JN8AZ2NF4J9663527', '1FM5K7FH0JGB88674',
                                                    'WA1BCCFS2JR017660', '1FMJK1HTXKEA58993', '2C4RC1EG3LR239594',
                                                    '1HGCV2F92LA023098', '1HGCV2F91MA016368', '2FMPK4K90MBA15626',
                                                    '7SAYGAEEXNF359991', '7FART6H94ME038167', '1FTEW1C83PFA34503',
                                                    '1N6AA1FB2MN517371', '1C4RJKBG4M8210506', '5FNRL6H89NB023023',
                                                    '1C6SRFLT4KN692607', '2C3CDZFJ3NH223484', '7SAYGDED4RF045509',
                                                    '1FMJK1HT2MEA86368', 'JTJHY7AX7H4234279', '5FNYG1H8XSB031954',
                                                    '5TFNA5DB6PX102030', '4JGDM2DB4PA020751', '5TDERKECXRS180866'],
    (('Highlights', ('Heated Seats',)),): ['5FNYF5H33LB021069', '1HGCV2F92LA023098', '1HGCV2F91MA016368',
                                           '5FNRL6H71KB094087', '5FNYF5H39NB007342', '5FNYF5H27NB031209',
                                           '2HKRS3H48RH324291', '5FNRL6H89NB023023'],
    (('Highlights', ('Moon Roof', 'Rear Wiper')),): ['5FNRL6H71KB094087', '5FNYF5H27NB031209',
                                                     '2HKRS3H48RH324291', '5FNRL6H89NB023023'],
    (('Highlights', ('Remote Start',)),): {'2025 Civic': '42 in stock', '2024 Civic': '2 in stock',
                                           '2025 HR-V': '22 in stock', '2025 Accord': '8 in stock',
                                           '2024 Accord': '1 in stock', '2024 Accord Hybrid': '51 in stock',
                                           '2024 CR-V': '1 in stock', '2025 CR-V': '56 in stock',
                                           '2025 Accord Hybrid': '11 in stock', '2025 CR-V Hybrid': '104 in stock',
                                           '2024 Ridgeline': '14 in stock', '2025 Pilot': '82 in stock',
                                           '2024 Odyssey': '1 in stock', '2025 Odyssey': '37 in stock',
                                           '2025 Ridgeline': '22 in stock', '2025 Passport': '17 in stock',
                                           '2024 Passport': '4 in stock', '2024 Prologue': '72 in stock',
                                           '2024 Pilot': '1 in stock'},
    (('Body Style', ('4D Crew Cab',)),): {'2024 Ridgeline': '14 in stock', '2025 Ridgeline': '22 in stock'},
    (('Body Style', ('4D Hatchback', "4D Sedan")),): {'2024 Accord': '10 in stock',
                                                      '2024 Accord Hybrid': '51 in stock',
                                                      '2024 Civic': '2 in stock',
                                                      '2025 Accord': '28 in stock',
                                                      '2025 Accord Hybrid': '25 in stock',
                                                      '2025 Civic': '48 in stock'},
    (('Body Style', ('4D Hatchback',)),): {'2024 Civic': '2 in stock', '2025 Civic': '20 in stock'},
    (('Body Style', ('4D Sedan',)),): ['1G1PA5SH4E7432609', '4T1BE46K89U350245', '2HGFB2F58CH557632',
                                       '3VW2K7AJ6DM383011', '5NPE34AB1FH126710', '1HGCR2F5XDA235332',
                                       'JM1GJ1W61E1132621', '4T1BK1EB7FU145472', '1HGCR2F54FA266739',
                                       '3FA6P0LU6KR255667', '4T1B11HK2JU585073', '5NPE34AF7KH759418',
                                       '1HGCV1F12KA171710', '2HGFC2F50JH516437', '5YFEPMAE1MP206426',
                                       '2HGFC2F74HH556340', 'JTHBK1GG9E2113824', '2C3CDXBG9MH508588',
                                       '5YFEPMAE0NP345383', '1HGCV1F49JA201536', 'JTHBK1GG7E2100585',
                                       'JTHBK1GG2G2214674', '1HGCV1F39JA170537', '4T1G11AK5MU605997',
                                       '2HGFE2F20NH545314', '19XZE4F5XNE010343', '1HGCV1F17LA057171',
                                       '2HGFE2F50NH552144', '19XFC1F38ME206530', '1HGCV1F31NA027510',
                                       '2HGFE2F51PH510942', '1HGCV1F34NA052501', '4T1K61AK1LU397496',
                                       '1HGCV2F92LA023098', '2HGFE2F52PH500341', 'JTHBA1D27J5068712',
                                       '1HGCV2F91MA016368', '3MW5U7J09N8C34682'],
    (('Body Style', ('2D Coupe', '4D CrewMax')),): ['1HGCS22828A002488', '1FA6P8TH5K5134472', '1FA6P8CF0H5312604',
                                                    '2C3CDZFJ3NH223484', '5TFLA5DB4NX051342', '5TFNA5DB6PX102030'],
    (('Body Style', ('4D Sport Utility',)),): ['5FNYF5H33LB021069', '5FNYF5H39NB007342', '5FNYF5H27NB031209',
                                               '2HKRS3H48RH324291'],
    (('Body Style', ('4D Sport Utility', '4D Passenger Van')),): ['5FNYF5H33LB021069', '5FNRL6H71KB094087',
                                                                  '5FNYF5H39NB007342', '5FNYF5H27NB031209',
                                                                  '2HKRS3H48RH324291', '5FNRL6H89NB023023'],
    (('Engine', ('1.5 liters / 4 Cylinder',)),): {'2024 Accord': '10 in stock', '2024 CR-V': '1 in stock',
                                                  '2024 Civic': '1 in stock', '2025 Accord': '28 in stock',
                                                  '2025 CR-V': '86 in stock', '2025 Civic': '3 in stock'},
    (("Engine", ("2.0 liters / 4 Cylinder", "Electric Motor")),): {'2024 Accord Hybrid': '51 in stock',
                                                                   '2024 Civic': '1 in stock',
                                                                   '2024 Prologue': '72 in stock',
                                                                   '2025 Accord Hybrid': '25 in stock',
                                                                   '2025 CR-V Hybrid': '104 in stock',
                                                                   '2025 Civic': '45 in stock',
                                                                   '2025 HR-V': '35 in stock'},
    (('Engine', ('2.4 liters / 4 Cylinder',)),): ['5J6RM3H38EL043027', '1GNALBEK5DZ104445', '4T1BE46K89U350245',
                                                  '3C4NJDBB6HT679649', 'KNDPM3ACXH7248062', '1HGCR2F5XDA235332',
                                                  '2HKRM3H70CH513741', 'ZACCJABB1HPG02006', '2HKRM4H38FH604277',
                                                  '1HGCR2F54FA266739', '5NPE34AF7KH759418', '5J6RW6H31KL002620'],
    (('Engine', ('6.2 liters / 8 Cylinder', '5.7 liters / 8 Cylinder')),): ['5TDKY5G1XAS027511', '1C6RR7GT8KS656886',
                                                                            '1GYS3CKJXHR396687', '1GKS1CKJXGR220771',
                                                                            '1GYS3BKJ1HR388021', '1GNSCCKJ8KR317599',
                                                                            '1C6SRFLT4KN692607', 'JTJHY7AX7H4234279',
                                                                            '1GKS2JKL0PR504011', '1GKS2JKL9PR466410'],
    (('Engine', ('2.0 liters / 4 Cylinder',)),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '2HGFE2F51PH510942',
                                                  '1HGCV2F92LA023098', '1HGCV2F91MA016368'],
    (('Engine', ('3.5 liters / V6 Cylinder', '1.5 liters / 4 Cylinder')),): ['5FNYF5H33LB021069', '5FNRL6H71KB094087',
                                                                             '5FNYF5H39NB007342', '5FNYF5H27NB031209',
                                                                             '2HKRS3H48RH324291', '5FNRL6H89NB023023'],
    (('Engine', ('3.5 liters / V6 Cylinder',)),): {'2024 Odyssey': '1 in stock', '2024 Passport': '4 in stock',
                                                   '2024 Pilot': '1 in stock', '2024 Ridgeline': '14 in stock',
                                                   '2025 Odyssey': '61 in stock', '2025 Passport': '17 in stock',
                                                   '2025 Pilot': '116 in stock', '2025 Ridgeline': '22 in stock'},
    (('Transmission', ('10-Speed Automatic w/OD',)),): {'2024 Odyssey': '1 in stock', '2024 Pilot': '1 in stock',
                                                        '2025 Odyssey': '61 in stock', '2025 Pilot': '116 in stock'},
    (("Transmission", ("6-Speed Manual w/OD", "9-Speed Automatic w/OD")),): {'2024 Passport': '4 in stock',
                                                                             '2024 Ridgeline': '14 in stock',
                                                                             '2025 Civic': '3 in stock',
                                                                             '2025 Passport': '17 in stock',
                                                                             '2025 Ridgeline': '22 in stock'},
    (('Transmission', ('8-Speed Automatic w/OD',)),): ['YV440MDK4F2674537', '5UXWX9C56G0D64484', '4T1B11HK2JU585073',
                                                       '1C4RJEAG6KC534540', '2C3CDXBG9MH508588', '3VV0B7AX3MM156696',
                                                       '4T1G11AK5MU605997', 'JTJBZMCA6H2018980', '1C6RR7GT8KS656886',
                                                       '1C4HJXFG2JW258659', '4T1K61AK1LU397496', '1C4RJECG0LC152938',
                                                       '1C4HJXDG6LW229185', 'JTHBA1D27J5068712', '2FMPK4K90MBA15626',
                                                       '2T2ZZMCAXJC109769', '5TDYZ3DC1KS983236', '1C4RJKBG4M8210506',
                                                       '1C6SRFLT4KN692607', '2C3CDZFJ3NH223484', 'JTJHY7AX7H4234279',
                                                       '3MW5U7J09N8C34682'],
    (('Transmission', ('8-Speed Automatic w/OD', '6-Speed Manual')),): ['YV440MDK4F2674537', '5UXWX9C56G0D64484',
                                                                        '4T1B11HK2JU585073', '1C4RJEAG6KC534540',
                                                                        '2C3CDXBG9MH508588', '3VV0B7AX3MM156696',
                                                                        '4T1G11AK5MU605997', 'JTJBZMCA6H2018980',
                                                                        '1FA6P8TH5K5134472', '1FA6P8CF0H5312604',
                                                                        '1C6RR7GT8KS656886', '1C4HJXFG2JW258659',
                                                                        '4T1K61AK1LU397496', '1C4RJECG0LC152938',
                                                                        '1C4HJXDG6LW229185', 'JTHBA1D27J5068712',
                                                                        '2FMPK4K90MBA15626', '2T2ZZMCAXJC109769',
                                                                        '5TDYZ3DC1KS983236', '1C4RJKBG4M8210506',
                                                                        '1C6SRFLT4KN692607', '2C3CDZFJ3NH223484',
                                                                        'JTJHY7AX7H4234279', '3MW5U7J09N8C34682'],
    (('Transmission', ('1-Speed CVT w/OD',)),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '2HGFE2F51PH510942',
                                                 '2HKRS3H48RH324291'],
    (('Transmission', ('9-Speed Automatic w/OD', '10-Speed Automatic w/OD')),): ['1HGCV2F92LA023098',
                                                                                 '1HGCV2F91MA016368',
                                                                                 '5FNRL6H71KB094087',
                                                                                 '5FNYF5H39NB007342',
                                                                                 '5FNYF5H27NB031209',
                                                                                 '5FNRL6H89NB023023'],
    (('Transmission', ('9-Speed Automatic w/OD',)),): {'2024 Passport': '4 in stock', '2024 Ridgeline': '14 in stock',
                                                       '2025 Passport': '17 in stock', '2025 Ridgeline': '22 in stock'},
    (('Drivetrain', ('AWD',)),): {'2025 HR-V': '4 in stock', '2025 CR-V': '37 in stock',
                                  '2025 CR-V Hybrid': '47 in stock', '2024 Ridgeline': '14 in stock',
                                  '2025 Pilot': '83 in stock', '2025 Ridgeline': '22 in stock',
                                  '2025 Passport': '17 in stock', '2024 Passport': '4 in stock',
                                  '2024 Pilot': '1 in stock', '2024 Prologue': '50 in stock'},
    (("Drivetrain", ("AWD", "FWD")),): {'2025 Civic': '48 in stock', '2025 HR-V': '35 in stock',
                                        '2024 Civic': '2 in stock', '2024 Accord': '10 in stock',
                                        '2025 Accord': '28 in stock', '2025 CR-V': '86 in stock',
                                        '2024 Accord Hybrid': '51 in stock', '2024 CR-V': '1 in stock',
                                        '2025 Accord Hybrid': '25 in stock', '2025 CR-V Hybrid': '104 in stock',
                                        '2024 Ridgeline': '14 in stock', '2025 Pilot': '116 in stock',
                                        '2024 Odyssey': '1 in stock', '2025 Odyssey': '61 in stock',
                                        '2025 Ridgeline': '22 in stock', '2025 Passport': '17 in stock',
                                        '2024 Passport': '4 in stock', '2024 Prologue': '72 in stock',
                                        '2024 Pilot': '1 in stock'},
    (('Drivetrain', ('4x4',)),): ['3C4NJDBB6HT679649', 'JTJBM7FX7B5029301', '1C4BJWDG7CL229830', '1C4BJWDG1GL123119',
                                  '1C6RR7GT8KS656886', '1C4HJXFG2JW258659', '1C4HJXDG6LW229185', '3GTU2NEC6JG277454',
                                  '3GCUKREC4JG398913', '1N6AA1FB2MN517371', '1C4RJKBG4M8210506', '1C6SRFLT4KN692607',
                                  '5TFLA5DB4NX051342', '1FMDE5CH5PLA90834', 'JTJHY7AX7H4234279', '5TFNA5DB6PX102030',
                                  '1GKS2JKL0PR504011', '1GKS2JKL9PR466410'],
    (('Drivetrain', ('4x4', 'RWD')),): ['3C4NJDBB6HT679649', 'JTEZU14RX78074639', '1FMJU1HT6HEA51526',
                                        '5TDKY5G1XAS027511', 'JTJBM7FX7B5029301', 'JN1CV6FE6DM770662',
                                        '1C4BJWDG7CL229830', '1C4RJEAG6KC534540', '2C3CDXBG9MH508588',
                                        'JN8AZ2NF4J9663527', '1C4BJWDG1GL123119', '1FMJK1HTXKEA58993',
                                        '1FA6P8TH5K5134472', '1FA6P8CF0H5312604', '1C6RR7GT8KS656886',
                                        '1C4HJXFG2JW258659', '1GYS3CKJXHR396687', '1C4RJECG0LC152938',
                                        '1C4HJXDG6LW229185', '1GNSCCKC3JR246554', '3GTU2NEC6JG277454',
                                        'JTHBA1D27J5068712', '1GKS1CKJXGR220771', '3GCUKREC4JG398913',
                                        '3TYAX5GN7MT021868', '1FTEW1C83PFA34503', '1GYS3BKJ1HR388021',
                                        '1N6AA1FB2MN517371', '1C4RJKBG4M8210506', '1GNSCCKJ8KR317599',
                                        '3TMAZ5CN0NM193723', '1C6SRFLT4KN692607', '2C3CDZFJ3NH223484',
                                        '7SAYGDED4RF045509', '1FMJK1HT2MEA86368', '5TFLA5DB4NX051342',
                                        '1FMDE5CH5PLA90834', 'JTJHY7AX7H4234279', '3MW5U7J09N8C34682',
                                        '5TFNA5DB6PX102030', '4JGDM2DB4PA020751', '1GKS2JKL0PR504011',
                                        '1GKS2JKL9PR466410'],
    (('Drivetrain', ('FWD',)),): ['2HGFE2F20NH545314', '19XFL2H87NE002933', '5FNYF5H33LB021069', '2HGFE2F51PH510942',
                                  '1HGCV2F92LA023098', '1HGCV2F91MA016368', '5FNRL6H71KB094087', '5FNYF5H39NB007342',
                                  '5FNYF5H27NB031209', '2HKRS3H48RH324291', '5FNRL6H89NB023023'],
    (('Fuel Type', ('Hybrid',)),): {'2024 Accord Hybrid': '51 in stock', '2025 Accord Hybrid': '25 in stock',
                                    '2025 CR-V Hybrid': '104 in stock'},
    (("Fuel Type", ("Electric", "Hybrid")),): {'2024 Accord Hybrid': '51 in stock', '2024 Prologue': '72 in stock',
                                               '2025 Accord Hybrid': '25 in stock', '2025 CR-V Hybrid': '104 in stock'},
    (('Fuel Type', ('Gasoline',)),): ['1G1PA5SH4E7432609', 'WA1LKAFP3AA045148', '5J6RM3H38EL043027',
                                      '1HGCS22828A002488', '1GNALBEK5DZ104445', '4T1BE46K89U350245',
                                      '5UXWX9C55D0D04644', '3C4NJDBB6HT679649', 'JTEZU14RX78074639',
                                      '1FMJU1HT6HEA51526', '2HGFB2F58CH557632', '3VW2K7AJ6DM383011',
                                      '5NPE34AB1FH126710', 'WA1LGAFE5DD017032', 'KNDPM3ACXH7248062',
                                      '1HGCR2F5XDA235332', '5FNRL5H32GB062053', '5TDKY5G1XAS027511',
                                      'JM1GJ1W61E1132621', 'JTJBM7FX7B5029301', '2HKRM3H70CH513741',
                                      '5FNRL5H63FB121460', 'JN1CV6FE6DM770662', 'YV440MDK4F2674537',
                                      'ZACCJABB1HPG02006', '4T1BK1EB7FU145472', '2HKRM4H38FH604277',
                                      '1HGCR2F54FA266739', '3GYFNBE39FS517481', '5UXWX9C56G0D64484',
                                      '1C4BJWDG7CL229830', '4T1B11HK2JU585073', '5N1DR2MN3HC658958',
                                      '5NPE34AF7KH759418', '1C4RJEAG6KC534540', '1HGCV1F12KA171710',
                                      '1FM5K7F80GGC57727', '2HGFC2F50JH516437', '5YFEPMAE1MP206426',
                                      '1FM5K7D8XJGA88858', '5FNYF5H51HB001220', 'JTMYFREV8GJ069534',
                                      '2HGFC2F74HH556340', '2T2ZK1BAXFC174326', '5GAERBKW7JJ257100',
                                      'JTHBK1GG9E2113824', '5FNYF5H57JB032347', '2C3CDXBG9MH508588',
                                      'JN8AZ2NF4J9663527', '1FM5K7FH0JGB88674', '3VV0B7AX3MM156696',
                                      '5J6RW6H31KL002620', '1C4BJWDG1GL123119', 'WA1BCCFS2JR017660',
                                      '5YFEPMAE0NP345383', '1HGCV1F49JA201536', 'JTHBK1GG7E2100585',
                                      'JTHBK1GG2G2214674', '1FMJK1HTXKEA58993', '1HGCV1F39JA170537',
                                      '4S4WMALDXM3423008', '4T1G11AK5MU605997', '2HGFE2F20NH545314',
                                      '1GNERGKW5LJ117987', '7FARW1H88JE049833', '19XZE4F5XNE010343',
                                      '1HGCV1F17LA057171', 'JTJBZMCA6H2018980', '2HGFE2F50NH552144',
                                      '19XFC1F38ME206530', 'JM3KFADM4M0383626', '5FPYK3F51HB018187',
                                      '7FARW2H82JE100894', '5N1DL0MN7LC532214', '1FA6P8TH5K5134472',
                                      '19XFL2H87NE002933', '5FNRL6H73LB071072', '1GNERJKW5MJ218526',
                                      '1HGCV1F31NA027510', '1FA6P8CF0H5312604', '1C6RR7GT8KS656886',
                                      '5GAERCKW4JJ225646', '5FNYF5H22NB026919', '5FNYF5H33LB021069',
                                      '1C4HJXFG2JW258659', '2HGFE2F51PH510942', '1HGCV1F34NA052501',
                                      '7FARW2H75ME029346', '2C4RC1EG3LR239594', '4T1K61AK1LU397496',
                                      '1GYS3CKJXHR396687', '1HGCV2F92LA023098', '5FNRL6H72KB054634',
                                      '1C4RJECG0LC152938', '1C4HJXDG6LW229185', '1GNSCCKC3JR246554',
                                      '2HGFE2F52PH500341', '3GTU2NEC6JG277454', 'JTHBA1D27J5068712',
                                      '1HGCV2F91MA016368', '1GKS1CKJXGR220771', '5FPYK3F54PB005058',
                                      '5FNRL6H71KB094087', '5FNYF5H39NB007342', 'JTJDARBZ7M2191101',
                                      '2FMPK4K90MBA15626', '5FNRL6H56MB002098', '3GCUKREC4JG398913',
                                      '1GYKPCRS4LZ158894', 'WA1CNAFY1K2069184', '2T2ZZMCAXJC109769',
                                      '5TDYZ3DC1KS983236', '3TYAX5GN7MT021868', '5FNYF5H27NB031209',
                                      '1FTEW1C83PFA34503', '1GYS3BKJ1HR388021', '1N6AA1FB2MN517371',
                                      '2HKRS3H48RH324291', '3GKALVEG1RL323167', '1C4RJKBG4M8210506',
                                      '4S4BTADCXS3120394', '1GNSCCKJ8KR317599', '3TMAZ5CN0NM193723',
                                      '5FNYF5H29NB023208', '5FNRL6H89NB023023', '5FNRL6H63PB045388',
                                      '1C6SRFLT4KN692607', '2C3CDZFJ3NH223484', 'SHHFK8G79LU200370',
                                      '1FMJK1HT2MEA86368', '5TFLA5DB4NX051342', '1FMDE5CH5PLA90834',
                                      'JTJHY7AX7H4234279', '3MW5U7J09N8C34682', '5FNYG1H8XSB031954',
                                      '5TFNA5DB6PX102030', '5TDERKECXRS180866', '1GKS2JKL0PR504011',
                                      '1GKS2JKL9PR466410'],
    (('Fuel Type', ('Gasoline', 'Hybrid')),): ['1FADP5EU5HL111486', '1G1PA5SH4E7432609', 'WA1LKAFP3AA045148',
                                               '5J6RM3H38EL043027', '1HGCS22828A002488', '1GNALBEK5DZ104445',
                                               '4T1BE46K89U350245', '5UXWX9C55D0D04644', '3C4NJDBB6HT679649',
                                               'JTEZU14RX78074639', '1FMJU1HT6HEA51526', '2HGFB2F58CH557632',
                                               '3VW2K7AJ6DM383011', '5NPE34AB1FH126710', 'WA1LGAFE5DD017032',
                                               'KNDPM3ACXH7248062', '1HGCR2F5XDA235332', '5FNRL5H32GB062053',
                                               '5TDKY5G1XAS027511', 'JM1GJ1W61E1132621', 'JTJBM7FX7B5029301',
                                               '2HKRM3H70CH513741', '5FNRL5H63FB121460', 'JN1CV6FE6DM770662',
                                               'YV440MDK4F2674537', 'ZACCJABB1HPG02006', '4T1BK1EB7FU145472',
                                               '2HKRM4H38FH604277', '1HGCR2F54FA266739', '3GYFNBE39FS517481',
                                               '5UXWX9C56G0D64484', '3FA6P0LU6KR255667', '1C4BJWDG7CL229830',
                                               '4T1B11HK2JU585073', '5N1DR2MN3HC658958', '5NPE34AF7KH759418',
                                               '1C4RJEAG6KC534540', '1HGCV1F12KA171710', '1FM5K7F80GGC57727',
                                               '2HGFC2F50JH516437', '5YFEPMAE1MP206426', '1FM5K7D8XJGA88858',
                                               '5FNYF5H51HB001220', 'JTMYFREV8GJ069534', '2HGFC2F74HH556340',
                                               '2T2ZK1BAXFC174326', '5GAERBKW7JJ257100', 'JTHBK1GG9E2113824',
                                               '5FNYF5H57JB032347', '2C3CDXBG9MH508588', 'JN8AZ2NF4J9663527',
                                               '1FM5K7FH0JGB88674', '3VV0B7AX3MM156696', '5J6RW6H31KL002620',
                                               '1C4BJWDG1GL123119', 'WA1BCCFS2JR017660', '5YFEPMAE0NP345383',
                                               '1HGCV1F49JA201536', 'JTHBK1GG7E2100585', 'JTHBK1GG2G2214674',
                                               '1FMJK1HTXKEA58993', '1HGCV1F39JA170537', '4S4WMALDXM3423008',
                                               '4T1G11AK5MU605997', '2HGFE2F20NH545314', '1GNERGKW5LJ117987',
                                               '7FARW1H88JE049833', '19XZE4F5XNE010343', '1HGCV1F17LA057171',
                                               'JTJBZMCA6H2018980', '2HGFE2F50NH552144', '19XFC1F38ME206530',
                                               'JM3KFADM4M0383626', '5FPYK3F51HB018187', '7FARW2H82JE100894',
                                               '5N1DL0MN7LC532214', '1FA6P8TH5K5134472', '19XFL2H87NE002933',
                                               '5FNRL6H73LB071072', '1GNERJKW5MJ218526', '1HGCV1F31NA027510',
                                               '1FA6P8CF0H5312604', '1C6RR7GT8KS656886', '5GAERCKW4JJ225646',
                                               '5FNYF5H22NB026919', '5FNYF5H33LB021069', '1C4HJXFG2JW258659',
                                               '2HGFE2F51PH510942', '1HGCV1F34NA052501', '7FARW2H75ME029346',
                                               '2C4RC1EG3LR239594', '4T1K61AK1LU397496', '1GYS3CKJXHR396687',
                                               '1HGCV2F92LA023098', '5FNRL6H72KB054634', '1C4RJECG0LC152938',
                                               '1C4HJXDG6LW229185', '1GNSCCKC3JR246554', '2HGFE2F52PH500341',
                                               '3GTU2NEC6JG277454', 'JTHBA1D27J5068712', '1HGCV2F91MA016368',
                                               '1GKS1CKJXGR220771', '5FPYK3F54PB005058', '5FNRL6H71KB094087',
                                               '5FNYF5H39NB007342', 'JTJDARBZ7M2191101', '2FMPK4K90MBA15626',
                                               '5FNRL6H56MB002098', '3GCUKREC4JG398913', '1GYKPCRS4LZ158894',
                                               'WA1CNAFY1K2069184', '2T2ZZMCAXJC109769', '5TDYZ3DC1KS983236',
                                               '3TYAX5GN7MT021868', '7FART6H94ME038167', '5FNYF5H27NB031209',
                                               'JTM16RFV4ND060101', '1FTEW1C83PFA34503', '1GYS3BKJ1HR388021',
                                               '1N6AA1FB2MN517371', '2HKRS3H48RH324291', '3GKALVEG1RL323167',
                                               '1C4RJKBG4M8210506', '4S4BTADCXS3120394', 'JTMRWRFV8ND158275',
                                               '1GNSCCKJ8KR317599', '3TMAZ5CN0NM193723', '5FNYF5H29NB023208',
                                               '5FNRL6H89NB023023', '5FNRL6H63PB045388', '1C6SRFLT4KN692607',
                                               '2C3CDZFJ3NH223484', 'SHHFK8G79LU200370', '1FMJK1HT2MEA86368',
                                               '5TFLA5DB4NX051342', '1FMDE5CH5PLA90834', 'JTJHY7AX7H4234279',
                                               '3MW5U7J09N8C34682', '5FNYG1H8XSB031954', '5TFNA5DB6PX102030',
                                               '5TDERKECXRS180866', '1GKS2JKL0PR504011', '1GKS2JKL9PR466410'],
    (('Fuel Type', ('Electric',)),): {'2024 Prologue': '72 in stock'}}
def get_filters_from_tuple(filters):
    results = ["Price", "Monthly Payment", "Down Payment", "Credit Score"]
    [results.append(filter_item[0]) for filter_item in filters]
    return results


def final_report_sub_title(value: str, trade_in_provider: TradeInProvider):
    sub_title = "Value" if value else "Range"
    if trade_in_provider == TradeInProvider.TRADE_IN_VALET:
        return f'Your Trade-in Valet Trade-In {sub_title}'
    if trade_in_provider == TradeInProvider.TRADE_PENDING:
        return f'Your TradePending Trade-In {sub_title}'
    if trade_in_provider == TradeInProvider.AUTO_HUB:
        return f"Your AutoHub Trade-In {sub_title}"
    return f"Your Kelley Blue Book® Trade-In {sub_title}"


def final_report_title(trade_in_provider):
    from src.web.entities.trade_in_entity import TradeInProvider
    if trade_in_provider == TradeInProvider.ASBURY:
        return "We would love to buy your vehicle!"
    else:
        return "Your vehicle's trade-in estimate"


final_report_disclaimer = "This is not an offer to purchase your vehicle. Inspection of your vehicle is required. Final offer may vary depending on condition and real-time market value."


class VehicleType:
    NEW = "New"
    PRE_OWNED = "pre-owned"
    CERTIFIED = "Certified"
    USED = 'Used'
