from pytest_check import check_func

from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextArea
from kom_framework.src.web.page_object import PageObject
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.consumer.desktop.cbo.hamburger_menu_component import HamburgerMenuComponent
from src.web.consumer.desktop.cbo.show_room_component import DealerLogInComponent
from src.web.consumer.desktop.cbo.sign_up import SignUp


@find_by(Xpath(".//div[@data-testid='welcome-screen-logo']/../.."))
class InStoreWelcomePage(PageObject):
    def __init__(self, consumer=None):
        self.consumer = consumer
        self.sign_up = SignUp(self)
        self.hamburger_menu = HamburgerMenuComponent(self)
        self.dealer_log_in = DealerLogInComponent(self)
        self.btn_hamburger = Button(Xpath(".//button[@aria-label='Menu toggle']"))
        self.btn_sign_up = Button(Xpath(".//button[@data-testid='welcome-new-customer-button']"))
        self.btn_dealer_toolbar_collapsed = Button(CssSelector("[data-testid='dealer-toolbar-expand-button']"))
        self.btn_dealer_toolbar_expanded = Button(CssSelector("[data-testid='dealer-toolbar-collapse-button']"))
        self.btn_dealer_avatar = Button(Xpath(".//span[@data-testid='dealer-toolbar-profile-toggle']"))

        # Sign_out modal
        self.txt_dealer_name = TextArea(CssSelector("[data-testid='dealer-name']"))
        self.txt_dealer_role = TextArea(CssSelector("[data-testid='dealer-name'] + span"))
        self.btn_dealer_sign_out = Button(CssSelector("[data-testid='dealer-sign-out-button']"))

    def open_actions(self):
        pass

    @step_decorator("WEB - InStorePage: Open sign up button")
    def open_sign_up_pii(self):
        self.btn_sign_up.click()
        assert self.sign_up.exists(), "Sign Up component wasn't opened"

    @step_decorator("WEB - ShowRoomComponent: Expand dealer toolbar")
    def expand_dealer_toolbar(self):
        self.btn_dealer_toolbar_collapsed.click()
        self.check_expanded_toolbar()

    @step_decorator("WEB - ShowRoomComponent: Collapse dealer toolbar")
    def collapse_dealer_toolbar(self):
        self.btn_dealer_toolbar_expanded.click()
        self.check_collapsed_toolbar()

    @step_decorator("WEB - ShowRoomComponent: Open Dealer info")
    def open_dealer_info(self):
        self.btn_dealer_avatar.click()
        assert self.txt_dealer_name.exists(wait_time=3), "Dealer info modal wasn't opened"

    @step_decorator("WEB - ShowRoomComponent: Get dealer name")
    def get_dealer_name(self):
        return self.txt_dealer_name.text

    @step_decorator("WEB - ShowRoomComponent: Get dealer role")
    def get_dealer_role(self):
        return self.txt_dealer_role.text

    @step_decorator("WEB - ShowRoomComponent: Dealer Sign Out")
    def dealer_sign_out(self):
        self.btn_dealer_sign_out.click()
        self.txt_dealer_name.wait_for.presence_of_element_located(wait_time=3, until=False)
        assert not self.btn_dealer_toolbar_expanded.exists(), "Dealer wasn't logged out"

    @check_func
    @step_decorator("WEB - Check that dealer toolbar is expanded")
    def check_expanded_toolbar(self):
        assert self.btn_dealer_toolbar_expanded.exists(wait_time=3), "Dealer toolbar wasn't expanded"

    @check_func
    @step_decorator("WEB - Check that dealer toolbar is collapsed")
    def check_collapsed_toolbar(self):
        assert self.btn_dealer_toolbar_collapsed.exists(wait_time=3), "Dealer toolbar wasn't collapsed"
