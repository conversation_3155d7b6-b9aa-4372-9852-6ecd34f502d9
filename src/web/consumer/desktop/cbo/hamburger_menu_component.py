from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON>, CssSelector
from kom_framework.src.web.data_types.element_types import But<PERSON>, TextBlock
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(Xpath("//button[contains(@class, 'Sidebar__StyledDealerLogo')]/.."))
class HamburgerMenuComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_sign_up = Button(CssSelector("[data-analytics='menu-interaction:menu-showroom-signup-button']"))
        self.txt_gubagoo_title = TextBlock(Xpath(".//div[@title='Gubagoo Inc.']"))
        self.btn_edit_profile = Button(CssSelector("[data-analytics='menu-interaction:menu-showroom-profile-details-button']"))
        self.btn_sign_out = Button(Xpath(".//button[contains(text(), 'Sign Out')]"))
        self.btn_used = Button(CssSelector("[data-analytics='menu-interaction:menu-shop-used-vehicles']"))
        self.btn_new = Button(CssSelector("[data-analytics='menu-interaction:menu-shop-new-vehicles']"))
        self.btn_certified = Button(CssSelector("[data-analytics='menu-interaction:menu-shop-certified-vehicles']"))
        self.btn_my_vehicles = Button(CssSelector("[data-analytics='menu-interaction:menu-showroom-orders-link']"))
        self.btn_dealer_log_in = Button(CssSelector("[data-analytics='cta-interaction:showroom-dealer-login-button']"))
        self.btn_open_welcome_page = Button(Xpath(".//a[contains(text(), 'Open Welcome Page')]"))

    def open_actions(self):
        self.ancestor.exists(wait_time=5)
        self.ancestor.btn_hamburger.js.scroll_into_view("true")
        self.ancestor.btn_hamburger.js.click()

    @step_decorator("HamburgerMenu - SignUp: Sign up")
    def open_edit_profile(self):
        self.btn_edit_profile.click()
        assert self.ancestor.sign_up.exists(wait_time=3), "Sign up modal wasn't opened"

    @step_decorator("HamburgerMenu - SignUp: Sign out")
    def sign_out_from_showroom(self):
        self.btn_sign_out.click()
        assert not self.exists(wait_time=3), "Hamburger menu wasn't closed"

    @step_decorator("HamburgerMenu - Select vehicle type from hamburger menu")
    def select_vehicle_type(self, vehicle_type: str):
        if vehicle_type == 'New':
            self.btn_new.click()
        if vehicle_type == 'Used':
            self.btn_used.click()
        if vehicle_type == 'Certified':
            self.btn_certified.click()
        assert not self.exists(wait_time=3), "Hamburger menu wasn't closed"

    @step_decorator("HamburgerMenu - Open Dealer Sign-in")
    def open_dealer_sign_in(self):
        self.btn_dealer_log_in.click()
        assert self.ancestor.dealer_log_in.exists(wait_time=3), "Dealer Sign In form wasn't opened"

    @step_decorator("HamburgerMenu - Click dealer Sign-out")
    def select_dealer_sign_out(self):
        self.btn_sign_out.click()

    @step_decorator("HamburgerMenu - Check dealer log in button exists")
    def check_dealer_log_in_button(self):
        assert self.btn_dealer_log_in.exists(wait_time=3), "Dealer wasn't logged out"

