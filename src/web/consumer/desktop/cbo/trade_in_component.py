from selenium.common.exceptions import TimeoutException

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON>path, CssSelector, Id
from kom_framework.src.web.data_types.element_list_types import AnyList
from kom_framework.src.web.data_types.element_types import SelectExtended, Input, Button, TextBlock, Image, Spinner
from kom_framework.src.web.support.page_factory import find_by
from src.utils.converter import Converter
from src.utils.decorators import step_decorator
from src.web.consumer import OwnershipType
from src.web.consumer.desktop.cbo import final_report_sub_title, final_report_title, final_report_disclaimer
from src.web.entities.lead_entity import WebLeadEntity
from src.web.entities.trade_in_entity import TradeInEntity, TradeInProvider
from src.web.entities.trade_in_personal_information_entity import PersonalInformation


@find_by(Xpath('.//*[@data-testid="current-step-trade_in"]'))
class TradeInComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.trade_in_value = None
        self.pii_form = PiiForm(self)
        self.report = TradeInReportComponent(self.ancestor)
        self.final_report = TradeInFinalReportComponent(self.ancestor)
        self.payoff_error = TradeInPayoffErrorComponent(self.ancestor)
        self.vin_modal = TradeInFindVinModal(self)
        self.manual_estimate = TradeInManualEstimate(self)
        self.manual_estimate_payments = TradeInManualEstimatePayments(self)
        self.warning_credit_pull = WarningCreditPull(self)
        self.success_credit_pull = SuccessCreditPull(self)
        self.check_my_score_form = CheckMyScoreForm(self)
        self.select_your_trim = SelectYourTrimLevel(self)

        self.ddl_year = SelectExtended(link_locator=CssSelector("input[placeholder='Select year']"),
                                       option_list_locator=CssSelector("[role='option']"),
                                       search_input_locator=CssSelector("input[placeholder='Select year']"))
        self.ddl_make = SelectExtended(link_locator=CssSelector("input[placeholder='Select make']"),
                                       option_list_locator=CssSelector("[role='option']"),
                                       search_input_locator=CssSelector("input[placeholder='Select make']"))
        self.ddl_model = SelectExtended(link_locator=CssSelector("input[placeholder='Select model']"),
                                        option_list_locator=CssSelector("[role='option']"),
                                        search_input_locator=CssSelector("input[placeholder='Select model']"))
        self.ddl_trim = SelectExtended(link_locator=CssSelector("input[placeholder='Select trim']"),
                                       option_list_locator=CssSelector("[role='option']"),
                                       search_input_locator=CssSelector("input[placeholder='Select trim']"))
        self.ddl_body = SelectExtended(link_locator=CssSelector("input[placeholder='Select Body']"),
                                       option_list_locator=CssSelector("[role='option']"),
                                       search_input_locator=CssSelector("input[placeholder='Select Body']"))
        self.ddl_transmission = SelectExtended(link_locator=CssSelector("input[placeholder='Select Transmission']"),
                                               option_list_locator=CssSelector("[role='option']"),
                                               search_input_locator=CssSelector(
                                                   "input[placeholder='Select Transmission']"))
        self.ddl_engine = SelectExtended(link_locator=CssSelector("input[placeholder='Select Engine']"),
                                         option_list_locator=CssSelector("[role='option']"),
                                         search_input_locator=CssSelector("input[placeholder='Select Engine']"))
        self.ddl_driveline = SelectExtended(link_locator=CssSelector("input[placeholder='Select Driveline']"),
                                            option_list_locator=CssSelector("[role='option']"),
                                            search_input_locator=CssSelector(
                                                "input[placeholder='Select Driveline']"))
        self.inp_mileage = Input(Xpath(".//input[@placeholder='Mileage']"))
        self.btn_owned = Button(Xpath(".//div[contains(@class,'btn-select') and text()='Owned']"))
        self.btn_financed = Button(Xpath(".//div[contains(@class,'btn-select') and text()='Financed']"))
        self.inp_loan = Input(CssSelector("#estimateInput"))
        self.btn_see_trade_in_value = Button(
            CssSelector("[data-testid='questions-screen-next-button'],[aria-label='Check my score for free'] button"))

        self.btn_yes = Button(Xpath(".//p[contains(text(),'Yes, I have a trade-in')]/.."))
        self.btn_next = Button(CssSelector("[data-testid='cbo-nav-bottom'] button"))
        self.btn_skip = Button(Xpath(".//p[contains(text(),\"No, I don’t have a trade-in.\")]/.."))

        self.btn_make_model = Button(CssSelector("[for='Make/Model']"))
        self.inp_make_model = Input(Id("Make/Model"))
        self.btn_find_vehicle = Button(CssSelector("[data-testid='find-vehicle-btn']"))
        self.inp_license_plate = Input(CssSelector("#licensePlate"))
        self.ddl_state = SelectExtended(link_locator=CssSelector("[name='stateSelect']"),
                                        option_list_locator=CssSelector("#us-state-select-menu li"),
                                        search_input_locator=CssSelector("[name='stateSelect']"))
        self.txt_found_vehicles = TextBlock(
            CssSelector('[data-testid="current-step-trade_in"] :last-child>[data-testid="trade-in-card-text"]'))
        self.txt_error_messages = AnyList(CssSelector(".error"))
        self.btn_vin = Button(Xpath(".//button[contains(text(),'Enter your VIN.')]"))
        self.btn_manual_estimate = Button(Xpath(".//button[contains(text(),'Provide manual estimate')]"))
        self.btn_airbags_deployed = Button(CssSelector("[for=airbags_deployed-id]"))
        self.inp_repair_costs = Input(CssSelector("#repair_costs"))
        self.ddl_lender = SelectExtended(link_locator=CssSelector("[name='lenderSelect']"),
                                         option_list_locator=CssSelector("[role='option']"),
                                         search_input_locator=CssSelector("[name='lenderSelect']"))
        self.inp_ssn = Input(CssSelector("#SSNInput"))
        self.txt_loan_payoff = TextBlock(Xpath(".//div[contains(@class,'PayOfferCard__TableContainer')]"))
        self.btn_making_payments_yes = Button(Xpath(".//label[@for='makingPayments-yes-id']/.."))
        self.btn_making_payments_no = Button(Xpath(".//label[@for='makingPayments-no-id']/.."))
        self.ddl_enter_your_vehicle = SelectExtended(link_locator=CssSelector("[placeholder='Enter your vehicle']"),
                                                     option_list_locator=CssSelector(".autocomplete__menu--item"),
                                                     search_input_locator=CssSelector(
                                                         "[placeholder='Enter your vehicle']"))
        self.txt_trade_in_empty = TextBlock(CssSelector(".trade-in__empty"))
        self.trim_buttons = AnyList(CssSelector(".row.gutters button"))
        self.btn_submit = Button(CssSelector("button[type = 'submit']"))
        self.switcher_manual_estimate = Button(CssSelector("label[for='Manual-Estimate']"))
        self.spn_vehicle_card = Spinner(Xpath("//div[contains(@class,'FoundVehicleCardPreloader')]"))

    @step_decorator('WEB - TradeInComponent: Open Trade-in tab')
    def open_actions(self):
        self.ancestor.rebates.open()
        self.ancestor.rebates.btn_next.click()

    @step_decorator('WEB - TradeInComponent: Wait for loading')
    def wait_for_loading(self):
        self.ancestor.wait_for_text_exists("Loading", wait_time=3)
        self.ancestor.wait_while_text_exists("Loading", wait_time=60)

    @step_decorator('WEB - TradeInComponent: Provide vehicle details')
    def provide_vehicle_information(self, vehicle: TradeInEntity, trade_in_provider: TradeInProvider):
        self.btn_make_model.click()
        if trade_in_provider == TradeInProvider.TRADE_PENDING:
            self.ddl_enter_your_vehicle.select_item_by_text(
                f"{vehicle.year} {vehicle.make} {vehicle.model} {vehicle.trim}", delay_for_options_to_appear_time=2)
            import time
            time.sleep(2)
        else:
            if vehicle.year:
                self.ddl_year.select_item_by_text(vehicle.year)
            if vehicle.make:
                self.ddl_make.select_item_by_text(vehicle.make)
            if vehicle.model:
                self.ddl_model.select_item_by_text(vehicle.model)
            if vehicle.trim:
                self.ddl_trim.select_item_by_text(vehicle.trim)
        if trade_in_provider == TradeInProvider.TRADE_IN_VALET:
            if vehicle.body:
                self.ddl_body.select_item_by_text(vehicle.body)
            if vehicle.transmission:
                self.ddl_transmission.select_item_by_text(vehicle.transmission)
            if vehicle.engine:
                self.ddl_engine.select_item_by_text(vehicle.engine)
            if vehicle.driveline:
                self.ddl_driveline.select_item_by_text(vehicle.driveline)
        self.btn_find_vehicle.click()
        self.txt_found_vehicles.exists(10)
        assert vehicle.year in self.txt_found_vehicles.text_content, \
            f'Unable to find {vehicle.year} in found vehicle card {self.txt_found_vehicles.text_content}'

    @step_decorator('WEB - TradeInComponent: Provide vehicle details')
    def provide_vin_number(self, vehicle: TradeInEntity):
        self.vin_modal.open().apply_vin_number(vehicle)

    @step_decorator('WEB - TradeInComponent: Provide vehicle conditions')
    def provide_vehicle_conditions(self, vehicle: TradeInEntity):
        self.provide_vehicle_conditions_first_step(vehicle)
        self.provide_vehicle_conditions_second_step(vehicle)

    @step_decorator('WEB - TradeInComponent: Provide License plate')
    def provide_license_plate(self, vehicle: TradeInEntity):
        from time import sleep
        sleep(5)
        self.inp_license_plate.send_keys(vehicle.license_plate)
        self.ddl_state.select_item_by_text(vehicle.state)
        self.btn_find_vehicle.click()
        self.txt_found_vehicles.exists(10)
        assert vehicle.year in self.txt_found_vehicles.text_content, \
            f'Unable to find {vehicle} in found vehicle card {self.txt_found_vehicles.text_content}'
        if self.select_your_trim.exists():
            self.select_your_trim.select_trim(vehicle)

    @step_decorator('WEB - TradeInComponent: Provide manual estimate')
    def provide_manual_estimate(self, vehicle):
        self.btn_make_model.click()
        self.manual_estimate.open().apply_manual_estimate(vehicle)

    @step_decorator('WEB - TradeInComponent: Apply vehicle details')
    def apply_vehicle_details(self, vehicle: TradeInEntity, trade_in_provider: TradeInProvider = TradeInProvider.KBB):
        if self.btn_yes.exists():
            self.btn_yes.click()
        self.inp_license_plate.wait_for.presence_of_element_located(wait_time=10)
        if vehicle.license_plate:
            self.provide_license_plate(vehicle)
        elif vehicle.vin:
            self.provide_vin_number(vehicle)
        elif vehicle.manual_estimation:
            self.provide_manual_estimate(vehicle)
        else:
            self.provide_vehicle_information(vehicle, trade_in_provider)

    @step_decorator('WEB - TradeInComponent: Provide vehicle conditions first step')
    def provide_vehicle_conditions_first_step(self, vehicle: TradeInEntity):
        if vehicle.mileage:
            self.inp_mileage.clear_and_send_keys(vehicle.mileage, wait_time=5)
        self.provide_accident_condition(vehicle)
        self.provide_mechanical_condition(vehicle)
        self.btn_submit.js.click()

    @step_decorator('WEB - TradeInComponent: Provide vehicle conditions first step')
    def provide_vehicle_conditions_second_step(self, vehicle: TradeInEntity):
        self.btn_making_payments_yes.js.scroll_into_view(align='false')
        if vehicle.ownership:
            if vehicle.ownership == OwnershipType.OWNED:
                self.btn_making_payments_no.click()
            else:
                self.btn_making_payments_yes.click()
                if vehicle.loan:
                    if vehicle.license_plate:
                        self.switcher_manual_estimate.click()
                    self.inp_loan.clear_and_send_keys(vehicle.loan)
                elif vehicle.lender:
                    self.ddl_lender.select_item_by_text(vehicle.lender.lender, delay_for_options_to_appear_time=5)
                    if vehicle.lender.ssn:
                        self.inp_ssn.send_keys(vehicle.lender.ssn)
                else:
                    btn_lease = Button(CssSelector("[for='Lease']"))
                    btn_lease.ancestor = self
                    btn_lease.click()
                    if self.check_my_score_form.exists(wait_time=2):
                        self.check_my_score_form.fill_form(personal_information=self.ancestor.pii_entity)
        self.btn_see_trade_in_value.js.click()
        self.btn_see_trade_in_value.wait_for.presence_of_element_located(wait_time=20, until=False)

    @step_decorator('WEB - TradeInComponent: Trade-in')
    def trade_in(self, vehicle: TradeInEntity, trade_in_provider: TradeInProvider, lead_info: WebLeadEntity = None):
        if trade_in_provider == TradeInProvider.AUTO_HUB:
            self.ancestor.auto_hub_widget.trade_in(vehicle, lead_info)
        elif trade_in_provider == TradeInProvider.KBB_ICO:
            self.ancestor.kbb_ico_widget.trade_in(vehicle, lead_info)
        else:
            self.apply_vehicle_details(vehicle, trade_in_provider)
            if not vehicle.manual_estimation:
                self.provide_vehicle_conditions(vehicle)
            if vehicle.ownership != OwnershipType.LEASE:
                if self.pii_form.exists():
                    self.pii_form.sign_up(lead_info)
                assert self.final_report.exists(10)
                self.trade_in_value = self.final_report.get_trade_in_value() \
                    if not vehicle.manual_estimation else vehicle.manual_estimation
                return self.trade_in_value
            else:
                assert self.payoff_error.exists(5)

    @step_decorator('WEB - TradeInComponent: Remove Trade-in')
    def remove_trade_in(self):
        if not self.exists():
            self.ancestor.btn_trade_in.click()
        self.final_report.btn_remove.click(wait_time=3)
        self.ancestor.btn_trade_in.wait_for.text_to_be_present_in_element("None Selected", wait_time=3)

    @step_decorator('WEB - TradeInComponent: Edit Trade-in')
    def edit_trade_in(self):
        self.final_report.btn_edit.js.scroll_into_view()
        self.final_report.btn_edit.click(wait_time=5)

    @step_decorator('WEB - TradeInComponent: Check condition question page')
    def check_condition_question_page(self, trade_in_vehicle: TradeInEntity):
        result = []
        if trade_in_vehicle.mileage != self.inp_mileage.get_content().replace(",", ""):
            result.append(f"Actual mileage: {self.inp_mileage.get_content().replace(',', '')}\""
                          f"Expected mileage {trade_in_vehicle.mileage}")
        locator = "Yes" if trade_in_vehicle.accidents else "No"
        btn_accidents = Button(Xpath(f".//p[@data-testid='has_had_accidents']/..//div[contains(text(),'{locator}')]"))
        btn_accidents.ancestor = self
        locator = "Yes" if trade_in_vehicle.mechanical_issues else "No"
        btn_mechanical_issues = Button(
            Xpath(f".//p[@data-testid='has_had_mechanical_issues']/..//div[contains(text(),'{locator}')]"))
        btn_mechanical_issues.ancestor = self
        if not btn_accidents.is_enabled():
            result.append("Accidents button was not enabled")
        elif trade_in_vehicle.accidents:
            if trade_in_vehicle.accidents.airbags_deployed and not self.btn_airbags_deployed.is_enabled():
                result.append("airbags deployed button was not enabled")
            if trade_in_vehicle.accidents.repair_costs != Converter.extract_digits(self.inp_repair_costs.get_content()):
                result.append(f"Actual repair costs: {Converter.extract_digits(self.inp_repair_costs.get_content())}"
                              f"Expected repair costs: {trade_in_vehicle.accidents.repair_costs}")
        if not btn_mechanical_issues.is_enabled():
            result.append("Mechanical issues button was not enabled")
        elif trade_in_vehicle.mechanical_issues:
            for issue in trade_in_vehicle.mechanical_issues:
                btn_issue = Button(Xpath(f'.//span[contains(text(),"{issue.value["name"]}")]/..'))
                btn_issue.ancestor = self
                if not btn_issue.is_enabled():
                    result.append(f"{issue} button was not enabled")
        self.btn_submit.click()
        if trade_in_vehicle.ownership == OwnershipType.OWNED and not self.btn_making_payments_no.is_enabled():
            result.append("making payments 'No' button was not enabled")
        elif trade_in_vehicle.ownership in (OwnershipType.LEASE, OwnershipType.FINANCED) and \
                not self.btn_making_payments_yes.is_enabled():
            result.append("making payments 'Yes' button was not enabled")
        if trade_in_vehicle.loan and trade_in_vehicle.loan != self.inp_loan.get_content():
            result.append(f"Actual estimate the amount you owe: {self.inp_loan.get_content()}"
                          f"Expected repair costs: {trade_in_vehicle.loan}")

    @step_decorator('WEB - TradeInComponent: Skip Trade-in')
    def skip_trade_in(self):
        self.btn_skip.click()
        try:
            self.ancestor.spn_preloader.wait_for_appear_and_disappear()
        except TimeoutException:
            Log.warning("Spinner for loading Coverage did not appear")

    @step_decorator('WEB - TradeInComponent: Provide accident condition')
    def provide_accident_condition(self, vehicle: TradeInEntity):
        btn_accidents = Button(CssSelector(
            '[for="' + ("has_had_accidents-yes-id" if vehicle.accidents else "has_had_accidents-no-id") + '"]'))
        btn_accidents.ancestor = self
        btn_accidents.wait_for.visibility_of_element_located(wait_time=5)
        btn_accidents.js.click()
        if vehicle.accidents:
            if vehicle.accidents.airbags_deployed:
                self.btn_airbags_deployed.click()
            if vehicle.accidents.repair_costs:
                self.inp_repair_costs.clear_and_send_keys(vehicle.accidents.repair_costs)

    @step_decorator('WEB - TradeInComponent: Provide mechanical condition')
    def provide_mechanical_condition(self, vehicle: TradeInEntity):
        btn_mechanical_issues = Button(
            Id("has_had_mechanical_issues-yes-id" if vehicle.mechanical_issues else "has_had_mechanical_issues-no-id"))
        btn_mechanical_issues.ancestor = self
        btn_mechanical_issues.js.click()
        if vehicle.mechanical_issues:
            for issue in vehicle.mechanical_issues:
                btn_issue = Button(Xpath(f'.//span[contains(text(),"{issue.value["name"]}")]/..'))
                btn_issue.ancestor = self
                btn_issue.click()

    @step_decorator('WEB - TradeInComponent: Get confirmed loan payoff')
    def get_loan_payoff(self):
        return self.txt_loan_payoff.text

    @step_decorator('WEB - TradeInComponent: Open making payments form')
    def open_making_payments_form(self, vehicle: TradeInEntity):
        self.apply_vehicle_details(vehicle)
        self.provide_vehicle_conditions_first_step(vehicle)
        self.btn_making_payments_yes.click()

    @step_decorator('WEB - TradeInComponent: Get trade-in lenders')
    def get_trade_in_lenders(self, vehicle: TradeInEntity):
        self.open_making_payments_form(vehicle)
        return self.ddl_lender.get_options_list(delay_for_options_to_appear_time=5)

    @step_decorator('WEB - TradeInComponent: select transmission')
    def select_transmission(self, transmission_value):
        btn_transmission = Button(Xpath(f".//button[contains(text(),'{transmission_value}')]"))
        btn_transmission.ancestor = self
        btn_transmission.click()
        self.btn_find_vehicle.wait_for.element_to_be_clickable(wait_time=3)

    @step_decorator('WEB - TradeInComponent: refresh page')
    def refresh_page(self):
        self.ancestor.refresh()
        self.ancestor.exists(wait_time=5)
        self.ancestor.switch_to.default_content()
        self.ancestor.consumer.iframe_new_vdp.switch_to()
        self.ancestor.trade_in.wait_for.presence_of_element_located(wait_time=10)
        return self.ancestor.trade_in


@find_by(Xpath(".//div[@class='trade-in-report']//div[@class='trade-in-report']/.."))
class TradeInReportComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_low_price = TextBlock(
            Xpath(".//div[@class='trade-in-report__price-range']/div[contains(text(), 'Low')]/.."))
        self.txt_high_price = TextBlock(
            Xpath(".//div[@class='trade-in-report__price-range']/div[contains(text(), 'High')]/.."))
        self.btn_close = Button(CssSelector(".icon-close"))
        self.btn_continue = Button(CssSelector(".trade-in-report__btn"))

    def open_actions(self):
        pass


@find_by(Xpath(
    ".//h1[contains(text(),\"Your vehicle's trade-in estimate\") or contains(text(),'We would love to buy your vehicle!')"
    " or contains(text(),'Trade-in value applied to deal')]/.. | //div[@aria-label='Add Trade-In Modal']"))
class TradeInFinalReportComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_title = TextBlock(CssSelector('h1'))
        self.txt_sub_title = TextBlock(Xpath('.//h1/../div/div/p'))
        self.tooltip = Button(CssSelector('[role="tooltip"]'))
        self.txt_disclaimer = TextBlock(Xpath("./p[@color='dimText']"))
        self.txt_price = TextBlock(Xpath(".//sup//.. | .//p[contains(@class,'eszObc')]"))
        self.txt_calculation = TextBlock(CssSelector("[data-testid='trade-in-calculation']"))
        self.btn_edit = Button(CssSelector("[data-testid='trade-in-edit-appraisal']"))
        self.btn_remove = Button(Xpath(".//p[contains(text(),'Remove trade-in')]"))
        self.btn_apply_trade_in = Button(
            Xpath(".//button[contains(text(),'Apply Trade-In') or contains(text(),'Continue')]"))
        self.btn_cont_without_trade_in = Button(CssSelector("[data-testid='desktop-skip-btn']"))
        self.txt_vehicle_card = TextBlock(CssSelector("[data-testid='trade-in-card-text']"))
        self.txt_value_range = TextBlock(CssSelector("[data-testid='trade-in-infobox'] p"))
        self.txt_trade_in_infobox = TextBlock(
            Xpath("(//div[contains(@class,'ResultsValueCard')]/../div)[2] | .//div[@data-testid='trade-in-infobox']/p"))

    def open_actions(self):
        pass

    @step_decorator('WEB - TradeInReportComponent: Get Trade-in value')
    def get_trade_in_value(self):
        self.wait_for.presence_of_element_located(wait_time=10)
        if self.txt_calculation.exists():
            return self.txt_trade_in_infobox.text.split(" ")[0]
        return self.txt_price.text.split(" ")[0]

    @step_decorator('WEB - TradeInReportComponent: Remove Trade-in')
    def remove(self):
        self.wait_for.presence_of_element_located(wait_time=10)
        self.btn_remove.js.click()

    @step_decorator('WEB - TradeInReportComponent: Apply Trade-in')
    def apply_trade_in(self):
        self.btn_apply_trade_in.wait_for.presence_of_element_located(wait_time=10)
        payment_amount = None
        if self.ancestor.txt_payment_amount.exists():
            payment_amount = self.ancestor.txt_payment_amount.text
        self.btn_apply_trade_in.js.click()
        if payment_amount:
            self.ancestor.txt_payment_amount.exists()
            self.ancestor.txt_payment_amount.wait_for.text_to_be_present_in_element(text=payment_amount, wait_time=15,
                                                                                    until=False)
        try:
            self.ancestor.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=2)
        except Exception as e:
            Log.error(e)

    @step_decorator('WEB - TradeInReportComponent: Get tooltip text')
    def get_tooltip_text(self):
        self.action_chains.move_to_element(self.tooltip.find()).perform()
        return self.ancestor.tooltip.text

    @step_decorator('WEB - TradeInReportComponent: Get trade-in value range text')
    def get_trade_in_value_range_text(self):
        return Converter.extract_digits(self.txt_value_range.text)

    @step_decorator('WEB - TradeInReportComponent: Get trade-in value range text')
    def get_trade_in_value_text(self, trade_in_provider: TradeInProvider = TradeInProvider.KBB):
        if trade_in_provider != TradeInProvider.ASBURY:
            return self.txt_trade_in_infobox.text
        return self.txt_value_range.text

    @step_decorator('WEB - TradeInReportComponent: check final report page')
    def check_final_report_page(self, trade_in_vehicle: TradeInEntity, tooltip: str = None, trade_in_value: str = None,
                                value_range=None, trade_in_provider: TradeInProvider = TradeInProvider.KBB):
        result = []
        if final_report_title(trade_in_provider) != self.txt_title.text:
            result.append(
                f"Actual title: {self.txt_title.text}\nExpected title: {final_report_title(trade_in_provider)}")
        if (not trade_in_vehicle.manual_estimation and
                final_report_sub_title(trade_in_value, trade_in_provider) != self.txt_sub_title.text):
            result.append(f"Actual subtitle: {self.txt_sub_title.text}"
                          f"\nExpected subtitle: {final_report_sub_title(trade_in_value, trade_in_provider)}")
        if tooltip and tooltip != self.get_tooltip_text():
            result.append(f"Actual tooltip: {self.get_tooltip_text()}\nExpected tooltip: {tooltip}")
        if trade_in_provider != TradeInProvider.ASBURY and trade_in_provider != TradeInProvider.AUTO_HUB:
            if not trade_in_vehicle.manual_estimation and trade_in_value != self.get_trade_in_value_text(
                    trade_in_provider):
                result.append(f"Actual trade-in value: {self.get_trade_in_value_text(trade_in_provider)}"
                              f"\ntrade-in value: {trade_in_value}")
        if final_report_disclaimer != self.txt_disclaimer.text:
            result.append(f"Actual disclaimer: {self.txt_disclaimer.text}"
                          f"\nExpected disclaimer: {final_report_disclaimer}")
        if trade_in_vehicle.make not in self.txt_vehicle_card.text:
            result.append(f"{trade_in_vehicle.make} make was not found in {self.txt_vehicle_card.text}")
        if trade_in_vehicle.model not in self.txt_vehicle_card.text:
            result.append(f"{trade_in_vehicle.model} model was not found in {self.txt_vehicle_card.text}")
        if trade_in_vehicle.trim not in self.txt_vehicle_card.text:
            result.append(f"{trade_in_vehicle.trim} trim was not found in {self.txt_vehicle_card.text}")
        if trade_in_vehicle.year not in self.txt_vehicle_card.text:
            result.append(f" {trade_in_vehicle.year} year was not found in {self.txt_vehicle_card.text}")
        if not self.btn_edit.exists():
            result.append("Edit button was not found")
        if not self.btn_apply_trade_in.exists():
            result.append("Apply trade-in button was not found")
        if not self.btn_cont_without_trade_in.exists():
            result.append("Continue without trade-in button was not found")
        if not trade_in_vehicle.manual_estimation and value_range and str(
                value_range) != self.get_trade_in_value_range_text():
            result.append(f"Actual trade-in value range: {self.get_trade_in_value_range_text()}\n"
                          f"Expected trade-in value range: {value_range}")
        return result


@find_by(Xpath(".//h1[contains(text(),\"We’ll reach out about your leased trade-in\")"
               " or contains(text(),'Your trade-in information has been submitted!')]/.. "))
class TradeInPayoffErrorComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_header = TextBlock(CssSelector("p.cLSZYA"))
        self.btn_cont_without_trade_in = Button(CssSelector("[data-testid='desktop-skip-btn']"))
        self.btn_continue = Button(Xpath(".//button[contains(text(),'Continue')]"))

    def open_actions(self):
        pass

    @step_decorator('WEB - TradeInPayoffErrorComponent: Apply trade-in')
    def apply_trade_in(self):
        left_navigation_text = self.ancestor.btn_trade_in.text
        self.btn_continue.click()
        self.ancestor.btn_trade_in.wait_for.text_to_be_present_in_element(wait_time=5, text=left_navigation_text,
                                                                          until=False)


@find_by(Xpath(".//form[@data-testid='find-vin-modal']/.."))
class TradeInFindVinModal(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.modal_error = TradeInVinModalError(self)
        self.inp_vin_number = Input(CssSelector("#vinNumber"))
        self.btn_find_vehicle = Button(Xpath(".//button[contains(text(),'Find Vehicle')]"))

    def open_actions(self):
        if self.ancestor.btn_yes.exists():
            self.ancestor.btn_yes.click()
        self.ancestor.btn_vin.click(wait_time=2)
        self.wait_for.presence_of_element_located(wait_time=5)

    @step_decorator('WEB - TradeInFindVinModal: Apply vin number')
    def apply_vin_number(self, vehicle: TradeInEntity):
        self.enter_vin_number(vehicle.vin)
        self.ancestor.txt_found_vehicles.exists(10)
        assert vehicle.year in self.ancestor.txt_found_vehicles.text_content, \
            f'Unable to find {vehicle} in found vehicle card {self.ancestor.txt_found_vehicles.text_content}'

    @step_decorator('WEB - TradeInFindVinModal: Enter vin number')
    def enter_vin_number(self, vin_number: str):
        self.inp_vin_number.clear_and_send_keys(vin_number)
        self.btn_find_vehicle.click()


@find_by(CssSelector("[data-testid='find-vin-error']"))
class TradeInVinModalError(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_message = TextBlock(CssSelector("p"))
        self.btn_estimate_trade_in = Button(CssSelector("p a"))
        self.btn_dismiss_error = Button(CssSelector("[aria-label='Dismiss error']"))

    def open_actions(self):
        pass


@find_by(Xpath(".//form[@data-testid='manual-estimate-form']/.."))
class TradeInManualEstimate(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_your_vehicle = Input(CssSelector("#WHAT_VEHICLE"))
        self.inp_estimated_value = Input(CssSelector("input#VEHICLE_VALUE"))
        self.inp_mileage = Input(CssSelector("input#VEHICLE_MILEAGE"))
        self.btn_continue = Button(Xpath(".//button[contains(text(),'Continue')]"))

    def open_actions(self):
        self.ancestor.btn_manual_estimate.click()
        self.btn_continue.wait_for.element_to_be_clickable(wait_time=5)

    @step_decorator('WEB - TradeInManualEstimate: Apply manual estimation')
    def apply_manual_estimate(self, vehicle: TradeInEntity):
        self.provide_manual_estimate(vehicle)
        self.ancestor.manual_estimate_payments.apply_your_owe(vehicle)

    @step_decorator('WEB - TradeInManualEstimate: Apply manual estimation')
    def provide_manual_estimate(self, vehicle: TradeInEntity):
        self.inp_your_vehicle.send_keys(vehicle.year + " " + vehicle.make + " " + vehicle.model + " " + vehicle.trim)
        self.inp_estimated_value.send_keys(vehicle.manual_estimation)
        self.inp_mileage.send_keys(vehicle.mileage)
        self.btn_continue.click()
        self.ancestor.manual_estimate_payments.exists(5)


@find_by(Xpath(".//form[@data-testid='manual-estimate-form']/.."))
class TradeInManualEstimatePayments(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_yes = Button(Xpath(".//label[contains(@for,'isMakingPayments-yes-id')]/.."))
        self.btn_no = Button(Xpath(".//label[contains(@for,'isMakingPayments-no-id')]/.."))
        self.inp_estimate_you_owe = Input(CssSelector("input#estimateInput"))
        self.btn_continue = Button(Xpath(".//button[contains(text(),'Continue')]"))
        self.switcher_manual_estimate = Button(CssSelector("label[for='Manual-Estimate']"))

    def open_actions(self):
        pass

    @step_decorator('WEB - TradeInManualEstimate: Apply your owe')
    def apply_your_owe(self, vehicle: TradeInEntity):
        if vehicle.ownership == OwnershipType.FINANCED:
            self.btn_yes.click()
            # self.switcher_manual_estimate.click()
            self.inp_estimate_you_owe.clear_and_send_keys(f'{vehicle.loan:.2f}')
        else:
            self.btn_no.click()
        self.btn_continue.click()


@find_by(Xpath(".//div[contains(@class,'WarningCreditPull')]"))
class WarningCreditPull(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.warning_image = Image(CssSelector("[alt='Warning icon']"))
        self.btn_close = Button(CssSelector("[alt='Warning close']"))
        self.btn_add_details = Button(CssSelector("div span"))
        self.txt_message = TextBlock(CssSelector("div"))

    def open_actions(self):
        pass


@find_by(Xpath(".//div[contains(@class,'Payoff__SuccessCreditPull')]"))
class SuccessCreditPull(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.success_image = Image(CssSelector("[alt='Success icon']"))
        self.btn_edit_details = Button(CssSelector("span"))

    def open_actions(self):
        pass


@find_by(Xpath("//form[contains(@class,'pii-form')]/../../../.."))
class PiiForm(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_name = Input(Id("pii-name"))
        self.inp_phone = Input(Id("pii-phone"))
        self.inp_email = Input(Id("pii-email"))
        self.inp_zip = Input(Id("pii-zip"))
        self.btn_show_my_trade_in_value = Button(Xpath(".//button[text()='Show My Trade-In Value']"))

    def open_actions(self):
        pass

    @step_decorator("WEB - SignUp: Sign up")
    def sign_up(self, lead_info: WebLeadEntity):
        self.inp_name.send_keys(f"{lead_info.first_name} {lead_info.last_name}")
        self.inp_phone.send_keys(lead_info.phone)
        self.inp_email.send_keys(lead_info.email)
        if self.inp_zip.exists():
            self.inp_zip.send_keys(lead_info.zip)
        self.btn_show_my_trade_in_value.click()
        self.wait_for.presence_of_element_located(wait_time=10, until=False)


@find_by(CssSelector('[aria-label="Check my score for free"]'))
class CheckMyScoreForm(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_first_name = Input(CssSelector('[name="first_name"]'))
        self.inp_last_name = Input(CssSelector('[name="last_name"]'))
        self.inp_phone = Input(CssSelector('[name="phone"]'))
        self.inp_email = Input(CssSelector('[name="email"]'))
        self.inp_suite = Input(CssSelector('[name="suite"]'))
        self.btn_confirm = Button(CssSelector('#confirm'))
        self.ddl_street_address = SelectExtended(link_locator=CssSelector("[name='address']"),
                                                 search_input_locator=CssSelector("[name='address']"),
                                                 option_list_locator=Xpath("//div[@class='pac-item']"))

    def open_actions(self):
        pass

    @step_decorator('WEB - CheckMyScoreForm: Fill form')
    def fill_form(self, personal_information: PersonalInformation):
        self.inp_first_name.js.scroll_into_view()
        if personal_information.first_name:
            self.set_first_name(personal_information.first_name)
        if personal_information.last_name:
            self.set_last_name(personal_information.last_name)
        if personal_information.phone:
            self.set_phone(personal_information.phone)
        if personal_information.email:
            self.set_email(personal_information.email)
        if personal_information.street_address:
            self.set_street_address(personal_information.street_address)
        self.btn_confirm.js.click()
        # if personal_information.suite:
        #     self.set_suite(personal_information.suite)

    @step_decorator('WEB - CheckMyScoreForm: set first name')
    def set_first_name(self, first_name: None):
        if first_name != self.inp_first_name.get_attribute("value"):
            self.inp_first_name.clean_with_backspace()
            self.inp_first_name.send_keys(first_name)

    @step_decorator('WEB - CheckMyScoreForm: set last name')
    def set_last_name(self, last_name: None):
        if last_name != self.inp_last_name.get_attribute("value"):
            self.inp_last_name.clean_with_backspace()
            self.inp_last_name.send_keys(last_name)

    @step_decorator('WEB - CheckMyScoreForm: set phone')
    def set_phone(self, phone: None):
        if phone != Converter.extract_digits(self.inp_phone.get_attribute("value")):
            self.inp_phone.clean_with_backspace()
            self.inp_phone.send_keys(phone)

    @step_decorator('WEB - CheckMyScoreForm: set email')
    def set_email(self, email: None):
        if email != self.inp_email.get_attribute("value"):
            self.inp_email.clean_with_backspace()
            self.inp_email.send_keys(email)

    @step_decorator('WEB - CheckMyScoreForm: set street address')
    def set_street_address(self, street_address: str = None):
        if street_address != self.ddl_street_address.get_attribute("value"):
            try:
                self.ddl_street_address.select_item_by_text(street_address, add_space=True)
            except Exception:
                Log.info(f"select {street_address} street address second time")
                self.ddl_street_address.select_item_by_text(street_address, add_space=True)

    @step_decorator('WEB - CheckMyScoreForm: set suite')
    def set_suite(self, suite: None):
        if suite != self.inp_suite.get_attribute("value"):
            self.inp_suite.clean_with_backspace()
            self.inp_suite.send_keys(suite)


@find_by(Xpath(".//h2[text()='Select your trim level']/../.."))
class SelectYourTrimLevel(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_what_trim_level = Button(Xpath(".//button[text()='What is my trim level?']"))
        self.btn_submit = Button(CssSelector('button[type="submit"]'))
        self.ddl_trim = SelectExtended(link_locator=CssSelector('input[name="trim"]'),
                                       search_input_locator=CssSelector('input[name="trim"]'),
                                       option_list_locator=CssSelector('[role="option"]'))

    @step_decorator('WEB - SelectYourTrimLevel: select trim')
    def select_trim(self, vehicle: TradeInEntity):
        self.ddl_trim.select_item_by_text(text=vehicle.trim)
        self.btn_submit.click()
