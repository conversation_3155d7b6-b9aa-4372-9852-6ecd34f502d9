from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath
from kom_framework.src.web.data_types.element_types import Button
from kom_framework.src.web.support.page_factory import find_by
from src.utils.admin_tools.gb1_settings_enums.cbo_employee_pricing import EmployeePricingFlow
from src.utils.decorators import step_decorator


@find_by(Xpath(
    "//h2[text()='Do you qualify for manufacturer pricing?' or text()='Select your manufacturer pricing plan']/../.."))
class EmployeePricing(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_qualified = Button(Xpath(".//button[text()='Yes, I’m qualified']"))
        self.btn_skip = Button(Xpath(".//button[text()='No, skip']"))
        self.btn_confirm = Button(Xpath(".//button[text()='Confirm']"))
        self.btn_cancel = Button(Xpath(".//button[text()='Cancel Discount']"))

    def open_actions(self):
        self.ancestor.payment.btn_view.click(wait_time=5)

    @step_decorator("WEB - EmployeePricing: select {1} plan")
    def select_plan(self, plan_name: str):
        btn_plan = Button(Xpath(f"//h4[text()='{plan_name}']/../div"))
        btn_plan.ancestor = self.ancestor
        btn_plan.click()
        self.btn_confirm.click()
        self.btn_confirm.wait_for.presence_of_element_located(wait_time=5, until=False)
        self.ancestor.payment.txt_manufacturer_discount.wait_for.text_to_be_present_in_element(text="applied!",
                                                                                               wait_time=5)

    @step_decorator("WEB - EmployeePricing: apply employee pricing {1} plan")
    def apply_employee_pricing_plan(self, plan_name: str,
                                    employee_pricing_flow: EmployeePricingFlow = EmployeePricingFlow.BANNER):
        if employee_pricing_flow == EmployeePricingFlow.BANNER:
            self.ancestor.payment.btn_view.click()
        self.btn_qualified.wait_for.presence_of_element_located(wait_time=10)
        self.btn_qualified.wait_for.element_to_be_clickable(wait_time=10)
        self.btn_qualified.click()
        self.btn_confirm.wait_for.presence_of_element_located(wait_time=5)
        self.select_plan(plan_name)
