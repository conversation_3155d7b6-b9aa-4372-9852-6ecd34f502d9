import re
import time

from deepdiff import DeepDiff
from pytest_check import check_func
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.keys import Keys

from kom_framework.src.general import Log
from kom_framework.src.web import page_load_time
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON>path, CssSelector, Id
from kom_framework.src.web.data_types.element_list_types import <PERSON><PERSON><PERSON>
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spinner, AnyType, SelectExtended, Input
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.page_object import PageObject
from kom_framework.src.web.support.page_factory import find_by
from src import resq_manager_user_name, resq_password
from src.utils.converter import Converter
from src.utils.decorators import step_decorator
from src.web.consumer import PaymentOptionType
from src.web.consumer.desktop.cbo import SortingType, get_grid_view, get_filters_from_tuple, VehicleType
from src.web.consumer.desktop.cbo.dealer_log_in_component import DealerLogInComponent
from src.web.consumer.desktop.cbo.hamburger_menu_component import HamburgerMenuComponent
from src.web.consumer.desktop.cbo.sign_up import SignUp
from src.web.consumer.desktop.vdp.my_vehicles_container import MyVehiclesContainer
from src.web.entities.credit_application_entity import WebCreditApplicationEntity, PropertyType
from src.web.entities.lead_entity import WebLeadEntity
from src.web.entities.showroom_payment_option_entity import ShowroomPaymentOptionEntity


class GarageItemStructure(object):
    def __init__(self):
        self.vin_stock = TextBlock(CssSelector('.cbo-sidebar__vehicle-info, .cbo-sidebar__top-vin'))
        self.tag = TextBlock(CssSelector(".cbo-sidebar__top-type"))
        self.title = TextBlock(CssSelector(".cbo-sidebar__top-title"))
        self.continue_your_deal = Button(CssSelector(".btn-deal"))
        self.remove = Button(CssSelector(".cbo-sidebar__close"))
        self.progress = TextBlock(CssSelector(".progress-text"))
        self.progress_bar = AnyType(CssSelector(".progress-bar__filled "))

        self.specs = Button(Xpath(".//div[contains(@class, 'summary-card-tabs__item') and contains(text(), 'Specs')]"))
        self.features = AnyList(CssSelector('.details-list--highlights .details-list__content-item'))
        self.body = TextBlock(Xpath(".//span[contains(@class, 'icon-body')]/.."))
        self.mileage = TextBlock(Xpath(".//span[contains(@class, 'icon-lane-assist')]/.."))
        self.transmission = TextBlock(Xpath(".//span[contains(@class, 'icon-gearbox')]/.."))
        self.engine = TextBlock(Xpath(".//span[contains(@class, 'icon-engine')]/.."))
        self.mpg = TextBlock(Xpath(".//span[contains(@class, 'icon-mpg')]/.."))
        self.drivetrain = TextBlock(Xpath(".//span[contains(@class, 'icon-drivetrain')]/.."))
        self.exterior = TextBlock(Xpath(".//span[contains(text(), 'Exterior')]/.."))
        self.interior = TextBlock(Xpath(".//span[contains(text(), 'Interior')]/.."))
        self.exterior_color = TextBlock(Xpath(".//span[contains(text(), 'Exterior')]/../../span"))
        self.interior_color = TextBlock(Xpath(".//span[contains(text(), 'Interior')]/../../span"))

        self.payment = Button(
            Xpath(".//div[contains(@class, 'summary-card-tabs__item') and not(contains(text(), 'Specs'))]"))
        self.monthly_payment = TextBlock(CssSelector(".payment-per-month__value"))
        self.dealer_discount = TextBlock(
            CssSelector(".price_breakdown__money-off, .price-with-breakdown__main-price--off"))
        self.msrp = TextBlock(Xpath(
            ".//div[contains(text(), 'MSRP')]/..//div[contains(@class, 'details-list__content-item-price') or contains(@class, 'price-with-breakdown__main-price')]"))
        self.your_price = TextBlock(Xpath(
            ".//div[contains(text(), 'Your Price')]/../div[@class='details-list__content-item-price' or @class='price-with-breakdown__main-price']"))
        self.trade_in = TextBlock(Xpath(".//div[contains(text(), 'Trade-In')]/.."))
        self.term = TextBlock(Xpath(".//div[contains(text(), 'Term')]/.."))

    @step_decorator("WEB - GarageItemStructure: Get payment")
    def get_payment(self):
        return float(self.monthly_payment.text.split()[0].split("/")[0].replace("$", "").replace(",", ""))


@find_by(CssSelector(".modal__content.garage__remove-session-modal.remove-session-modal"))
class RemoveComponent(Component):
    def __init__(self, ancestor, vin):
        super().__init__(ancestor)
        self.vin = vin

        self.btn_cancel = Button(CssSelector(".remove-session-modal__cancel"))
        self.btn_remove = Button(CssSelector(".remove-session-modal__confirm"))

    def open_actions(self):
        self.ancestor.get_vehicle(self.vin).remove.click()

    @step_decorator("WEB - RemoveComponent: Confirm")
    def confirm(self):
        quantity = self.ancestor.tbl_garage_items.size
        self.btn_remove.click()
        self.ancestor.get_vehicles().wait_for.number_of_elements(quantity - 1, wait_time=3)


@find_by(Xpath(".//h2[contains(@class, 'FavoritesLimit')]/../.."))
class MaxFavoritesComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_max_favorites = TextBlock(Xpath(".//h2[contains(@class, 'FavoritesLimit')]"))
        self.btn_sign_up = Button(Xpath(".//button[@data-color='nakedBlack']"))
        self.btn_close = Button(Xpath(".//button[@data-testid='modal-close-button']"))

    def open_actions(self):
        pass

    @step_decorator("WEB - MaxFavoriteComponent: Close Modal")
    def close_modal(self):
        self.btn_close.click()
        assert not self.exists(wait_time=2), "Max Favorites modal wasn't closed"

    @check_func
    def check_my_favorites_modal_is_opened(self):
        assert self.exists(wait_time=3), "Max Favorites modal is not displayed"


class VehicleCardType:
    MODEL = "model"
    VIN = "vin"


class VehicleCardStructure:
    def __init__(self):
        self.year = TextBlock(CssSelector("[data-testid='showroom-vehicle-card__year']"))
        self.mileage = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__mileage"]'))
        self.vin_number = AnyType(Xpath('.//p[@data-testid="showroom-vehicle-card__year"]/..'))
        self.price = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__price-locked"]'))
        self.btn_favorites = Button(CssSelector('[data-testid="showroom-vehicle-card__button--favorite"]'))
        self.btn_view_details = Button(CssSelector('[data-testid="showroom-vehicle-card__button--card"]'))
        self.btn_buy_now = Button(CssSelector('[data-testid="showroom-vehicle-card__button--buy"]'))
        self.txt_vin = TextBlock(Xpath(".//div[contains(text(), 'VIN')]/span"))
        self.txt_engine = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__cylinders"]'))
        self.txt_drivetrain = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__drivetrain"]'))
        self.txt_body = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__body"]'))
        self.txt_horsepower = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__horsepower"]'))


class ModelCardStructure:
    def __init__(self):
        self.vehicle_name = AnyType(CssSelector("[data-testid='showroom-model-card__model-name']"))
        self.stock = AnyType(CssSelector("[data-testid='showroom-model-card__stock-amount']"))
        self.price = TextBlock(Xpath('.//div[@data-testid="showroom-price__not-is-locked"]/div/span'))
        self.cost = TextBlock(CssSelector('div[data-testid="showroom-model-card__monthly-starting-from"]>div'))
        self.btn_arrow = Button(Xpath(".//button[contains(@class,'ModelCard__ArrowButton')]"))


class FilterStructure:
    def __init__(self):
        self.btn_filter = Button(CssSelector("[data-testid='showroom-filter__toggle']"))
        self.filter_name = Button(CssSelector("[data-testid='showroom-filter__toggle-name']"))
        self.tbl_filter_items = Table(CssSelector("[data-testid='showroom-filter__list-item']"), FilterItemStructure)
        self.tbl_filter_items_active = Table(
            CssSelector("[data-testid='showroom-filter__list-item'][data-selected='true']"),
            FilterItemStructure)
        self.filter_label = AnyType(CssSelector("[data-testid='showroom-filter__toggle--label']"))

    def filter_expanded(self):
        return self.btn_filter.get_attribute("aria-expanded") == "true"

    def expand_collapse_filter(self, state: bool):
        if state != self.filter_expanded():
            self.btn_filter.js.click()
            self.btn_filter.wait_for.attribute_value_to_be_present_in_element("aria-expanded", str(state).lower(),
                                                                              wait_time=5)


class SearchItemStructure:
    def __init__(self):
        self.txt_year = TextBlock(Xpath(".//span[@data-testid='search-item-year']"))
        self.txt_make = TextBlock(Xpath(".//span[@data-testid='search-item-make']"))
        self.txt_model = TextBlock(Xpath(".//span[@data-testid='search-item-model']"))
        self.txt_trim = TextBlock(Xpath(".//span[@data-testid='search-item-trim']"))
        self.txt_stock_number = TextBlock(Xpath(".//span[@data-testid='search-item-stock-number']"))


class FilterItemStructure:
    def __init__(self):
        self.amount = AnyType(CssSelector("[data-testid='showroom-filter__amount'] span"))
        self.name = Button(CssSelector("[data-testid='showroom-filter__list-item--name']"))


class ShowRoomComponentBehaviour:
    def __init__(self):
        self.sign_up = SignUp(self)
        self.hamburger_menu = HamburgerMenuComponent(self)
        self.my_vehicles_container = MyVehiclesContainer(self)
        self.dealer_log_in = DealerLogInComponent(self)
        self.max_favorites = MaxFavoritesComponent(self)
        self.tbl_garage_items = Table(CssSelector('.garage__item '), GarageItemStructure)
        self.btn_close = Button(CssSelector(".close-btn"))
        self.btn_cancel_close = Button(Xpath(".//button[contains(@class, 'CancelButton')]"))
        self.btn_close_confirm = Button(Xpath(".//button[contains(@class, 'DeleteButton')]"))
        self.btn_dealer_toolbar_collapsed = Button(CssSelector("[data-testid='dealer-toolbar-expand-button']"))
        self.btn_clear_all = Button(CssSelector("[data-testid='showroom-reset-button']"))
        self.total_vehicles = AnyType(CssSelector("[data-testid='showroom-vehicles-amount']"))
        self.tbl_showroom_filters = Table(CssSelector("[data-testid='showroom-filter']"), FilterStructure)
        self.tbl_vehicle_card = Table(CssSelector("[data-testid='showroom-vehicle-card']"), VehicleCardStructure)
        self.tbl_model_card = Table(CssSelector("[data-testid='showroom-model-card']"), ModelCardStructure)
        self.txt_active_tab = TextBlock(
            CssSelector("[data-testid='showroom-filters__type-switcher'] [data-active='true']"))
        self.txt_active_payment = TextBlock(CssSelector("[data-testid='showroom-filters__payment-type-switcher'] [data-active='true']"))
        self.ddl_sorting = SelectExtended(
            link_locator=Xpath(".//button[contains(@id,'showroom-header__sorting-dropdown')]"),
            option_list_locator=CssSelector("[role='menuitem']"))
        self.txt_sort_selected_option = TextBlock(Xpath(".//button[contains(@id,'showroom-header__sorting-dropdown')]"))
        self.spn_preloader = Spinner(CssSelector("[data-testid='spinner']"))
        self.inp_search = Input(CssSelector("input[aria-label='Search inventory']"))
        self.btn_search = Button(Xpath(".//span[contains(@class,'SearchBtn')]"))
        self.int_monthly_min = Input(Id("monthly-min"))
        self.int_monthly_max = Input(Id("monthly-max"))
        self.int_price_min = Input(Id("price-min"))
        self.int_price_max = Input(Id("price-max"))
        self.int_down_payment = Input(CssSelector("[name='down_payment-input']"))
        self.secure_credit_app = SecureCreditApplication(self)
        self.btn_apply_financing = Button(Xpath(".//button[contains(@data-analytics,'apply-financing-button')]"))
        self.btn_profile_name = Button(Xpath(".//button[@aria-label='Toggle profile menu']/span"))
        self.btn_profile_details = Button(Xpath(".//button[@data-analytics='menu-interaction:menu-showroom-profile-details-button']"))
        self.ddl_shop = SelectExtended(
            link_locator=Xpath(".//button[@data-testid='select-vehicle-type-filter-trigger']"),
            option_list_locator=Xpath(".//button[@data-testid='select-vehicle-type-filter-option']"))
        self.txt_main_header = TextBlock(Xpath(".//h1[contains(@class, 'Heading')]"))
        self.btn_hamburger = Button(Xpath(".//button[@data-testid='burger-button']"))
        self.tbl_search = Table(Xpath(".//div[@data-testid='search-item-description']"), SearchItemStructure)
        self.btn_my_vehicles = Button(Xpath(".//button[@aria-label='Open my vehicles']"))
        # Dealer toolbar
        self.btn_dealer_toolbar_collapsed = Button(CssSelector("[data-testid='dealer-toolbar-expand-button']"))
        self.btn_dealer_toolbar_expanded = Button(CssSelector("[data-testid='dealer-toolbar-collapse-button']"))
        self.btn_dealer_avatar = Button(Xpath(".//span[@data-testid='dealer-toolbar-profile-toggle']"))

    @step_decorator("WEB - ShowRoomComponent: login")
    def login(self, lead_info: WebLeadEntity):
        sign_up = self.sign_up.open()
        sign_up.sign_up(lead_info)
        try:
            self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=3)
        except TimeoutException:
            pass

    @step_decorator("WEB - ShowRoomComponent: Sign Up via hamburger manu")
    def sign_up_hamburger_menu(self, lead_info: WebLeadEntity):
        self.sign_up.sign_up(lead_info, hamburger_menu=True)
        try:
            self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=3)
        except TimeoutException:
            pass

    @step_decorator("WEB - ShowRoomComponent: apply payments")
    def apply_payments(self, payment_options: ShowroomPaymentOptionEntity):
        self.select_vehicle_type(payment_options.vehicle_type)
        self.select_payment_type(payment_options.payment_option)
        if payment_options.monthly_payment_min:
            self.select_monthly_payment(payment_options.monthly_payment_min, payment_options.monthly_payment_max)
        if payment_options.down_payment:
            self.select_down_payment(payment_options.down_payment)
        if payment_options.price_min:
            self.select_price(payment_options.price_min, payment_options.price_max)
        try:
            self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=2)
        except TimeoutException:
            pass

    @step_decorator("WEB - ShowRoomComponent: Get vehicles")
    def get_vehicles(self):
        return self.tbl_garage_items

    @step_decorator("WEB - ShowRoomComponent: Get vehicle by {1} vin")
    def get_vehicle(self, vin):
        return self.tbl_garage_items.get_row_by_column_pattern("vin_stock", vin)

    @step_decorator("WEB - ShowRoomComponent: Get vehicle vins")
    def get_vehicles_vins(self):
        vin_stock = self.tbl_garage_items.get_column_values("vin_stock")
        return [re.search(r"VIN: (\w*)Stock: #(\w*)", vehicle).group(1) for vehicle in vin_stock]

    @step_decorator("WEB - ShowRoomComponent: Remove vehicles by {1} vin")
    def remove_vehicle(self, vin):
        RemoveComponent(self, vin).open().confirm()

    @step_decorator("WEB - ShowRoomComponent: Close Show Room")
    def close_show_room(self):
        self.btn_close.click()
        assert self.txt_main_header.exists(wait_time=3, until=False), "The showroom wasn't closed"

    @step_decorator("WEB - ShowRoomComponent: Cancel close In Store Show Room")
    def cancel_close_show_room_in_store(self):
        self.btn_close.click()
        self.btn_cancel_close.exists(wait_time=2)
        self.btn_cancel_close.click()
        assert not self.btn_cancel_close.exists(wait_time=2), "Close confirmation modal wasn't closed"

    @step_decorator("WEB - ShowRoomComponent: Close In Store Show Room")
    def close_show_room_in_store(self):
        self.btn_close.click()
        self.btn_close_confirm.exists(wait_time=2)
        self.btn_close_confirm.click()
        assert self.consumer.in_store.exists(wait_time=3), "Showroom wasn't closed or Welcome page is not displayed"

    @step_decorator("WEB - ShowRoomComponent: Check dealer toolbar is collapsed")
    def check_dealer_toolbar_collapsed(self):
        assert self.btn_dealer_toolbar_collapsed.exists(wait_time=3), \
            "Dealer is not logged in, dealer toolbar is not displayed"

    @step_decorator("WEB - ShowRoomComponent: Expand dealer toolbar")
    def expand_dealer_toolbar(self):
        self.btn_dealer_toolbar_collapsed.click()
        assert self.btn_dealer_toolbar_expanded.exists(wait_time=3), "Dealer toolbar wasn't expanded"

    @step_decorator("WEB - ShowRoomComponent: Select Vehicle type")
    def select_vehicle_type_option(self, vehicle_type: str):
        initial_header_text = self.txt_main_header.text
        initial_total_vehicle_text = self.total_vehicles.text
        self.ddl_shop.select_item_by_text(vehicle_type + " Vehicles")
        self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=3)
        self.txt_main_header.wait_for.text_equal(initial_header_text, wait_time=3, until=False)
        self.total_vehicles.wait_for.text_equal(initial_total_vehicle_text, wait_time=5, until=False)
        assert (self.txt_main_header.text.split()[1] == vehicle_type and
                self.total_vehicles.text.split()[1] == vehicle_type), \
            (f"Vehicle type text wasn't changed Expected text: {vehicle_type}. Actual header text:"
             f" {self.txt_main_header.text.split()[1]}. Actual total vehicle text: {self.total_vehicles.text.split()[1]}")

    @step_decorator("WEB - ShowRoomComponent: Clear all filters")
    def clear_all_filters(self):
        if self.btn_clear_all.exists(2):
            self.btn_clear_all.js.scroll_into_view("false")
            time.sleep(1)
            self.btn_clear_all.click()
            try:
                self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=2)
            except TimeoutException:
                Log.info("spinner doesn't appeared")

    @step_decorator("WEB - ShowRoomComponent: Expand {1} filter")
    def expand_filter(self, filter_type):
        showroom_filter = self.tbl_showroom_filters.get_row_by_column_value("filter_name", filter_type)
        showroom_filter.expand_collapse_filter(True)

    @step_decorator("WEB - ShowRoomComponent: Collapse {1} filter")
    def collapse_filter(self, filter_type):
        showroom_filter = self.tbl_showroom_filters.get_row_by_column_value("filter_name", filter_type, wait_time=5)
        showroom_filter.expand_collapse_filter(False)

    @step_decorator("WEB - ShowRoomComponent: Select {2} value for {1} filter ")
    def select_filter_item(self, filter_name, filter_item):
        self.tbl_showroom_filters.get_row_by_column_value("filter_name", filter_name) \
            .tbl_filter_items.get_row_by_column_value("name", filter_item).name.js.click()
        try:
            self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=2)
        except TimeoutException:
            Log.info("spinner doesn't appeared")

    @step_decorator("WEB - ShowRoomComponent: Apply filters")
    def apply_filters(self, filters):
        for filter_list in filters:
            for filter_name in filter_list:
                if not isinstance(filter_name, tuple):
                    self.expand_filter(filter_name)
                    for value in filter_list[1]:
                        self.select_filter_item(filter_name, value)

    @step_decorator("WEB - ShowRoomComponent: Collapse filters")
    def collapse_filters(self, filters):
        for filter_type in filters:
            if isinstance(filter_type, tuple):
                for filter_name in filter_type:
                    if isinstance(filter_name, str):
                        self.collapse_filter(filter_name)
            else:
                self.collapse_filter(filter_type)

    @step_decorator("WEB - ShowRoomComponent: Get filter label for {1} filter")
    def get_filter_label(self, filter_type):
        return self.tbl_showroom_filters.get_row_by_column_pattern("btn_filter", filter_type).filter_label.text

    @step_decorator("WEB - ShowRoomComponent: Get selected filter items name for {1} filter")
    def get_selected_filter_items_name(self, filter_name):
        return self.tbl_showroom_filters.get_row_by_column_pattern("btn_filter", filter_name) \
            .tbl_filter_items_active.get_column_values("name")

    @step_decorator("WEB - ShowRoomComponent: Get vehicles card data by {1} vehicle card type")
    def get_vehicles_card_data(self, vehicle_type: VehicleCardType):
        vehicles_list = self.get_all_vehicle_cards(vehicle_type)
        if vehicle_type.__eq__(VehicleCardType.MODEL):
            return {model.vehicle_name.text: model.stock.text for model in vehicles_list.get_content()}
        elif vehicle_type.__eq__(VehicleCardType.VIN):
            return [vehicle.vin_number.get_attribute("data-testid") for vehicle in
                    vehicles_list.get_content()]

    @step_decorator("WEB - ShowRoomComponent: Get all vehicles cards by {1} card type")
    def get_all_vehicle_cards(self, vehicle_type: VehicleCardType, wait_time=60):
        if vehicle_type.__eq__(VehicleCardType.MODEL):
            return self.tbl_model_card
        elif vehicle_type.__eq__(VehicleCardType.VIN):
            expected_amount = int(self.get_vehicle_total_amount())
            end_time = time.time() + wait_time
            while end_time > time.time():
                result = self.tbl_vehicle_card
                if result.size >= expected_amount:
                    return result
                result.get_row_by_index(result.size - 1).vin_number.js.scroll_into_view()
                time.sleep(0.5)
            assert expected_amount == result.size, "couldn't find all vehicles\n" \
                                                   f"Expected:{expected_amount}\nActual:{result.size}"

    @step_decorator("WEB - ShowRoomComponent: Get all prices")
    def get_cards_column_value(self, vehicle_type: VehicleCardType, column: str):
        return self.get_all_vehicle_cards(vehicle_type).get_column_values(column)

    @step_decorator("WEB - ShowRoomComponent: Get all prices and replace Call for price")
    def get_cards_column_values_with_replace(self, vehicle_type: VehicleCardType, column: str, replacement_value,
                                             value_to_replace):
        values = self.get_cards_column_value(vehicle_type, column)
        return Converter.replace_in_list(values, replacement_value, value_to_replace)

    @step_decorator("WEB - ShowRoomComponent: Get total amount of vehicles")
    def get_vehicle_total_amount(self):
        return self.total_vehicles.text.split(" ")[0]

    @step_decorator("WEB - ShowRoomComponent: Get sort options text")
    def get_sort_options_text(self):
        self.ddl_sorting.js.scroll_into_view("false")
        self.ddl_sorting.js.scroll_into_view("true")
        return self.ddl_sorting.get_options_list()

    @step_decorator("WEB - ShowRoomComponent: Select sorting option {1}")
    def select_sorting_option(self, sorting_option: SortingType):
        if sorting_option != self.txt_sort_selected_option.text_content:
            self.ddl_sorting.js.scroll_into_view("false")
            time.sleep(1)
            self.ddl_sorting.js.scroll_into_view("true")
            self.ddl_sorting.select_item_by_text(sorting_option)
            try:
                self.spn_preloader.wait_for_appear_and_disappear(wait_time=2)
            except TimeoutException:
                Log.info("Spinner was not appeared")

    @step_decorator("WEB - ShowRoomComponent: Get unselected filters")
    def get_unselected_filters(self, filter_to_exclude):
        result = {}
        filter_to_exclude = get_filters_from_tuple(filter_to_exclude)
        for filter_name in self.tbl_showroom_filters.get_column_text("filter_name"):
            if filter_name not in filter_to_exclude:
                filter_items_result = {}
                self.expand_filter(filter_name)
                for filter_item in self.__read_filters(filter_name):
                    filter_items_result[filter_item.split("\n")[0]] = filter_item.split("\n")[1]
                    result[filter_name] = filter_items_result
                self.collapse_filter(filter_name)
        return result

    def __read_filters(self, filter_name):
        data_list = self.tbl_showroom_filters.get_row_by_column_value("filter_name", filter_name) \
            .tbl_filter_items.elements_texts
        attempt = 0
        while attempt < 3:
            if not any(item == '' for item in data_list):
                if not data_list:
                    raise ValueError(f"There is no data for {filter_name} filter")
                return data_list
            attempt += 1
            Log.info(f"Failed to read all data for {filter_name} filter {data_list}.\nRetry #{attempt}")
            # time.sleep(0.2)
            data_list = self.tbl_showroom_filters.get_row_by_column_value("filter_name", filter_name) \
                .tbl_filter_items.elements_texts

        else:
            raise ValueError(f"Failed to retrieve all data for {filter_name} filter.\n{data_list}")

    @step_decorator("WEB - ShowRoomComponent: Get the sum of unselected filters")
    def get_sum_of_unselected_filters(self, actual_unselected_filters):
        result = {}
        for keys, items in actual_unselected_filters.items():
            result[keys] = sum([int(item) for item in items.values()])
        return result

    @step_decorator("WEB - ShowRoomComponent: Get the sum of grid view cards")
    def get_sum_of_grid_view(self, actual_unselected_filters):
        return sum([int(items.split(" ")[0]) for keys, items in actual_unselected_filters.items()])

    @step_decorator("WEB - ShowRoomComponent: Check vehicle grid view based on selected filter")
    def check_grid_view(self, filters, vehicle_type: VehicleCardType):
        return DeepDiff(self.get_vehicles_card_data(vehicle_type), get_grid_view[filters]).pretty()

    @step_decorator("WEB - ShowRoomComponent: Check unselected filters")
    def check_unselected_filters(self, filters, fx_ss_unselected_filters):
        actual_unselected_filters = self.get_unselected_filters(filters)
        return DeepDiff(actual_unselected_filters, fx_ss_unselected_filters.get(filters)).pretty()

    @step_decorator("WEB - ShowRoomComponent: Check filter label collapsed")
    def check_filter_label_collapsed(self, filters: tuple):
        results = []
        for filter_element in filters:
            for filter_name in filter_element:
                if isinstance(filter_name, str):
                    actual_filters = self.get_filter_label(filter_name)
                    if len(filter_element[1]) != int(actual_filters):
                        results.append(f"Filter {filter_name} : expected {len(filter_element[1])} but "
                                       f"{int(actual_filters)}")
        return results

    @step_decorator("WEB - ShowRoomComponent: Check filter label expanded")
    def check_filter_label_expanded(self, filters: tuple):
        results = []
        for filter_name, filter_value in filters:
            selected_filters = self.get_selected_filter_items_name(filter_name)
            filter_value_list = list(filter_value)
            filter_value_list.sort()
            selected_filters.sort()
            if selected_filters != filter_value_list:
                results.append(f"Filter {filter_name} \n: expected {filter_value_list}\n but "
                               f"{selected_filters}")
        return results

    @step_decorator("WEB - ShowRoomComponent: Get selected tab")
    def get_selected_tab(self):
        return self.txt_active_tab.text

    @step_decorator("WEB - ShowRoomComponent: get all vin numbers")
    def get_all_vin_numbers(self):
        return self.get_all_vehicle_cards(VehicleCardType.VIN) \
            .get_column_attribute("vin_number", "data-testid")

    @step_decorator("WEB - ShowRoomComponent: select vehicle type")
    def select_vehicle_type(self, vehicle_type: VehicleType):
        if self.txt_active_tab.text != vehicle_type:
            button = Button(Xpath(f"//button[contains(text(),'{vehicle_type.lower()}')]"))
            button.ancestor = self
            button.click()
            try:
                self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=2)
            except TimeoutException:
                pass

    @step_decorator("WEB - ShowRoomComponent: select payment type")
    def select_payment_type(self, payment_option: PaymentOptionType):
        if self.txt_active_payment.text != payment_option.lower():
            button = Button(Xpath(f"//button[contains(text(),'{payment_option.lower()}')]"))
            button.ancestor = self
            button.click()
            try:
                self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=2)
            except TimeoutException:
                pass

    @step_decorator("WEB - ShowRoomComponent: select payment type")
    def select_monthly_payment(self, min_payment: str, max_payment: str):
        self.int_monthly_min.clear_and_send_keys(min_payment)
        try:
            self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=5)
        except TimeoutException:
            pass
        self.int_monthly_max.clear_and_send_keys(max_payment)
        try:
            self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=5)
        except TimeoutException:
            pass

    @step_decorator("WEB - ShowRoomComponent: select down type")
    def select_down_payment(self, down_payment: str, retry: int = 3):
        while retry:
            self.int_down_payment.clear_and_send_keys(down_payment)
            try:
                self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=5)
            except TimeoutException:
                pass
            if Converter.extract_digits(self.int_down_payment.get_attribute("value")) != down_payment:
                retry -= 1
                self.select_down_payment(down_payment, retry)
            else:
                break

    @step_decorator("WEB - ShowRoomComponent: select price")
    def select_price(self, min_price: str, max_price: str):
        self.int_price_min.clear_and_send_keys(min_price)
        self.int_price_max.clear_and_send_keys(max_price)
        try:
            self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=2)
        except TimeoutException:
            pass

    @step_decorator("WEB - ShowRoomComponent: Select first model card")
    def select_first_model_card(self):
        all_vehicles = self.get_all_vehicle_cards(VehicleCardType.MODEL)
        assert all_vehicles.size > 0
        first_vehicle = all_vehicles.get_row_by_index(0)
        first_vehicle.btn_arrow.click()
        try:
            self.spn_preloader.wait_for_appear_and_disappear(wait_until_appears=2)
        except TimeoutException:
            pass

    @step_decorator("WEB - ShowRoomComponent: Select model card")
    def select_model_card(self, vehicle_model: str):
        self.tbl_model_card.exists(5)
        vehicle = self.tbl_model_card.get_row_by_column_value('vehicle_name', vehicle_model, wait_time=3)
        vehicles_in_stock = vehicle.stock.text
        vehicle.btn_arrow.js.scroll_into_view("false")
        time.sleep(1)
        vehicle.btn_arrow.js.scroll_into_view("true")
        vehicle.btn_arrow.click()
        assert self.tbl_vehicle_card.exists(wait_time=5)
        return vehicles_in_stock

    @step_decorator("WEB - ShowRoomComponent: select vin card")
    def get_first_vin_number(self):
        all_vehicles = self.get_all_vehicle_cards(VehicleCardType.VIN)
        assert all_vehicles.size > 0
        vin_number = all_vehicles.get_column_attribute("vin_number", "data-testid")[0]
        return vin_number

    @step_decorator("WEB - ShowRoomComponent: find vehicle by {1} vin")
    def find_vehicle_by_vin(self, vin: str):
        self.inp_search.clear_and_send_keys(vin)
        self.inp_search.action_chains.send_keys(Keys.ENTER).perform()
        try:
            self.spn_preloader.wait_for_appear_and_disappear()
        except Exception as e:
            Log.error(e)

    @step_decorator("WEB - ShowRoomComponent: Send keys to search input")
    def search_action(self, text: str):
        self.inp_search.clean_with_backspace()
        self.inp_search.clear_and_send_keys(text)

    @step_decorator("WEB - ShowRoomComponent: Open VDP from search")
    def open_vdp_from_search(self, stock_number: str):
        self.tbl_search.exists(5)
        vehicle = self.tbl_search.get_row_by_column_value('txt_stock_number', stock_number)
        vehicle.txt_model.click()
        self.switch_to_last_tab()

    @step_decorator("WEB - ShowRoomComponent: Get search itme data")
    def get_search_item_info(self, stock_number: str):
        self.tbl_search.exists(5)
        vehicle = self.tbl_search.get_row_by_column_value('txt_stock_number', stock_number, wait_time=3)
        year = vehicle.txt_year.text
        make = vehicle.txt_make.text
        model = vehicle.txt_model.text
        trim = vehicle.txt_trim.text
        return {'year': year, 'make': make, 'model': model, 'trim': trim}

    @step_decorator("WEB - ShowRoomComponent: Search with enter")
    def search_action_with_enter(self, text: str):
        time.sleep(1)
        self.btn_hamburger.action_chains.scroll_to_element()
        self.search_action(text)
        self.inp_search.action_chains.send_keys(Keys.ENTER).perform()
        try:
            self.spn_preloader.wait_for_appear_and_disappear()
        except Exception as e:
            Log.error(e)

    @step_decorator("WEB - ShowRoomComponent: open vdp by {1} vin")
    def open_vdp_by_vin(self, vin: str):
        vehicle = self.tbl_vehicle_card.get_row_by_attribute_value("vin_number", "data-testid", vin, wait_time=5)
        vehicle.year.js.scroll_into_view()
        time.sleep(1)
        self.action_chains.move_to_element(vehicle.year.find()).perform()
        assert vehicle.btn_view_details.is_displayed()
        vehicle.btn_view_details.click()
        self.switch_to_last_tab()

    @step_decorator("WEB - ShowRoomComponent: Open In Store by clicking Open Welcome page button")
    def open_in_store(self):
        self.hamburger_menu.btn_open_welcome_page.click()
        self.switch_to_last_tab()

    @step_decorator("WEB - ShowRoomComponent: Check consumer name in header is not present")
    @check_func
    def check_consumer_name_not_present(self):
        assert not self.btn_profile_name.exists(wait_time=3), "Consumer wasn't logged out"

    @step_decorator("WEB - ShowRoomComponent: Get consumer name from the header")
    def get_consumer_name(self):
        return self.btn_profile_name.text

    @step_decorator("WEB - ShowRoomComponent: Open PII form")
    def open_pii(self):
        self.btn_profile_name.js.scroll_into_view('false')
        self.btn_profile_name.js.scroll_into_view()
        self.btn_profile_name.click()
        self.btn_profile_details.exists(3)
        self.btn_profile_details.click()
        assert self.sign_up.exists(3), "PII modal wasn't opened"

    @step_decorator("WEB - ShowRoomComponent: Toggle vehicles favorite status")
    def toggle_favorite_status(self, vehicle_vin, mark: bool = True):
        action = "mark" if mark else "unmark"
        expected_state = "true" if mark else "false"
        opposite_state = "false" if mark else "true"

        vehicle = self.tbl_vehicle_card.get_row_by_attribute_value(
                "vin_number", "data-testid", vehicle_vin, wait_time=5)

        vehicle.btn_favorites.js.scroll_into_view("true")
        time.sleep(1)
        vehicle.btn_favorites.js.scroll_into_view("false")

        if vehicle.btn_favorites.get_attribute('aria-pressed', wait_time=3) == opposite_state:
            vehicle.btn_favorites.click()
            if not self.max_favorites.exists(wait_time=3):
                assert vehicle.btn_favorites.get_attribute('aria-pressed', wait_time=3) == expected_state, \
                    f"Vehicle with VIN {vehicle_vin} was not successfully {action}ed as/from favorite"
        else:
            raise AssertionError(f"Vehicle with VIN {vehicle_vin} is already {action}ed as/from favorite")


@find_by(CssSelector(".garage"))
class CBOShowRoomComponent(Component, ShowRoomComponentBehaviour):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        ShowRoomComponentBehaviour.__init__(self)

    @step_decorator("WEB - CBOShowRoomComponent: Open Show Room")
    def open_actions(self):
        self.ancestor.btn_garage.click()
        try:
            self.ancestor.spn_preloader.wait_for_appear_and_disappear()
        except TimeoutException:
            Log.warning("Spinner for loading Coverage did not appear")


@find_by(Xpath(".//div[contains(@id, 'showroom-container')]/.."))
class ShowRoom(PageObject, ShowRoomComponentBehaviour):
    def __init__(self, consumer_name=None, consumer=None):
        self.consumer_name = consumer_name
        self.consumer = consumer
        self.spn_preloader = Spinner(CssSelector(".preloader"))
        ShowRoomComponentBehaviour.__init__(self)

    @step_decorator("WEB - ConsumerShowRoomComponent: Open Show Room")
    def open_actions(self):
        if self.driver:
            self.switch_to.default_content()
        self.consumer.open()
        self.consumer.btn_garage.click()
        self.consumer.cbo.uuid = self.consumer.cbo.get_uuid()
        self.consumer.iframe_new_vdp.switch_to()

    def setup_page(self):
        self.total_vehicles.wait_for.text_equal("0 vehicles", wait_time=page_load_time, until=False)


@find_by(Xpath(".//div[contains(@class,'StandAloneCreditApp__Container')]"))
class SecureCreditApplication(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_first_name = Input(CssSelector("input[name='first_name']"))
        self.inp_last_name = Input(CssSelector("input[name='last_name']"))
        self.inp_email = Input(CssSelector("input[name='email']"))
        self.inp_phone = Input(CssSelector("input[name='phone']"))
        self.inp_date_of_birth = Input(CssSelector("input[name='date_of_birth']"))
        self.inp_social_security_number = Input(CssSelector("input[name='social_security_number']"))

        self.inp_street = Input(CssSelector("input[name='street']"))
        self.inp_suite = Input(CssSelector("input[name='suite']"))
        self.inp_city = Input(CssSelector("input[name='city']"))
        self.inp_state = Input(CssSelector("input[name='state']"))
        self.inp_zip = Input(CssSelector("input[name='zip']"))
        self.inp_move_in_date_month = Input(CssSelector("input[name='move_in_date_month']"))
        self.inp_move_in_date_year = Input(CssSelector("input[name='move_in_date_year']"))
        self.inp_monthly_payment = Input(CssSelector("input[name='monthly_payment']"))

        self.btn_property_rent = Button(Xpath(".//div[contains(text(), 'My landlord')]"))
        self.btn_property_own = Button(Xpath(".//div[contains(text(), 'I own it')]"))
        self.btn_property_member = Button(Xpath(".//div[contains(text(), 'Family member/relative')]"))

        self.inp_employment_status = Input(CssSelector("input[name='employment_status']"))
        self.inp_job_title = Input(CssSelector("input[name='job_title']"))
        self.inp_employer_name = Input(CssSelector("input[name='employer_name']"))
        self.inp_employer_phone_number = Input(CssSelector("input[name='employer_phone_number']"))
        self.inp_job_start_date_month = Input(CssSelector("input[name='job_start_date_month']"))
        self.inp_job_start_date_year = Input(CssSelector("input[name='job_start_date_year']"))
        self.inp_annual_gross_income = Input(CssSelector("input[name='annual_gross_income']"))

        self.btn_no_applicant = Button(CssSelector("label[for='no-radio']"))
        self.btn_yes_applicant = Button(CssSelector("label[for='yes-radio']"))

        self.txt_application_reviewed = TextBlock(CssSelector("label[for='Application Reviewed']"))

        self.btn_application_reviewed_confirm = Button(CssSelector("label[for='Application Reviewedconfirm']"))
        self.btn_arbitration_agreement_confirm = Button(CssSelector("label[for='Arbitration Agreementconfirm']"))

        self.btn_submit = Button(Xpath(".//div/button[contains(text(),'Submit Application')]"))

    @step_decorator('WEB - SecureCreditApplication: Open Credit Application Form')
    def open_actions(self):
        self.ancestor.btn_apply_financing.click()

    @step_decorator('WEB - SecureCreditApplication: Fill Credit App Form')
    def fill(self, application: WebCreditApplicationEntity = None):
        if application.first_name:
            self.inp_first_name.clear_and_send_keys(application.first_name)
        if application.last_name:
            self.inp_last_name.clear_and_send_keys(application.last_name)
        if application.email:
            self.inp_email.clear_and_send_keys(application.email)
        if application.phone:
            self.inp_phone.clear_and_send_keys(application.phone)
        if application.date_of_birth:
            self.inp_date_of_birth.clear_and_send_keys(application.date_of_birth)
        if application.social_security_number:
            self.inp_social_security_number.clear_and_send_keys(application.social_security_number)
        if application.street:
            self.inp_street.clear_and_send_keys(application.street)
        if application.suite:
            self.inp_suite.clear_and_send_keys(application.suite)
        if application.city:
            self.inp_city.clear_and_send_keys(application.city)
        if application.state:
            self.inp_state.clear_and_send_keys(application.state)
        if application.zip:
            self.inp_zip.clear_and_send_keys(application.zip)
        if application.move_in_date_month:
            self.inp_move_in_date_month.clear_and_send_keys(application.move_in_date_month)
        if application.move_in_date_year:
            self.inp_move_in_date_year.clear_and_send_keys(application.move_in_date_year)
        if application.property_type:
            if application.property_type == PropertyType.OWNED:
                self.btn_property_own.js.click()
            elif application.property_type == PropertyType.RENT:
                self.btn_property_rent.js.click()
            elif application.property_type == PropertyType.FAMILY_MEMBER:
                self.btn_property_member.js.click()
        if application.monthly_payment:
            self.inp_monthly_payment.clear_and_send_keys(application.monthly_payment)
        if application.employment_status:
            self.inp_employment_status.type_keys(application.employment_status)
        if application.job_title:
            self.inp_job_title.clear_and_send_keys(application.job_title)
        if application.employer_name:
            self.inp_employer_name.clear_and_send_keys(application.employer_name)
        if application.employer_phone_number:
            self.inp_employer_phone_number.clear_and_send_keys(application.employer_phone_number)
        if application.job_start_date_month:
            self.inp_job_start_date_month.clear_and_send_keys(application.job_start_date_month)
        if application.job_start_date_year:
            self.inp_job_start_date_year.clear_and_send_keys(application.job_start_date_year)
        if application.annual_gross_income:
            self.inp_annual_gross_income.clear_and_send_keys(application.annual_gross_income)
        self.check_terms_and_conditions()
        self.btn_submit.click()

    @step_decorator('WEB - SecureCreditApplication: Check application and arbitration agreement')
    def check_terms_and_conditions(self):
        self.txt_application_reviewed.js.click()
        self.btn_application_reviewed_confirm.js.click()
        self.btn_arbitration_agreement_confirm.js.click()
