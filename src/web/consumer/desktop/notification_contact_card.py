from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_types import TextBlock
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(CssSelector(".gg-notification__msg"))
class NotificationContactCard(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.title = TextBlock(CssSelector(".gg-contact-minimized__title"))
        self.name = TextBlock(CssSelector(".gg-contact-minimized__details strong"))
        self.email = TextBlock(CssSelector(".gg-contact-minimized__details-contact"))

    def open_actions(self):
        pass

    @step_decorator('WEB - NotificationContactCard: Get email')
    def get_email(self):
        return self.email.text.split("\n")[1]
