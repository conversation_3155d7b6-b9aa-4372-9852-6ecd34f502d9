from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector, Xpath
from kom_framework.src.web.data_types.element_types import Button
from kom_framework.src.web.support.page_factory import find_by


@find_by(Xpath("//div[contains(@class, 'PDPAConsent')]"))
class PDPAPopUp(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.start_chat_btn = Button(CssSelector("button[aria-label='Consent to Start Chat']"))
        self.pdpa_policy_link = Button(Xpath(".//a[text()='PDPA Policy']"))

    def open_actions(self):
        pass
