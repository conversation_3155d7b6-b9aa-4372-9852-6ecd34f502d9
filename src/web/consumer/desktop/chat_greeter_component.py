from selenium.webdriver.common.keys import Keys

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector, Xpath
from kom_framework.src.web.data_types.element_types import Button, Input, TextBlock, Image
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(Xpath(".//div[contains(@class, 'StyledComponents__GreeterOuterWrapper')]"))
class ChatGreeterComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_message = Input(Xpath(".//div[contains(@class, 'MessageBoxMessageWrapper')]//textarea"))
        self.btn_send = Button(CssSelector("button[aria-label='Send Message']"))
        self.txt_invite = TextBlock(Xpath(".//span[contains(@class, 'StyledComponents__GreeterMessage')]"))
        self.txt_operator_name = TextBlock(Xpath(".//div[contains(@class, 'Card__CardHeaderContent') and @type='name']/p"))
        self.img_operator_picture = Image(Xpath(".//div[contains(@class, 'Card__CardHeaderImage')]"))
        ##self.btn_sms = Button(CssSelector(".gg-chat-reply__sms.gg-go-sms"))
        self.btn_lets_chat = Button(Xpath(".//button[contains(text(), \"Let's chat\")]"))
        self.btn_later = Button(Xpath(".//button[contains(text(), 'Maybe Later')]"))
        self.btn_close = Button(Xpath(".//button[@aria-label='Close']"))
        self.btn_qr1 = Button(Xpath(".//button[contains(@class, 'Button__QuickReplyButton')][1]"))
        self.btn_qr2 = Button(Xpath(".//button[contains(@class, 'Button__QuickReplyButton')][2]"))
        self.img_holiday_flag = Image(Xpath(".//div[contains(@class, 'HolidayTopperWrapper')]"))
        self.btn_terms = Button(Xpath(".//a[contains(text(), 'Terms')]"))
        self.btn_gubagoo = Button(Xpath(".//a[contains(text(), 'Gubagoo')]"))
        self.btn_sms = Button(Xpath(".//button[contains(@aria-label,'Chat with SMS')]"))
        self.img_alt_holiday = Image(Xpath(".//span[contains(@class,'StyledComponents__InlineHolidayIconWrapper')]/img"))
        self.img_new_year = Image(Xpath(".//span[contains(@class,'StyledComponents__InlineHolidayIconWrapper')]"))

    @step_decorator('WEB: Open "ChatGreeter" Component')
    def open_actions(self):
        self.wait_for.visibility_of_element_located(wait_time=20)

    @step_decorator('WEB - ChatGreeterComponent: Send an initial {1} message')
    def send_initial_message(self, message):
        #self.btn_lets_chat.click()

        self.inp_message.send_keys(message)
        self.inp_message.action_chains.send_keys(Keys.ENTER).perform()
        #self.inp_message.action_chains.send_keys(Keys.ENTER).perform()
        # try:
        #     self.ancestor.spn_wait.wait_for_appear_and_disappear()
        # except TimeoutException:
        #     Log.warning("Spinner was not appeared after sending initial message")

    @step_decorator('WEB - ChatGreeterComponent: Get name of operator')
    def get_operator_name(self):
        return self.txt_operator_name.text

    @step_decorator('WEB - ChatGreeterComponent: Get operator avatar')
    def get_operator_avatar(self):
        return self.img_operator_picture.value_of_css_property('background-image').replace("\")", "").replace("url(\"", "")

    @step_decorator('WEB - ChatGreeterComponent: Get label of quick reply 1')
    def get_qr1_label(self):
        return self.btn_qr1.text

    @step_decorator('WEB - ChatGreeterComponent: Get label of quick reply 2')
    def get_qr2_label(self):
        return self.btn_qr2.text

    @step_decorator('WEB - ChatGreeterComponent: Select quick reply 1')
    def select_qr1(self):
        self.btn_qr1.click()
        assert self.ancestor.chat_component.exists(5)

    @step_decorator('WEB - ChatGreeterComponent: Select quick reply 2')
    def select_qr2(self):
        self.btn_qr2.click()
        assert self.ancestor.chat_component.exists(5)

    @step_decorator('WEB - ChatGreeterComponent: Close')
    def close(self):
        self.txt_invite.js.mouseover()
        self.txt_invite.action_chains.move_to_element().perform()
        self.btn_close.js.click()
        self.wait_for.visibility_of_element_located(until=False, wait_time=3)

    @step_decorator('WEB - ChatGreeterComponent: Verify holiday theme were applied')
    def has_holiday(self, name):
        return self.img_holiday_flag.exists() and self.img_holiday_flag.get_attribute("data-testid") == name

    @step_decorator('WEB - ChatGreeterComponent: Verify holiday theme were applied')
    def has_alt_holiday(self, alt):
        return self.img_alt_holiday.exists() and self.img_alt_holiday.get_attribute("alt") == alt

    @step_decorator('WEB - ChatGreeterComponent: Verify NEW YEAR theme were applied')
    def has_new_year_theme(self):
        return self.img_new_year.exists()

    @step_decorator('WEB - ChatGreeterComponent: get greeter message')
    def get_greeter_message(self):
        return self.txt_invite.text

    @step_decorator('WEB - ChatGreeterComponent: get send button text')
    def get_sms_btn_text(self):
        return self.btn_sms.text


@find_by(Xpath(".//div[contains(@class, 'StyledComponents__GreeterCardHeader')]/../.."))
class AutoEngageGreeterComponent(Component):
    def open_actions(self):
        self.wait_for.visibility_of_element_located(wait_time=20)

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_invite = TextBlock(Xpath(".//div[contains(@class, 'StyledComponents__ContentWrapper')]/p"))
        self.img_operator_picture = Image(Xpath(".//div[contains(@class, 'Card__CardHeaderImage')]"))
        self.btn_close = Button(CssSelector("button[aria-label='Close']"))
        self.btn_lets_chat = Button(Xpath('.//button[text()="Let\'s chat"]'))
        self.btn_later = Button(Xpath('.//button[text()="Maybe Later"]'))
        self.btn_txt_us = Button(Xpath(".//div/button[contains(text(), 'Text Us')]"))

    @step_decorator('WEB - AutoEngageGreeterComponent: close the greeter')
    def close(self):
        self.txt_invite.action_chains.move_to_element()
        self.txt_invite.js.mouseover()
        self.btn_close.js.click()
        self.wait_for.visibility_of_element_located(until=False, wait_time=3)

    @step_decorator('WEB - AutoEngageGreeterComponent: get invite text')
    def get_invite_text(self):
        return self.txt_invite.text

    @step_decorator('WEB - AutoEngageGreeterComponent: click on lets chat button')
    def click_lets_chat(self):
        self.btn_lets_chat.click()
        self.ancestor.welcome.wait_for.visibility_of_element_located(wait_time=3)

    @step_decorator('WEB - AutoEngageGreeterComponent: click on text us button')
    def click_text_us(self):
        self.btn_txt_us.click()
