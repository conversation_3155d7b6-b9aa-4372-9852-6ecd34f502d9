from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Css<PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_types import Button, Input, TextBlock
from kom_framework.src.web.support.page_factory import find_by

from src.utils.decorators import step_decorator


@find_by(Xpath("//div[contains(@class, 'SMSWrapper')]"))
class SmsComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_message = Input(CssSelector("textarea[name=message]"))
        self.inp_phone = Input(CssSelector("input[data-testid='guba-sms-form-phone']"))
        self.inp_name = Input(CssSelector("input[data-testid='guba-sms-form-name']"))
        self.btn_text_us = Button(CssSelector('button[type=submit]'))
        self.btn_cancel = Button(Xpath('.//button[text()= "Cancel"]'))
        self.btn_resend = Button(Xpath(".//div[contains(@class, 'StyledComponents__Send')]"))
        self.btn_agree = Button(Xpath(".//button[text()= 'I Agree']"))
        self.btn_disagree = Button(Xpath(".//button[text()= 'I Disagree']"))
        self.txt_result_title = TextBlock(Xpath(".//div[contains(@class, 'SuccessTitle')]"))
        self.error = 'gg-invalid'
        self.error_msg = "error-message"
        self.btn_ok = Button(CssSelector('button[type=submit]'))

    @step_decorator('WEB - SMSComponent: Open "Sms" Component')
    def open_actions(self, sms_button=False):
        self.ancestor.welcome.open()
        if sms_button:
            self.ancestor.welcome.btn_sms.click()
        else:
            self.ancestor.welcome.btn_text_us.click()
        if not self.exists(3):
            if sms_button:
                self.ancestor.welcome.btn_sms.click()
            else:
                self.ancestor.welcome.btn_text_us.click()

    @step_decorator('WEB - SMSComponent: Cancel')
    def cancel(self):
        self.btn_cancel.click()

    @step_decorator('WEB - SMSComponent: Text us {1} phone and {2} message')
    def text_us(self, phone, message, name=None):
        if name:
            self.inp_name.send_keys(name)
        self.inp_message.send_keys(message)
        self.inp_phone.send_keys(phone)
        self.btn_text_us.click()
        self.btn_agree.wait_for.presence_of_element_located(5, message="I Agree button was not displayed")
        self.btn_agree.click()
        self.btn_agree.wait_for.text_to_be_present_in_element("Sending...", wait_time=10, until=False)
        self.txt_result_title.wait_for.text_to_be_present_in_element("Text sent to", wait_time=10)
        #self.btn_text_us.click()
        return self.txt_result_title.text

    @step_decorator('WEB - SMSComponent: Text us {1} phone and {2} message')
    def fill(self, phone, message, name=None):
        if name:
            self.inp_name.send_keys(name)
        self.inp_message.send_keys(message)
        self.inp_phone.send_keys(phone)
        self.btn_text_us.click()
        self.btn_agree.wait_for.presence_of_element_located(5, message="I Agree button was not displayed")
        self.btn_agree.click()
        self.btn_agree.wait_for.text_to_be_present_in_element("Sending...", wait_time=10, until=False)
        self.txt_result_title.wait_for.text_to_be_present_in_element("Text sent to", wait_time=10)
        self.btn_ok.click()

    @step_decorator('WEB - SMSComponent: get label from message field')
    def get_message_label(self):
        return self.inp_message.get_attribute('aria-label')

    @step_decorator('WEB - SMSComponent: check required field error for all fields')
    def check_for_error(self):
        name = self.check_name_error()
        phone = self.check_phone_error()
        message = self.check_message_error()
        return [name, message, phone]

    @step_decorator('WEB - SMSComponent: check required field error for name field')
    def check_name_error(self):
        return self.error_msg in self.inp_name.get_attribute("aria-describedby")

    @step_decorator('WEB - SMSComponent: check required field error for phone field')
    def check_phone_error(self):
        return self.error_msg in self.inp_phone.get_attribute("aria-describedby")

    @step_decorator('WEB - SMSComponent: check required field error for message field')
    def check_message_error(self):
        return self.error in self.inp_message.get_attribute("class")

    @step_decorator('WEB - SMSComponent: hit send again')
    def send_again(self):
        return self.btn_resend.click()
