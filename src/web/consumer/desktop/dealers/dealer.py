import os
import random

from selenium.common.exceptions import TimeoutException
from time import sleep

from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON><PERSON>ctor, <PERSON>path, Id
from kom_framework.src.web.data_types.element_types import But<PERSON>, TextBlock
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.page_object import PageObject
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.consumer.consumer_page_behaviour import ConsumerPageBehaviour


class DoralKiaStructure:
    def __init__(self):
        self.title = Button(CssSelector(".vehicleDetailsLink"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("body.homepage,.snap-content"))
class DealerDoralKia(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="http://www.doralkia.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector(".stat-search-submit"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(CssSelector(".vinDisplay>span"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../../../../../../../.."),
                                DoralKiaStructure)

    def setup_page(self):
        sleep(1)
        self.btn_search.click()
        sleep(2)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerDoralKia: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerDoralKia: Get vin')
    def get_vin(self):
        return self.txt_vin.text


class DoralHyundaiStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2.result-title"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector(".ae-lang-en.ae-device-desktop.ae-launcher"))
class DealerDoralHyundai(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="https://www.doralhyundai.com/new-vehicles"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector(".submit-search"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(CssSelector("#vin"))

        self.tbl_result = Table(Xpath(".//button[contains(@class,'cbo-button cbo-primary')]/../../../../.."),
                                DoralHyundaiStructure)

    def setup_page(self):
        assert self.exists(wait_time=5), "Page not found"
        assert self.btn_payment.exists(wait_time=10), "Button not found"
        assert self.tbl_result.exists(wait_time=15), "Inventory table can't be found"

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerDoralHyundai: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(5)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerDoralHyundai: Get vin')
    def get_vin(self):
        return self.txt_vin.text


class GatorlandToyotaStructure:
    def __init__(self):
        self.title = Button(CssSelector(".vehicleDetailsLink"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector(".snap-content"))
class DealerGatorlandToyota(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="https://www.gatorlandtoyota.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector("button.stat-search-submit"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//li[contains(text(), 'VIN')]"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../../../../../../../.."),
                                GatorlandToyotaStructure)

    def setup_page(self):
        sleep(1)
        self.btn_search.click()
        sleep(2)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerGatorlandToyota: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerGatorlandToyota: Get vin')
    def get_vin(self):
        return self.txt_vin.get_attribute("textContent").split()[1]


class FredToyotaStructure:
    def __init__(self):
        self.title = Button(CssSelector(".vehicle-card-details-container h2 a"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector(".toyota-red-white"))
class DealerFredToyota(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="https://www.fredandersontoyota.com/all-inventory/index.htm"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector("button.submit-search"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//li[contains(text(), 'VIN')]"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../../../../../.."),
                                FredToyotaStructure)

    def setup_page(self):
        assert self.exists(wait_time=5), "Page not found"
        assert self.btn_payment.exists(wait_time=10), "Button not found"
        assert self.tbl_result.exists(wait_time=15), "Inventory table can't be found"

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerFredToyota: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerFredToyota: Get vin')
    def get_vin(self):
        return self.txt_vin.get_attribute("textContent").split()[1]


class CoxToyotaStructure:
    def __init__(self):
        self.title = Button(CssSelector(".vehicle-item__title"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector(".ae-lang-en.ae-device-desktop.ae-launcher"))
class DealerCoxToyota(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, url="https://www.coxtoyota.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector(".js-browse-inventory-find"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(
            Xpath(".//div[contains(@class, 'vehicle-highlights__item')]/span[contains(@class, 'df-icon-hashtag')]/.."))

        self.tbl_result = Table(Xpath(".//button[contains(@class, 'cbo-button cbo-primary')]/../../../../../../.."),
                                CoxToyotaStructure)
        self.btn_prompt_close = Button(Xpath(".//a[contains(@class, 'nl-close')]"))

    def setup_page(self):
        sleep(1)
        # self.btn_prompt_close.click()
        # sleep(2)
        self.js.scroll_down()
        sleep(2)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerCoxToyota: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerCoxToyota: Get vin')
    def get_vin(self):
        return self.txt_vin.text


class VolkswagenNewPortRicheyStructure:
    def __init__(self):
        self.title = Button(CssSelector(".vehicle-item__title"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("body[data-alias='index'],body[data-alias='inventory'],body[data-alias='vehicle-details']"))
class DealerVolkswagenNewPortRichey(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, url="http://www.vwofnpr.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector(".js-browse-inventory-find"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//div[contains(text(), 'VIN')]/../../td[@class='value']"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../.."),
                                VolkswagenNewPortRicheyStructure)

    def setup_page(self):
        sleep(1)
        self.btn_search.js.click()
        sleep(2)
        self.js.scroll_down()
        sleep(2)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerVolkswagenNewPortRichey: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerVolkswagenNewPortRichey: Get vin')
    def get_vin(self):
        return self.txt_vin.text


class PhilHughesHondaStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2>span.title-bottom"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("div#whitewrap"))
class DealerPhilHughesHonda(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, url="https://www.philhugheshonda.com/new-vehicles/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector("button[data-action='inventorySearch']"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(CssSelector("span#vin"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../.."),
                                PhilHughesHondaStructure)

    def setup_page(self):
        sleep(2)
        self.js.scroll_down()
        self.update_user_agent()
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode, update_user_agent=True)

    @step_decorator('WEB - DealerGarberBuick: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerGarberBuick: Get vin')
    def get_vin(self):
        return self.txt_vin.text


class NissanOfLithiaSpringsStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2.vehicleTitle [data-loc]"))
        self.payment = Button(CssSelector('.hidden-xs .cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.hidden-xs .cbo-button.cbo-trade'))


@find_by(Xpath("//div[@class='snap-content']/.."))
class NissanOfLithiaSprings(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, url="https://www.nissanoflithiasprings.com/searchnew.aspx"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(Id("submit_button_basic_search"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(CssSelector("[ff_vin]"))
        self.btn_close_modal_window = Button(Id("nsCloseBtn"))
        self.tbl_result = Table(CssSelector("div[data-vehicle-information]"), NissanOfLithiaSpringsStructure)

    def setup_page(self):
        sleep(1)
        self.close_modal_window()
        self.btn_search.click()
        sleep(2)
        self.js.scroll_down()
        sleep(2)
        self.close_modal_window()
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    def close_modal_window(self):
        if self.btn_close_modal_window.exists(wait_time=2):
            self.btn_close_modal_window.js.click()

    @step_decorator('WEB - DealerKellyNissan: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.click()
        sleep(1)
        try:
            if vehicle.title.exists(2):
                vehicle.title.js.scroll_into_view()
                vehicle.title.js.click()
        except (TimeoutException, IndexError):
            pass
        sleep(1)
        try:
            if vehicle.title.exists(2):
                vehicle.title.js.scroll_into_view()
                vehicle.title.click()
        except (TimeoutException, IndexError):
            pass
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerKellyNissan: Get vin')
    def get_vin(self):
        return self.txt_vin.get_attribute('ff_vin')


class DavidBruceToyotaStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2>a[aria-label='More Details']"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("body.tt-18"))
class DealerDavidBruceToyota(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, url="http://www.davidbrucetoyota.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector(".adv_search"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//td[contains(text(), 'VIN')]/../td[@class='details-overview_data']"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../.."),
                                DavidBruceToyotaStructure)

    def setup_page(self):
        sleep(1)
        self.btn_search.click()
        sleep(2)
        self.js.scroll_down()
        sleep(15)
        assert self.tbl_result.exists(wait_time=30)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerDavidBruceToyota: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerDavidBruceToyota: Get vin')
    def get_vin(self):
        self.txt_vin.exists(wait_time=5)
        return self.txt_vin.text


class KenGarffBuickGMCStructure:
    def __init__(self):
        self.title = Button(Xpath(".//h2[@class='result-title']/span[@class='title-bottom']"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("div#whitewrap"))
class DealerKenGarffBuickGMC(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, url="https://www.kengarffgm.com/used-vehicles/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(CssSelector("span#vin"))

        self.tbl_result = Table(Xpath(".//button[contains(@class, 'cbo-button cbo-primary')]/../../../../.."),
                                KenGarffBuickGMCStructure)

    def setup_page(self):
        sleep(2)
        self.js.scroll_down()
        self.update_user_agent()
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode, update_user_agent=True)

    @step_decorator('WEB - DealerKenGarffBuickGMC: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerKenGarffBuickGMC: Get vin')
    def get_vin(self):
        return self.txt_vin.get_attribute("textContent")


class AllStarDodgeChryslerJeepRamStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2>a[aria-label='More Details']"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(Xpath("//a[@href='https://www.314allstar.com/']/../../../../../../../../../..//body"))
class DealerAllStarDodgeChryslerJeepRam(PageObject, ConsumerPageBehaviour):  # not stable

    def __init__(self, consumer_name=None, url="https://www.314allstar.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector(".adv_search"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//td[contains(text(), 'VIN')]/../td[@class='details-overview_data']"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../.."),
                                AllStarDodgeChryslerJeepRamStructure)

    def setup_page(self):
        sleep(15)
        self.btn_search.exists(5)
        self.btn_search.js.click()
        sleep(2)
        self.js.scroll_down()
        sleep(15)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerAllStarDodgeChryslerJeepRam: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerAllStarDodgeChryslerJeepRam: Get vin')
    def get_vin(self):
        self.txt_vin.exists(wait_time=5)
        return self.txt_vin.text


class GettelAcuraStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2>a.stat-text-link"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector(".snap-content"))
class DealerGettelAcura(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, url="http://www.gettelacura.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector("#submit_button_basic_search"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//li[contains(text(), 'VIN')]"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../../../../../.."),
                                GettelAcuraStructure)

    def setup_page(self):
        sleep(1)
        self.btn_search.click()
        sleep(2)
        self.js.scroll_down()
        sleep(2)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerGettelAcura: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerGettelAcura: Get vin')
    def get_vin(self):
        return self.txt_vin.get_attribute("textContent").split()[1]


class EdMorseAlfaRomeoStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2>a.stat-text-link"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector(".snap-content"))
class DealerEdMorseAlfaRomeo(PageObject, ConsumerPageBehaviour):

    def __init__(self, consumer_name=None, url="https://www.alfaromeousaofbrandon.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector("#submit_button_basic_search"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//li[@class='vinDisplay']/strong[contains(text(), 'VIN')]/../span"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../../../../../../../.."),
                                EdMorseAlfaRomeoStructure)

    def setup_page(self):
        sleep(1)
        self.btn_search.click()
        sleep(2)
        self.js.scroll_down()
        sleep(2)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerEdMorseAlfaRomeo: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerEdMorseAlfaRomeo: Get vin')
    def get_vin(self):
        return self.txt_vin.get_attribute("textContent")


class GunnNissanStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2>a.stat-text-link"))
        self.payment = Button(CssSelector('.cbo-button.btn.btn-success'))


@find_by(CssSelector("body.homepage,.snap-content"))
class DealerGunnNissan(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="http://gunnnissan.com/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector("#submit_button_basic_search"))
        self.btn_payment = Button(CssSelector('.cbo-button.btn.btn-success'))
        self.btn_close_pop_up = Button(CssSelector("#closeForm"))
        self.txt_vin = TextBlock(Xpath(".//li[contains(text(), 'VIN')]"))

        self.tbl_result = Table(
            Xpath(".//div[@class='row']//button[contains(@class,'cbo-button')]/../../../../../../.."),
            GunnNissanStructure)

    def setup_page(self):
        sleep(1)
        # if self.btn_close_pop_up.exists(1):
        #     self.btn_close_pop_up.click()
        self.btn_search.js.click()
        sleep(2)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerGunnNissan: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerGunnNissan: Get vin')
    def get_vin(self):
        return self.txt_vin.get_attribute("textContent").split()[1]


class RobertsToyotaStructure:
    def __init__(self):
        self.title = Button(CssSelector("h6.inventory-item_vehicle-title a"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("body.ae-lang-en.ae-device-desktop.ae-launcher"))
class DealerRobertsToyota(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="https://www.robertstoyota.com/new-toyota-columbia-tn#"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_search = Button(CssSelector("a.btn.btn-primary.js-browse-inventory-find"))
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.btn_close_pop_up = Button(CssSelector("#close-frame"))
        self.txt_vin = TextBlock(Xpath(".//div[contains(text(), 'VIN')]/../../td[@class='value']"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../.."),
                                RobertsToyotaStructure)

    def setup_page(self):
        sleep(1)
        self.js.scroll_down()
        sleep(5)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerRobertsToyota: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerRobertsToyota: Get vin')
    def get_vin(self):
        return self.txt_vin.text


# @find_by(CssSelector("body.ae-lang-en.ae-device-desktop.ae-launcher"))
# class DealerLEVC(PageObject, ConsumerPageBehaviour):
#
#     def __init__(self,
#                  consumer_name=None,
#                  url="https://www.levc.com/dealer-network/"):
#         ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None, mode=device_mode)
#         self.consumer_url = url
#         self.btn_cookie = Button(CssSelector('div#__ndcc_cookieImpliedConsent'))
#
#     def setup_page(self):
#         assert self.btn_chat_bubble.exists(wait_time=60)
#
#     def open_actions(self):
#         self.get(self.consumer_url, mode=self.mode)


class FindlayMazdaStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2>a"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("div#whitewrap"))
class DealerFindlayMazda(PageObject, ConsumerPageBehaviour):  # not stable

    def __init__(self, consumer_name=None, url="https://www.findlaymazda.com/new-vehicles/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//span[contains(text(), 'VIN')]/../span[@class='vinstock-number']"))

        self.tbl_result = Table(
            Xpath(".//tr[@class='hidden-xs']//button[contains(@class, 'cbo-button cbo-primary')]/../../../../../../.."),
            FindlayMazdaStructure)

    def setup_page(self):
        sleep(2)
        self.js.scroll_down()
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerSouthDadeToyota: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerSouthDadeToyota: Get vin')
    def get_vin(self):
        self.txt_vin.exists(wait_time=5)
        return self.txt_vin.text


class BrooklynChryslerStructure:
    def __init__(self):
        self.title = Button(CssSelector("h2.vehicle-card-title a"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("body.ae-lang-en.ae-device-desktop.ae-launcher"))
class DealerBrooklynChrysler(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="https://www.brooklynchrysler.com/new-inventory/index.htm"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(Xpath(".//div[@id='vehicle-title1-app-root']//li[contains(text(), 'VIN')]"))

        self.tbl_result = Table(Xpath(".//button[@class='cbo-button cbo-primary']/../../../../../.."),
                                BrooklynChryslerStructure)

    def setup_page(self):
        sleep(1)
        self.js.scroll_down()
        sleep(5)
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode)

    @step_decorator('WEB - DealerBrooklynChrysler: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerBrooklynChrysler: Get vin')
    def get_vin(self):
        return self.txt_vin.text.split()[1]


class StadiumToyotaStructure:
    def __init__(self):
        self.title = Button(CssSelector(".result-title"))
        self.payment = Button(CssSelector("button[data-flow='payments']"))
        self.trade_in = Button(CssSelector("button[data-flow='trade_in']"))


@find_by(CssSelector("body.ae-lang-en.ae-device-desktop.ae-launcher"))
class DealerStadiumToyota(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="https://www.stadiumtoyota.com/new-vehicles/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(CssSelector("#vin"))
        self.tbl_result = Table(CssSelector(".result-wrap.new-vehicle"), StadiumToyotaStructure)

    def setup_page(self):
        sleep(1)
        self.js.scroll_down()
        sleep(5)
        self.update_user_agent()
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode, update_user_agent=True)

    @step_decorator('WEB - DealerStadiumToyota: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerStadiumToyota: Get vin')
    def get_vin(self):
        return self.txt_vin.text


class DoralToyotaStructure:
    def __init__(self):
        self.title = Button(CssSelector("span.title-bottom"))
        self.payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.trade_in = Button(CssSelector('.cbo-button.cbo-trade'))


@find_by(CssSelector("body.ae-lang-en.ae-device-desktop.ae-launcher"))
class DealerDoralToyota(PageObject, ConsumerPageBehaviour):

    def __init__(self,
                 consumer_name=None,
                 url="https://www.doraltoyota.com/new-vehicles/"):
        ConsumerPageBehaviour.__init__(self, consumer_name=consumer_name, initial_message=None,
                                       mode=os.environ['DEVICE_MODE_NAME'])
        self.consumer_url = url
        self.btn_payment = Button(CssSelector('.cbo-button.cbo-primary'))
        self.btn_trade_in = Button(CssSelector('.cbo-button.cbo-trade'))
        self.txt_vin = TextBlock(CssSelector("span#vin"))

        self.tbl_result = Table(Xpath(".//div[@class='result-wrap new-vehicle stat-image-link ']"),
                                DoralToyotaStructure)

    def setup_page(self):
        sleep(1)
        self.js.scroll_down()
        sleep(5)
        self.update_user_agent()
        assert self.tbl_result.exists(wait_time=15)

    def open_actions(self):
        self.get(self.consumer_url, mode=self.mode, update_user_agent=True)

    @step_decorator('WEB - DealerDoralToyota: Open vehicle')
    def open_vehicle(self):
        vehicle = self.tbl_result.get_row_by_index(random.randint(0, self.tbl_result.size - 1))
        vehicle.title.js.click()
        sleep(1)
        self.exists(wait_time=10)
        sleep(2)
        assert self.txt_vin.exists(wait_time=5)

    @step_decorator('WEB - DealerDoralToyota: Get vin')
    def get_vin(self):
        return self.txt_vin.text
