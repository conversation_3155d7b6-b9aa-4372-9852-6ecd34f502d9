from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON>ss<PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_types import Button, Input, TextBlock, CheckBox
from kom_framework.src.web.support.page_factory import find_by

from src.utils.decorators import step_decorator


@find_by(CssSelector('#offerPopup'))
class SpotlightComponent(Component):
    def __init__(self, ancestor, offer_name=None):
        super().__init__(ancestor)
        self.offer_name = offer_name
        self.inp_name = Input(CssSelector("input[placeholder='Name']"))
        self.inp_email = Input(CssSelector("input[placeholder='Email']"))
        self.inp_phone = Input(CssSelector("input[placeholder='Phone']"))
        self.btn_ask_about_offer = Button(CssSelector("button[aria-label='Ask About Offer']"))
        self.chk_terms = CheckBox(Xpath(".//input[@id='publisherOfferTermsCheckbox']/../div[@aria-hidden='true']"), attribute='checked', checked_value='true')
        self.btn_get_offer = Button(CssSelector("button[aria-label='Get Offer Button']"))
        self.txt_hooray = TextBlock(Xpath(".//h1[contains(text(), 'Hooray!')]"))
        self.btn_close_hooray = Button(Xpath(".//button[@data-testid='offer-ui__closing-window-button']"))

    @step_decorator('WEB - SpotlightComponent: Open "Offer" Component')
    def open_actions(self, slide=True):
        self.ancestor.btn_spotlight_engagement.exists(5)
        if slide:
            self.ancestor.btn_spotlight_engagement.action_chains.drag_and_drop_by_offset(xoffset=1, yoffset=0).perform()
        else:
            self.ancestor.btn_see_offer.click()
        self.wait_for.visibility_of_element_located(3)

    @step_decorator('WEB - SpotlightComponent: ask about offer')
    def ask_about_offer(self):
        self.btn_ask_about_offer.click()

    @step_decorator('WEB - SpotlightComponent: open offer by offer name')
    def open_offer(self, offer_name):
        btn_open_offer = Button(CssSelector(f"button[aria-label='{offer_name}']"))
        btn_open_offer.ancestor = self
        btn_open_offer.js.scroll_into_view()
        btn_open_offer.js.click()

    @step_decorator('WEB - SpotlightComponent: Fill the offer with {1} name, {2} phone, {3} email')
    def fill_form(self, name, phone, email):
        self.inp_name.send_keys(name)
        self.inp_email.send_keys(email)
        self.inp_phone.send_keys(phone)
        self.chk_terms.check()

    @step_decorator('WEB - SpotlightComponent: get offer')
    def get_offer(self):
        assert self.btn_get_offer.wait_for.element_to_be_clickable(2), 'terms and condition not checked'
        self.btn_get_offer.click()
        assert self.txt_hooray.exists(2), 'confirmation message is not displayed'
        self.btn_close_hooray.click()
