from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON>path, CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON>ton, TextBlock, AnyType
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.consumer.desktop.vdp import MyVehiclesActiveTab


class VehicleCardStructure:
    def __init__(self):
        self.year = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__year"]'))
        self.btn_favorites = Button(CssSelector('[data-testid="showroom-vehicle-card__button--favorite"]'))
        self.vin_number = AnyType(Xpath(".//p[@data-testid='showroom-vehicle-card__year']/.."))
        self.txt_unlock_price = TextBlock(CssSelector('[data-testid="showroom-price__not-is-locked"]'))
        self.txt_locked_price = TextBlock(CssSelector('showroom-vehicle-card__price-locked'))
        self.txt_house_power = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__horsepower"]'))
        self.txt_cylinders = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__cylinders"]'))
        self.txt_drivetrain = TextBlock(CssSelector('[data-testid="showroom-vehicle-card__drivetrain"]'))
        self.btn_view_details = Button(CssSelector("[data-testid='showroom-vehicle-card__button--card']"))
        self.btn_buy_now = Button(CssSelector("[data-testid='showroom-vehicle-card__button--buy']"))

    @step_decorator("WEB - MyVehiclesContainer: remove from favorites")
    def remove_from_favorites(self):
        if eval(self.btn_favorites.get_attribute("aria-pressed").title()):
            self.btn_favorites.click()


@find_by(Xpath("//div[contains(@class,'MyVehicles__TabsContainer')]/../.."))
class MyVehiclesContainer(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_title = TextBlock(Xpath(".//h1[contains(text(), 'My Vehicles')]"))
        self.btn_back = Button(Xpath(".//button[@aria-label='Back to Showroom']"))
        self.tbl_vehicle_card = Table(CssSelector('[data-testid="showroom-vehicle-card"]'),
                                      VehicleCardStructure)
        self.btn_order = Button(Xpath(".//button[@data-testid='my-vehicles-tab-orders']"))
        self.btn_favorites = Button(Xpath(".//button[@data-testid='my-vehicles-tab-favorites']"))
        self.btn_viewed_vehicles = Button(Xpath(".//button[@data-testid='my-vehicles-tab-viewed-vehicles']"))
        self.txt_active_tab = TextBlock(Xpath(".//button[@data-active='true']"))
        self.btn_clear_all = Button(Xpath(".//button//p[contains(text(), 'Clear All')]"))
        self.btn_confirm_remove_all = Button(Xpath(".//button[contains(text(), 'Yes, Remove All')]"))
        self.txt_no_favorites = TextBlock(Xpath(".//p[contains(text(), 'Looks like')]"))

    def open_actions(self, vdp: bool = True):
        if vdp:
            if not self.exists():
                self.ancestor.consumer.vdp3.btn_saved_cars.click()
                assert self.exists(wait_time=5), "My Vehicles modal wasn't opened"
        else:
            if not self.exists():
                self.ancestor.btn_my_vehicles.js.scroll_into_view()
                self.ancestor.btn_my_vehicles.click()
                assert self.exists(wait_time=5), "My Vehicles modal wasn't opened"
        return self

    @step_decorator("WEB - MyVehiclesContainer: Switch to {1} tab")
    def switch_tab(self, tab_name: str):
        tab = Button(Xpath(f"//p[contains(.,'{tab_name}')]"))
        tab.ancestor = self
        tab.click()

    @step_decorator("WEB - MyVehiclesContainer: Get vehicle by {1} vin number")
    def get_vehicles_by_vin_number(self, vin_number: str):
        self.switch_tab("Favorites")
        return self.tbl_vehicle_card.get_row_by_attribute_value("vin_number", "data-testid", vin_number)

    @step_decorator("WEB - MyVehiclesContainer: remove from favorites by {1} vin number")
    def remove_from_favorites_by_vin(self, vin_number: str):
        self.get_vehicles_by_vin_number(vin_number).remove_from_favorites()

    @step_decorator("WEB - MyVehiclesContainer: Open My vehicles from showroom hamburger menu")
    def open_from_showroom_hamburger(self):
        self.ancestor.hamburger_menu.btn_my_vehicles.click()
        assert self.exists(wait_time=3), "My vehicle container wasn't opened"

    @step_decorator("WEB - MyVehiclesContainer: Click Back Button")
    def click_back_button(self):
        self.btn_back.click()
        self.ancestor.exists(wait_time=3), 'Showroom is not shown'

    @step_decorator("WEB - MyVehiclesContainer: Click Back Browser Button")
    def click_browser_back_button(self):
        self.ancestor.back()
        self.ancestor.exists(wait_time=3), 'Showroom is not shown'

    @step_decorator("WEB - MyVehiclesContainer: Get active tab button text")
    def get_active_tab_button(self):
        return self.txt_active_tab.text

    @step_decorator("WEB - MyVehiclesContainer: Get all favorites vehicles")
    def get_favorites_vehicles(self):
        assert self.get_active_tab_button() == MyVehiclesActiveTab.FAVORITES, "Incorrect tab is opened"
        return self.tbl_vehicle_card.get_column_attribute(column_name="vin_number", attribute='data-testid')

    @step_decorator("WEB - MyVehiclesContainer: Get all favorites vehicles")
    def clear_all_favorites_vehicles(self):
        self.btn_clear_all.click()
        self.btn_confirm_remove_all.wait_for.presence_of_element_located(wait_time=2)
        self.btn_confirm_remove_all.click()
        assert self.txt_no_favorites.exists(wait_time=3), "Favorite vehicles weren't removed"

