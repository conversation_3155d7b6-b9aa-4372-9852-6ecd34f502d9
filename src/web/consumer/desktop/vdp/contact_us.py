from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Id, Xpath
from kom_framework.src.web.data_types.element_types import Input, Button
from kom_framework.src.web.support.page_factory import find_by
from src.web.entities.lead_entity import WebLeadEntity


@find_by(Xpath(".//h1[text()='Contact Us']/../../.."))
class ContactUs(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_full_name = Input(Id("VDP-CONTACT-FORM-full_name"))
        self.inp_phone = Input(Id("VDP-CONTACT-FORM-phone"))
        self.inp_email = Input(Id("VDP-CONTACT-FORM-email"))
        self.inp_message = Input(Id("VDP-CONTACT-FORM-message"))
        self.btn_submit = Button(Xpath(".//button[text()='Submit']"))
        self.btn_ok = Button(Xpath("//button[text()='OK']"))

    def open_actions(self):
        self.ancestor.btn_contact_us.click()
        self.wait_for.presence_of_element_located(wait_time=2)

    def fill_form(self, lead_info: WebLeadEntity):
        self.inp_full_name.send_keys(f"{lead_info.first_name} {lead_info.last_name}")
        self.inp_phone.send_keys(lead_info.phone)
        self.inp_email.send_keys(lead_info.email)
        self.inp_message.send_keys(lead_info.notes)
        self.btn_submit.click()
        self.btn_ok.click(wait_time=2)
