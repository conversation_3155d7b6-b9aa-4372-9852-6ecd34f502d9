from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath, Id
from kom_framework.src.web.data_types.element_types import Input, Button
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.entities.lead_entity import WebLeadEntity


@find_by(Xpath("//h2[text()='Unlock our best prices']/.."))
class UnlockBestPricePII(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_name = Input(Id("pii-name"))
        self.inp_phone = Input(Id("pii-phone"))
        self.inp_email = Input(Id("pii-email"))
        self.inp_zip = Input(Id("pii-zip"))
        self.btn_get_best_price = Button(Xpath(".//button[text()='Get Best Price']"))

    def open_actions(self):
        self.ancestor.btn_unlock_price.click()

    @step_decorator("WEB - UnlockBestPricePII: get best price")
    def get_best_price(self, lead_info: WebLeadEntity):
        self.inp_name.send_keys(f"{lead_info.first_name} {lead_info.last_name}")
        self.inp_phone.send_keys(lead_info.phone)
        self.inp_email.send_keys(lead_info.email)
        self.inp_zip.send_keys(lead_info.zip)
        self.btn_get_best_price.click()
        self.wait_for.presence_of_element_located(wait_time=5, until=False)
