from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_list_types import AnyList
from kom_framework.src.web.data_types.element_types import Image, Button
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.converter import Converter
from src.utils.decorators import step_decorator
from src.web.consumer import VDPVersion, get_vdp_version


@find_by(Xpath(".//div[contains(@class,'VehicleImages')]/div[@data-testid='carousel-container']"))
class Images(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.img_image = Image(CssSelector("[aria-hidden='false'] img"))
        self.btn_next = Button(CssSelector("[aria-label='View Next']"))
        self.btn_previous = Button(CssSelector("[aria-label='View Previous']"))
        self.anl_bubbles = AnyList(CssSelector("[data-type='bubble']"))
        self.tbl_bubbles = Table(CssSelector("[data-type='bubble']"), Bubbles)

    def open_actions(self):
        pass

    @step_decorator("WEB - Images: check bubbles")
    def check_bubbles(self):
        result = []
        idx = 1
        last_image_idx = self.tbl_bubbles.get_content()[len(self.tbl_bubbles.get_content()) - 1].get_button_index()
        bubble = self.get_active_bubble()
        while idx <= last_image_idx:
            if idx != 1:
                self.btn_next.click()
                from time import sleep
                sleep(0.5)
                bubble = self.get_active_bubble()
                if not self.btn_previous.is_displayed():
                    result.append(f"previous button doesn't displayed for the f{idx} image")
            else:
                if self.btn_previous.is_displayed():
                    result.append(f"previous button displayed on the first image")
            if f"View image {idx}" != bubble.get_aria_label():
                result.append(f"{bubble.get_aria_label()} is not active")
            if idx == last_image_idx:
                if self.btn_next.is_displayed():
                    result.append(f"next button displayed on the last image")
            idx += 1
        return result

    @step_decorator("WEB - Images: active bubble")
    def get_active_bubble(self):
        return self.tbl_bubbles.get_row_by_attribute_value("btn_bubble", "data-active", "true")

    @step_decorator("WEB - Images: get current image index")
    def get_current_image_index(self):
        return self.get_active_bubble().get_aria_label().split(" ")[2]


class Bubbles:
    def __init__(self):
        self.btn_bubble = Button(Xpath("."))

    def get_aria_label(self):
        return self.btn_bubble.get_attribute("aria-label")

    def get_button_index(self):
        return int(Converter.extract_digits(self.get_aria_label()))


@find_by(CssSelector("[aria-label='View all slides full screen']"))
class SlidesFullScreen(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_close_full_screen = Button(CssSelector("[aria-label='Close full screen view']"))

    def open_actions(self):
        if get_vdp_version() == VDPVersion.V4:
            self.ancestor.images.img_image.click()
        else:
            self.ancestor.btn_full_screen.click()

    @step_decorator("WEB - SlidesFullScreen: check autoscroll to the current image")
    def check_autoscroll_to_current_image(self, image_index):
        image = Image(CssSelector(f"[data-testid='carousel-image-{int(image_index) - 1}']"))
        image.ancestor = self.ancestor
        return image.is_displayed()
