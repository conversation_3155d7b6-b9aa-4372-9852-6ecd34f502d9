import re
import time

import allure
from allure import step
from allure_commons.types import LinkType

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Css<PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.consumer import message_to_start_chat
from src.web.consumer.ai_behaviour import AIBehaviour
from src.web.consumer.desktop.cbo.cbo_page import CBOPage
from src.web.entities.trade_in_entity import TradeInProvider
from src.web.entities.lead_entity import WebLeadEntity


class ConsumerPageBehaviour(AIBehaviour):

    def __init__(self, consumer_name=None, initial_message=None, mode=None):
        super().__init__(self)
        self.consumer_name = consumer_name
        self.initial_message = f"Hello, my name is {self.consumer_name}. {message_to_start_chat}" \
            if not initial_message else initial_message
        self.mode = mode
        self.chat_id = None

        self.specials_offer = SpecialsOfferComponent(self)
        self.dynamic_engagement = DynamicEngagementComponent(self)
        self.chat_engagement = ChatEngagementComponent(self)
        self.txt_unread = TextBlock(
            Xpath("//div[contains(@class,'Badge')]/span"))
        self.btn_xtime = Button(CssSelector('button[data-testid="xtime_service_scheduling"]>a'))
        self.btn_gm = Button(CssSelector('button[data-testid="gm_service_scheduling"]>a'))
        self.btn_autoloop = Button(CssSelector('button[data-testid="autoloop_service_scheduling"]>a'))
        self.btn_service_portal = Button(CssSelector('button[data-testid="service_portal_service_scheduling"]>a'))
        self.btn_live_play = Button(CssSelector('button[data-testid="non_ai_lp"]>a'))
        self.btn_live_play_model = Button(CssSelector('button[data-testid="non_ai_model_lp"]>a'))
        self.btn_live_play_test_drive_with_vin = Button(CssSelector('button[data-testid="test_drive_lp_with_vin"]>a'))
        self.btn_live_play_test_drive_no_vin = Button(CssSelector('button[data-testid="test_drive_lp_without_vin"]>a'))
        self.btn_live_play_test_drive_parent_vin = Button(
            CssSelector('button[data-testid="test_drive_lp_with_parent_vin"]>a'))
        self.btn_live_play_xtime_appointment = Button(CssSelector('button[data-testid="appointment_scheduling_lp"]>a'))
        self.spn_wait = Spinner(Xpath(".//*[@class='gg-loading']"))
        self.iframe_new_vdp = IFrame(Xpath('.//iframe[@title="Vehicle Details Page"]'))
        self.btn_garage = Button(CssSelector("div.cbo-garage"))
        self.spn_preloader = Spinner(CssSelector(".preloader"))  # TODO: verify if it is used
        self.spn_showroom = Spinner(CssSelector(".cbo-spinner--showroom"))
        self.spn_cbo = Spinner(Xpath(".//*[@class='cbo-spinner']"))
        self.btn_direct_payment = Button(Xpath(".//span[@class='cbo-button' and @data-flow='payments']"))
        self.btn_direct_trade_in = Button(Xpath(".//span[@class='cbo-button' and @data-flow='trade_in']"))

    @step_decorator('WEB - DesktopCBOConsumerPage: Get Operator Avatar src')
    def get_operator_avatar_src(self):
        return self.chat_component.img_operator_avatar.get_attribute('src')

    @step_decorator('WEB - DesktopCBOConsumerPage: Open Direct Payment')
    def direct_payment(self):
        self.btn_direct_payment.click()
        self.spn_cbo.wait_for_appear_and_disappear()
        self.iframe_new_vdp.switch_to()
        self.cbo.set_session_key(self.get_session_key())
        assert self.cbo.exists(5)
        assert self.cbo.start_blur.exists(5)
        return self.cbo

    @step_decorator('WEB - DesktopCBOConsumerPage: Open Direct Trade in')
    def direct_trade_in(self):
        self.btn_direct_trade_in.click()
        self.spn_cbo.wait_for_appear_and_disappear()
        self.iframe_new_vdp.switch_to()
        self.cbo.set_session_key(self.get_session_key())
        assert self.cbo.exists(5)
        assert self.cbo.trade_in.exists(5)
        return self.cbo

    @step_decorator('WEB - DesktopCBOConsumerPage: Close Greeter')
    def close_greeter(self):
        self.switch_to.default_content()
        if self.chat_greeter.exists():
            self.chat_greeter.close()
        self.iframe_new_vdp.switch_to()

    @step_decorator('WEB - DesktopCBOConsumerPage: Get Greeter message')
    def get_greeter_message(self):
        self.switch_to.default_content()
        message = self.chat_greeter.txt_invite.text
        self.iframe_new_vdp.switch_to()
        return message

    @step_decorator('WEB - DesktopCBOConsumerPage: Get Notification message')
    def get_notification_message(self):
        self.switch_to.default_content()
        message = self.notification_component.txt_notification_msg.text
        self.iframe_new_vdp.switch_to()
        return message

    @step_decorator('WEB - DesktopCBOConsumerPage: Open CBO')
    def open_cbo(self, vin, pii_entity=None, cbo_cta_button=None, trade_in_provider: TradeInProvider = None) -> CBOPage:
        self.cbo.vin = vin
        self.cbo.pii_entity = pii_entity
        self.cbo.open_how = cbo_cta_button
        self.cbo.trade_in_provider = trade_in_provider
        self.cbo.open()
        return self.cbo

    @step_decorator("WEB - ConsumerPage: Get Chat id")
    def get_chat_id(self, wait_time=3):
        end_time = time.time() + wait_time
        while (self.chat_id is None or self.chat_id == '1') and time.time() < end_time:
            time.sleep(0.5)
            self.chat_id = self.driver.execute_script(self.js_session_id)
            if self.chat_id and isinstance(self.chat_id, int):
                self.chat_id = str(self.chat_id)
                if self.chat_id != '1':
                    allure.dynamic.link(f"https://desktop-rc.resq.rocks/history/{self.chat_id}",
                                        link_type=LinkType.LINK,
                                        name=f"Chat {self.chat_id} in History")
        # with step(f"WEB - ConsumerPage: Chat id is {self.chat_id}"):
        #     Log.info(f"WEB - ConsumerPage: Chat id is {self.chat_id}")
        return self.chat_id

    @step_decorator("WEB - ConsumerPage: Get updated Chat id")
    def get_new_chat_id(self):
        self.chat_id = str(self.driver.execute_script(self.js_session_id))
        if self.chat_id and self.chat_id != '1':
            allure.dynamic.link(f"https://desktop-rc.resq.rocks/history/{self.chat_id}", link_type=LinkType.LINK,
                                name=f"Chat {self.chat_id} in History")
            with step(f"WEB - ConsumerPage: Chat id is {self.chat_id}"):
                Log.info(f"WEB - ConsumerPage: Chat id is {self.chat_id}")
        return self.chat_id

    @step_decorator("WEB - ConsumerPage: Get Chat status")
    def get_chat_status(self):
        status = str(self.driver.execute_script(self.js_session_status))
        Log.info(f"WEB - ConsumerPage: Chat status is {status}")
        return status

    @step_decorator("WEB - ConsumerPage: Get Chat forward status")
    def get_chat_forward_status(self):
        status = str(self.driver.execute_script(self.js_session_forward_status))
        Log.info(f"WEB - ConsumerPage: Chat forward status is {status}")
        return status

    @step_decorator('WEB - ConsumerPage: Start chat')
    def start_chat(self, message=None):
        if message:
            self.initial_message = message
            self.chat_component.initial_message = message
        self.chat_component.open()
        return self.get_chat_id()

    @step_decorator('WEB - ConsumerPage: Collapse Chat Component')
    def collapse_chat(self):
        if self.chat_component.exists(2):
            time.sleep(1)
            self.chat_component.close()

    @step_decorator('WEB - ConsumerPage: Start chat from bubble')
    def start_chat_from_bubble(self, message=None):
        if message:
            self.initial_message = message
        self.switch_to.default_content()
        self.chat_component.open_from_bubble(self.initial_message)
        return self.get_chat_id()

    @step_decorator('WEB - ConsumerPage: Send a {1} message')
    def send_message(self, message):
        self.chat_component.open()
        self.chat_component.send_message(message)

    @step_decorator('WEB - ConsumerPage: Send a {1} message')
    def send_message_quick(self, message):
        self.chat_component.open()
        self.chat_component.send_message_quick(message)

    @step_decorator("WEB - ConsumerPage: View {1} offer")
    def view_offer(self, name):
        self.offer.offer_name = name
        return self.offer.open()

    @step_decorator("WEB - ConsumerPage: Ask about {1} offer")
    def ask_about_offer(self, name):
        offer = self.view_offer(name)
        offer.btn_ask.click()
        assert self.chat_component.exists(2), "Chat was not displayed"

    @step_decorator("WEB - ConsumerPage: Get {1} offer now")
    def get_offer_now(self, offer_name, first_name, last_name, phone, email):
        offer = self.view_offer(offer_name)
        offer.fill_offer(first_name, last_name, phone, email)
        offer.submit()

    @step_decorator("WEB - ConsumerPage: Get announcement button label")
    def get_announcement_button_label(self):
        if self.btn_announcement.exists(2):
            return self.btn_announcement.text

    @step_decorator("WEB - ConsumerPage: Close announcement button")
    def close_announcement_button(self):
        self.btn_announcement_close.click()

    @step_decorator("WEB - ConsumerPage: look up phone in publisher")
    def check_lookup_by_phone(self, lead_info: WebLeadEntity = None):
        self.service_portal.vehicle.inp_phone.send_keys(lead_info.phone)
        self.service_portal.btn_next.click()
        self.spn_wait.wait_for_appear_and_disappear()

        assert self.service_portal.vehicle.ddl_year.get_selected_option() == lead_info.vehicle_year, 'year not selected or incorrect'
        assert self.service_portal.vehicle.ddl_make.get_selected_option() == lead_info.vehicle_make, 'make not selected or incorrect'
        assert self.service_portal.vehicle.ddl_model.get_selected_option() == lead_info.vehicle_model, 'model not selected or incorrect'

        self.service_portal.btn_next.click()
        self.spn_wait.wait_for_appear_and_disappear()
        self.service_portal.services.select_services(services=lead_info.appointment_services)
        self.service_portal.date.fill(transport=lead_info.transport_type)

        assert self.service_portal.contact.inp_first_name.get_content() == lead_info.first_name
        assert self.service_portal.contact.inp_last_name.get_content() == re.sub(r'^(.)(.*)$',
                                                                  lambda m: m.group(1) + '*' * len(m.group(2)),
                                                                  lead_info.last_name)
        assert self.service_portal.contact.inp_phone.get_content() == re.sub(r'^(\d{3})(\d{3})(\d{4})$', r'(***) ***-\3',
                                                              lead_info.phone)
        assert self.service_portal.contact.inp_email.get_content() == re.sub(r'^(.)(.*)(.@.+)$',
                                                              lambda m: m.group(1) + '*' * len(m.group(2)) + m.group(3),
                                                              lead_info.email)


class SpecialsStructure:
    def __init__(self):
        self.offer_title = TextBlock(CssSelector('div.gg-da-item__title'))
        self.offer_get = Button(CssSelector('div.gg-da-item__btn'))


@find_by(CssSelector(".gg-da-offers"))
class SpecialsOfferComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.tbl_specials_content = Table(CssSelector("div.gg-da-item"), SpecialsStructure)

    @step_decorator('WEB - SpecialsOfferComponent: Open "Offer from Specials" Component')
    def open_actions(self):
        self.ancestor.btn_special.click()

    @step_decorator("WEB - SpecialsOfferComponent: Get Specials content")
    def get_offer(self, offer_name=None):
        self.tbl_specials_content.get_row_by_column_value("offer_title", offer_name, wait_time=5).offer_get.click()


@find_by(Xpath(".//div[contains(@class,'DynamicButtonContainer__StyledDynamicButtonContainer')]"))
class DynamicEngagementComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_icon_default = Button(CssSelector(".gg-chat-icon__bubble"))
        self.btn_icon_smile = Button(CssSelector(".gg-chat-icon--smile"))
        self.btn_icon_heart = Button(CssSelector(".gg-heart-icon-wrapper"))
        self.txt_label = TextBlock(CssSelector('div.gg-dynamic-chat-button__label-container'))
        self.btn_start_chat = Button(Xpath(".//div/button[@aria-label='Start Chat']"))
        self.btn_close = Button(CssSelector("#gg-collapse-chat-button"))
        self.btn_text_us = Button(Xpath(".//div/button[@aria-label='Text Us']"))
        self.btn_qr1 = Button(Xpath(".//Button[@data-testid='dynamic-engagement-button__qr1']"))
        self.btn_qr2 = Button(Xpath(".//Button[@data-testid='dynamic-engagement-button__qr2']"))

    @step_decorator("WEB - DynamicEngagementComponent: Get dynamic engagement label")
    def get_label(self):
        return self.txt_label.text

    @step_decorator("WEB - DynamicEngagementComponent: Open Dynamic engagement")
    def open_actions(self):
        pass

    @step_decorator("WEB - DynamicEngagementComponent: Expand dynamic button")
    def expand_button(self, auto_expand=True):
        if not auto_expand:
            self.ancestor.btn_dynamic_engagement.click(wait_time=3)
        self.txt_label.wait_for.presence_of_element_located(wait_time=10)

    @step_decorator("WEB - DynamicEngagementComponent: close dynamic engagement")
    def close_engagement(self):
        self.btn_close.click()

    @step_decorator("WEB - DynamicEngagementComponent: Open qr1")
    def open_qr1(self):
        self.btn_qr1.click()

    @step_decorator("WEB - DynamicEngagementComponent: Open qr2")
    def open_qr2(self):
        self.btn_qr2.click()


@find_by(Xpath(".//div[contains(@class, 'ChatBubbleContainer__ChatBubbleContainerWrapper')]"))
class ChatEngagementComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_icon_default = Button(CssSelector(".gg-chat-icon__bubble"))
        self.btn_icon_smile = Button(CssSelector(".gg-chat-icon--smile"))
        self.btn_icon_heart = Button(CssSelector(".gg-heart-icon-wrapper"))
        self.txt_label = TextBlock(Xpath(".//button[@data-testid='chat-icon-engagement-button']/p[@class='gg-chat-button-text']"))
        self.btn_close = Button(Xpath(".//div[contains(@class,'CloseIcon__CloseIconWrapper') and @aria-label='Close']"))
        self.btn_chat = Button(Xpath(".//button[@data-testid='chat-icon-engagement-button']"))

    @step_decorator("WEB - ChatEngagementComponent: Get chat engagement label")
    def get_label(self):
        return self.txt_label.text

    @step_decorator("WEB - ChatEngagementComponent: Open Chat engagement")
    def open_actions(self):
        pass

    @step_decorator("WEB - ChatEngagementComponent: close chat engagement")
    def close_engagement(self):
        self.btn_close.js.click()

    @step_decorator("WEB - ChatEngagementComponent: Open chat")
    def open_chat_engagement(self):
        self.btn_chat.click()
