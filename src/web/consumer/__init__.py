import os
from datetime import datetime
import re

from src.web.entities.message_entity import MessageFactory, MessageOwner


class PaymentOptionType:
    LEASE = "Lease"
    FINANCE = "Finance"
    CASH = "Cash"
    
class DynamicButtonType:
    DYNAMIC = 'dynamic'
    CLASSIC = 'classic'


class DynamicButtonIcon:
    DEFAULT = 'default'
    SMILE = 'smile'
    HEART = 'heart'
    NONE = 'none'


class DynamicButtonAnimation:
    AUTO_EXPAND_COLLAPSE = 'auto_expand_and_collapse'
    AUTO_EXPAND = 'auto_expand'
    ONLY_ON_PRESS = 'only_on_press'


class InventoryType:
    ALL = 'all'
    NEW = 'new'
    USED = 'used'


class SmsRequiredFields:
    NAME_MESSAGE_PHONE = "name, message, phone"
    PHONE_MESSAGE = "phone, message"
    NAME_PHONE = "name, phone"
    PHONE = "phone"


class HolidayTheme:
    INDEPENDENCE = "independence_day"
    CANADA_DAY = "canada_day"
    HALLOWEEN = "halloween"
    THANKSGIVING = "thanksgiving"
    CHRISTMAS = "xmas"
    NEW_YEAR = "nye"
    ST_PATRICKS_DAY = "st_patricks_day"
    ST_PATRICKS_ALT = "Four Leaf Clover"


class KBBConditionType:
    FAIR = "Fair"
    GOOD = "Good"
    VERY_GOOD = "Very Good"
    EXCELLENT = "Excellent"


class TradeInCondition:
    TRADE_IN_CONDITION_EXPLAINED = "Trade-In Conditions Explained\nExcellent: Looks new and is in excellent mechanical condition.\nVery Good: Has minor cosmetic defects and is free of major mechanical problems.\nGood: Has some repairable cosmetic defects and is free of major mechanical problems.\nFair: Has some cosmetic defects that require repairing and/or replacing."


class TPConditionType:
    FAIR = "Tad Rough"
    GOOD = "Normal Wear"
    VERY_GOOD = "Like New"
    EXCELLENT = "???"


TP_TO_KBB = {TPConditionType.FAIR: KBBConditionType.FAIR,
             TPConditionType.GOOD: KBBConditionType.GOOD,
             TPConditionType.VERY_GOOD: KBBConditionType.EXCELLENT,
             TPConditionType.EXCELLENT: KBBConditionType.EXCELLENT}


def condition_type_to_kbb(condition):
    if condition in TP_TO_KBB.keys():
        return TP_TO_KBB[condition]
    return condition


def timestamp_format(sec):
    return {0: "a few seconds ago",
            1: "1 min ago",
            2: "2 mins ago"}[int(sec // 60)]


class OwnershipType:
    OWNED = "No, I have the title"
    FINANCED = "Yes, it has a loan"
    LEASE = "Due to your trade being a lease, one of our certified lease agents will contact you directly. Please continue completing your online purchase."


class ServiceProviderType:
    XTIME = "XTime"
    GM = "GM"
    AUTOLOOP = "Autoloop"
    SERVICEPORTAL = "ServicePortal"


class EPriceDecisionQRType:
    EXPLORE_PAYMENT = "Explore Payments"
    BOOK_TEST_DRIVE = "Book Test Drive"
    CALL_ME = "Call Me"


class TransportType:
    DROPOFF = "Dropoff"
    LOANER = "Loaner"
    SHUTTLE = "Shuttle"
    RENTAL = "Rental"
    WAITER = "Waiter"
    VALET = "Valet"


class ServicesType:
    DIAGNOSIS = "Engine Performance Diagnosis (Running Rough, Other)"
    REPLACE_TIRE = "Replace 1 Tire"
    REPLACE_WIPER_BLADES = "Replace Wiper Blades"
    TIRE_ROTATION = "Tire Rotation"
    TIRE_INSTALLATION = "Tire Installation Op Code"
    BRAKE_SYSTEM_DIAGNOSIS = "Brake System Diagnosis"
    ROTATE_TIRES = "Rotate and Balance"  # "Rotate tires" #Tire Balance and Rotate
    TELL_US_MORE = "Tell Us More"
    TIRE_REPAIR = "Tire Repair (Low Pressure Light)"
    TIRE_ROTATE_AND_BALANCE = "Tire Rotate and Balance"
    OIL_CHANGE = "Oil Change"
    GM_OIL_CHANGE = "!!ADDTLSERVICES_OIL_CHANGE"
    CYL_TUP_PLAT_TRK = "4 CYL TUP PLAT TRK"
    COOLANT_SERVICE = "COOLANT SERVICE"
    ENGINE_FLUSH = "ENGINE FLUSH"
    GENERAL_MAINTENANCE = "GENERAL MAINTENANCE"


default_phone = "5103442660"
default_zip = "10001"
ai_wait_time = 30
message_to_start_chat = "I want to talk to your manager Simon"


class VDPVersion:
    V3 = "v3"
    V4 = "v4"


def get_vdp_version():
    return VDPVersion.V4 if os.environ.get('VDP_VERSION') == VDPVersion.V4 else VDPVersion.V3


class AIBotResponse:
    VEHICLES_TOO_MANY_VEHICLES = MessageFactory.text("Are there any colors or features you are looking for?")
    VEHICLES_VEHICLE_MANY_FOUND_MORE = MessageFactory.text("of these in stock, do you have a price range in mind?")
    VEHICLES_MULTIPLE_VEHICLES_FOUND = MessageFactory.text('Sure thing! Here you go:')
    VEHICLES_NO_VEHICLE_FOUND = MessageFactory.text(
        "I’m not seeing that model listed online, but let me have my team confirm availability for you.")
    VEHICLES_SINGLE_VEHICLE_MISSING = MessageFactory.text("Let me have my team confirm available inventory for you.")
    VEHICLES_MULTIPLE_VEHICLES_AVAIL = MessageFactory.text("We have several of these vehicles available:")
    VEHICLES_VEHICLE_WHICH_ONE = MessageFactory.text("Which of the above vehicles are you most interested in?")
    VEHICLES_VEHICLE_FOLLOWUP_MANY = MessageFactory.text(
        'Would you like our sales team to follow up with you about these vehicles?')
    VEHICLES_VEHICLE_FOLLOWUP = MessageFactory.text(
        'Would you like our sales team to follow up with you on this vehicle?')
    VEHICLES_YOUR_VEHICLE_INFO_REQUEST = MessageFactory.text("What's your vehicle's year, make, and model?")
    VEHICLES_MANY_VEHICLES_PUSH = MessageFactory.text('Here are a few:')
    VEHICLES_SINGLE_MODEL_AVAIL = MessageFactory.text('Yes, we have one left!')
    VEHICLES_SINGLE_VEHICLE_MATCH = MessageFactory.text("That's a great choice!")
    VEHICLES_VEHICLE_LICENSE_PLATE_REQUEST = MessageFactory.text('What\'s the license plate of your vehicle?')
    VEHICLES_VEHICLE_LICENSE_PLATE_INVALID = MessageFactory.text(
        'That doesn\'t look like a valid license plate. Please enter a valid plate value.')
    VEHICLE_LICENSE_PLATE_FALLBACK = MessageFactory.text('Ok, let\'s get your trade-in vehicle another way.')
    VEHICLE_VEHICLE_STATE_REGISTERED_REQUEST = MessageFactory.text('What state is your vehicle registered in?')
    VEHICLE_VEHICLE_STATE_CONFIRMATION = MessageFactory.text('Is your vehicle registered in the state of')
    VEHICLE_VEHICLE_LICENSE_PLATE_FAILURE = MessageFactory.text(
        'Hmm, I can\'t seem to lookup a vehicle with the information you provided.')
    VEHICLE_VEHICLE_LICENSE_PLATE_TRY_AGAIN = MessageFactory.text(
        'Please ensure you have entered the correct license plate and state. Would you like to try again?')

    VEHICLES_VEHICLE_CONFIRM = MessageFactory.text('Just to confirm, is this the vehicle you are looking for?')
    VEHICLES_MODEL_OR_VIN_REQUEST = MessageFactory.text("Do you have a specific model in mind?")
    VEHICLES_MODEL_OR_VIN_REPEATED = MessageFactory.text(
        "I can also look up by VIN or stock number if you have one handy.")
    VEHICLES_SINGLE_VEHICLE_AVAIL = MessageFactory.text('Yes, it is available!')
    VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST = MessageFactory.text(
        "Is there a specific vehicle you’re looking to purchase?")
    VEHICLES_TRADE_IN_PII_TRANSITION_KBB = MessageFactory.text(
        "Ok! Let me get a few additional pieces of information and I’ll get the Kelley Blue Book® Trade-in Range for you.")
    VEHICLES_TRADE_IN_PII_TRANSITION = MessageFactory.text(
        "Ok! Let me get a few additional pieces of information and I’ll get the trade-in value for you.")
    VEHICLES_VEHICLE_MATCH_FINANCE_LEASE = MessageFactory.text(
        'Great choice! Are you looking to finance or lease the vehicle?')
    VEHICLES_ZIP_REQUEST = MessageFactory.text('To check incentives and specials near you, can I get your zip code?')
    VEHICLES_ZIP_CODE_REQUEST = MessageFactory.text('Can you please provide your ZIP code?')
    VEHICLES_ZIP_TOO_FAR = MessageFactory.text("It looks like you're far away, so we will have a service associate follow up with you to help you with your request.")
    VEHICLES_ZIP_WRONG = MessageFactory.text("Hmm, the ZIP code you provided doesn't seem right. Please enter a valid ZIP code.")
    VEHICLES_VEHICLE_LOOKUP_BY_PII = MessageFactory.text(
        "If you're an existing customer, I can look up your record using your email or phone number. Could you please provide at least one?")
    VEHICLES_CUSTOMER_VEHICLES_CHOOSE_TEXT = MessageFactory.text("Please select the vehicle you're inquiring about.")

    RECALL_RECALL_NUMBER = MessageFactory.text("Do you have a recall number?")
    RECALL_VIN_NUMBER = MessageFactory.text("Do you have a VIN of the vehicle?")
    RECALL_FOLLOWUP = MessageFactory.text("I will have my service team reach out about your recall")

    @staticmethod
    def VEHICLES_CUSTOMER_VEHICLES_FOUND_TEXT(vehicles_found, source_input):
        return MessageFactory.text(f"I found {vehicles_found} vehicles listed for your {source_input}.")

    @staticmethod
    def VEHICLES_PROVIDE_VR_LINK_PREAMBLE(lease_or_finance, vehicle):
        return MessageFactory.text(
            f'Your monthly payment is ready! Click below to view your {lease_or_finance} payment options for the {vehicle}')

    @staticmethod
    def VEHICLES_VEHICLE_CONDITION_REQUEST(year, model):
        return MessageFactory.text(f"What's the condition of your {year} {model}?")

    @staticmethod
    def VEHICLES_PROVIDE_VR_LINK(name):
        return MessageFactory.text(
            f'Thanks for providing that information, {name}. Click here to view your payment options:')

    @staticmethod
    def VEHICLES_VEHICLE_MILEAGE_REQUEST(year, model):
        return MessageFactory.text(f"What's the mileage of your {year} {model}?")

    @staticmethod
    def VEHICLES_VEHICLE_TRIM_REQUEST(year, model):
        return MessageFactory.text(f"What's the trim of your {year} {model}?")

    @staticmethod
    def VEHICLES_VEHICLE_DRIVETRAIN_REQUEST(year, model):
        return MessageFactory.text(f"What's the drivetrain of your {year} {model}?")

    @staticmethod
    def VEHICLES_VEHICLE_BODY_REQUEST(year, model):
        return MessageFactory.text(f"What's the body type of your {year} {model}?")

    @staticmethod
    def VEHICLES_VEHICLE_CLARIFY_YOUR_MODEL_Q(year, model):
        return MessageFactory.text(f"Can you clarify the specific {model} you have?")

    @staticmethod
    def VEHICLES_VEHICLE_ENGINE_REQUEST(year, model):
        return MessageFactory.text(f"What's the engine of your {year} {model}?")

    @staticmethod
    def VEHICLES_CARFAX_ONE_OWNER(vehicle=None):
        return MessageFactory.text(f'According to Carfax this {vehicle} had just 1 owner!')

    @staticmethod
    def VEHICLES_FULL_CARFAX(found):
        return MessageFactory.text(f'Here are Carfax reports for {found} vehicles matching your inquiry:')

    @staticmethod
    def VEHICLES_MANY_VEHICLES_AVAIL(found=346):
        return MessageFactory.text(f'That\'s a really popular choice, we have {found} in stock.')

    @staticmethod
    def VEHICLES_VEHICLE_YEAR_REQUEST(make):
        return MessageFactory.text(f"What's the year of your {make}?")

    @staticmethod
    def VEHICLES_VEHICLE_TEST_DRIVE(year, make, model):
        return MessageFactory.text(f'Would you like to schedule a test drive of this {year} {make} {model}?')

    @staticmethod
    def VEHICLES_E_PRICE_DECISION(model):
        return MessageFactory.text(f'How can we get you behind the wheel of this {model}?')

    # VEHICLES_STOCK_NUM_REQUEST = MessageFactory.text('Do you have VIN or registration number? I can also look up by VIN or stock number if you have one handy.')

    TEST_DRIVE_BOOKING_SUCCESS = MessageFactory.text('You are all set')
    TEST_DRIVE_RANGE_FOR_DEPARTMENT_OPEN = MessageFactory.text("This week our showroom is open")
    TEST_DRIVE_DATE_QUESTION = MessageFactory.text("What is your preferred date for the test drive?")
    TEST_DRIVE_WHAT_VEHICLE = MessageFactory.text("What is the vehicle you are looking to test drive?")

    @staticmethod
    def TEST_DRIVE_WHAT_VEHICLE_YEAR(model):
        return MessageFactory.text(f"What is the year of the {model} you are looking to test drive?")

    PII_REQUEST_NAME = MessageFactory.text("May I have your name")
    PII_REQUEST_LASTNAME = MessageFactory.text('And your last name too, please')
    PII_REQUEST_LAST_NAME_TOO = MessageFactory.text("May I have your last name too, please?")
    PII_REQUEST_FIRSTNAME = MessageFactory.text('And your first name too, please')
    PII_REQUEST_PHONE = MessageFactory.text("What's the best phone number to contact you on?")
    PII_REQUEST_EMAIL = MessageFactory.text("Can you please provide your e-mail")
    PII_INVALID_EMAIL = MessageFactory.text('doesn\'t look right. Can you send it again, please?')
    PII_INVALID_PHONE = MessageFactory.text('doesn\'t seem right. Can you give it to me again, please?')
    PII_REQUEST_NAME_FOR_VEHICLE = MessageFactory.text(
        "May I have your name, so that our sales team can contact you with the best price for this vehicle?")
    PII_NEED_FULL_PII_PHONE = MessageFactory.text(
        "To schedule your appointment I need both your e-mail and phone #, can you please give me your phone #")
    PII_NEED_FULL_PII_EMAIL = MessageFactory.text(
        "To schedule your appointment I need both your e-mail and phone #, can you please give me your e-mail address?")
    PII_REQUEST_EMAIL_NO_PHONE = MessageFactory.text("May I have your e-mail address please?")
    PII_REQUEST_PHONE_SECOND_TIME = MessageFactory.text("May I have your phone number please?")

    @staticmethod
    def PARTS_BOOKING_SUCCESS(part=None):
        if part:
            return MessageFactory.text(
                f"Thanks, I'll have our parts team follow up with you as soon as possible about the {part}")
        else:
            return MessageFactory.text("Thanks, I'll have our parts team follow up with you as soon as possible")

    PARTS_PART_QUESTION = MessageFactory.text("What part can I help you with?")

    XTIME_PICK_SERVICE = MessageFactory.text("What service are you looking for?")
    XTIME_PICK_SERVICE_ALT = MessageFactory.text("Which service matches your needs best?")
    XTIME_DATE_QUESTION = MessageFactory.text('Got it. What date works best for you?')
    XTIME_CONFIRMATION = MessageFactory.text("Does this look ok?")
    XTIME_SERVICE_INQUIRY = MessageFactory.text('What service did you need done on your vehicle?')

    @staticmethod
    def XTIME_SERVICE_INQUIRY_WITH_INFO(name, model):
        return MessageFactory.text(f"Thanks {name}! Now, what service did you need done on your {model}?")

    @staticmethod
    def XTIME_TIME_QUESTION(day):
        return MessageFactory.text(f"What time on {day} works best for you?")

    @staticmethod
    def XTIME_TIME_PREFERENCES(day):
        return MessageFactory.text(f'What time {day.strip()} would you like to come in?')

    XTIME_CLOSEST_TIME = MessageFactory.text('Closest available time is at')
    XTIME_DROPOFF_QUESTION = MessageFactory.text('Will you be dropping off or waiting?')
    XTIME_WE_HAVE_AN_OPENING = MessageFactory.text('Great, I will book you in at that time!')

    GENERIC_WHAT_CAN_I_DO = MessageFactory.text("What can I do for you today?")
    GENERIC_STILL_WITH_ME = MessageFactory.text("Are you still with me?")
    GENERIC_STILL_THERE = MessageFactory.text("Just checking if you are still there. I'll be here if you need help.")
    GENERIC_MAKE_NOTE_OF_INFO = MessageFactory.text("I'll make note of this information, thank you.")
    GENERIC_SERVICE_FOLLOWUP = MessageFactory.text("Would you like to schedule a service appointment now?")
    GENERIC_TRANSFER_TO_OP = MessageFactory.text("My apologies, one moment please")
    GENERIC_CLOSE_SHORT = MessageFactory.text("Thank you. Have a nice day")
    GENERIC_CLOSING_STATEMENT = MessageFactory.text("Thank you for visiting us. Have a great day!")
    GENERIC_WHAT_ELSE = MessageFactory.text("Ok, what else can I do for you?")

    ##############

    SERVICE_INQUIRY_NON_XTIME = MessageFactory.text("What service are you looking to make an appointment for?")
    TIME_QUESTION = MessageFactory.text('What is your preferred time')
    DATETIME_QUESTION_NON_XTIME = MessageFactory.text('What is your preferred date for the service appointment?')
    NO_48_HOURS_BOOK = MessageFactory.text('service appointments cannot be booked within 48 hours of this chat.')
    PICK_SERVICE = MessageFactory.text('Please pick a service from the list below:')
    ANOTHER_TIME = MessageFactory.text("Is there perhaps another time that works for you?")
    CLOSEST_TIME_END = MessageFactory.text('Does this work for you?')
    GREETING = MessageFactory.text("name is Alice. I'd be happy to help you")

    THANK_YOU_FOR_VISITING = MessageFactory.text("Thank you for visiting us. Have a great day!")
    HAVE_A_GREAT_DAY = MessageFactory.text("It was a pleasure chatting with you, (?P<name>.+). Have a great day!")

    CLOSEST_OPENING = MessageFactory.text('Closest available opening is at ')
    SECOND = MessageFactory.text("It's great to have you with us! I'm sorry, can you clarify your inquiry?")
    ONE_AVAILABLE = MessageFactory.text("Yes, we have one available. Is it what you are looking for?")
    AVAILABILITY = MessageFactory.text(
        "Yes, it is available. Just to confirm, is this the vehicle you are looking for?")

    BEST_PRICE_VEHICLES_PUSH = MessageFactory.text('We have 4 available, with price ranging from $3,990 to $5,200')
    BEST_PRICE_KIA_OPTIMA_PUSH = MessageFactory.text('We have 4 available, with price ranging from $18,595 to $18,833')
    BEST_PRICE_2017_PUSH = MessageFactory.text('We have 4 available, with price ranging from $10,988 to $13,991')
    CHEVROLET_COST = MessageFactory.text("Price for 2019 Chevrolet Equinox is $22,990")
    EQUINOX_COST = MessageFactory.text("We have one 2018 Equinox left at $19,790!")
    IMPREZA_COST = MessageFactory.text("We have one 2019 Impreza left at $22,837!")
    CIVIC_COST = MessageFactory.text("We have one 2018 Civic Type R left at $34,890!")
    STOCK_COST = MessageFactory.text("We have one 2017 Corvette left at $69,997!")
    NEW_STOCK_COST = MessageFactory.text("We have one 2023 Odyssey left at $42,900!")
    PRICE_RANGE = MessageFactory.text("Price varies from $18,948 to $28,545.")
    CHEVROLETS_347 = MessageFactory.text("We have 347 2019 Chevrolets available - which model are you looking for?")
    VEHICLE_MATCH = MessageFactory.text(
        "That's a great choice! Let me get your contact info so we can get you the best price for this vehicle")
    ASK_MODEL = MessageFactory.text("Do you have a specific model in mind?")
    AI_SERVICE_DEPT = MessageFactory.text("Our hours of operation for Service department are Mon-Sat: 9:00am - 6:00pm")
    AI_SALES_DEPT = MessageFactory.text("Our hours of operation for Sales department are Mon-Sat: 9:00am - 6:00pm")
    ANYTHING_ELSE = MessageFactory.text("Is there anything else I can assist you with?")
    AI_HAPPY_TO_HELP = MessageFactory.text("I\'d be happy to help you with")
    AI_TRANSPARENCY_GREET = MessageFactory.text("Hi! How may I help you today?")

    AI_SCHEDULE_HELP = MessageFactory.text("Let me help you schedule a test drive for this vehicle.")
    LIVE_PLAY_APPOINTMENT_old = MessageFactory.text(
        "I\'m happy to help you to schedule a service appointment.\n\nLet me get a few details to book you in!")
    LIVE_PLAY_APPOINTMENT = MessageFactory.text(
            "Hey\n👋\n! I\'m happy to help you to schedule a service appointment.\n\nLet me get a few details to book you in!")
    # AI_CONFIRM_TIME = MessageFactory.text("Great! I will pencil you in for ")
    AI_UNRELATED_MILEAGE = MessageFactory.text("This vehicle has 3,888 MI")
    AI_UNRELATED_PRICE = MessageFactory.text("This vehicle is currently listed at $23,794.")
    AI_PRICING_OFFER = MessageFactory.text(
        "While I cannot personally confirm your offer, I'll be happy to pass it along to my team for consideration!")
    AI_PRICING_NEGOTIABLE = MessageFactory.text(
        "I am not certain if the pricing is negotiable but I'd be happy to have my team confirm!")
    AI_LEASING_TERM_MILEAGE = MessageFactory.text("Do you know what lease term or mileage package you will need?")
    AI_START_NEW_CHAT = MessageFactory.text("Do you want to start a new chat or continue?")
    DECISION_START_NEW_CHAT = "Start New Chat"
    DECISION_CONTINUE = "Continue"

    SYSTEM_MESSAGE_INTERESTED_IN_TEST_DRIVE = MessageFactory.text("I'm interested in Schedule Test Drive")
    SYSTEM_MESSAGE_INTERESTED_IN_SERVICE_APPOINTMENT = MessageFactory.text(
        "I'm interested in Schedule Service Appointment")
    SYSTEM_MESSAGE_INTERESTED_IN_ONE_TEST = MessageFactory.text("I'm interested in Test One")

    SYSTEM_MESSAGE_CLICKED_TEST_DRIVE_PARENT_VIN = MessageFactory.text(
        "Customer clicked new Live Play: Schedule Test Drive (AI Assisted)\nVIN: 1GYKNDRSXKZ235150")
    SYSTEM_MESSAGE_CLICKED_TEST_DRIVE_VIN = MessageFactory.text(
        "Customer clicked new Live Play: Schedule Test Drive (AI Assisted)\nVIN: JA4AP3AUXKU020555")

    AT_LEASING_PROVIDE_QUOTE = MessageFactory.text(
        "While I do not have lease pricing on hand, I'd be happy to have my team provide a quote!")

    LIVE_PLAY = [MessageFactory.text("First Message"),
                 MessageFactory.text("Slightly longer second message"),
                 MessageFactory.text("Really verbose and kinda long 3rd message for no particular reason")]
    LIVE_PLAY_MODEL = [MessageFactory.text("First message"),
                       MessageFactory.text("Second message {model} included"),
                       MessageFactory.text("3rd message for no particular reason")]

    LIVE_PLAY_EPRICE = [MessageFactory.text("I would be happy to give you our best price for this vehicle!"),
                        MessageFactory.text(
                            "Let me get your contact info so we can get you the best price for this vehicle.")]

    LIVE_PLAY_EPRICE_NO_VIN = [MessageFactory.text("I would be happy to give you our best price for this vehicle!"),
                               MessageFactory.text(
                                   "Do you have a stock number or VIN of the vehicle that you're looking for?")]

    LIVE_PLAY_EPRICE_WRONG_VIN = [MessageFactory.text("I would be happy to give you our best price for this vehicle!"),
                                  MessageFactory.text(
                                      "I’m not seeing that model listed online, but let me have my team confirm availability for you.")]

    LIVE_PLAY_CLARIFICATION = MessageFactory.text(
        "It's great to have you with us! I'm sorry, can you clarify your inquiry?")
    LEASE_OR_PURCHES = MessageFactory.text("Are you looking to purchase or lease?")
    CARFAX_MATCHING = MessageFactory.text("vehicles matching your inquiry")
    TRADE_IN_TP_GREETING = MessageFactory.text("Hi my name is Alice! I just need a few details to get your valuation.")
    TRADE_IN_GREETING = MessageFactory.text(
        "Hi my name is Alice! I just need a few details to get your Kelley Blue Book® Trade-in Range.")
    TRADE_IN_KBB_ESTIMATION = MessageFactory.text(
        "Thanks (?P<name>.+)\! The Kelley Blue Book® Trade-in Range of your (?P<year>\d+) (?P<model>.+) is between \$(?P<estimate_low>.+) and \$(?P<estimate_high>.+).")
    TRADE_IN_ESTIMATION = MessageFactory.text(
        "Thanks (?P<name>.+)\! The trade-in value of your (?P<year>\d+) (?P<model>.+) is between \$(?P<estimate_low>.+) and \$(?P<estimate_high>.+).")
    TRADE_IN_REPORT = MessageFactory.text("You can view the market report for your vehicle at this link")

    @staticmethod
    def NON_XTIME_BOOKING_SUCCESS(name):
        return MessageFactory.text(
            f"You are all set {name}! I have gone ahead and noted your preferred date and time. Our team will be in touch with you to confirm availability")

    @staticmethod
    def VEHICLE_MODEL_REQUEST(make):
        return MessageFactory.text(f"What's the model of your {make}?")

    @staticmethod
    def VEHICLE_MODEL_YEAR_REQUEST(make):
        return MessageFactory.text(f"What's model & year of your {make}?")

    @staticmethod
    def GREETING_NEUTRAL(name="Alice"):
        return MessageFactory.text(f"Hi! My name is {name}. How may I help you?")

    @staticmethod
    def OPERATOR(name="Alice"):
        return MessageFactory.text(f"Hi my name is {name}. It’s great to have you with us!",
                                   owner=MessageOwner.OPERATOR)

    @staticmethod
    def GREETING_AI(name="Alice"):
        return MessageFactory.text(f"Hi my name is {name}. I'd be happy to help you with that!")

    @staticmethod
    def APPOINTMEMT_APPT_LOOKUP_FOUND_PREAMBLE(name):
        return MessageFactory.text(f"Ok {name}, here's what I found:")

    APPOINTMEMT_APPT_LOOKUP_CANCEL = MessageFactory.text('Do you need to cancel an appointment?')
    APPOINTMEMT_APPT_LOOKUP_CANCEL_CHOOSE = MessageFactory.text('Which appointment did you want to cancel?')
    APPOINTMEMT_APPT_LOOKUP_CANCEL_CONFIRM = MessageFactory.text('Are you sure you want to cancel this appointment?')

    @staticmethod
    def APPOINTMEMT_APPT_CANCELLATION_CONFIRMED(name):
        return MessageFactory.text(f'Ok {name}, I cancelled the appointment for you.')

    @staticmethod
    def E_PRICE(price, discount):
        return MessageFactory.text(f", our price for this vehicle is ${price}! That is ${discount} less than the MSRP!")

    @staticmethod
    def E_price_conditional(price):
        return MessageFactory.text(f", our price for this vehicle is ${price}!")

    @staticmethod
    def E_price_null():
        return MessageFactory.text(f", our price for this vehicle is not available via chat! We can have our sales team follow-up with you about pricing information.")

    @staticmethod
    def LIVE_PLAY_TEST_DRIVE_VEHICLE_VIN_MATCH(year, model):
        return MessageFactory.text(f"{year} {model} is a great choice!")

    @staticmethod
    def VEHICLE_COLOR_MATCH(color):
        return MessageFactory.text(
            f'{color} is a great choice! Let me get your contact info so we can get you the best price for this vehicle')

    @staticmethod
    def VEHICLE_LEAD_FOLLOWUP(model=None):
        if model:
            return MessageFactory.text(
                f'Thanks! I’ll have our sales team follow up with you as soon as possible regarding your interest in the {model}')
        else:
            return MessageFactory.text('Thanks! I’ll have our sales team follow up with you as soon as possible')

    @staticmethod
    def TRADE_IN_VEHICLE_LEAD_FOLLOWUP(year, model, trim=""):
        return MessageFactory.text(
            f'I’ll have our sales team follow up with you as soon as possible regarding your trade-in and your interest in the {year} {model}{" " + trim if trim != "" else ""}.')

    @staticmethod
    def VEHICLE_NO_MATCH(model=None):
        if model:
            return MessageFactory.text(
                f"I apologize, I don't have that information on hand. Let me get with my team about this {model}.")
        else:
            return MessageFactory.text(
                "I apologize, I don't have that information on hand. Let me get with my team about this")

    @staticmethod
    def VEHICLES_MATCHING(found):
        return MessageFactory.text(
            f'There are {found} vehicles matching your inquiry, can you please clarify which one you are interested in.')

    @staticmethod
    def AI_ALl_DEPARTMENTS(week_day_id=datetime.today().weekday(), week_day="Today"):
        return {0: MessageFactory.text(
            f"{week_day} Sales dept. is open from 9:00am to 6:00pm, Service dept. is open from 9:00am to 6:00pm and Parts dept. is closed"),
            1: MessageFactory.text(f"{week_day} we are open from 9:00am to 6:00pm"),
            2: MessageFactory.text(
                f"{week_day} Sales dept. is open from 9:00am to 6:00pm, Service dept. is open from 9:00am to 6:00pm and Parts dept. is closed"),
            3: MessageFactory.text(f"{week_day} we are open from 9:00am to 6:00pm"),
            4: MessageFactory.text(f"{week_day} we are open from 9:00am to 6:00pm"),
            5: MessageFactory.text(
                f"{week_day} Sales dept. is open from 9:00am to 6:00pm, Service dept. is open from 9:00am to 6:00pm and Parts dept. is closed"),
            6: MessageFactory.text(
                f"{week_day} we are closed. Our regular hours of operation are:\n\nSales\nMon-Sat: 9:00am - 6:00pm\n\nService\nMon-Sat: 9:00am - 6:00pm\n\nParts\nTue: 9:00am - 6:00pm\nThu-Fri: 9:00am - 6:00pm")}[
            week_day_id]

    @staticmethod
    def AI_SALES_DEPARTMENTS(week_day_id=datetime.today().weekday(), week_day="Today"):
        return {0: MessageFactory.text(f"{week_day} Sales dept. is open from 9:00am to 6:00pm"),
                1: MessageFactory.text(f"{week_day} Sales dept. is open from 9:00am to 6:00pm"),
                2: MessageFactory.text(f"{week_day} Sales dept. is open from 9:00am to 6:00pm"),
                3: MessageFactory.text(f"{week_day} Sales dept. is open from 9:00am to 6:00pm"),
                4: MessageFactory.text(f"{week_day} Sales dept. is open from 9:00am to 6:00pm"),
                5: MessageFactory.text(f"{week_day} Sales dept. is open from 9:00am to 6:00pm"),
                6: MessageFactory.text(f"{week_day} Sales dept. is closed")}[week_day_id]

    @staticmethod
    def DEALERSHIP_TODAY(week_day_id=datetime.today().weekday(), dict=False, day_name="hours for today"):
        doh = {0: {"Sales": "9 am — 6 pm", "Service": "9 am — 6 pm", "Parts": "Closed"},
               1: {"Sales": "9 am — 6 pm", "Service": "9 am — 6 pm", "Parts": "9 am — 6 pm"},
               2: {"Sales": "9 am — 6 pm", "Service": "9 am — 6 pm", "Parts": "Closed"},
               3: {"Sales": "9 am — 6 pm", "Service": "9 am — 6 pm", "Parts": "9 am — 6 pm"},
               4: {"Sales": "9 am — 6 pm", "Service": "9 am — 6 pm", "Parts": "9 am — 6 pm"},
               5: {"Sales": "9 am — 6 pm", "Service": "9 am — 6 pm", "Parts": "Closed"},
               6: {"Sales": "Closed", "Service": "Closed", "Parts": "Closed"},
               "H": {"Sales": "8 am — 2 pm", "Service": "8 am — 2:30 pm", "Parts": "8 am — 3 pm"}}
        if not dict:
            return MessageFactory.text(
                f"Our {day_name} are:\n\nSales: {doh[week_day_id]['Sales']}\nService: {doh[week_day_id]['Service']}\nParts: {doh[week_day_id]['Parts']}")
        else:
            return doh[week_day_id]

    @staticmethod
    def DEALERSHIP_DEPARTMENT(dep="Sales"):
        return {"Sales": MessageFactory.text(
            "Here are the hours for our Sales department:\n\nMon - Sat: 9 am — 6 pm\nSun: Closed"),
            "Service": MessageFactory.text(
                "Here are the hours for our Service department:\n\nMon - Sat: 9 am — 6 pm\nSun: Closed"),
            "Parts": MessageFactory.text(
                "Here are the hours for our Parts department:\n\nMon: Closed\nTue: 9 am — 6 pm\nWed: Closed\nThu - Fri: 9 am — 6 pm\nSat - Sun: Closed"),
        }[dep]

    @staticmethod
    def DEALERSHIP_DEPARTMENT_CHANGED(dep="Sales", week_day_id=datetime.today().weekday()):
        value = "Closed"
        return {0: MessageFactory.text(
            f"Here are the hours for our {dep} department:\n\nMon: {value}\nTue - Sun: 10 am — 4 pm"),
            1: MessageFactory.text(
                f"Here are the hours for our {dep} department:\n\nMon: 10 am — 4 pm\nTue: {value}\nWed - Sun: 10 am — 4 pm"),
            2: MessageFactory.text(
                f"Here are the hours for our {dep} department:\n\nMon - Tue: 10 am — 4 pm\nWed: {value}\nThu - Sun: 10 am — 4 pm"),
            3: MessageFactory.text(
                f"Here are the hours for our {dep} department:\n\nMon - Wed: 10 am — 4 pm\nThu: {value}\nFri - Sun: 10 am — 4 pm"),
            4: MessageFactory.text(
                f"Here are the hours for our {dep} department:\n\nMon - Thu: 10 am — 4 pm\nFri: {value}\nSat - Sun: 10 am — 4 pm"),
            5: MessageFactory.text(
                f"Here are the hours for our {dep} department:\n\nMon - Fri: 10 am — 4 pm\nSat: {value}\nSun: 10 am — 4 pm"),
            6: MessageFactory.text(
                f"Here are the hours for our {dep} department:\n\nMon - Sat: 10 am — 4 pm\nSun: {value}")}[
            week_day_id]

    @staticmethod
    def EMPLOYMENT_LINK(url):
        return MessageFactory.text(f"Here is the link to our employment opportunities: {url}")

    @staticmethod
    def CREDIT_APP_CTA_MESSAGE(name, account):
        return f"Hi {name}, {account} has sent you a credit application to fill out. Click below to get started!"

    @staticmethod
    def FOCUS_NAME(name):
        return MessageFactory.text(f"Am I speaking with {name}?")

    @staticmethod
    def FOCUS_PHONE(phone):
        return MessageFactory.text(f"Can I still reach you at the phone number ending in {phone[-4]}")

    @staticmethod
    def FOCUS_EMAIL(email):
        email = re.sub(r'^(.{2}).*?(@.+)$', r'\1****\2', email)
        return MessageFactory.text(f"Is your preferred email still {email}")
