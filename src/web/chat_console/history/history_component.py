import time
from selenium.common.exceptions import TimeoutException

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON><PERSON>, Button, SelectExtended, Image, Spinner
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.chat_console.history.chat_details_component import ChatDetailsComponent


class ChatListStructure:
    def __init__(self):
        #id	customer	subject	started	duration	ended	lead	operator	rating	website	disposition	chat status
        self.chat_id = TextBlock(CssSelector('td:nth-of-type(1)>.link'))
        self.chat_subject = TextBlock(CssSelector(".subject"))
        self.chat_lead = TextBlock(CssSelector("td.history-table-cell__lead span.cc-label--darkblue"))
        self.chat_disposition = SelectExtended(link_locator=CssSelector(".history-table-cell__disposition .Select-control"),
                                               option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"))
        self.avatar = Image(Xpath(".//td[@class='history-table-cell__pic'][2]//img"))
        self.impersonated_icon = Image(CssSelector('img[data-testid="impersonation-avatar-badge"]'))
        self.operator = TextBlock(Xpath(".//td[@class='history-table-cell__pic'][2]//img/../../div[2]"))


@find_by(Xpath(".//div[@class='ch-container ']/.."))
class HistoryComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.chat_details = None
        self.btn_new_history = Button(Xpath(".//div[text()='New History']/following-sibling::div"))
        self.tbl_chat_list = Table(Xpath(".//table[@class='table-custom']/tbody/tr"), ChatListStructure)
        self.btn_enable_notifications = Button(Xpath(".//button[@class='btn btn-success' and text()='Enable']"))
        self.spn_loader = Spinner(CssSelector(".list-loading"))

    @step_decorator('WEB - HistoryComponent: Open')
    def open_actions(self):
        self.ancestor.open()
        self.ancestor.btn_history_tab.click()
        try: #TODO: remove when GL-4104
            self.spn_loader.wait_for_appear_and_disappear(wait_time=5)
        except TimeoutException as e:
            Log.warning("There is no spinner")


    # def setup_page(self):
    #     if self.btn_enable_notifications.exists():
    #         self.btn_enable_notifications.click()

    @step_decorator('WEB - HistoryComponent: Select Chat by {1} id')
    def select_chat_by_id(self, chat_id, wait_time=60):
        chat_in_history = self.wait_for_chat_by_value(chat_id, wait_time=wait_time)
        chat_in_history.chat_id.js.scroll_into_view()
        chat_in_history.chat_id.js.click()

    @step_decorator("WEB - HistoryComponent: Get chat by id")
    def get_chat_by_value(self, chat_id):
        return self.tbl_chat_list.get_row_by_column_value("chat_id", chat_id, wait_time=10)

    @step_decorator("WEB - HistoryComponent: Get disposition of chat id {1}")
    def get_chat_disposition(self, chat_id):
        return self.wait_for_chat_by_value(chat_id).chat_disposition.text

    @step_decorator("WEB - HistoryComponent: Change disposition of chat id {1}")
    def change_chat_disposition(self, chat_id, disposition):
        self.wait_for_chat_by_value(chat_id).chat_disposition.select_item_by_text(disposition)

    @step_decorator("WEB - HistoryComponent: Wait for chat by  {1} id")
    def wait_for_chat_by_value(self, chat_id, wait_time=30, retry_delay=2):
        end_time = time.time() + wait_time
        while True:
            chat_list = self.get_chat_ids()
            if chat_id in chat_list:
                Log.info(f"Chat {chat_id} was found in {wait_time - (end_time - time.time())} s")
                return self.get_chat_by_value(chat_id)
            time.sleep(retry_delay)
            assert time.time() < end_time, f"Chat {chat_id} was not found in {chat_list}"
            self.ancestor.refresh()

    @step_decorator("WEB - HistoryComponent: Get chat ids")
    def get_chat_ids(self):
        try:
            self.spn_loader.wait_for_appear_and_disappear(wait_until_appears=1)
        except TimeoutException:
            Log.warning("Spinner was not appeared")
        assert self.tbl_chat_list.is_visible(5), "History table was not found on the page"
        return self.tbl_chat_list.get_column_values("chat_id", wait_time=5)

    @step_decorator("WEB - HistoryComponent: Open chat detail by {1} id")
    def open_chat_detail(self, chat_id):
        self.open()
        self.chat_details = ChatDetailsComponent(self.ancestor, chat_id).open()
        return self.chat_details

    @step_decorator("WEB - HistoryComponent: Open lead detail by {1} id")
    def open_lead_detail(self, chat_id):
        self.chat_details = self.open_chat_detail(chat_id)
        self.chat_details.btn_lead.click()
        assert self.chat_details.lead_form.exists(3)
        return self.chat_details

    @step_decorator("WEB - HistoryComponent: Toggle New History ON")
    def toggle_new_history(self):
        self.btn_new_history.click()
