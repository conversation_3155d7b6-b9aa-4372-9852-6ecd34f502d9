from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON>path, CssSelector
from kom_framework.src.web.data_types.element_types import Text<PERSON>lock, Button, Image, AnyType
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.chat_console.chat.lead_form_component import LeadFormComponent
from src.web.chat_console.history.cusromer_details_component import CustomerDetailsComponent


class MessageStructure:
    def __init__(self):
        self.info = Image(CssSelector('div.message-body__lead-info'))
        self.body = TextBlock(CssSelector('div.message-body__text'))
        self.time = TextBlock(CssSelector('div.message-body__timestamp span'))
        self.image = Image(Xpath('.//span[@class="history_operator_name"]/../../..//img'))
        self.impersonated_icon = Image(CssSelector('img[data-testid="impersonation-avatar-badge"]'))
        self.dictionary = TextBlock(CssSelector('div .dictionary-info'))
        self.offer_title = TextBlock(CssSelector('div.message-body--offer div.message-body__description'))
        self.single_vehicle_title = TextBlock(Xpath(
            './div/div[contains(@class, "message-body--inventory")]//div[contains(@class, "message-body__title")]'))
        self.several_vehicles = Table(CssSelector('.message-slider-container .message-body--inventory'),
                                      SeveralVehiclesStructure)


class SeveralVehiclesStructure:
    def __init__(self):
        self.plus = Button(CssSelector('.icon-plus'))
        self.title = TextBlock(CssSelector('.message-body__title'))
        self.price = TextBlock(CssSelector('.message-body__description'))
        self.stock = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Stock")]/../td[contains(@class, "message-body__information-value")]'))
        self.color = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Color")]/../td[contains(@class, "message-body__information-value")]'))
        self.transmission = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Transmission")]/../td[contains(@class, "message-body__information-value")]'))
        self.engine = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Engine")]/../td[contains(@class, "message-body__information-value")]'))
        self.mileage = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Mileage")]/../td[contains(@class, "message-body__information-value")]'))


@find_by(CssSelector('.ch-details'))
class ChatDetailsComponent(Component):
    def open_actions(self):
        self.ancestor.history_component.select_chat_by_id(self.chat_id)

    def setup_component(self):
        if self.get_chat_id() != self.chat_id:
            self.close()
            self.open_actions()

    def __init__(self, ancestor, chat_id=None):
        self.chat_id = chat_id
        super().__init__(ancestor)
        self.tbl_chat_content = Table(CssSelector("div.message-row:not(.message-row--system)"), MessageStructure)
        self.btn_lead = Button(CssSelector("[href='#lead']"))
        self.btn_customer = Button(CssSelector("[href='#customer']"))
        self.btn_close_modal = Button(CssSelector(".icon-arrow-left"))
        self.lead_form = LeadFormComponent(ancestor=self.ancestor)
        self.customer = CustomerDetailsComponent(ancestor=self)
        self.img_operator_avatar = Image(CssSelector(".operator img"))
        self.txt_operator_name = AnyType(CssSelector(".operator .operator__name"))
        self.tbl_message_resq = Table(CssSelector("div.message-row.message-row--resq"), MessageStructure)

    @step_decorator('WEB - ChatDetailsComponent: Get chat id')
    def get_chat_id(self):
        self.customer.open()
        return self.customer.txt_chat_id.text

    @step_decorator('WEB - ChatDetailsComponent: Close window')
    def close(self):
        self.btn_close_modal.click()

    @step_decorator("WEB - ChatDetailsComponent: Get message by {1} pattern")
    def get_message_by_pattern(self, pattern):
        return self.tbl_chat_content.get_row_by_column_pattern("body", pattern, wait_time=5, reversed_order=True)

    @step_decorator("WEB - ChatDetailsComponent: Get message by {1} regex")
    def get_message_by_regex(self, pattern):
        return self.tbl_chat_content.get_row_by_column_regex("body", pattern, wait_time=5, reversed_order=True)
