from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_types import Text<PERSON><PERSON>, Button
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(Xpath('//div[@class="customer-details__additional"]/..'))
class CustomerDetailsComponent(Component):

    def open_actions(self):
        self.ancestor.btn_customer.click()

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_chat_id = TextBlock(Xpath(".//div[@class='label' and text()='Chat ID']/../div[@class='value']"))
        self.txt_disposition = TextBlock(Xpath(".//div[@class='label' and text()='Disposition']/../div[@class='value']"))
        self.txt_lead_submitted_by = TextBlock(
            Xpath(".//div[@class='label' and text()='Lead submitted by']/..//div[@class='submitted-by']"))
        self.btn_show_more = Button(CssSelector(".customer-details__show-more"))
        self.txt_customer_name = TextBlock(CssSelector(".customer-details__name"))
        self.txt_customer_phone = TextBlock(CssSelector(".customer-details__phone"))
        self.txt_customer_email = TextBlock(CssSelector(".customer-details__email"))

        self.txt_vehicle_of_interest = TextBlock(Xpath(".//div[@class='item-appointment__title' and text()='Vehicle of interest']/../div[@class='item-appointment__description']"))
        self.txt_department = TextBlock(Xpath(".//div[@class='label' and text()='Department']/../div[@class='value']"))
        self.txt_lead_type = TextBlock(Xpath(".//div[@class='label' and text()='Lead Type']/../div[@class='value']"))
        self.txt_vehicle_type = TextBlock(Xpath(".//div[@class='label' and text()='Vehicle Type']/../div[@class='value']"))

    @step_decorator("WEB - CustomerDetailsComponent: Get vehicle of interest")
    def get_vehicle_of_interest(self):
        return self.txt_vehicle_of_interest.text

    @step_decorator("WEB - CustomerDetailsComponent: Get department")
    def get_department(self):
        return self.txt_department.text

    @step_decorator("WEB - CustomerDetailsComponent: Get lead type")
    def get_lead_type(self):
        return self.txt_lead_type.text

    @step_decorator("WEB - CustomerDetailsComponent: Get vehicle type")
    def get_vehicle_type(self):
        return self.txt_vehicle_type.text
