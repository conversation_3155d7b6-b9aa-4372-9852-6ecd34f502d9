from selenium.common import TimeoutException

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_list_types import AnyList
from kom_framework.src.web.data_types.element_types import Text<PERSON>lock, SelectExtended, Image, Spinner, Input, Button
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.chat_console.history import HistoryTimeRange


class ChatListStructure:
    def __init__(self):
        #ID, Customer, Subject, Started, Duration, Ended, Handled By, Website and Disposition
        self.chat_id = TextBlock(Xpath('.//p[contains(@data-testid, "id-cell")]'))
        self.chat_subject = TextBlock(CssSelector('[data-testid="subject-cell"]'))
        self.chat_lead = TextBlock(Xpath(".//div[contains(@id, 'lead-badge')]"))
        self.chat_disposition = SelectExtended(link_locator=CssSelector(".//div[contains(@id, 'history-chat-disposition')]"),
                                               option_list_locator=Xpath(".//div[contains(@class, 'menu')]"))


        #self.avatar = Image(Xpath(".//td[@class='history-table-cell__pic'][2]//img"))
        self.impersonated_icon = Image(CssSelector('img[data-testid="impersonation-avatar-badge"]'))
        self.operator = TextBlock(Xpath(".//td[7]"))


@find_by(Xpath(".//div[@id='history-filters-status']/../../../.."))
class NewHistoryComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.chat_details = None
        self.tbl_new_chat_list = Table(CssSelector('[data-testid="history-chat-row"]'), ChatListStructure)
        self.spn_loader = Spinner(CssSelector(".list-loading"))
        self.lst_header = AnyList(CssSelector("#history-list thead tr th"))
        self.header_text = ["ID", "Customer", "Subject", "Started", "Duration", "Ended", "Handled By", "Website", "Disposition"]
        self.inp_search = Input(CssSelector('.form-search input'))
        self.btn_date_range = TextBlock(CssSelector(".cc-calendar__date"))
        self.range_calendar = CalendarDatePickerComponent(self)

    def setup_component(self):
        self.range_calendar.open()
        self.range_calendar.select_range(HistoryTimeRange.ONE_MONTH)
        self.wait_for_loading()

    @step_decorator('WEB - NewHistoryComponent: Open')
    def open_actions(self):
        self.ancestor.open()
        self.ancestor.history_component.open()
        self.ancestor.history_component.toggle_new_history()

    def get_table_header(self):
        self.wait_for_loading()
        return self.lst_header.elements_texts

    @step_decorator('WEB - NewHistoryComponent: Type {1} in to the search field')
    def search_with_typing(self, string):
        self.inp_search.clear_and_type_keys_to_invisible_field(string)

    @step_decorator('WEB - NewHistoryComponent: Paste {1} in to the search field')
    def search_with_pasting(self, string):
        self.inp_search.clear_and_send_keys(string)

    def get_chat_list(self):
        self.wait_for_loading()
        self.tbl_new_chat_list.exists(wait_time=10)
        return self.tbl_new_chat_list

    def get_chat_list_ids(self):
        return self.get_chat_list().get_column_values("chat_id", wait_time=5)

    def wait_for_loading(self):
        try:
            self.spn_loader.wait_for_appear_and_disappear()
        except TimeoutException:
            Log.warning("Spinner was not appeared after clicking submit button")


@find_by(CssSelector(".daterangepicker"))
class CalendarDatePickerComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_month = TextBlock(CssSelector(".month"))
        self.btn_prev = Button(CssSelector(".prev.available"))
        self.btn_next = Button(CssSelector(".next.available"))
        self.ddl_date = SelectExtended(link_locator=CssSelector("tbody"),
                                       option_list_locator=CssSelector("td.available:not(.off)"),
                                       extent_list_by_click_on_field=False)

    @step_decorator('WEB - CalendarDatePickerComponent: Open Component')
    def open_actions(self):
        self.ancestor.btn_date_range.click()

    def select_range(self, range):
        range_button = Button(CssSelector(f"li[data-range-key='{range}']"))
        range_button.ancestor = self
        range_button.click()

    # @step_decorator('WEB - CalendarDatePickerComponent: Pick a day')
    # def pick_a_day(self, event_date):
    #     while datetime.datetime.strptime(self.txt_month.text, '%b %Y').date().month != event_date.month:
    #         if datetime.datetime.strptime(self.txt_month.text, '%b %Y').date().month > event_date.month and event_date.year == datetime.datetime.strptime(self.txt_month.text, '%b %Y').date().year:
    #             self.btn_prev.click()
    #         else:
    #             self.btn_next.click()
    #     self.ddl_date.select_item_by_text(str(event_date.day))