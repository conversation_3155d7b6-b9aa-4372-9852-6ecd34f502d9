import allure
from pytest_check import check_func

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector, Xpath
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON><PERSON>, CheckBox, Button, Image
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


class UserListStructure:
    def __init__(self):
        self.name = TextBlock(CssSelector('[data-testid="user_row__full_name"]'))
        self.role_id = TextBlock(CssSelector('[data-testid="user_row__username_id"]'))
        self.name_in_chat = TextBlock(CssSelector('[data-testid="user_row__display_name"]'))
        self.role = TextBlock(CssSelector('[data-testid="user_row__group_name"]'))
        self.account_group = TextBlock(CssSelector('[data-testid="user_row__primary_certification_name"]'))
        self.free_channels = TextBlock(CssSelector('[data-testid="user_row__used_channels"]'))
        self.auto_accept = CheckBox(Xpath('.//input[contains(@name, "auto-accept")]'))
        self.online = CheckBox(Xpath('.//input[contains(@name, "status")]'))
        self.certification = TextBlock(CssSelector('[data-testid="user_row__certifications"]'))
        self.impersonate = Button(Xpath(".//*[contains(@data-testid, 'impersonation-manage-chats')]"))
        self.impersonate_avatar = Image(Xpath(".//*[contains(@data-testid, 'impersonation-manage-chats')]//img"))


@find_by(Xpath('.//div[contains(@data-testid, "users-container")]/..|//div[contains(@class, "users-container")]/..'))
class UsersComponent(Component):

    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.tbl_user_list = Table(CssSelector('tr[data-testid="user_row"]'), UserListStructure)

    @step_decorator('WEB - UsersComponent: Open')
    def open_actions(self):
        self.ancestor.open()
        self.ancestor.btn_users_tab.click()

    @step_decorator("CHECK - Impersonate button presence in users page")
    @check_func
    def check_impersonate_button_in_users_list(self):
        self.open()
        for user in self.tbl_user_list.get_content(wait_time=3):
            if user.role.text in ['Operators', 'VR Specialists', 'Administrators']:
                with allure.step(f"Button is present for {user.role.text}"):
                    assert user.impersonate.exists() and user.impersonate.text == "Manage chats",\
                        f"User {user.name.text} as {user.role.text} has to have 'Manage chats' button"
            else:
                with allure.step(f"Button is not present for {user.role.text}"):
                    assert not user.impersonate.exists(), f"User {user.name.text} has not to have 'Manage chats' button"

    @step_decorator('WEB - UsersComponent: Impersonate {1} user')
    def impersonate(self, user_name, operator_id=None):
        self.open()
        user = self.get_user(user_name)
        assert user.impersonate.exists(), f"Impersonate button is not displayed for {user_name}"
        assert user.impersonate.text == "Manage chats", \
            f"Impersonate status must be 'Manage chats', not {user.impersonate.text}"
        user.impersonate.click()

        try:
            self.exists(5)
            user = self.get_user(user_name)
            user.impersonate.exists(5)
            user.impersonate.wait_for.text_to_be_present_in_element("Managing chats", wait_time=5)
        except Exception as e:
            Log.error("Can't find 'Managing chats' in impersonation button")

    @step_decorator('WEB - UsersComponent: Get avatar from impersonated button of {1}')
    def get_impersonate_user_avatar(self, user_name):
        self.open()
        user = self.get_user(user_name)
        assert user.impersonate.exists(), f"Impersonate button is not displayed for {user_name}"
        return user.impersonate_avatar.get_attribute("src")

    @step_decorator('WEB - UsersComponent: Get user by name {1}')
    def get_user(self, user_name):
        self.tbl_user_list.exists(5)
        return self.tbl_user_list.get_row_by_column_pattern("role_id", pattern=user_name, wait_time=5)







