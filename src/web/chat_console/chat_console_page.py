import os

from selenium.common.exceptions import TimeoutException

from kom_framework.src.general import Log
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_types import Spinner, TextBlock
from kom_framework.src.web.page_object import PageObject
from kom_framework.src.web.support.page_factory import find_by
from src import chat_console_account_id, chat_console_manager_user_name, chat_console_password
from src import chat_console_url, chat_console_manager_id
from src.utils.decorators import step_decorator
from src.web.chat_console.chat.chat_component import ChatComponent
from src.web.chat_console.chat.cleared_chat_component import ClearedChatComponent
from src.web.chat_console.history.history_component import HistoryComponent
from src.web.chat_console.history.new_history_component import NewHistoryComponent
from src.web.chat_console.profile.quick_responses import QuickResponsesComponent
from src.web.chat_console.side_bar import SideBar
from src.web.chat_console.sign_in_page import ChatConsoleSignInPage
from src.web.chat_console.users.users_component import UsersComponent


@find_by(CssSelector(".chat-console-2"))
class ChatConsolePage(PageObject, SideBar):

    def __init__(self, account_id=chat_console_account_id,
                 user_name_value=chat_console_manager_user_name,
                 password_value=chat_console_password,
                 operator_id_value=chat_console_manager_id):
        SideBar.__init__(self)
        self.__branch_name__ = os.environ["CHAT_CONSOLE_BRANCH"]
        self.account_id = account_id
        self.user_name = user_name_value
        self.password = password_value
        self.operator_id = operator_id_value
        # Page elements
        self.chat_component = ChatComponent(self)
        self.spn_gubagoo = Spinner(CssSelector(".cc-loader"))
        self.history_component = HistoryComponent(self)
        self.new_history_component = NewHistoryComponent(self)
        self.users_component = UsersComponent(self)
        self.txt_dictionary = TextBlock(CssSelector('.dictionary-info__description'))
        self.cleared_chat = ClearedChatComponent(self)
        self.quick_responses = QuickResponsesComponent(self)

    @step_decorator('WEB - ChatConsolePage: Open Browser and enter Chat Console Page URL')
    def open_actions(self):
        self.get(chat_console_url[self.__branch_name__])
        singing_page = ChatConsoleSignInPage()
        singing_page.set_session_key(self.get_session_key())
        if singing_page.exists():
            singing_page.sign_in(self.account_id, self.user_name, self.password)
            try:
                self.exists(5)
                self.spn_gubagoo.exists(5)
                self.spn_gubagoo.wait_for_appear_and_disappear()
            except TimeoutException:
                Log.warning("Spinner was not appeared after clicking submit button")

    def setup_page(self):
        pass
