import allure

from selenium.common.exceptions import ElementClickInterceptedException
from time import sleep

from kom_framework.src.general import Log
from kom_framework.src.web import http_request_wait_time
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, <PERSON>Block, CheckBox
from kom_framework.src.web.support.page_factory import find_by
from src.rest_api.services.operator import Operator
from src.utils.decorators import step_decorator


class SideBar:
    def __init__(self):
        self.btn_avatar = Button(CssSelector(".cc-sidebar__user img"))
        self.btn_deals_tips_right = Button(CssSelector(".icon-to-right"))
        self.btn_deals_tips_left = Button(CssSelector(".icon-to-left"))
        self.btn_online = Button(CssSelector(".m-operator span"))
        self.modal_operator = ModalOperatorComponent(self)
        self.btn_dashboard_tab = Button(CssSelector('a[href="/dashboard"]'))
        self.btn_chat_tab = Button(CssSelector('a[href="/console"]'))
        self.btn_history_tab = Button(CssSelector('a[href="/history"]'))
        self.btn_users_tab = Button(CssSelector('a[href="/users"]'))
        self.btn_visitors_tab = Button(CssSelector('a[href="/visitors"]'))

    @step_decorator('WEB - SideBar: Sign out')
    def sign_out(self):
        api_status = self.get_api_online_status()
        if api_status:
            self.modal_operator.open().btn_sign_out.click()
            self.wait_while_text_exists("Logging you out...", http_request_wait_time)

    @step_decorator('WEB - SideBar: Get current modal operator name')
    def get_current_operator_name(self):
        self.modal_operator.open()
        modal_operator_name = self.modal_operator.txt_operator_name.text
        self.modal_operator.close()
        Log.info(f'WEB - SideBar: Get current modal operator name - {modal_operator_name}')
        return modal_operator_name

    @step_decorator('WEB - SideBar: Close dealer tips')
    def close_deals_tips(self):
        if self.btn_deals_tips_left.exists(3):
            self.btn_deals_tips_left.js.click()

    @step_decorator('WEB - SideBar: Get online status')
    def get_online_status(self):
        if "__status--offline" in self.btn_online.get_attribute("class"):
            return False
        if "__status--online" in self.btn_online.get_attribute("class"):
            return True

    @step_decorator('WEB - SideBar: Get auto accept status')
    def get_auto_accept_status(self):
        self.modal_operator.open()
        return self.modal_operator.chk_accept.is_selected()

    @step_decorator('API - ChatConsolePage: Get auto accept status')
    def get_api_auto_accept_status(self):
        return Operator.get_operator_info(self.operator_id, self.account_id, self.user_name, self.password).auto_accept

    @step_decorator('API - ChatConsolePage: Get online status')
    def get_api_online_status(self):
        return Operator.get_operator_info(self.operator_id, self.account_id, self.user_name, self.password).status

    @step_decorator('WEB - SideBar: Set auto accept toggle {1}')
    def set_auto_accept_toggle(self, status=True):
        auto_accept_api_status = self.get_api_auto_accept_status()
        auto_accept_ui_status = self.get_auto_accept_status()
        assert auto_accept_ui_status == auto_accept_api_status, \
            f"API auto accept status {auto_accept_api_status} doesnt mach visual status {auto_accept_ui_status}"
        try:
            if auto_accept_ui_status != status:
                self.modal_operator.accept_toggle()
                if self.get_api_auto_accept_status() != status:
                    with allure.step("!!!Retry Click auto-accept toggle"):
                        self.modal_operator.accept_toggle()
        except ElementClickInterceptedException:
            if status is True:
                self.chat_component.accept_chats_from_queue()
                self.modal_operator.open()
                self.modal_operator.btn_accept.click()
        finally:
            if status is True:
                self.chat_component.accept_chats_from_queue(wait_time=1)
        auto_accept_api_status = self.get_api_auto_accept_status()
        auto_accept_ui_status = self.get_auto_accept_status()
        assert auto_accept_ui_status == auto_accept_api_status, \
            f"API auto accept status {auto_accept_api_status} doesnt mach visual status {auto_accept_ui_status}"
        assert auto_accept_api_status == status, \
            f"Operator {self.operator_id} of {self.account_id} account is not auto accept: {auto_accept_api_status}"
        self.modal_operator.close()

    @step_decorator('WEB - SideBar: Set online toggle {1}')
    def set_online_toggle(self, status=True):
        online_api_status = self.get_api_online_status()
        online_ui_status = self.get_online_status()
        assert online_ui_status == online_api_status, \
            f"API online status {online_api_status} doesnt mach visual status {online_ui_status}"
        if online_api_status != status:
            self.modal_operator.online_toggle()
        online_api_status = self.get_api_online_status()
        assert online_api_status == status, \
            f"Operator {self.operator_id} of {self.account_id} account is not online: {online_api_status}"
        try:
            self.modal_operator.close()
        except ElementClickInterceptedException:
            if status is True:
                self.chat_component.accept_chats_from_queue()
                self.modal_operator.close()

    @step_decorator('WEB - SideBar: Go online')
    def go_online(self):
        online_api_status = self.get_api_online_status()
        auto_accept_api_status = self.get_api_auto_accept_status()

        if not online_api_status or not auto_accept_api_status:
            self.set_online_toggle(True)
            self.set_auto_accept_toggle(True)


@find_by(CssSelector(".modal-operator"))
class ModalOperatorComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_sign_out = Button(CssSelector(".modal-operator__footer .anchor"))
        self.txt_operator_name = TextBlock(CssSelector(".modal-operator__account-name"))
        self.btn_online = Button(CssSelector("label[for='status']"))
        self.btn_accept = Button(CssSelector("label[for='autoAccept']"))
        self.chk_accept = CheckBox(CssSelector("#autoAccept"), attribute="checked", checked_value="true")
        # For admin
        self.quick_responses = Button(Xpath(".//*[@class='modal-operator__links']/li/a[text()='Quick Responses']"))

    @step_decorator('WEB - ModalOperator: Open "Modal Operator" Component')
    def open_actions(self):
        if self.ancestor.btn_deals_tips_right.exists(2):
            self.ancestor.btn_deals_tips_right.click()
        self.ancestor.btn_avatar.click()

    @step_decorator('WEB - ModalOperator: Click online toggle')
    def online_toggle(self):
        self.open()
        self.btn_online.click()
        sleep(2)

    @step_decorator('WEB - ModalOperator: Click auto-accept toggle')
    def accept_toggle(self):
        self.open()
        self.btn_accept.click()
        sleep(2)

    @step_decorator('WEB - ModalOperator: Close "Modal Operator" Component')
    def close(self):
        self.ancestor.btn_avatar.click()

    @step_decorator('WEB - ModalOperator: Open Quick Responses')
    def open_quick_responses(self):
        self.open()
        self.quick_responses.click()
