from time import sleep

import allure
from selenium.webdriver import Keys
from selenium.common.exceptions import TimeoutException

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Css<PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_list_types import <PERSON><PERSON>, AnyList
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextBlock, SelectExtended, Input, Spinner
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from test.web_tests.chat_console.profile.quick_responses import QrCategory


class MessageStructure:
    def __init__(self):
        self.inp_message = Input(CssSelector(".qr-list__item--edit textarea"))
        self.btn_save = Button(Xpath(".//button[text()='Save']"))
        self.btn_add = Button(Xpath(".//button[text()='Add']"))
        self.btn_delete = Button(Xpath(".//button[text()='Delete']"))
        self.btn_cancel = Button(Xpath(".//button[text()='Cancel']"))
        self.txt_message = TextBlock(Xpath("."))


@find_by(CssSelector('.qr-container'))
class QuickResponsesComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.spn_account_selector = Spinner(Xpath(".//*[contains(@class, 'loadingIndicator')]"))
        self.dll_account_selector = SelectExtended(
            CssSelector(".account-select"), CssSelector("[role='option']"))
        self.btn_reset = Button(Xpath(".//button[text()='Reset to default']"))
        self.btn_add_new_category = Button(CssSelector(".qr-category-top__new-category"))
        self.btn_edit_category = Button(CssSelector(".qr-content-top__title span.btn-edit"))
        self.lst_categories = Menu(CssSelector(".qr-category-list li"))
        self.txt_selected_category = TextBlock(CssSelector(".qr-content-top__title>div:first-child"))
        self.inp_category = Input(CssSelector("input.account-group-form-input"))
        self.btn_save_category = Button(Xpath('.//button[text()="Save"]'))
        self.btn_delete_category = Button(Xpath('.//button[text()="Delete"]'))
        self.confirm_component = ConfirmComponent(self.ancestor)
        self.btn_new_message = Button(CssSelector(".qr-content-top__new-message"))
        self.tbl_messages = Table(CssSelector(".qr-list__item"), MessageStructure)
        self.btn_bulk_list_view = Button(CssSelector(".qr-content-switch span"))
        self.bulk = BulkEditComponent(self)
        self.spn_list_loading = Spinner(CssSelector(".list-loading"))
        self.account = None

    @step_decorator('WEB - QuickResponsesComponent: Open "Quick Replies" Component')
    def open_actions(self, account=None):
        self.ancestor.modal_operator.open_quick_responses()
        if account:
            self.account = account

    def setup_component(self, account=None):
        if account:
            self.select_account(account)
        with allure.step("Workaround for category to not be selected"):
            if self.get_current_category() == "":
                self.lst_categories.select_first_enabled()
        if self.bulk.exists(2):
            self.btn_bulk_list_view.js.scroll_into_view(align="false")
            self.btn_bulk_list_view.click()
            self.btn_bulk_list_view.wait_for.text_to_be_present_in_element("bulk", 5)
        assert self.btn_add_new_category.exists(5), "Quick Responses right top panel is not displayed"

    @step_decorator('WEB - QuickResponsesComponent: Reset to default')
    def reset_to_default(self):
        self.btn_reset.click()
        self.confirm_component.confirm("Reset the entire script to the default?")
        sleep(20)

    @step_decorator('WEB - QuickResponsesComponent: Select account')
    def select_account(self, account_name):
        try:
            self.spn_account_selector.wait_for_appear_and_disappear(3, 5)
        except TimeoutException:
            Log.debug("Account selector spinner was not displayed")
        if account_name != self.dll_account_selector.text:
            self.dll_account_selector.select_item_by_text(account_name, pattern=False, delay_for_options_to_appear_time=3)
            self.spn_list_loading.wait_for_appear_and_disappear(wait_time=60)
            self.lst_categories.wait_for.number_of_elements(0, wait_time=7, until=False)
        else:
            Log.info(f"Account {account_name} was already selected")

        self.lst_categories.wait_for.presence_of_all_elements_located(wait_time=30)

    @step_decorator('WEB - QuickResponsesComponent: Get selected category')
    def get_current_category(self):
        if self.txt_selected_category.exists(1):
            return self.txt_selected_category.text
        return ""

    @step_decorator('WEB - QuickResponsesComponent: Selected category')
    def select_category(self, category):
        if category not in self.get_current_category():
            assert self.lst_categories.select_menu_section_by_name(category)
            self.txt_selected_category.wait_for.text_to_be_present_in_element(category, wait_time=5)
        assert category in self.get_current_category(), f"Category {category} was not selected"

    @step_decorator('WEB - QuickResponsesComponent: Get all categories')
    def get_categories_names(self):
        self.lst_categories.wait_for.presence_of_all_elements_located(wait_time=7)
        return self.lst_categories.elements_texts

    @step_decorator('WEB - QuickResponsesComponent: Add category')
    def add_category(self, category):
        size = self.lst_categories.size
        self.btn_add_new_category.click()
        assert self.inp_category.exists(3), "Input of category was not displayed"
        self.inp_category.send_keys(category)
        self.btn_save_category.click()
        self.lst_categories.wait_for.number_of_elements(size+1, wait_time=7)

    @step_decorator('WEB - QuickResponsesComponent: Edit category')
    def edit_category(self, category_old, category_new, account=None):
        self.open(account=account)
        self.select_category(category_old)
        self.btn_edit_category.click()
        assert self.inp_category.exists(3), "Input for edit category did not appeared"
        self.inp_category.clear_and_send_keys(category_new)
        self.btn_save_category.click()
        self.txt_selected_category.wait_for.text_to_be_present_in_element(category_new, wait_time=3)

    @step_decorator('WEB - QuickResponsesComponent: Delete category')
    def delete_category(self, category):
        size = self.lst_categories.size
        self.select_category(category)
        with allure.step("Click edit category button"):
            self.btn_edit_category.click()
        assert self.btn_delete_category.exists(3), "Delete category button did not appeared"
        with allure.step("Click delete category button"):
            self.btn_delete_category.click()
        self.confirm_component.confirm(f"Delete {category}?")
        sleep(3) #TODO: remove sleep
        with allure.step(f"Wait for number of categories become {size-1}"):
            self.lst_categories.wait_for.number_of_elements(elements_count=size-1, wait_time=10)
            self.lst_categories.select_first_enabled()

    @step_decorator('WEB - QuickResponsesComponent: Reorder category to position')
    def reorder_category(self, category, position):
        pass


    ### -----------------------------------------------------------------------------------------------------------

    @step_decorator('WEB - QuickResponsesComponent: Get all messages of category')
    def get_all_messages_of_category(self, category):
        self.select_category(category)
        return self.tbl_messages.get_column_text("txt_message", wait_time=7)

    @step_decorator('WEB - QuickResponsesComponent: Add new message to category')
    def add_new_message(self, category, message):
        self.select_category(category)
        index = self.tbl_messages.size
        self.btn_new_message.click()
        new_message = self.tbl_messages.get_row_by_index(index)
        assert new_message.inp_message.exists(wait_time=5), "Input field did not appear"
        new_message.inp_message.clear_and_send_keys(message)
        with allure.step('Click add button'):
            new_message.btn_add.click()
        with allure.step('Wait for new message to be added'):
            self.spn_list_loading.wait_for_appear_and_disappear()
            new_message.inp_message.wait_for.invisibility_of_element_located()
        # cancel adding new messages
        if self.tbl_messages.size == index+2:
            new_message = self.tbl_messages.get_row_by_index(index+1)
            new_message.btn_cancel.click()
            self.tbl_messages.wait_for.number_of_elements(index+1)

    @step_decorator('WEB - QuickResponsesComponent: Edit message in category')
    def edit_message(self, category, message_old, message_new):
        self.select_category(category)
        edit_message = self.tbl_messages.get_row_by_column_value(column_name="txt_message",
                                                                 value=message_old)
        edit_message.txt_message.click()
        assert edit_message.inp_message.exists(wait_time=5), "Editable field did not appear"
        edit_message.inp_message.clear_and_send_keys(message_new)
        edit_message.btn_save.click()
        self.spn_list_loading.wait_for_appear_and_disappear()
        edit_message.inp_message.wait_for.invisibility_of_element_located(wait_time=20)
        assert not edit_message.btn_save.exists(3, until=False), "Editable field still displayed"

    @step_decorator('WEB - QuickResponsesComponent: Bulk Edit message in category')
    def edit_message_bulk(self, category, message_old, message_new):
        self.bulk.open(self.account, category)
        self.bulk.edit(message_old, message_new)

    @step_decorator('WEB - QuickResponsesComponent: Delete message from category')
    def delete_message(self, category, message):
        self.select_category(category)
        delete_message = self.tbl_messages.get_row_by_column_value(column_name="txt_message",
                                                                   value=message)
        delete_message.txt_message.click()
        assert delete_message.inp_message.exists(), "Editable field did not appear"
        delete_message.btn_delete.click()
        self.confirm_component.confirm("Delete this message?")
        self.spn_list_loading.wait_for_appear_and_disappear()
        #delete_message.inp_message.wait_for.invisibility_of_element_located()

    @step_decorator('WEB - QuickResponsesComponent: Reorder message')
    def reorder_message(self, category, message, position):
        pass


@find_by(CssSelector(".confirmation-modal"))
class ConfirmComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_message = TextBlock(CssSelector(".modal-body div"))
        self.btn_cancel = Button(CssSelector('.btn-secondary'))
        self.btn_confirm = Button(CssSelector('#btn-confirm'))

    def open_actions(self):
        pass

    @step_decorator('WEB - ConfirmComponent: Confirm {1}')
    def confirm(self, text_message):
        assert self.exists(5), "Confirm Component was not displayed"
        self.txt_message.wait_for.text_to_be_present_in_element(text_message, 5)
        with allure.step("Click confirm button"):
            self.btn_confirm.click()
        with allure.step("Wait for Confirm Component to disappear"):
            self.wait_for.visibility_of_element_located(wait_time=3, until=False)


@find_by(CssSelector(".qr-bulk"))
class BulkEditComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.list_elements = AnyList(CssSelector("li"))
        self.whole_message = Input(CssSelector('div[data-contents="true"]'))
        self.btn_save = Button(CssSelector("button"))

    def open_actions(self, account, category):
        self.ancestor.open(account)
        self.ancestor.select_category(category)
        self.ancestor.btn_bulk_list_view.click()

    @step_decorator('WEB - BulkEditComponent: Edit messages')
    def edit(self, old, new):
        assert old in self.whole_message.text
        position_begin = self.whole_message.text.find(old)
        self.action_chains.click(self.list_elements.find()[0]).send_keys(Keys.HOME).perform()
        for i in range(position_begin):
            self.action_chains.send_keys(Keys.RIGHT).perform()
        self.action_chains.key_down(Keys.SHIFT).perform()
        for i in range(len(old)):
            self.action_chains.send_keys(Keys.RIGHT).perform()
        self.action_chains.key_up(Keys.SHIFT).perform()
        self.ancestor.ancestor.driver.get_screenshot_as_png()
        allure.attach(self.ancestor.ancestor.driver.get_screenshot_as_png(),
            #DriverManager.sessions[session].get_screenshot_as_png(),
                      'BulkEditSelection_screenshot',
                      allure.attachment_type.PNG)
        self.action_chains.send_keys(Keys.DELETE).perform()
        self.action_chains.send_keys(new).perform()
        allure.attach(self.ancestor.ancestor.driver.get_screenshot_as_png(),
                      #DriverManager.sessions[session].get_screenshot_as_png(),
                      'BulkEditEnter_screenshot',
                      allure.attachment_type.PNG)
        self.btn_save.click()
        self.ancestor.spn_list_loading.wait_for_appear_and_disappear()




