import re

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_list_types import Menu
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextBlock
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(CssSelector('div[data-testid="quick-response-widget"],div[data-testid="chat-detail__widget"]'))
class QuickRepliesComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_close = Button(CssSelector('[data-testid="quick-response-button-close"]'))
        self.btn_back = Button(CssSelector('[data-testid="quick-response-button-back"]'))
        self.mn_categories = Menu(CssSelector("[data-testid='quick-response-category'],[data-testid='i-list-categories-test-item']"))
        self.mn_messages = Menu(CssSelector("[data-testid='quick-response-message'],[data-testid='li-list-message-test-item']"))
        self.mn_title = Menu(CssSelector("[data-testid='quick-response-message']"))
        self.current_category = TextBlock(CssSelector("[data-testid='quick-response-selected-category']"))
        self.quick_response_title = TextBlock(CssSelector("[data-testid='quick-response-title'],[class='widget-top__title']"))


    @step_decorator('WEB - QuickRepliesComponent: Open "Quick Replies" Component')
    def open_actions(self):
        self.ancestor.btn_quick_replies.click()

    def setup_component(self, *args, **kwargs):
        if "active" not in self.ancestor.btn_quick_replies.get_attribute("class"):
            self.open()

    @step_decorator('WEB - QuickRepliesComponent: Close "Quick Replies" Component')
    def close(self):
        self.btn_close.click()

    @step_decorator('WEB - QuickRepliesComponent: Find quick reply {1}')
    def find_reply(self, reply):
        self.select_category(reply.category)
        if reply.placeholder:
            reply.message = reply.message.format(*reply.placeholder)
            self.select_reply_by_message_text_content(reply)
            self.confirm_auto_fills(reply)

        else:
            self.select_reply_by_message(reply)

    def confirm_auto_fills(self, reply):
        filled_placeholders = self.ancestor.btn_copilot.find(3)
        assert len(filled_placeholders) == len(reply.placeholder)
        for i, regex_pattern in enumerate(reply.placeholder):
            regex = re.compile(regex_pattern)
            confirmed = self.confirm_placeholder(regex, filled_placeholders)
            assert confirmed, f"{reply.placeholder[i]} was not confirmed"
            reply.message = reply.message.replace(reply.placeholder[i], confirmed)
            reply.placeholder[i] = confirmed
            if self.ancestor.btn_copilot.exists():
                filled_placeholders = self.ancestor.btn_copilot.find(3)

    @step_decorator('WEB - QuickRepliesComponent: Confirm quick reply auto fill {1}')
    def confirm_placeholder(self, regex, filled_placeholder):
        for filled_button in filled_placeholder:
            filled_button_text = filled_button.text
            if regex.search(filled_button_text):
                filled_button.click()
                return filled_button_text
        return False

    @step_decorator('WEB - QuickRepliesComponent:  Select quick reply {1} category')
    def select_category(self, category):
        if category not in self.get_selected_category():
            if 'Quick Responses' not in self.get_selected_category() and \
                    "Saved Responses" not in self.get_selected_category():
                self.btn_back.click()
            assert self.mn_categories.select_menu_section_by_pattern(category), "Quick Response category was not found"

    @step_decorator('WEB - QuickRepliesComponent: Get selected category')
    def get_selected_category(self):
        if self.current_category.exists():
            return self.current_category.text
        else:
            return self.quick_response_title.text

    @step_decorator('WEB - QuickRepliesComponent: Select quick replies {1}')
    def select_replies(self, replies):
        self.open()
        for reply in replies:
            self.find_reply(reply)

    @step_decorator('WEB - QuickRepliesComponent: Select quick reply {1} by message text')
    def select_reply_by_message(self, reply):
        assert self.mn_messages.select_menu_section_by_pattern(reply.message), \
            f"QR: {reply.message} was not found in {reply.category}"

    def select_reply_by_message_text_content(self, reply):
        message_pattern = reply.message
        found_messages = self.get_available_messages_text_content()
        found_pattern = [message for message in found_messages if re.search(message_pattern, message)]
        assert found_pattern, f"Message {message_pattern} was not found in {found_messages}"
        assert len(found_pattern) == 1, "More then one message match pattern"
        assert self.mn_messages.select_menu_section_by_regex(found_pattern[0])

    @step_decorator('WEB - QuickRepliesComponent: Get available categories')
    def get_available_categories(self):
        self.open()
        if 'Quick Responses' not in self.get_selected_category() and \
                "Saved Responses" not in self.get_selected_category():
            self.btn_back.click()
        return self.mn_categories.elements_texts

    @step_decorator('WEB - QuickRepliesComponent: Get available messages from category')
    def get_available_messages(self, category):
        self.open()
        self.select_category(category)
        cleaned_strings = [re.sub(r'^\d+\.\s?', '', s) for s in self.mn_messages.elements_texts]
        return cleaned_strings

    @step_decorator('WEB - QuickRepliesComponent: Get available messages text content from category')
    def get_available_messages_text_content(self):
        return self.mn_messages.get_attribute_value("textContent")


