import datetime

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON>, CssSelector
from kom_framework.src.web.data_types.element_types import Button, Input, SelectExtended, TextBlock
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.chat_console.chat.offers_component import OffersComponent
from src.web.chat_console.chat.vehicles_component import VehiclesComponent
from src.web.entities.lead_entity import WebLeadEntity


@find_by(Xpath('//button[text()="Submit lead"]/../../../..'))
class LeadFormComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_first_name = Input(CssSelector("input[name='first_name']"))
        self.inp_last_name = Input(CssSelector("input[name='last_name']"))
        self.inp_email = Input(CssSelector("input#email"))
        self.inp_phone = Input(CssSelector("input#phone"))
        self.inp_appointment = Input(CssSelector("input.form-control--calendar"))
        self.inp_postal_code = Input(CssSelector("input[name='postal_code']"))
        self.inp_vin_or_stock = Input(CssSelector("input[name='vin_or_stock']"))
        self.inp_make = Input(CssSelector("input[name='vehicle_make']"))
        self.inp_model = Input(CssSelector("input[name='vehicle_model']"))
        self.inp_year = Input(CssSelector("input[name='vehicle_year']"))

        self.ddl_time = SelectExtended(link_locator=Xpath(
            ".//div[@class='Select-placeholder' and text()='Time']/../..//span[@class='Select-arrow']|//input[@name='appointment_time']/../.."),
                                       option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"),
                                       search_input_locator=Xpath(
                                           ".//div[@class='col-5 pd-l0 brr0']//div[@class='Select-input']/input"))
        if hasattr(self.ancestor, "__branch_name__") and "chat_console/develop" in self.ancestor.__branch_name__:
            self.ddl_make = SelectExtended(link_locator=Xpath(".//input[@name='vehicle_make']/.."),
                                           option_list_locator=Xpath(".//input[@name='vehicle_make']/../..//*[@role='option']"),
                                           search_input_locator=Xpath(".//input[@name='vehicle_make']/..//input[@role='combobox']"))
            self.ddl_model = SelectExtended(link_locator=Xpath(".//input[@name='vehicle_model']/.."),
                                            option_list_locator=Xpath(".//input[@name='vehicle_model']/../..//*[@role='option']"),
                                            search_input_locator=Xpath(".//input[@name='vehicle_model']/..//input[@role='combobox']"))
            self.ddl_year = SelectExtended(link_locator=Xpath(".//input[@name='vehicle_year']/.."),
                                           option_list_locator=Xpath(".//input[@name='vehicle_year']/..//*[@role='option']"),
                                           search_input_locator=CssSelector("input[aria-label='lead_form_vehicle_year']"))
            self.ddl_department = SelectExtended(link_locator=Xpath(".//input[@name='department_id']/../.."),
                                                 option_list_locator=Xpath(".//input[@name='department_id']/../..//*[@role='option']"))
        else:
            self.ddl_make = SelectExtended(
                link_locator=Xpath(".//input[@name='vehicle_make']/../.."),
                option_list_locator=Xpath(".//div[@role='option']"),
                search_input_locator=Xpath(".//input[@name='vehicle_make']/../..//input[@role='combobox']"))
            self.ddl_model = SelectExtended(
                link_locator=Xpath(".//input[@name='vehicle_model']/../.."),
                option_list_locator=Xpath(".//div[@role='option']"),
                search_input_locator=Xpath(".//input[@name='vehicle_model']/../..//input[@role='combobox']"))
            self.ddl_year = SelectExtended(
                link_locator=Xpath(".//input[@aria-label='lead_form_vehicle_year']/../../../../.."),
                option_list_locator=Xpath(".//div[@role='option']"),
                search_input_locator=Xpath(".//input[@aria-label='lead_form_vehicle_year']"))
            self.ddl_department = SelectExtended(
                link_locator=Xpath(".//input[@name='department_id']/../.."),
                option_list_locator=Xpath("//div[@role='option']"))
        self.inp_notes = Input(CssSelector("textarea[name='notes']"))
        self.btn_submit = Button(CssSelector("button[type='submit']"))
        self.btn_submit_another_lead = Button(CssSelector(".lead-form__lead-sent.lead-sent button"))
        self.txt_lead_sent = Button(CssSelector("#previous-leads__sent-text"))

    @step_decorator('WEB - LeadFormComponent: Open "Lead Form" Component')
    def open_actions(self):
        self.ancestor.ancestor.close_deals_tips()
        if VehiclesComponent(self.ancestor).exists():
            VehiclesComponent(self.ancestor).close()
        if OffersComponent(self.ancestor).exists():
            OffersComponent(self.ancestor).close()
        self.ancestor.btn_lead.click()

    @step_decorator('WEB - LeadFormComponent: Input first name {1}')
    def input_first_name(self, name):
        self.inp_first_name.clean_with_action_chain()
        self.inp_first_name.clear_and_send_keys(name)

    @step_decorator('WEB - LeadFormComponent: Input last name {1}')
    def input_last_name(self, name):
        self.inp_last_name.clear_and_send_keys(name)

    @step_decorator('WEB - LeadFormComponent: Input email {1}')
    def input_email(self, email):
        self.inp_email.clean_with_action_chain()
        self.inp_email.clear_and_send_keys(email)

    @step_decorator('WEB - LeadFormComponent: Input phone number {1}')
    def input_phone(self, phone):
        self.inp_phone.clean_with_action_chain()
        self.inp_phone.clear_and_type_keys_to_invisible_field(phone)

    @step_decorator('WEB - LeadFormComponent: Input date {1}')
    def input_date(self, appointment_date):
        ancestor = self.ancestor
        if "ChatConsolePage" not in ancestor.__class__.__name__:
            ancestor = ancestor.ancestor
        CalendarDatePickerComponent(ancestor).open().pick_a_day(appointment_date)
        # self.inp_appointment.clean_with_command_a()
        # self.inp_appointment.send_keys(appointment_date)
        # self.inp_appointment.send_keys(Keys.ENTER)

    @step_decorator('WEB - LeadFormComponent: Input time {1}')
    def input_time(self, time):
        self.ddl_time.select_item_by_text(time)

    @step_decorator('WEB - LeadFormComponent: Input vehicle type {1}')
    def input_vehicle_type(self, vehicle_type):
        button = Button(Xpath(f"//input[@name='vehicle_type' and @value='{vehicle_type}']/.."))
        button.ancestor = self
        button.click()

    @step_decorator('WEB - LeadFormComponent: Input lead type {1}')
    def input_lead_type(self, lead_type):
        button = Button(Xpath(f"//input[@name='lead_type' and @value='{lead_type}']/.."))
        button.ancestor = self
        button.click()

    @step_decorator('WEB - LeadFormComponent: Input zip {1}')
    def input_zip(self, zip):
        self.inp_postal_code.clear_and_send_keys(zip)

    @step_decorator('WEB - LeadFormComponent: Input stock number {1}')
    def input_stock_number(self, stock_number):
        self.inp_vin_or_stock.clear_and_send_keys(stock_number)

    @step_decorator('WEB - LeadFormComponent: Input department {1}')
    def input_department(self, department):
        self.ddl_department.select_item_by_text(department)

    @step_decorator('WEB - LeadFormComponent: Input notes {1}')
    def input_notes(self, notes):
        self.inp_notes.send_keys(notes)

    @step_decorator('WEB - LeadFormComponent: Input vehicle model {1}')
    def input_vehicle_model(self, vehicle_model):
        self.ddl_model.select_item_by_text(vehicle_model)

    @step_decorator('WEB - LeadFormComponent: Input vehicle make {1}')
    def input_vehicle_make(self, vehicle_make):
        self.ddl_make.select_item_by_text(vehicle_make)

    @step_decorator('WEB - LeadFormComponent: Input vehicle year {1}')
    def input_vehicle_year(self, vehicle_year):
        self.ddl_year.select_item_by_text(vehicle_year)

    @step_decorator('WEB - LeadFormComponent: Fill Lead Form with {1}')
    def fill_form(self, lead: WebLeadEntity = None):
        if lead.first_name:
            self.input_first_name(lead.first_name)
        if lead.last_name:
            self.input_last_name(lead.last_name)
        if lead.email:
            self.input_email(lead.email)
        if lead.phone:
            self.input_phone(lead.phone)
        if lead.appointment_date:
            self.input_date(lead.appointment_date)
        if lead.appointment_time:
            self.input_time(lead.appointment_time)
        if lead.zip:
            self.input_zip(lead.zip)
        if lead.vehicle_type:
            self.input_vehicle_type(lead.vehicle_type)
        if lead.stock_number:
            self.input_stock_number(lead.stock_number)
        if lead.vehicle_year:
            self.input_vehicle_year(lead.vehicle_year)
        if lead.vehicle_make:
            self.input_vehicle_make(lead.vehicle_make)
        if lead.vehicle_model:
            self.input_vehicle_model(lead.vehicle_model)
        if lead.lead_type:
            self.input_lead_type(lead.lead_type)
        if lead.notes:
            self.input_notes(lead.notes)
        if lead.department:
            self.input_department(lead.department)

    @step_decorator('WEB - LeadFormComponent: Submit Lead')
    def submit_lead(self):
        self.btn_submit.click()
        if "ChatComponent" in self.ancestor.__class__.__name__:
            self.btn_submit_another_lead.wait_for.presence_of_element_located(wait_time=60)
            self.wait_for.text_to_be_present_in_element("Lead has been sent", wait_time=60)
            self.btn_submit_another_lead.click()
        else:
            # wait for notification
            pass


@find_by(CssSelector(".react-datepicker"))
class CalendarDatePickerComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_month = TextBlock(CssSelector(".react-datepicker__current-month"))
        self.btn_prev = Button(CssSelector(".react-datepicker__navigation--previous"))
        self.btn_next = Button(CssSelector(".react-datepicker__navigation--next"))
        self.ddl_date = SelectExtended(link_locator=CssSelector("tbody"),
                                       option_list_locator=CssSelector("td.available:not(.off)"),
                                       extent_list_by_click_on_field=False)

    @step_decorator('WEB - CalendarDatePickerComponent: Open Component')
    def open_actions(self):
        if self.ancestor.chat_component.exists():
            self.ancestor.chat_component.lead_component.inp_appointment.click()
        else:
            self.ancestor.history_component.chat_details.lead_form.inp_appointment.click()

    @step_decorator('WEB - CalendarDatePickerComponent: Pick a day')
    def pick_a_day(self, event_date):
        event_date = datetime.datetime.strptime(event_date, '%m/%d/%Y').date()
        while datetime.datetime.strptime(self.txt_month.text, '%B %Y').date().month != event_date.month:
            if datetime.datetime.strptime(self.txt_month.text, '%B %Y').date().month > event_date.month and\
                    event_date.year == datetime.datetime.strptime(self.txt_month.text, '%B %Y').date().year:
                self.btn_prev.click()
            else:
                self.btn_next.click()

        btn_date = Button(CssSelector(f'.react-datepicker__day:not(.react-datepicker__day--outside-month)[aria-label="day-{event_date.day}"]'))
        btn_date.ancestor = self
        btn_date.click()
