from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON>ss<PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_types import But<PERSON>, SelectExtended, TextBlock
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.chat_console.chat_console_page import ChatConsolePage


@find_by(CssSelector('.modal-content'))
class CloseChatComponent(Component):
    def __init__(self, ancestor: ChatConsolePage):
        super().__init__(ancestor)
        self.btn_close = Button(CssSelector('[aria-label="Close"] button'))
        self.btn_cancel = Button(Xpath(".//div[@class='modal-footer']/button[contains(text(), 'Cancel')]"))
        self.btn_close_chat = Button(Xpath(".//div[@class='modal-footer']/button[contains(text(), 'Close chat')]"))
        self.ddl_disposition = SelectExtended(CssSelector('[data-testid="disposition-selector"] + div'),
                                              option_list_locator=Xpath('//*[@data-testid="disposition-menu"]//div[@ role="option"]'),
                                              message_locator=Xpath('//*[@data-testid="disposition-selector"]/..//*[contains(@class, "singleValue")]'))

    @step_decorator('WEB - CloseChatComponent: Open "Close Chat" Component')
    def open_actions(self):
        self.ancestor.chat_component.btn_close.click()

    @step_decorator("WEB - CloseChatComponent: Close Chat")
    def close(self, disposition=None):
        if disposition:
            self.ddl_disposition.select_item_by_text(disposition)
        self.ddl_disposition.wait_for.text_equal('Select...', until=False)
        self.btn_close_chat.click()
        self.wait_for.invisibility_of_element_located(wait_time=10)
