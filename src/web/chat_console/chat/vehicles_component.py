from selenium.common.exceptions import TimeoutException

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector, Xpath
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Input, Spinner
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


class VehiclesStructure:
    def __init__(self):
        self.new = TextBlock(CssSelector('.wt-inventory__new'))
        self.insert = Button(CssSelector('.icon-big-arrow-left'))
        self.description = TextBlock(CssSelector('.inventory-description__txt'))
        self.title = TextBlock(CssSelector('.inventory-description__title'))
        self.price = TextBlock(CssSelector('.inventory-description__price'))


@find_by(CssSelector('.wt-inventory.chat-detail__widget:not([style*="display: none"])'))
class VehiclesComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_close = Button(CssSelector("span.icon-close"))
        self.btn_all = Button(Xpath('.//label[contains(@class, "btn-secondary") and contains(text(), "All")]'))
        self.btn_new = Button(Xpath('.//label[contains(@class, "btn-secondary") and contains(text(), "New")]'))
        self.btn_used = Button(Xpath('.//label[contains(@class, "btn-secondary") and contains(text(), "Used")]'))
        self.btn_cpo = Button(Xpath('.//label[contains(@class, "btn-secondary") and contains(text(), "CPO")]'))
        self.inp_search = Input(CssSelector('input.form-control--search'))
        self.btn_more_filters = Button(CssSelector('.wt-inventory_more'))
        self.spn_loading = Spinner(Xpath(".//*[@class='list-loading']"))
        self.tbl_vehicles = Table(CssSelector('.inventory-item'), VehiclesStructure)

    @step_decorator('WEB - VehiclesComponent: Open "Vehicles" Component')
    def open_actions(self):
        self.ancestor.btn_car.click()
        try:
            self.spn_loading.wait_for_appear_and_disappear()
        except TimeoutException:
            Log.warning("Spinner was not appeared after opening Vehicles Component")

    @step_decorator('WEB - VehiclesComponent: Close "Vehicles" Component')
    def close(self):
        self.btn_close.click()

    @step_decorator('WEB - VehiclesComponent: Search vehicle by {1}')
    def search(self, template):
        self.inp_search.clean_with_action_chain()
        self.inp_search.send_keys(template)
        try:
            self.spn_loading.wait_for_appear_and_disappear()
        except TimeoutException:
            Log.warning("WEB - VehiclesComponent:  Spinner was not displayed")

    @step_decorator('WEB - VehiclesComponent:  Select vehicle {1}')
    def select_vehicle(self, name, template=None):
        if template is None:
            template = name
        self.search(template)
        self.tbl_vehicles.get_row_by_column_value("title", name).insert.click()

    @step_decorator('WEB - VehiclesComponent:  Select vehicles {1}')
    def select_vehicles(self, vehicles=None):
        self.open()
        for vehicle in vehicles:
            self.select_vehicle(vehicle.name, vehicle.stock)
