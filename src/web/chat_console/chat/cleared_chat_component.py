from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_types import Button
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(Xpath("//div[text()='Chats cleared']/ancestor::div[@class='modal-content']"))
class ClearedChatComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_ok = Button(CssSelector('button'))

    @step_decorator('WEB - ClearedChatComponent: Open "Cleared Chat" Component')
    def open_actions(self):
        pass

    @step_decorator('WEB - ClearedChatComponent: Close "Cleared Chat" Component')
    def close(self):
        self.btn_ok.click()
        self.wait_for.invisibility_of_element_located(wait_time=3)
