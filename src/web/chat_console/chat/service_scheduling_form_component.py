import datetime
import random
import time

from pytz import timezone
from selenium.common.exceptions import TimeoutException

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import Xpath, CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, Input, SelectExtended, <PERSON><PERSON><PERSON>, Spinner
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.chat_console.chat.offers_component import OffersComponent
from src.web.chat_console.chat.vehicles_component import VehiclesComponent
from src.web.consumer import ServiceProviderType
from src.web.entities.lead_entity import WebLeadEntity


@find_by(Xpath('//button[text()="Schedule Appointment"]/../../../..'))
class ServiceSchedulingFormComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.inp_first_name = Input(CssSelector("input[name='firstName']"))
        self.inp_last_name = Input(CssSelector("input[name='lastName']"))
        self.inp_email = Input(CssSelector("input[name='emailAddress']"))
        self.inp_phone = Input(CssSelector("input[name='phoneNumber']"))

        if hasattr(self.ancestor.ancestor, "__branch_name__") and "chat_console/develop" in self.ancestor.ancestor.__branch_name__:
            self.ddl_make = SelectExtended(link_locator=Xpath(".//input[@name='vehicle_make']/.."),
                                           option_list_locator=Xpath(".//input[@name='vehicle_make']/../..//*[@role='option']"),
                                           search_input_locator=Xpath(".//input[@name='vehicle_make']/..//input[@role='combobox']"))
            self.ddl_model = SelectExtended(link_locator=Xpath(".//input[@name='vehicle_model']/.."),
                                            option_list_locator=Xpath(".//input[@name='vehicle_model']/../..//*[@role='option']"),
                                            search_input_locator=Xpath(".//input[@name='vehicle_model']/..//input[@role='combobox']"))
            self.ddl_year = SelectExtended(link_locator=Xpath(".//input[@name='vehicle_year']/.."),
                                           option_list_locator=Xpath(".//input[@name='vehicle_year']/..//*[@role='option']"),
                                           search_input_locator=CssSelector("input[aria-label='lead_form_vehicle_year']"))
        else:
            self.ddl_make = SelectExtended(link_locator=Xpath(".//div[@class='Select-placeholder' and text()='Make']/../..//span[@class='Select-arrow']|//input[@name='make']/../..//span[@class='Select-arrow']"),
                                           option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"),
                                           search_input_locator=Xpath(".//div[@class='col pd-l0 brr0']//div[@class='Select-input']/input"))
            self.ddl_model = SelectExtended(link_locator=Xpath(".//div[@class='Select-placeholder' and text()='Model']/../..//span[@class='Select-arrow']|//input[@name='model']/../..//span[@class='Select-arrow']"),
                                            option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"),
                                            search_input_locator=Xpath(".//div[@class='form-group row'][2]//div[@class='Select-input']/input"))
            self.ddl_year = SelectExtended(link_locator=Xpath(".//div[@class='Select-placeholder' and text()='Year']/../..//span[@class='Select-arrow']|//input[@name='year']/../..//span[@class='Select-arrow']"),
                                           option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"),
                                           search_input_locator=Xpath(".//div[@class='col-4 pd-r0 brl0']//div[@class='Select-input']/input"))

        self.inp_vin = Input(CssSelector("input[name='vin']"))
        self.ddl_service = SelectExtended(link_locator=Xpath(
            ".//div[@class='Select-placeholder' and text()='Select services']/../..//span[@class='Select-arrow']|//input[@name='service']/../..//span[@class='Select-arrow']"),
                                          option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"),
                                          hide_list_by_click_on_field=True)
        self.ddl_date = SelectExtended(link_locator=Xpath(
            ".//div[@class='Select-placeholder' and text()='Date']/../..|//input[@name='appointmentDate']/../.."),
                                       option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"))
        self.ddl_time = SelectExtended(link_locator=Xpath(
            ".//div[@class='Select-placeholder' and text()='Time']/../..//span[@class='Select-arrow']|//input[@name='appointmentTime']/../..//span[@class='Select-arrow']"),
                                       option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"))
        self.ddl_transportation = SelectExtended(link_locator=Xpath(
            ".//div[@class='Select-placeholder' and text()='Dropoff']/../..//span[@class='Select-arrow']|//input[@name='transportType']/../..//span[@class='Select-arrow']|//div[text()='Select transportation']/../..//span[@class='Select-arrow']"),
                                                 option_list_locator=Xpath(".//div[contains(@class, 'Select-option')]"))
        self.spn_loading = Spinner(Xpath(".//div[@class='Select-placeholder' and text()='Loading...']"))
        self.inp_notes = Input(CssSelector("textarea[name='comment']"))
        self.btn_submit = Button(Xpath('.//button[text()="Schedule Appointment"]'))

        ######################

        self.inp_appointment = Input(CssSelector("input.form-control--calendar"))
        self.inp_make = Input(CssSelector("input[name='make']"))
        self.inp_model = Input(CssSelector("input[name='model']"))
        self.inp_year = Input(CssSelector("input[name='year']"))
        self.btn_submit_another_lead = Button(CssSelector(".lead-sent__btn.btn.btn-primary.btn-block"))
        self.txt_lead_sent = Button(CssSelector("#previous-leads__sent-text"))

    @step_decorator('WEB - ServiceSchedulingFormComponent: Open "Service Scheduling Form" Component')
    def open_actions(self):
        self.ancestor.ancestor.close_deals_tips()
        if VehiclesComponent(self.ancestor).exists():
            VehiclesComponent(self.ancestor).close()
        if OffersComponent(self.ancestor).exists():
            OffersComponent(self.ancestor).close()
        self.ancestor.btn_service_appt.click()

    @step_decorator('WEB - ServiceSchedulingFormComponent: Fill XTime Form with {1}')
    def fill_form(self, lead: WebLeadEntity = None, wait_time=15, service_provider=ServiceProviderType.XTIME):
        self.__provide_first_name(lead.first_name)
        self.__provide_last_name(lead.last_name)
        self.__provide_email(lead.email)
        self.__provide_phone(lead.phone)
        self.__provide_stock_number(lead.stock_number)
        self.__provide_vehicle_year(lead.vehicle_year)
        self.__provide_vehicle_make(lead.vehicle_make)
        self.__provide_vehicle_model(lead.vehicle_model)
        self.__provide_appointment_services(lead.appointment_services)
        lead.appointment_date = self.__provide_appointment_date(wait_time)
        lead.appointment_time = self.__provide_appointment_time(wait_time)
        tz = "America/Los_Angeles" if lead.custom_type == ServiceProviderType.XTIME else "US/Eastern"
        lead.appointment_timestamp = timezone(tz).localize(datetime.datetime.combine(
            datetime.datetime.strptime(lead.appointment_date, '%m/%d/%Y').date(),
            datetime.datetime.strptime(lead.appointment_time, '%I:%M %p').time()))
        if lead.custom_type == ServiceProviderType.AUTOLOOP:
            lead.appointment_timestamp = lead.appointment_timestamp.isoformat(timespec='seconds')
        else:
            lead.appointment_timestamp = lead.appointment_timestamp.isoformat(timespec='minutes')

        self.__provide_transport_type(lead.transport_type)
        self.__provide_notes(lead.notes)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Submit')
    def submit(self):
        self.btn_submit.click()

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide first name {1}')
    def __provide_first_name(self, value):
        if value:
            self.inp_first_name.clear()
            self.inp_first_name.type_keys(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide last name {1}')
    def __provide_last_name(self, value):
        if value:
            self.inp_last_name.clear()
            self.inp_last_name.type_keys(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide email {1}')
    def __provide_email(self, value):
        if value:
            self.inp_email.clear()
            self.inp_email.type_keys(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide phone {1}')
    def __provide_phone(self, value):
        if value:
            self.inp_phone.clear()
            self.inp_phone.type_keys(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide stock number {1}')
    def __provide_stock_number(self, value):
        if value:
            self.inp_vin.clear()
            self.inp_vin.type_keys(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide vehicle year {1}')
    def __provide_vehicle_year(self, value):
        if value:
            self.ddl_year.select_item_by_text(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide vehicle make {1}')
    def __provide_vehicle_make(self, value):
        if value:
            self.ddl_make.select_item_by_text(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide vehicle model {1}')
    def __provide_vehicle_model(self, value):
        if value:
            self.ddl_model.select_item_by_text(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide transport type {1}')
    def __provide_transport_type(self, value):
        if value:
            self.ddl_transportation.select_item_by_text(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide notes {1}')
    def __provide_notes(self, value):
        if value:
            self.inp_notes.clear()
            self.inp_notes.type_keys(value)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide appointment services {1}')
    def __provide_appointment_services(self, value):
        try:
            self.spn_loading.wait_for_appear_and_disappear()
        except TimeoutException:
            pass

        for serv in value:
            self.ddl_service.select_item_by_text(serv)

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide random date')
    def __provide_appointment_date(self, wait_time):
        #  Wait for loader
        try:
            self.ddl_date.get_options_list()
            self.spn_loading.wait_for_appear_and_disappear()
        except TimeoutException:
            if self.spn_loading.exists(3):
                self.spn_loading.wait_for_appear_and_disappear()
        #  Wait for date option
        end_time = time.time() + wait_time
        while len(self.ddl_date.get_options_list()) <= 2 and time.time() < end_time:
            pass  # wait for date options
        assert len(
            self.ddl_date.get_options_list()) >= 2, f"There is no date option in {self.ddl_date.get_options_list()}"
        #  Select random date
        appointment_to_send = random.choice([opt for opt in self.ddl_date.get_options_list()])
        self.ddl_date.select_item_by_text(appointment_to_send)
        if datetime.datetime.strptime(appointment_to_send,
                                      '%a, %b %d').month < datetime.datetime.today().month:
            year = str(datetime.datetime.today().year + 1)
        else:
            year = str(datetime.datetime.today().year)
        appointment_date = datetime.datetime.strptime(appointment_to_send + " " + year,
                                                      '%a, %b %d %Y').strftime('%m/%d/%Y')
        return appointment_date

    @step_decorator('WEB - ServiceSchedulingFormComponent: Provide appointment time')
    def __provide_appointment_time(self, wait_time):
        end_time = time.time() + wait_time
        while len(self.ddl_time.get_options_list()) <= 2 and time.time() < end_time:
            pass  # wait for time options
        assert len(self.ddl_time.get_options_list()) >= 2, \
            f"There is no time option in {self.ddl_time.get_options_list()}"
        appointment_time = random.choice([opt for opt in self.ddl_time.get_options_list()])
        self.ddl_time.select_item_by_text(appointment_time)
        return appointment_time


@find_by(CssSelector('.service-lead-sent'))
class ServiceSchedulingSuccessComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)

        self.txt_appointment = TextBlock(CssSelector(".service-lead-sent__appointment-time"))
        self.txt_code = TextBlock(CssSelector(".service-lead-sent__confirmation"))
        self.btn_schedule_another_service = Button(Xpath(".//button[text()='Schedule another service']"))

    def open_actions(self):
        pass  # no action required to open

    @step_decorator('WEB - ServiceSchedulingSuccessComponent: Get confirmation code')
    def get_confirmation_code(self):
        return self.txt_code.text

    @step_decorator('WEB - ServiceSchedulingSuccessComponent: Get appointment time')
    def get_appointment_time(self):
        return self.txt_appointment.text

    @step_decorator('WEB - ServiceSchedulingSuccessComponent: Schedule another service')
    def schedule_another_service(self):
        self.btn_schedule_another_service.click()
        self.wait_for.visibility_of_element_located(wait_time=5, until=False)
