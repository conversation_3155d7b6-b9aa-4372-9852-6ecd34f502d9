from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON>, CssSelector, ClassName
from kom_framework.src.web.data_types.element_types import But<PERSON>, TextBlock
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.chat_console.chat.offers_component import OffersComponent
from src.web.chat_console.chat.vehicles_component import VehiclesComponent


class DealershipWorkingHoursType:
    TODAY = "Today"
    SALES = "Sales"
    SERVICE = "Service"
    PARTS = "Parts"


class DealershipInfo:
    PHONES = "Phones"
    LOCATION = "Location"


@find_by(CssSelector('.dealer-info'))
class DealershipComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.holidays = DealershipHolidaysComponent(self)
        self.dealer_info = DealerInfoContactComponent(self)
        self.btn_hours = Button(Xpath(".//button[text()='Hours']"))
        self.btn_phones_location = Button(Xpath(".//button[text()='Phones & Location']"))

    @step_decorator('WEB - DealershipComponent: Open Dealership Component')
    def open_actions(self):
        self.ancestor.ancestor.close_deals_tips()
        if VehiclesComponent(self.ancestor).exists():
            VehiclesComponent(self.ancestor).close()
        if OffersComponent(self.ancestor).exists():
            OffersComponent(self.ancestor).close()
        self.ancestor.btn_dealership.click()

    @step_decorator('WEB - DealershipComponent: Insert working hours')
    def insert_working_hours(self, info: DealershipWorkingHoursType):
        self.open()
        if info == DealershipWorkingHoursType.TODAY:
            btn_insert_hours = Button(Xpath(
                f".//p[contains(@class, 'dealer-info__title') and contains(text(), '{info}')]/../../button[@class='dealer-info__insert']"))
        else:
            btn_insert_hours = Button(Xpath(
                f".//p[contains(@class, 'dealer-info__title') and contains(text(), '{info}')]/../button[@class='dealer-info__insert']"))
        btn_insert_hours.ancestor = self
        btn_insert_hours.click()

    @step_decorator('WEB - DealershipComponent: Insert upcoming holiday hours')
    def insert_upcoming_holiday_hours(self, name, date):
        self.open()
        assert self.holidays.get_open_hours_by_holiday_name(name=name, date=date), \
            f"Upcoming Holiday {name} at {date} was not found"
        btn_insert_hours = Button(Xpath(
            f'.//p[contains(@class, "dealer-info__title") and contains(text(), "{name}")]/../../button[@class="dealer-info__insert"]'))
        btn_insert_hours.ancestor = self
        btn_insert_hours.click()

    @step_decorator('WEB - DealershipComponent: Insert dealership contact info')
    def insert_dealer_contact_info(self):
        self.open()
        self.dealer_info.open()
        self.dealer_info.btn_insert_location.click()


@find_by(CssSelector('.dealer-holidays'))
class DealershipHolidaysComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_expand = Button(CssSelector(".icon-arrow-down"))
        self.btn_collapse = Button(CssSelector(".icon-arrow-up"))
        self.btn_insert = Button(CssSelector(".dealer-info__insert"))
        self.btn_next = Button(CssSelector(".icon-arrow-right:not(.disabled)"))
        self.btn_prev = Button(CssSelector(".icon-arrow-left:not(.disabled)"))
        self.txt_counter = TextBlock(CssSelector(".dealer-info__text"))
        self.txt_name = TextBlock(CssSelector(".dealer-info__title"))
        self.txt_date = TextBlock(CssSelector(".justify-content-between .dealer-info__text--light"))
        self.txt_sales_hours = TextBlock(
            Xpath(".//div[contains(@class, 'dealer-info__text--light') and text()='Sales']/../div[@class='col px-0']"))
        self.txt_service_hours = TextBlock(Xpath(
            ".//div[contains(@class, 'dealer-info__text--light') and text()='Service']/../div[@class='col px-0']"))
        self.txt_parts_hours = TextBlock(
            Xpath(".//div[contains(@class, 'dealer-info__text--light') and text()='Parts']/../div[@class='col px-0']"))

    @step_decorator('WEB - DealershipHolidaysComponent: Open Dealership Holidays Component')
    def open_actions(self):
        self.ancestor.open()

    def setup_component(self):
        if self.btn_expand.exists():
            self.btn_expand.click()

    @step_decorator('WEB - DealershipHolidaysComponent: Get holiday list')
    def get_holidays_list(self):
        self.open()
        while self.btn_prev.exists():
            self.btn_prev.click()
        condition = True
        holiday_list = []
        while condition:
            if self.txt_name.exists():
                holiday_list.append({"date": self.txt_date.text.split("\n")[1],
                                     "name": self.txt_name.text,
                                     "hours": {"Sales": self.txt_sales_hours.text,
                                               "Service": self.txt_service_hours.text,
                                               "Parts": self.txt_parts_hours.text}})
            condition = self.btn_next.exists()
            if condition:
                self.btn_next.click()
        return holiday_list

    @step_decorator('WEB - DealershipHolidaysComponent: Open hours by holiday {1} name')
    def get_open_hours_by_holiday_name(self, name, date):
        self.open()
        while self.btn_prev.exists():
            self.btn_prev.click()
        while not (self.txt_name.text.lower() == name.lower() and date.lower() in self.txt_date.text.lower()):
            if self.btn_next.exists():
                self.btn_next.click()
            else:
                return None
        return {"Sales": self.txt_sales_hours.text,
                "Service": self.txt_service_hours.text,
                "Parts": self.txt_parts_hours.text}


@find_by(ClassName('dealer-info__contact'))
class DealerInfoContactComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_insert_location = Button(CssSelector(".dealer-info__location .dealer-info__insert"))

    @step_decorator('WEB - DealerInfoContactComponent: Open Dealer Info Contact Component')
    def open_actions(self):
        self.ancestor.open()
        self.ancestor.btn_phones_location.click()
