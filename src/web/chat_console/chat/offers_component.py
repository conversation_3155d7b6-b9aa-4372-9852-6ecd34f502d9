from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_types import <PERSON>, Button, TextBlock, Input
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


class OffersStructure:
    def __init__(self):
        self.image = Image(CssSelector('.wt-offers__item_img>img'))
        self.insert = Button(CssSelector('.icon-big-arrow-left'))
        self.title = TextBlock(CssSelector('.wt-offers__description_title'))


@find_by(Xpath('.//div[@data-testid="offers-widget"]'))
class OffersComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_close = But<PERSON>(CssSelector("span.icon-close"))
        self.inp_search = Input(CssSelector('input.form-control--search'))
        self.tbl_offers = Table(CssSelector('.wt-offers__item'), OffersStructure)

    @step_decorator('WEB - OffersComponent:  Open "Offers" Component')
    def open_actions(self):
        self.ancestor.btn_offer.click()

    @step_decorator('WEB - OffersComponent:  Close "Offers" Component')
    def close(self):
        self.btn_close.click()

    @step_decorator('WEB - OffersComponent:  Search offer by {1}')
    def search(self, template):
        self.inp_search.clean_with_action_chain()
        self.inp_search.send_keys(template)
        # have to wait for something, it takes about 3 sec

    @step_decorator('WEB - OffersComponent: Select offer {1}')
    def select_offer(self, name):
        self.tbl_offers.get_row_by_column_value("title", name, wait_time=3).insert.click()

    @step_decorator('WEB - OffersComponent: Select offers {1}')
    def select_offers(self, offers_list):
        self.open()
        for offer in offers_list:
            self.select_offer(offer)
