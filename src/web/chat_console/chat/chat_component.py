import re

from selenium.common.exceptions import ElementClickInterceptedException
from selenium.webdriver.common.keys import Keys

from kom_framework.src.general import Log
from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import <PERSON><PERSON><PERSON>elector, Xpath
from kom_framework.src.web.data_types.element_list_types import <PERSON><PERSON><PERSON>
from kom_framework.src.web.data_types.element_types import <PERSON>ton, TextBlock, Input, SelectExtended, Image, AnyType
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator
from src.web.chat_console.chat.chat_queue_component import ChatQueueComponent
from src.web.chat_console.chat.dealership_component import DealershipComponent
from src.web.chat_console.chat.offers_component import OffersComponent
from src.web.chat_console.chat.quick_replies_component import QuickRepliesComponent
from src.web.chat_console.chat.resq_component import ResqWidgetComponent
from src.web.chat_console.chat.service_scheduling_form_component import ServiceSchedulingFormComponent, \
    ServiceSchedulingSuccessComponent
from src.web.chat_console.chat.vehicles_component import VehiclesComponent
from src.web.chat_console.profile.quick_responses import ConfirmComponent
from src.web.consumer import ServiceProviderType
from src.web.entities.lead_entity import WebLeadEntity
from src.web.entities.message_entity import MessageEntity


class ActiveChatsStructure:
    def __init__(self):
        self.response_time = TextBlock(CssSelector('[data-testid="response-timer-badge"]'))
        self.reengage = TextBlock(CssSelector('[data-testid="reengage-badge"]'))
        self.title = TextBlock(CssSelector('.chat-list__title'))
        self.description = TextBlock(CssSelector(".chat-list__description"))
        self.new = Button(CssSelector('.chat-list__badge--new'))
        self.last_message = TextBlock(CssSelector('.chat-list__message span'))
        self.chat_id = TextBlock(CssSelector('.chat-list__id'))
        self.duration = TextBlock(CssSelector('.chat-list__duration'))
        self.impersonated_avatar = Image(CssSelector('.chat-list__footer div img'))


class SeveralVehiclesStructure:
    def __init__(self):
        self.plus = Button(CssSelector('.icon-plus'))
        self.title = TextBlock(CssSelector('.message-body__title'))
        self.price = TextBlock(CssSelector('.message-body__description'))
        self.stock = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Stock")]/../td[contains(@class, "message-body__information-value")]'))
        self.color = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Color")]/../td[contains(@class, "message-body__information-value")]'))
        self.transmission = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Transmission")]/../td[contains(@class, "message-body__information-value")]'))
        self.engine = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Engine")]/../td[contains(@class, "message-body__information-value")]'))
        self.mileage = TextBlock(Xpath(
            './/td[contains(@class, "message-body__information-label") and contains(text(), "Mileage")]/../td[contains(@class, "message-body__information-value")]'))


class MessageStructure:
    def __init__(self):
        self.info = Image(CssSelector('div.message-body__lead-info'))
        self.body = TextBlock(CssSelector('div.message-body__text>div:not([class])'))
        self.body_class = TextBlock(CssSelector('div.message-body__text'))
        self.status = TextBlock(CssSelector('div.message-body__timestamp'))
        self.time = TextBlock(CssSelector('div.message-body__timestamp span'))
        self.dictionary = TextBlock(CssSelector('div .dictionary-info'))
        self.impersonate_badge = Image(CssSelector('img[data-testid="impersonation-avatar-badge"]'))
        self.offer_title = TextBlock(CssSelector('div.message-body--offer div.message-body__description'))
        self.single_vehicle_title = TextBlock(Xpath(
            './div/div[contains(@class, "message-body--inventory")]//div[contains(@class, "message-body__title")]'))
        self.several_vehicles = Table(CssSelector('.message-slider-container .message-body--inventory'),
                                      SeveralVehiclesStructure)
        self.avatar = Image(Xpath('.//div[1]//img'))
        self.ai_badge = Image(Xpath(".//div/img[contains(@src, '17087425d690280d7c2f5de0e06cdba1aa530a2a.png')]"))

    def whisper_in_timestamp(self):
        return "Whisper" in self.time.text

    def whisper_in_class(self):
        return "whisper" in self.body_class.get_attribute("class")


@find_by(CssSelector('.console.flex-container'))
class ChatComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.btn_send = Button(CssSelector('span.icon-send'))
        self.btn_whisper = Button(Xpath(".//li/span[text()='Whisper']/.."))
        self.btn_reply = Button(Xpath(".//li/span[text()='Reply']/.."))
        self.div_input_message_type = AnyType(CssSelector("div.chat-message"))
        self.inp_message = Input(CssSelector('div.chat-message__textarea__input'))
        self.tbl_active_chats = Table(CssSelector(".chat-list__item"), ActiveChatsStructure)
        self.tbl_chat_content = Table(CssSelector("div.message-row:not(.message-row--system):not(.message-row--visitor--typing)"), MessageStructure)
        self.lst_system_message = AnyList(CssSelector("div.message-row--system .message-body__text"))
        self.btn_close = Button(CssSelector('.chat-message__close'))
        self.btn_car = Button(CssSelector("span.icon-car"))
        self.btn_offer = Button(CssSelector("span.icon-offer"))
        self.div_active_chat = AnyType(CssSelector(".reply-box"))
        self.btn_quick_replies = Button(CssSelector("span[data-testid='icon-qr-test']"))
        self.btn_lead = Button(Xpath(".//li[contains(@class,'anchor') and contains(text(), 'Lead')]"))
        self.btn_service_appt = Button(Xpath(".//li[contains(@class,'anchor') and contains(text(), 'Service Appt.')]"))
        self.btn_dealership = Button(Xpath(".//li[contains(@class,'anchor') and contains(text(), 'Dealership')]"))
        self.btn_copilot = AnyList(CssSelector("button[class='copilot_quick_response_keyword']"))
        self.btn_transfer_to_vr = Button(CssSelector(".chat-message__transfer-vr"))
        self.txt_current_language = TextBlock(CssSelector('[data-testid="language-selector"]'))
        self.txt_header_banner = TextBlock(Xpath('.//header/../div[1]'))
        self.img_header_avatar = Image(Xpath(".//header/../div[1]//img"))
        self.ddl_language = SelectExtended(CssSelector('[data-testid="language-selector"]'),
                                           CssSelector('[data-testid="language-selector"] [role="listbox"] [role="option"]'))
        self.lst_operator_avatars = AnyList(CssSelector(".message-row--operator img"))
        self.lst_resq_avatars = AnyList(CssSelector(".message-row--resq img"))
        self.cmp_resq_widget = ResqWidgetComponent(self)
        self.queue = ChatQueueComponent(self.ancestor)
        from src.web.chat_console.chat.lead_form_component import LeadFormComponent
        self.lead_component = LeadFormComponent(self)

        self.service_scheduling_component = ServiceSchedulingFormComponent(self)
        self.service_scheduling_success_component = ServiceSchedulingSuccessComponent(self)
        self.dealer_component = DealershipComponent(self)
        self.offers_component = OffersComponent(self)
        self.quick_reply_component = QuickRepliesComponent(self)
        self.vehicles_component = VehiclesComponent(self)
        self.txt_visitor_typing = TextBlock(CssSelector(".message-row--visitor--typing"))
        self.txt_notification = TextBlock(CssSelector('.notification.notification-info:not(.notification-hidden)'))
        self.confirm_component = ConfirmComponent(self.ancestor)

    @step_decorator('WEB - ChatComponent: Open')
    def open_actions(self):
        self.ancestor.open()
        self.ancestor.btn_chat_tab.click()

    @step_decorator('WEB - ChatComponent: Get Chat language')
    def get_chat_language(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        return self.txt_current_language.text

    @step_decorator('WEB - ChatComponent: Set {1} language')
    def set_language(self, lang, chat_id=None):
        self.open()
        self.select_chat_by_id(chat_id)
        if self.get_chat_language(chat_id) != lang:
            self.ddl_language.select_item_by_text(lang)
        self.txt_current_language.wait_for.text_equal(lang, wait_time=5)

    @step_decorator('WEB - ChatComponent: Typing a {1} message')
    def typing_message(self, text, chat_id=None):
        self.select_chat_by_id(chat_id)
        self.inp_message.send_keys(text)

    @step_decorator('WEB - ChatComponent: Whisper mode')
    def select_whisper(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        self.btn_whisper.click()
        assert "chat-message--whisper" in self.div_input_message_type.get_attribute("class")

    @step_decorator('WEB - ChatComponent: Reply mode')
    def select_reply(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        self.btn_reply.click()
        assert "chat-message--whisper" not in self.div_input_message_type.get_attribute("class")

    @step_decorator('WEB - ChatComponent: Whisper a {1} message')
    def whisper_message(self, text, chat_id=None):
        self.select_whisper(chat_id)
        self.typing_message(text)
        self.btn_send.click()
        self.select_reply()

    @step_decorator('WEB - ChatComponent: Send a {1} message in chat')
    def send_message(self, message: MessageEntity, chat_id=None):
        self.select_chat_by_id(chat_id)
        if message.text:
            for text in re.split(r'(\n)', message.text):
                if text == "\n":
                    self.inp_message.action_chains.send_keys(Keys.END). \
                        key_down(Keys.SHIFT).send_keys(Keys.ENTER).key_up(
                        Keys.SHIFT).perform()
                else:
                    self.inp_message.send_keys(text)
        if message.quick_replies:
            self.quick_reply_component.select_replies(message.quick_replies)
        if message.vehicles:
            self.vehicles_component.select_vehicles(message.vehicles)
        if message.offers:
            self.offers_component.select_offers(message.offers)
        if message.dealership_hours:
            self.dealer_component.insert_working_hours(message.dealership_hours)
        if message.dealership_info:
            self.dealer_component.insert_dealer_contact_info()
        if message.upcoming_holiday:
            self.dealer_component.insert_upcoming_holiday_hours(name=message.upcoming_holiday["name"],
                                                                date=message.upcoming_holiday["date"])
        self.btn_send.click()
        if message.quick_replies and self.confirm_component.exists(3):
            self.confirm_component.confirm("The URL in your message doesn't match the account domain.")

    @step_decorator("WEB - ChatComponent: Get Chat content")
    def get_chat_content(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        # TODO: wait for typing to disappear
        self.txt_visitor_typing.wait_for.invisibility_of_element_located(wait_time=5)
        return self.tbl_chat_content

    @step_decorator("WEB - ChatComponent: Get Chat text content")
    def get_chat_text_content(self, chat_id=None):
        text_body = self.get_chat_content(chat_id).get_column_values("body")
        assert text_body is not None, f"Chat {chat_id} is empty"
        return [text for text in text_body if text is not None]

    @step_decorator("WEB - ChatComponent: Get message by {1} pattern")
    def get_message_by_pattern(self, pattern, chat_id=None, wait_time=5):
        return self.get_chat_content(chat_id).get_row_by_column_pattern("body", pattern,
                                                                        wait_time=wait_time,
                                                                        reversed_order=True)

    @step_decorator("WEB - ChatComponent: Get message by {1} regex")
    def get_message_by_regex(self, pattern, chat_id=None):
        return self.get_chat_content(chat_id).get_row_by_column_regex("body", pattern,
                                                                      wait_time=5, reversed_order=True)

    @step_decorator("WEB - ChatComponent: Get multiple vehicle slider from chat by {1} text")
    def get_multiple_vehicle_slider(self, text, chat_id=None):
        return self.get_chat_content(chat_id).get_rows_by_attribute_pattern("several_vehicles",
                                                                            "textContent", text,
                                                                            wait_time=5, reversed_order=True)

    @step_decorator("WEB - ChatComponent: Get all vehicle stocks from slider by {1} text")
    def get_all_stocks_from_slider(self, text, chat_id=None):
        return self.get_multiple_vehicle_slider(text, chat_id)[0].several_vehicles.get_column_values("stock")

    @step_decorator('WEB - ChatComponent: Close chat')
    def close_chat(self, chat_id=None, disposition=None, auto_engage=None):
        from src.web.chat_console.chat.close_chat_component import CloseChatComponent
        self.select_chat_by_id(chat_id)
        number_of_active_chats = self.tbl_active_chats.size
        close = CloseChatComponent(self.ancestor).open()
        close.close(disposition)
        if not auto_engage:
            if number_of_active_chats == 1:
                assert self.wait_for.text_to_be_present_in_element("", wait_time=30)
                # assert self.wait_for_text_exists("You have no chats right now"), "Chat was not closed"
            else:
                assert self.tbl_active_chats.wait_for.number_of_elements(number_of_active_chats - 1, wait_time=5)

    @step_decorator('WEB - ChatComponent: Select Chat by {1} id')
    def select_chat_by_id(self, chat_id):
        self.open()
        if chat_id is None:
            Log.warning(f"Chat id was not provided")
        elif not self.div_active_chat.exists() or self.div_active_chat.get_attribute("data-id") != chat_id:
            chat = self.get_active_chat_card(chat_id, wait_time=10)
            if chat is None:
                Log.error(f"Chat id {chat_id} was not found")
                raise Exception(f"Chat id {chat_id} was not found")
            else:
                try:
                    chat.chat_id.click()
                except ElementClickInterceptedException:
                    self.txt_notification.wait_for.invisibility_of_element_located(wait_time=5)
                    chat.chat_id.click()
        Log.info(f"Chat id {chat_id} is active")

    @step_decorator('WEB - ChatComponent: Get active chats id')
    def get_active_chat_ids(self):
        return self.tbl_active_chats.get_column_values("chat_id", wait_time=3)

    @step_decorator('WEB - ChatComponent: Get active chat card {1}')
    def get_active_chat_card(self, chat_id, wait_time=5):
        self.open()
        return self.tbl_active_chats.get_row_by_column_value("chat_id", value=chat_id, wait_time=wait_time)

    @step_decorator('WEB - ChatComponent: Wait for chat to disappear ')
    def wait_for_chat_to_disappear(self, chat_id):
        try:
            self.get_active_chat_card(chat_id).chat_id.wait_for.visibility_of_element_located(until=False, wait_time=10)
        except Exception as e:
            assert self.get_active_chat_card(chat_id) is None

    @step_decorator("WEB - ChatComponent: Get System Messages")
    def get_system_messages(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        return self.lst_system_message.elements_texts

    @step_decorator("WEB - ChatComponent: Wait for System Messages")
    def get_wait_for_system_messages(self, chat_id=None, text=""):
        self.select_chat_by_id(chat_id)
        return self.lst_system_message.wait_for_element_by_regex(pattern=text, wait_time=5)

    @step_decorator("WEB - ChatComponent: Submit Lead")
    def submit_lead(self, chat_id=None, lead: WebLeadEntity = None):
        self.select_chat_by_id(chat_id)
        self.lead_component.open()
        if lead is not None:
            self.lead_component.fill_form(lead)
        self.lead_component.submit_lead()
        return self.lst_system_message

    @step_decorator("WEB - ChatComponent: Submit XTime")
    def schedule_service_appointment(self, chat_id=None, lead: WebLeadEntity = None,
                                     service_provider=ServiceProviderType.XTIME):
        self.select_chat_by_id(chat_id)
        self.service_scheduling_component.open()
        self.service_scheduling_component.fill_form(lead, service_provider=service_provider)
        self.service_scheduling_component.submit()
        assert self.service_scheduling_success_component.exists(10), "Service Scheduling Success was not displayed"
        lead.appointment_id = self.service_scheduling_success_component.get_confirmation_code()
        date = self.service_scheduling_success_component.get_appointment_time()
        self.service_scheduling_success_component.schedule_another_service()
        return lead

    @step_decorator("WEB - ChatComponent: Get phone number from Lead")
    def get_lead_phone_number(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        from src.web.chat_console.chat.lead_form_component import LeadFormComponent
        lead_component = LeadFormComponent(self).open()
        return lead_component.inp_phone.get_content()

    @step_decorator("WEB - ChatComponent: Get email from Lead")
    def get_lead_email(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        from src.web.chat_console.chat.lead_form_component import LeadFormComponent
        lead_component = LeadFormComponent(self).open()
        return lead_component.inp_email.get_content()

    @step_decorator("WEB - ChatComponent: Wait from Lead email ")
    def wait_lead_email(self, chat_id=None, value=""):
        self.select_chat_by_id(chat_id)
        from src.web.chat_console.chat.lead_form_component import LeadFormComponent
        lead_component = LeadFormComponent(self).open()
        return lead_component.inp_email.wait_for.attribute_value_to_be_present_in_element(attribute="value",
                                                                                          value=value,
                                                                                          wait_time=7)

    @step_decorator("WEB - ChatComponent: Get Vehicle Year from Lead")
    def get_lead_vehicle_year(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        from src.web.chat_console.chat.lead_form_component import LeadFormComponent
        lead_component = LeadFormComponent(self).open()
        return lead_component.inp_year.get_content()

    @step_decorator("WEB - ChatComponent: Get header banner text")
    def get_header_banner_text(self, chat_id):
        self.select_chat_by_id(chat_id)
        if self.txt_header_banner.exists(wait_time=5):
            return self.txt_header_banner.text

    @step_decorator("WEB - ChatComponent: Get header banner avatar")
    def get_header_banner_avatar(self, chat_id):
        self.select_chat_by_id(chat_id)
        if self.img_header_avatar.exists(wait_time=5):
            return self.img_header_avatar.get_attribute("src")

    @step_decorator("WEB - ChatComponent: Get Vehicle Make from Lead")
    def get_lead_vehicle_make(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        from src.web.chat_console.chat.lead_form_component import LeadFormComponent
        lead_component = LeadFormComponent(self).open()
        return lead_component.inp_make.get_content()

    @step_decorator("WEB - ChatComponent: Get Vehicle Model from Lead")
    def get_lead_vehicle_model(self, chat_id=None):
        self.select_chat_by_id(chat_id)
        from src.web.chat_console.chat.lead_form_component import LeadFormComponent
        lead_component = LeadFormComponent(self).open()
        return lead_component.inp_model.get_content()

    @step_decorator('WEB - ChatComponent: Request chat to be resq')
    def request_chat_to_be_resq(self, chat_id=None, departments=["Sales"], persons=[]):
        Log.info(f'WEB - ChatComponent: Resq {departments} department by {persons}')
        self.select_chat_by_id(chat_id)
        assert self.cmp_resq_widget.exists(5), "Chat console Resq Widget Component does not exist"
        self.cmp_resq_widget.assign_departments(departments)
        self.cmp_resq_widget.notify_persons(persons)

    @step_decorator('WEB - ChatComponent:  Accept chats from queue if it is present')
    def accept_chats_from_queue(self, wait_time=3):
        if self.queue.exists(wait_time):
            Log.warning("WEB - ChatPage: Chats are coming from queue!!!!!!!!!!!!!")
            self.queue.accept()
            if self.ancestor.btn_deals_tips_right.exists(2):
                self.ancestor.btn_deals_tips_right.click()
            return True
        return False

    @step_decorator('WEB - ChatComponent: Transfer chat to VR')
    def transfer_chat(self, chat_id=None):
        from src.web.chat_console.chat.chat_transfer_component import ChatTransferComponent
        self.select_chat_by_id(chat_id)
        transfer = ChatTransferComponent(self).open()
        transfer.accept()
