import allure

from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextBlock, CheckBox
from kom_framework.src.web.data_types.table import Table
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


class UserStructure:
    def __init__(self):
        self.name = TextBlock(CssSelector("div"))
        self.status = TextBlock(CssSelector("span.resq-status-sent"))


class DepartmentStructure:
    def __init__(self):
        self.name = TextBlock(CssSelector("label"))
        self.status = CheckBox(CssSelector("input"), attribute="checked", checked_value="true")


@find_by(CssSelector('[data-testid="glive-widget"]'))
class ResqWidgetComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        # self.chk_sales = CheckBox(CssSelector('input[data-name="Sales"]'), attribute="checked", checked_value="true")
        # self.chk_service = CheckBox(CssSelector('input[data-name="Service"]'), attribute="checked",
        #                             checked_value="true")
        # self.chk_parts = CheckBox(CssSelector('input[data-name="Parts"]'), attribute="checked", checked_value="true")
        self.tbs_dep = Table(CssSelector('[data-testid="glive-widget-departments"]'), DepartmentStructure)
        self.tbl_users = Table(CssSelector('[data-testid="glive-widget-users"]'), UserStructure)
        self.txt_selected_department = Button(CssSelector('[data-testid="glive-widget-selected-departments"]'))
        self.btn_apply = Button(CssSelector('[data-testid="glive-widget-assign-btn"]'))

    @step_decorator('WEB - ResqWidgetComponent:  Open "Resq" Component')
    def open_actions(self):
        pass

    @step_decorator('WEB - ResqWidgetComponent:  Assign department')
    def assign_departments(self, departments=["Sales"]):
        assert self.exists(3), "Chat console Resq Widget Component does not exist"
        if not self.tbs_dep.exists() or self.tbl_users.exists():
            with allure.step("Click on selected department to go back to department list"):
                self.txt_selected_department.click()
                self.tbs_dep.exists(5)
        for i in range(self.tbs_dep.size):
            department = self.tbs_dep.get_row_by_index(i)
            if department.name.text in departments:
                with allure.step(f"Select {department.name.text} department"):
                    department.status.check()
            else:
                with allure.step(f"Unselect {department.name.text} department"):
                    department.status.check(False)
        self.btn_apply.click()

    @step_decorator('WEB - ResqWidgetComponent:  Notify a person')
    def notify_persons(self, persons=[]):
        assert self.exists(3), "Chat console Resq Widget Component does not exist"
        if not self.tbl_users.exists(3) or self.tbs_dep.exists():
            self.btn_apply.click()
        for person in persons:
            with allure.step(f"Notify {person}"):
                user = self.tbl_users.get_row_by_column_pattern("name", person, wait_time=3)
                assert user, f"Person {person} was not found"
                user.name.action_chains.double_click().perform()
                assert user.status.exists(5), f"Chat was not sent to {person}"

    @step_decorator('WEB - ResqWidgetComponent:  Get assigned department list')
    def get_assigned_department_list(self, chat_id):
        self.ancestor.select_chat_by_id(chat_id)
        assert self.exists(3), "Chat console Resq Widget Component does not exist"
        assert self.txt_selected_department.exists(5), ""
        self.txt_selected_department.js.scroll_into_view()
        return self.txt_selected_department.text.split(", ")

    @step_decorator('WEB - ResqWidgetComponent:  Get mentioned person list')
    def get_mentioned_person_list(self, chat_id):
        self.ancestor.select_chat_by_id(chat_id)
        assert self.exists(3), "Chat console Resq Widget Component does not exist"
        assert self.tbl_users.exists(), ""
        return [person.name.text for person in self.tbl_users.get_rows_by_attribute_pattern("status", "Sent")]

    @step_decorator('WEB - ResqWidgetComponent:  Wait for mentioned person status')
    def wait_mentioned_person_status(self, chat_id, person):
        person = self.tbl_users.get_row_by_column_pattern("name", person)
        if person.status.exists(wait_time=5):
            return person.status.text
