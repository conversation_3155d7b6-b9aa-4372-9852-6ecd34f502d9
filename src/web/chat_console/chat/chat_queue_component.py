from kom_framework.src.web.component import Component
from kom_framework.src.web.data_types import CssSelector
from kom_framework.src.web.data_types.element_types import <PERSON><PERSON>, TextBlock
from kom_framework.src.web.support.page_factory import find_by
from src.utils.decorators import step_decorator


@find_by(CssSelector('.chat-queued-modal'))
class ChatQueueComponent(Component):
    def __init__(self, ancestor):
        super().__init__(ancestor)
        self.txt_body = TextBlock(CssSelector(".modal-body"))

        self.btn_dismiss = Button(CssSelector(".btn-secondary"))
        self.btn_accept = Button(CssSelector(".btn-primary,.modal-footer button:last-child"))

    def open_actions(self):
        pass

    @step_decorator('WEB - ChatQueueComponent: Accept chats from queue')
    def accept(self):
        self.btn_accept.click()

    @step_decorator('WEB - ChatQueueComponent: Dismiss chats from queue')
    def dismiss(self):
        self.btn_dismiss.click()
