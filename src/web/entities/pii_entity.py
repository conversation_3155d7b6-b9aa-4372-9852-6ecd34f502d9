import names

from src.utils.phone_number import PhoneNumber
from src.web.consumer import default_zip


class PIIEntity:
    def __init__(self, down_payment=None, first_name=None, last_name=None, email=None, phone=None, zip_code=None,
                 street_address=None):
        self.down_payment = down_payment
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.phone = phone
        self.zip_code = zip_code
        self.street_address = street_address

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)


class PIIFactory:

    @classmethod
    def random_pii(cls, down_payment=None, first_name=None, last_name=None, email=None, phone=None,
                   zip_code=None, street_address=None) -> PIIEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_valid_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        down_payment = "2000" if down_payment is None else down_payment
        zip_code = default_zip if not zip_code else zip_code
        street_address = "4800 T-Rex Ave suite 350Boca Raton, FL 33431, USA" if not street_address else street_address
        return PIIEntity(down_payment=down_payment, first_name=first_name, last_name=last_name, email=email,
                         phone=phone, zip_code=zip_code, street_address=street_address)
