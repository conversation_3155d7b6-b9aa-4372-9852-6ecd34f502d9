from enum import Enum

from src.web.consumer import OwnershipType


class TradeInProvider:
    KBB = "kbb"
    KBB_ICO = "kbb_ico"
    ASBURY = "asbury"
    TRADE_PENDING = "tp"
    TRADE_IN_VALET = "tiv"
    AUTO_HUB = "auto_hub"


class MechanicalIssues(Enum):
    ENGINE = {"name": "Engine issues",
              "api_key": "engine_issues"}
    TRANSMISSION = {"name": "Transmission issues",
                    "api_key": "transmission_issues"}
    LIGHTS = {"name": "Dashboard warning lights",
              "api_key": "dashboard_warning_lights"}
    AIR_CONDITIONER = {"name": "A/C doesn't blow cold air",
                       "api_key": "ac_cold_air"}


class Accidents:
    def __init__(self, airbags_deployed: bool = None, repair_costs: str = None):
        self.airbags_deployed = airbags_deployed
        self.repair_costs = repair_costs


class Lender:
    def __init__(self, lender: str = None, ssn: str = None):
        self.lender = lender
        self.ssn = ssn


class TradeInEntity:
    def __init__(self, year=None, make=None, model=None, trim=None, mileage=None, accidents: Accidents = None,
                 mechanical_issues: list[MechanicalIssues] = None, condition: str = '', ownership=None, loan=None,
                 license_plate=None, state=None, vin="", manual_estimation=None, lender: Lender = None, body=None,
                 transmission=None, engine=None, driveline=None, continue_without_trade_in: bool = False):
        self.year = year
        self.make = make
        self.model = model
        self.trim = trim
        self.mileage = mileage
        self.condition = condition
        self.accidents = accidents
        self.mechanical_issues = mechanical_issues
        self.ownership = ownership
        self.loan = loan
        self.license_plate = license_plate
        self.state = state
        self.vin = vin
        self.manual_estimation = manual_estimation
        self.lender = lender
        self.body = body
        self.transmission = transmission
        self.engine = engine
        self.driveline = driveline
        self.continue_without_trade_in = continue_without_trade_in

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)


class TradeInEntityFactory:

    @classmethod
    def get_asbury(cls) -> TradeInEntity:
        return TradeInEntity(license_plate="GUBAHON", state="Florida", year="2016", mileage=25000, make="Honda",
                             model="Accord Sedan", ownership=OwnershipType.OWNED)

    @classmethod
    def get_asbury_with_issues(cls) -> TradeInEntity:
        return TradeInEntity(license_plate="GUBAHON", state="Florida", year="2016", mileage=25000, make="Honda",
                             model="ACCORD 4C", trim="4D SEDAN SPORT", ownership=OwnershipType.OWNED,
                             mechanical_issues=[MechanicalIssues.ENGINE, MechanicalIssues.TRANSMISSION,
                                                MechanicalIssues.LIGHTS],
                             condition="Good", vin="1HGCR2F58GA181100",
                             accidents=Accidents(airbags_deployed=True, repair_costs="1000"))

    @classmethod
    def get_auto_hub(cls) -> TradeInEntity:
        return TradeInEntity(year="2022", make="Honda", model="Accord", trim="EX-L",
                             mileage="150000", ownership=OwnershipType.OWNED)


    @classmethod
    def get_trade_in_valet(cls) -> TradeInEntity:
        return TradeInEntity(year="2009", make="Mazda", model="Mazda6", trim="i Touring", body="4dr Sdn Man",
                             transmission="Automatic", engine="3.7L V6 Cylinder Engine Gasoline Fuel 272 @ 6250",
                             driveline="Front Wheel Drive", mileage="12000", ownership=OwnershipType.OWNED)
