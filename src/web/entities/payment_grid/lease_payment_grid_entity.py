class LeasePaymentGridEntity:
    def __init__(self,
                 monthly_payment: float = None,
                 lender: str = None,
                 term: str = None,
                 rate: str = None,
                 down_payment: str = None,
                 mileage: str = None
                 ):
        self.monthly_payment = monthly_payment
        self.lender = lender
        self.term = term
        self.rate = rate
        self.down_payment = down_payment
        self.mileage = mileage

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        if isinstance(other, LeasePaymentGridEntity):
            return all(getattr(self, attr) == getattr(other, attr) for attr in vars(self))
        return False

    def __lt__(self, other):
        if isinstance(other, LeasePaymentGridEntity):
            return all(getattr(self, attr) < getattr(other, attr) for attr in vars(self))
        raise False

    def __gt__(self, other):
        if isinstance(other, LeasePaymentGridEntity):
            return all(getattr(self, attr) > getattr(other, attr) for attr in vars(self))
        raise False

