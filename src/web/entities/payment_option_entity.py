class PaymentOptionEntity:
    def __init__(self,
                 price: int = None,
                 term: str = None,
                 fin_inst: str = None
                 ):
        self.price = price
        self.term = term
        self.fin_inst = fin_inst

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        """Overrides the default implementation"""
        return self.term == other.term and self.fin_inst == other.fin_inst
