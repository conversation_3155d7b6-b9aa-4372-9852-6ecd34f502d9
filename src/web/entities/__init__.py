import datetime as DT

import dateutil.relativedelta as REL

from kom_framework.src.general import non_zero_padded

WorkingHoursRange48 = [f"{(DT.datetime.now() + DT.timedelta(hours=i)).strftime('%A')} {(DT.datetime.now() + DT.timedelta(hours=i)).hour}:00" \
                       for i in range(1, 48) \
                       if (DT.datetime.now() + DT.timedelta(hours=i)).weekday() < 6 \
                       and 9 <= (DT.datetime.now() + DT.timedelta(hours=i)).hour < 18]
WorkingHoursRange48Plus = [[f"{(DT.datetime.now() + DT.timedelta(hours=i)).strftime('%A')} {(DT.datetime.now() + DT.timedelta(hours=i)).hour}:00",
                            (DT.datetime.now() + DT.timedelta(hours=i)).strftime(f'%Y-%m-%d %{non_zero_padded}I:00%p').lower()]\
                       for i in range(48, 100) \
                       if (DT.datetime.now() + DT.timedelta(hours=i)).weekday() < 6 \
                       and 9 <= (DT.datetime.now() + DT.timedelta(hours=i)).hour < 18]


def is_sunday():
    return 3 if (DT.date.today()+REL.relativedelta(days=3)).weekday() != 6 else 4


class AppointmentToSend:
    TOMORROW = "at 5pm tomorrow"
    NEXT_FRIDAY = "3:30pm next friday"

    SUNDAY = "sunday 2pm"
    WEDNESDAY = "Wed 1pm"
    MORNING = (DT.date.today()+REL.relativedelta(days=is_sunday())).strftime('%A')
    IN_3_4_DAYS_0900 = (DT.date.today()+REL.relativedelta(days=is_sunday())).strftime('%A') + " "
    YESTERDAY = f"{(DT.date.today() - DT.timedelta(days=1 if DT.date.today().weekday()!=0 else 2)).strftime('%A')} 1pm"
    IN48HOURS = WorkingHoursRange48[-2]
    IN48PLUSHOURS = WorkingHoursRange48Plus[3][0]


AppointmentExpected = {
    AppointmentToSend.TOMORROW: [f"8:00am on Monday"] if DT.date.today().strftime("%a") == 'Sat' else [f"{hour}:{minutes}pm tomorrow" for hour in ["3", "4", "5", "6", "7"] for minutes in ["00", "15", "30", "45"]],
    AppointmentToSend.WEDNESDAY: f"{(DT.date.today()+REL.relativedelta(days=1, weekday=REL.WE)).strftime('%Y-%m-%d')} 1:00pm", # TODO: handle  situation when test is running on Wednesday afternoon
    AppointmentToSend.YESTERDAY: f"{(DT.date.today()+REL.relativedelta(days=6 if DT.date.today().weekday()!=0 else 5)).strftime('%Y-%m-%d')} 1:00pm", # TODO: handle  situation when test is running on Wednesday afternoon
    AppointmentToSend.NEXT_FRIDAY: [f"{hour}:{minutes}pm on Friday ({(DT.date.today()+REL.relativedelta(days=1, weekday=REL.FR)).strftime('%b %d')})" if (DT.date.today() + DT.timedelta(days=1)).strftime("%a") != 'Fri' else f"{hour}:{minutes}pm tomorrow" for hour in ["12", "1", "2", "3", "4", "5"] for minutes in ["00", "15", "30", "45"]],
    AppointmentToSend.SUNDAY: [(DT.date.today()+REL.relativedelta(days=1, weekday=REL.SU)-REL.relativedelta(days=1)).strftime('%b %d') if DT.date.today().strftime("%a") != 'Fri' else "tomorrow",
                               (DT.date.today()+REL.relativedelta(days=1, weekday=REL.SU)).strftime('%b %d'),
                               (DT.date.today()+REL.relativedelta(days=1, weekday=REL.SU)+REL.relativedelta(days=1)).strftime('%b %d')],
    AppointmentToSend.IN_3_4_DAYS_0900: (DT.date.today()+REL.relativedelta(days=is_sunday())).strftime('%Y-%m-%d 09:00'),
    AppointmentToSend.IN48HOURS: None,
    AppointmentToSend.IN48PLUSHOURS: WorkingHoursRange48Plus[3][1],
    AppointmentToSend.MORNING: (DT.date.today()+REL.relativedelta(days=is_sunday())).strftime('%Y-%m-%d 9:00am'),
}

dealership_working_hours = "Sales\nMon-Sat:\n9:00am - 6:00pm\nService\nMon-Sat:\n9:00am - 6:00pm\nParts\nTue:" \
                          "\n9:00am - 6:00pm\nThu-Fri:\n9:00am - 6:00pm"
