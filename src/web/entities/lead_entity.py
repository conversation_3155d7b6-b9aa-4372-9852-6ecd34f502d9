import json
import re
import random

import names

from kom_framework.src.general import Log
from src.rest_api.entities.leads.lead_entity import LeadEntity
from src.utils.phone_number import PhoneNumber
from src.web.chat_console import VehicleType, LeadType
from src.web.consumer import condition_type_to_kbb, ServiceProviderType
from src.web.entities import AppointmentExpected


class WebLeadEntity:
    def __init__(self, first_name=None,
                 last_name=None,
                 email=None,
                 email_to_send=None,
                 phone=None,
                 phone_to_send=None,
                 appointment_date=None,
                 appointment_time=None,
                 zip=None,
                 vehicle_type: VehicleType.NEW = None,
                 stock_number=None,
                 vehicle_year=None,
                 vehicle_make=None,
                 vehicle_model=None,
                 vehicle_vin=None,
                 department=None,
                 lead_type: LeadType = None,
                 notes=None,
                 transport_type=None,
                 appointment_services=None,
                 appointment=None,
                 appointment_id=None,
                 appointment_to_send=None,
                 appointment_services_to_send=None,
                 custom_type=None,
                 appointment_timestamp=None,
                 trade_in_trim=None,
                 trade_in_drivetrain=None,
                 trade_in_engine=None,
                 trade_in_body_type=None,
                 trade_in_year=None,
                 trade_in_model=None,
                 trade_in_make=None,
                 trade_in_mileage=None,
                 trade_in_condition=None,
                 trade_in_estimate_low=None,
                 trade_in_estimate_high=None,
                 trade_in_value=None,
                 recall_number=None):
        self.first_name = first_name
        self.last_name = last_name
        self.email = email.lower() if email else None
        self.email_to_send = email_to_send
        self.phone = phone
        self.phone_to_send = phone_to_send
        self.appointment_date = appointment_date
        self.appointment_time = appointment_time
        self.zip = zip
        self.vehicle_type = vehicle_type
        self.stock_number = stock_number
        self.vehicle_year = vehicle_year
        self.vehicle_make = vehicle_make
        self.vehicle_model = vehicle_model
        self.vehicle_vin = vehicle_vin
        self.department = department
        self.lead_type = lead_type
        self.recall_number = recall_number
        self.notes = notes
        self.transport_type = transport_type
        self.appointment_services = tuple(appointment_services) if isinstance(appointment_services,
                                                                              list) else appointment_services
        self.appointment_id = appointment_id
        self.appointment = appointment
        self.appointment_to_send = appointment_to_send
        self.appointment_timestamp = appointment_timestamp
        self.appointment_services_to_send = appointment_services_to_send
        self.custom_type = custom_type
        self.trade_in_trim = trade_in_trim
        self.trade_in_year = trade_in_year
        self.trade_in_model = trade_in_model
        self.trade_in_drivetrain = trade_in_drivetrain
        self.trade_in_engine = trade_in_engine
        self.trade_in_body_type = trade_in_body_type
        self.trade_in_make = trade_in_make
        self.trade_in_mileage = trade_in_mileage
        self.trade_in_condition = trade_in_condition
        self.trade_in_estimate_low = trade_in_estimate_low
        self.trade_in_estimate_high = trade_in_estimate_high
        self.trade_in_value = trade_in_value

    def __get_exclude_keys__(self, other):
        if isinstance(other, self.__class__):
            exclude = ["notes", "zip", "phone_to_send", "email_to_send", "appointment_to_send", "custom_type",
                       "appointment_services_to_send", 'trade_in_estimate_low', 'trade_in_value',
                       'trade_in_estimate_high', "trade_in_drivetrain", "trade_in_engine", "trade_in_body_type"]
            if self.custom_type == ServiceProviderType.XTIME and \
                    ((other.appointment is None and self.appointment is None)
                     or (self.appointment and other.appointment and
                         (other.appointment in self.appointment if isinstance(self.appointment, list)
                         else self.appointment in other.appointment))):
                exclude.append("appointment")
            elif self.custom_type == ServiceProviderType.SERVICEPORTAL and \
                    other.custom_type == ServiceProviderType.SERVICEPORTAL and \
                    self.transport_type.startswith(other.transport_type):
                exclude.append("transport_type")
            elif self.custom_type == ServiceProviderType.GM and other.custom_type == ServiceProviderType.GM:
                exclude.append("transport_type")
            elif self.custom_type == "Test-Drive":
                exclude.append('custom_type')
            if other.appointment_timestamp or self.appointment_timestamp:  # TODO: delete after bug is fixed
                exclude.append('appointment_time')
            if not ((self.notes is None and other.notes is None)
                    or (self.notes and other.notes and (self.notes in other.notes or other.notes in self.notes))):
                exclude.remove("notes")
            if isinstance(self.appointment_services, tuple) and isinstance(other.appointment_services, str) and \
                    all([service in other.appointment_services for service in self.appointment_services]):
                exclude.append("appointment_services")
            elif isinstance(other.appointment_services, tuple) and isinstance(self.appointment_services, str) and \
                    all([service in self.appointment_services for service in other.appointment_services]):
                exclude.append("appointment_services")
            return exclude

    def no_trade_in(self):
        import copy
        no_trade_in = copy.copy(self)
        no_trade_in.trade_in_trim = None
        no_trade_in.trade_in_year = None
        no_trade_in.trade_in_model = None
        no_trade_in.trade_in_drivetrain = None
        no_trade_in.trade_in_engine = None
        no_trade_in.trade_in_body_type = None
        no_trade_in.trade_in_make = None
        no_trade_in.trade_in_mileage = None
        no_trade_in.trade_in_condition = None
        no_trade_in.trade_in_estimate_low = None
        no_trade_in.trade_in_estimate_high = None
        no_trade_in.trade_in_value = None
        return no_trade_in

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        keys_to_remove = ['first_name',  'last_name', 'email', 'phone']
        dict_str = self.__dict__.copy()

        for key in keys_to_remove:
            dict_str.pop(key, None)
        return str(dict_str)
        #return str(self.__dict__)

    def __sub__(self, other):
        if isinstance(other, self.__class__):
            exclude = self.__get_exclude_keys__(other)
            this = self.get_value(exclude)
            that = other.get_value(exclude)
            return this.items() - that.items()

    def __eq__(self, other):
        """Overrides the default implementation"""
        if isinstance(other, self.__class__):
            exclude = self.__get_exclude_keys__(other)
            this = self.get_value(exclude)
            that = other.get_value(exclude)
            diff = this.items() ^ that.items()
            if len(diff) == 0:
                return True
            Log.debug(f"Difference: expected {this.items() - that.items()} does not mach received {that.items() - this.items()}")
            return False

    def get_value(self, exclude):
        if self.custom_type and self.custom_type == "KBB":
            if self.trade_in_condition:
                self.trade_in_condition = condition_type_to_kbb(self.trade_in_condition)
            if self.trade_in_body_type:
                self.trade_in_model = self.trade_in_body_type
                self.trade_in_body_type = None
        if self.trade_in_trim and self.trade_in_body_type:
            self.trade_in_trim += " " + self.trade_in_body_type
            self.trade_in_body_type = None
        if self.trade_in_trim and self.trade_in_engine:
            self.trade_in_trim += " " + self.trade_in_engine
            self.trade_in_engine = None
        if self.trade_in_trim and self.trade_in_drivetrain:
            self.trade_in_trim += " " + self.trade_in_drivetrain
            self.trade_in_drivetrain = None
        if self.appointment_services and len(self.appointment_services) == 1:
            self.appointment_services = re.sub(r"\s\s+", " ", self.appointment_services[0])
        return {k: self.__dict__[k].lower() if self.__dict__[k] is not None and isinstance(self.__dict__[k], str)
                else self.__dict__[k] for k in self.__dict__ if k not in exclude}


class WebLeadFactory:

    @classmethod
    def create(cls,
               first_name=None,
               last_name=None,
               email=None,
               email_to_send=None,
               phone=None,
               phone_to_send=None,
               appointment_date=None,
               appointment_time=None,
               zip=None,
               vehicle_type: VehicleType.NEW = None,
               stock_number=None,
               vehicle_year=None,
               vehicle_make=None,
               vehicle_model=None,
               vehicle_vin=None,
               department=None,
               lead_type: LeadType = None,
               notes=None,
               transport_type=None,
               appointment_services=None,
               appointment=None,
               appointment_id=None,
               appointment_to_send=None,
               appointment_services_to_send=None,
               appointment_timestamp=None,
               custom_type=None,
               trade_in_trim=None,
               trade_in_drivetrain=None,
               trade_in_engine=None,
               trade_in_body_type=None,
               trade_in_year=None,
               trade_in_model=None,
               trade_in_make=None,
               trade_in_mileage=None,
               trade_in_condition=None,
               trade_in_estimate_low=None,
               trade_in_estimate_high=None,
               trade_in_value=None
               ) -> WebLeadEntity:
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             email_to_send=email_to_send,
                             phone=phone,
                             phone_to_send=phone_to_send,
                             appointment_date=appointment_date,
                             appointment_time=appointment_time,
                             zip=zip,
                             vehicle_type=vehicle_type,
                             stock_number=stock_number,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             vehicle_vin=vehicle_vin,
                             department=department,
                             lead_type=lead_type,
                             notes=notes,
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             appointment=appointment,
                             appointment_id=appointment_id,
                             appointment_to_send=appointment_to_send,
                             appointment_services_to_send=appointment_services_to_send,
                             custom_type=custom_type,
                             appointment_timestamp=appointment_timestamp,
                             trade_in_trim=trade_in_trim,
                             trade_in_year=trade_in_year,
                             trade_in_model=trade_in_model,
                             trade_in_make=trade_in_make,
                             trade_in_drivetrain=trade_in_drivetrain,
                             trade_in_engine=trade_in_engine,
                             trade_in_body_type=trade_in_body_type,
                             trade_in_mileage=trade_in_mileage,
                             trade_in_condition=trade_in_condition,
                             trade_in_estimate_low=trade_in_estimate_low,
                             trade_in_estimate_high=trade_in_estimate_high,
                             trade_in_value=trade_in_value)

    @classmethod
    def random_lead(cls,
                    first_name=None,
                    last_name=None,
                    email=None,
                    phone=None,
                    email_to_send=None,
                    phone_to_send=None,
                    appointment_date=None,
                    appointment_time=None,
                    zip=None,
                    vehicle_type: VehicleType.NEW = None,
                    stock_number=None,
                    vehicle_year=None,
                    vehicle_make=None,
                    vehicle_model=None,
                    vehicle_vin=None,
                    department=None,
                    lead_type: LeadType = None,
                    notes=None,
                    transport_type=None,
                    appointment_services=None,
                    appointment=None,
                    appointment_id=None,
                    appointment_to_send=None,
                    appointment_services_to_send=None,
                    custom_type=None,
                    trade_in_drivetrain=None,
                    trade_in_trim=None,
                    trade_in_engine=None,
                    trade_in_body_type=None,
                    trade_in_year=None,
                    trade_in_model=None,
                    trade_in_make=None,
                    trade_in_mileage=None,
                    trade_in_condition=None,
                    trade_in_estimate_low=None,
                    trade_in_estimate_high=None,
                    trade_in_value=None) -> WebLeadEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             email_to_send=email_to_send,
                             phone=phone,
                             phone_to_send=phone_to_send,
                             appointment_date=appointment_date,
                             appointment_time=appointment_time,
                             zip=zip,
                             vehicle_type=vehicle_type,
                             stock_number=stock_number,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             vehicle_vin=vehicle_vin,
                             department=department,
                             lead_type=lead_type,
                             notes=notes,
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             appointment=appointment,
                             appointment_id=appointment_id,
                             appointment_to_send=appointment_to_send,
                             appointment_services_to_send=appointment_services_to_send,
                             custom_type=custom_type,
                             trade_in_trim=trade_in_trim,
                             trade_in_drivetrain=trade_in_drivetrain,
                             trade_in_engine=trade_in_engine,
                             trade_in_body_type=trade_in_body_type,
                             trade_in_year=trade_in_year,
                             trade_in_model=trade_in_model,
                             trade_in_make=trade_in_make,
                             trade_in_mileage=trade_in_mileage,
                             trade_in_condition=trade_in_condition,
                             trade_in_estimate_low=trade_in_estimate_low,
                             trade_in_estimate_high=trade_in_estimate_high,
                             trade_in_value=trade_in_value)

    @classmethod
    def random_lead_valid_phone(cls,
                    first_name=None,
                    last_name=None,
                    email=None,
                    phone=None,
                    email_to_send=None,
                    phone_to_send=None,
                    appointment_date=None,
                    appointment_time=None,
                    zip=None,
                    vehicle_type: VehicleType.NEW = None,
                    stock_number=None,
                    vehicle_year=None,
                    vehicle_make=None,
                    vehicle_model=None,
                    vehicle_vin=None,
                    department=None,
                    lead_type: LeadType = None,
                    notes=None,
                    transport_type=None,
                    appointment_services=None,
                    appointment_timestamp = None,
                    appointment=None,
                    appointment_id=None,
                    appointment_to_send=None,
                    appointment_services_to_send=None,
                    custom_type=None,
                    trade_in_drivetrain=None,
                    trade_in_trim=None,
                    trade_in_engine=None,
                    trade_in_body_type=None,
                    trade_in_year=None,
                    trade_in_model=None,
                    trade_in_make=None,
                    trade_in_mileage=None,
                    trade_in_condition=None,
                    trade_in_estimate_low=None,
                    trade_in_estimate_high=None,
                    trade_in_value=None) -> WebLeadEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_valid_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             email_to_send=email_to_send,
                             phone=phone,
                             phone_to_send=phone_to_send,
                             appointment_date=appointment_date,
                             appointment_time=appointment_time,
                             zip=zip,
                             vehicle_type=vehicle_type,
                             stock_number=stock_number,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             vehicle_vin=vehicle_vin,
                             department=department,
                             lead_type=lead_type,
                             notes=notes,
                             transport_type=transport_type,
                             appointment_timestamp=appointment_timestamp,
                             appointment_services=appointment_services,
                             appointment=appointment,
                             appointment_id=appointment_id,
                             appointment_to_send=appointment_to_send,
                             appointment_services_to_send=appointment_services_to_send,
                             custom_type=custom_type,
                             trade_in_trim=trade_in_trim,
                             trade_in_drivetrain=trade_in_drivetrain,
                             trade_in_engine=trade_in_engine,
                             trade_in_body_type=trade_in_body_type,
                             trade_in_year=trade_in_year,
                             trade_in_model=trade_in_model,
                             trade_in_make=trade_in_make,
                             trade_in_mileage=trade_in_mileage,
                             trade_in_condition=trade_in_condition,
                             trade_in_estimate_low=trade_in_estimate_low,
                             trade_in_estimate_high=trade_in_estimate_high,
                             trade_in_value=trade_in_value)


    @classmethod
    def vr_mock_zip_single_country(cls,
                    first_name=None,
                    last_name=None,
                    email=None,
                    phone="9092459967",
                    email_to_send=None,
                    phone_to_send=None,
                    appointment_date=None,
                    appointment_time=None,
                    zip="33431",#98125
                    vehicle_type: VehicleType.NEW = None,
                    stock_number=None,
                    vehicle_year=None,
                    vehicle_make=None,
                    vehicle_model=None,
                    vehicle_vin=None,
                    department=None,
                    lead_type: LeadType = None,
                    notes=None,
                    transport_type=None,
                    appointment_services=None,
                    appointment=None,
                    appointment_id=None,
                    appointment_to_send=None,
                    appointment_services_to_send=None,
                    custom_type=None,
                    trade_in_drivetrain=None,
                    trade_in_trim=None,
                    trade_in_engine=None,
                    trade_in_body_type=None,
                    trade_in_year=None,
                    trade_in_model=None,
                    trade_in_make=None,
                    trade_in_mileage=None,
                    trade_in_condition=None,
                    trade_in_estimate_low=None,
                    trade_in_estimate_high=None,
                    trade_in_value=None) -> WebLeadEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             email_to_send=email_to_send,
                             phone=phone,
                             phone_to_send=phone_to_send,
                             appointment_date=appointment_date,
                             appointment_time=appointment_time,
                             zip=zip,
                             vehicle_type=vehicle_type,
                             stock_number=stock_number,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             vehicle_vin=vehicle_vin,
                             department=department,
                             lead_type=lead_type,
                             notes=notes,
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             appointment=appointment,
                             appointment_id=appointment_id,
                             appointment_to_send=appointment_to_send,
                             appointment_services_to_send=appointment_services_to_send,
                             custom_type=custom_type,
                             trade_in_trim=trade_in_trim,
                             trade_in_drivetrain=trade_in_drivetrain,
                             trade_in_engine=trade_in_engine,
                             trade_in_body_type=trade_in_body_type,
                             trade_in_year=trade_in_year,
                             trade_in_model=trade_in_model,
                             trade_in_make=trade_in_make,
                             trade_in_mileage=trade_in_mileage,
                             trade_in_condition=trade_in_condition,
                             trade_in_estimate_low=trade_in_estimate_low,
                             trade_in_estimate_high=trade_in_estimate_high,
                             trade_in_value=trade_in_value)


    @classmethod
    def xtime(cls,
              first_name=None,
              last_name=None,
              email=None,
              phone=None,
              vehicle_year=None,
              vehicle_make=None,
              vehicle_model=None,
              notes=None,
              transport_type=None,
              appointment_services=[],
              appointment=None) -> WebLeadEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             phone=phone,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             lead_type=LeadType.SERVICE,
                             notes=notes,
                             department='Service',
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             custom_type=ServiceProviderType.XTIME,
                             appointment=appointment)

    @classmethod
    def focus_leads(cls,
                    first_name="GubagooIntegrationTest",
                    last_name="DoNotEdit",
                    email='<EMAIL>',
                    phone='**********',
                    vehicle_year="2017",
                    vehicle_make="Nissan",
                    vehicle_model="Rogue Sport",
                    trade_in_trim="SL Sport Utility 4D",
                    trade_in_mileage="15751",
                    trade_in_condition="Very Good",
                    notes=None,
                    transport_type=None,
                    appointment_services=[],
                    appointment=None) -> WebLeadEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             phone=phone,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             trade_in_trim=trade_in_trim,
                             trade_in_mileage=trade_in_mileage,
                             trade_in_condition=trade_in_condition,
                             lead_type=LeadType.SERVICE,
                             notes=notes,
                             department='Service',
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             custom_type=ServiceProviderType.XTIME,
                             appointment=appointment)

    @classmethod
    def service_appointment_valid_phone(cls,
                                        first_name=None,
                                        last_name=None,
                                        email=None,
                                        phone=None,
                                        vehicle_year=None,
                                        vehicle_make=None,
                                        vehicle_model=None,
                                        notes=None,
                                        transport_type=None,
                                        appointment_services=[],
                                        appointment=None,
                                        custom_type=ServiceProviderType.XTIME) -> WebLeadEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_valid_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             phone=phone,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             lead_type=LeadType.SERVICE,
                             notes=notes,
                             department='Service',
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             custom_type=custom_type,
                             appointment=appointment)

    @classmethod
    def test_drive(cls,
                   first_name=None,
                   last_name=None,
                   email=None,
                   phone=None,
                   vehicle_year=None,
                   vehicle_make=None,
                   vehicle_model=None,
                   vehicle_type=None,
                   vehicle_vin=None,
                   stock_number=None,
                   appointment=None,
                   appointment_to_send=None,
                   notes=None,
                   zip=None) -> WebLeadEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        if appointment_to_send:
            appointment = AppointmentExpected[appointment_to_send]
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             phone=phone,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             vehicle_vin=vehicle_vin,
                             vehicle_type=vehicle_type,
                             stock_number=stock_number,
                             lead_type=LeadType.SALES.capitalize(),
                             department='Sales',
                             custom_type="Test-Drive",
                             appointment=appointment,
                             appointment_to_send=appointment_to_send,
                             notes=notes,
                             zip=zip)

    @classmethod
    def recall(cls,
               first_name=None,
               last_name=None,
               email=None,
               phone=None,
               vehicle_year=None,
               vehicle_make=None,
               vehicle_model=None,
               vehicle_vin=None,
               stock_number=None,
               notes=None,
               recall_number=None) -> WebLeadEntity:
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        recall_number = str(random.randint(100000, 99999999)) if recall_number is None else recall_number
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             phone=phone,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             vehicle_vin=vehicle_vin,
                             lead_type=LeadType.SERVICE.capitalize(),
                             recall_number=recall_number,
                             department=LeadType.SERVICE.capitalize(),
                             notes=notes,
                             stock_number=stock_number,
                             custom_type="Recall")

    @classmethod
    def xtime_bot(cls,
                  first_name=None,
                  last_name=None,
                  email=None,
                  email_to_send=None,
                  phone=None,
                  phone_to_send=None,
                  vehicle_year=None,
                  vehicle_make=None,
                  vehicle_model=None,
                  transport_type=None,
                  appointment=None,
                  appointment_services=[],
                  appointment_services_to_send=None,
                  appointment_to_send=None,
                  notes=None,
                  zip=None) -> WebLeadEntity:
        #Log.debug(f"!!! WebLeadFactory xtime_bot {appointment_services}")
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        if appointment_to_send and appointment_to_send in AppointmentExpected.keys():
            appointment = AppointmentExpected[appointment_to_send]
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             email_to_send=email_to_send,
                             phone=phone,
                             phone_to_send=phone_to_send,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             lead_type=LeadType.SERVICE,
                             department='Service',
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             custom_type=ServiceProviderType.XTIME,
                             appointment=appointment,
                             appointment_to_send=appointment_to_send,
                             appointment_services_to_send=appointment_services_to_send,
                             notes=notes,
                             zip=zip
                             )

    @classmethod
    def xtime_bot_mocked(cls,
                         email=None,
                         email_to_send=None,
                         phone=None,
                         phone_to_send=None,
                         vehicle_year=None,
                         vehicle_make=None,
                         vehicle_model=None,
                         transport_type=None,
                         appointment=None,
                         appointment_services=[],
                         appointment_services_to_send=None,
                         appointment_to_send=None,
                         notes=None) -> WebLeadEntity:
        first_name = "Natalia"
        last_name = "Gubagova"
        if appointment_to_send and appointment_to_send in AppointmentExpected.keys():
            appointment = AppointmentExpected[appointment_to_send]
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        phone = PhoneNumber.new_phone() if phone is None else phone
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             email_to_send=email_to_send,
                             phone=phone,
                             phone_to_send=phone_to_send,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             lead_type=LeadType.SERVICE,
                             department='Service',
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             custom_type=ServiceProviderType.XTIME,
                             appointment=appointment,
                             appointment_to_send=appointment_to_send,
                             appointment_services_to_send=appointment_services_to_send,
                             notes=notes)

    @classmethod
    def deserialize(cls, lead: LeadEntity) -> WebLeadEntity:
        return WebLeadEntity(first_name=lead.first_name,
                             last_name=lead.last_name,
                             email=lead.email,
                             phone=lead.phone,
                             appointment_date=lead.meta.appointment_date,
                             appointment_time=lead.meta.appointment_time.replace("am", " AM").replace("pm",
                                                                                                      " PM") if lead.meta.appointment_time else lead.meta.appointment_time,
                             zip=lead.zip,
                             vehicle_type=lead.meta.vehicle_type,
                             stock_number=lead.meta.stock_number,
                             vehicle_year=lead.meta.vehicle_year,
                             vehicle_make=lead.meta.vehicle_make,
                             vehicle_model=lead.meta.vehicle_model,
                             vehicle_vin=lead.meta.vehicle_vin,
                             department=lead.department,
                             lead_type=lead.meta.lead_type,
                             notes=lead.meta.notes,
                             transport_type=lead.meta.appointment_transport,
                             appointment_services=lead.meta.appointment_services,
                             appointment=lead.meta.appointment,
                             appointment_id=lead.meta.appointment_id,
                             appointment_timestamp=lead.meta.appointment_timestamp,
                             trade_in_trim=json.loads(lead.meta.trade_in).get('trim') if lead.meta.trade_in else None,
                             trade_in_year=json.loads(lead.meta.trade_in).get('year') if lead.meta.trade_in else None,
                             trade_in_model=json.loads(lead.meta.trade_in).get('model') if lead.meta.trade_in else None,
                             trade_in_make=json.loads(lead.meta.trade_in).get('make') if lead.meta.trade_in else None,
                             trade_in_mileage=json.loads(lead.meta.trade_in).get(
                                 'mileage') if lead.meta.trade_in else None,
                             trade_in_condition=json.loads(lead.meta.trade_in).get(
                                 'condition') if lead.meta.trade_in else None,
                             trade_in_estimate_low=json.loads(lead.meta.trade_in).get(
                                 'estimate_low') if lead.meta.trade_in else None,
                             trade_in_estimate_high=json.loads(lead.meta.trade_in).get(
                                 'estimate_high') if lead.meta.trade_in else None,
                             trade_in_value=json.loads(lead.meta.trade_in).get('value') if lead.meta.trade_in else None,
                             recall_number=lead.meta.recall_number if lead.meta.recall_number else None)

    @classmethod
    def deserialize_to_xtime(cls, lead: LeadEntity) -> WebLeadEntity:
        return WebLeadEntity(first_name=lead.first_name,
                             last_name=lead.last_name,
                             email=lead.email,
                             phone=lead.phone,
                             appointment_date=lead.meta.appointment_date,
                             appointment_time=lead.meta.appointment_time.replace("am", " AM").replace("pm", " PM")
                             if lead.meta.appointment_time else lead.meta.appointment_time,
                             zip=lead.zip if lead.zip else None,
                             vehicle_type=lead.meta.vehicle_type,
                             stock_number=lead.meta.stock_number,
                             vehicle_year=lead.meta.vehicle_year,
                             vehicle_make=lead.meta.vehicle_make,
                             vehicle_model=lead.meta.vehicle_model,
                             department=lead.department,
                             lead_type=lead.meta.lead_type.lower(),
                             notes=lead.meta.notes,
                             transport_type=lead.meta.appointment_transport,
                             appointment_services=lead.meta.appointment_services,
                             # .split(", ") if lead.meta.appointment_services else None
                             appointment=lead.meta.appointment,
                             appointment_id=lead.meta.appointment_id,
                             appointment_timestamp=lead.meta.appointment_timestamp,
                             custom_type=ServiceProviderType.XTIME)

    @classmethod
    def deserialize_to_gm(cls, lead: LeadEntity) -> WebLeadEntity:
        return WebLeadEntity(first_name=lead.first_name,
                             last_name=lead.last_name,
                             email=lead.email,
                             phone=lead.phone,
                             appointment_date=lead.meta.appointment_date,
                             appointment_time=lead.meta.appointment_time.replace("am", " AM").replace("pm", " PM")
                             if lead.meta.appointment_time else lead.meta.appointment_time,
                             zip=lead.zip if lead.zip else None,
                             vehicle_type=lead.meta.vehicle_type,
                             stock_number=lead.meta.stock_number,
                             vehicle_year=lead.meta.vehicle_year,
                             vehicle_make=lead.meta.vehicle_make,
                             vehicle_model=lead.meta.vehicle_model,
                             department=lead.department,
                             lead_type=lead.meta.lead_type.lower(),
                             notes=lead.meta.notes,
                             transport_type=lead.meta.appointment_transport,
                             appointment_services=lead.meta.appointment_services,
                             # .split(", ") if lead.meta.appointment_services else None
                             appointment=lead.meta.appointment,
                             appointment_id=lead.meta.appointment_id,
                             appointment_timestamp=lead.meta.appointment_timestamp,
                             custom_type=ServiceProviderType.GM)

    @classmethod
    def deserialize_to_service_portal(cls, lead: LeadEntity) -> WebLeadEntity:
        return WebLeadEntity(first_name=lead.first_name,
                             last_name=lead.last_name,
                             email=lead.email,
                             phone=lead.phone,
                             appointment_date=lead.meta.appointment_date,
                             appointment_time=lead.meta.appointment_time.replace("am", " AM").replace("pm", " PM")
                             if lead.meta.appointment_time else lead.meta.appointment_time,
                             zip=lead.zip if lead.zip else None,
                             vehicle_type=lead.meta.vehicle_type,
                             stock_number=lead.meta.stock_number,
                             vehicle_year=lead.meta.vehicle_year,
                             vehicle_make=lead.meta.vehicle_make,
                             vehicle_model=lead.meta.vehicle_model,
                             department=lead.department,
                             lead_type=lead.meta.lead_type.lower(),
                             notes=lead.meta.notes,
                             transport_type=lead.meta.appointment_transport,
                             appointment_services=lead.meta.appointment_services,
                             # .split(", ") if lead.meta.appointment_services else None
                             appointment=lead.meta.appointment,
                             appointment_id=lead.meta.appointment_id,
                             appointment_timestamp=lead.meta.appointment_timestamp,
                             custom_type=ServiceProviderType.SERVICEPORTAL)

    @classmethod
    def service_portal_bot(cls,
                  first_name=None,
                  last_name=None,
                  email=None,
                  email_to_send=None,
                  phone=None,
                  phone_to_send=None,
                  vehicle_year=None,
                  vehicle_make=None,
                  vehicle_model=None,
                  transport_type=None,
                  appointment=None,
                  appointment_services=[],
                  appointment_services_to_send=None,
                  appointment_to_send=None,
                  notes=None) -> WebLeadEntity:
        #API - VehicleDetailsLog.debug(f"!!! WebLeadFactory xtime_bot {appointment_services}")
        first_name = names.get_first_name() if first_name is None else first_name
        last_name = names.get_last_name() if last_name is None else last_name
        phone = PhoneNumber.new_phone() if phone is None else phone
        email = f"{first_name}.{last_name}-<EMAIL>" if email is None else email
        if appointment_to_send and appointment_to_send in AppointmentExpected.keys():
            appointment = AppointmentExpected[appointment_to_send]
        return WebLeadEntity(first_name=first_name,
                             last_name=last_name,
                             email=email,
                             email_to_send=email_to_send,
                             phone=phone,
                             phone_to_send=phone_to_send,
                             vehicle_year=vehicle_year,
                             vehicle_make=vehicle_make,
                             vehicle_model=vehicle_model,
                             lead_type=LeadType.SERVICE,
                             department='Service',
                             transport_type=transport_type,
                             appointment_services=appointment_services,
                             custom_type=ServiceProviderType.SERVICEPORTAL,
                             appointment=appointment,
                             appointment_to_send=appointment_to_send,
                             appointment_services_to_send=appointment_services_to_send,
                             notes=notes
                             )