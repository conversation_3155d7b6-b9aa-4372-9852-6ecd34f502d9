from src import resq_dealer_name
from src.web.entities import dealership_working_hours
from test import get_resource_file_path


class DealershipEntity:
    def __init__(self, company_name=None, company_email=None, company_address=None, company_city=None,
                 company_state=None, company_zip=None, company_country=None, company_phone=None, dealer_name=None,
                 working_hours=None, dealer_location_map=None):
        self.company_name = company_name
        self.company_email = company_email
        self.company_address = company_address
        self.company_city = company_city
        self.company_state = company_state
        self.company_zip = company_zip
        self.company_country = company_country
        self.company_phone = company_phone
        self.dealer_name = dealer_name
        self.working_hours = working_hours
        self.dealer_location_map = dealer_location_map

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)


class DealershipFactory:

    @classmethod
    def default(cls, prefix="") -> DealershipEntity:
        return DealershipEntity(company_name="Gubagoo Inc.", company_email="<EMAIL>",
                                company_address="4800 T-Rex Ave. Suite 350",
                                company_city="Boca Raton", company_state="Florida",
                                company_zip="33431", company_country="United States", company_phone="8553592574",
                                dealer_name=resq_dealer_name, working_hours=dealership_working_hours,
                                dealer_location_map=get_resource_file_path(f"dealership_location_map{prefix}.png"))

    @classmethod
    def vdp_location(cls) -> DealershipEntity:
        return DealershipEntity(company_name="Gubagoo Inc.", company_email="<EMAIL>",
                                company_address="4800 T-Rex Ave # 350",
                                company_city="Boca Raton", company_state="FL",
                                company_zip="33431", company_country="USA", company_phone="8553592574")
