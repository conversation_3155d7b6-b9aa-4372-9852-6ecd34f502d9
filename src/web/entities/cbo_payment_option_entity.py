from src.web.consumer import PaymentOptionType


class CBOPaymentOptionEntity:
    def __init__(self, down_payment: str = None, credit_score: str = None, term: str = None, payment_lender: str = None,
                 mileage: str = None, payment_type: PaymentOptionType = None):
        self.down_payment = down_payment
        self.credit_score = credit_score
        self.payment_lender = payment_lender
        self.term = term
        self.payment_type = payment_type
        self.mileage = mileage

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)


class CBOPaymentOptionFactory:

    @classmethod
    def default_cbo_payment_option(cls, down_payment: str = None, term: str = None, credit_score: str = None,
                                   payment_lender: str = None, mileage: str = None,
                                   payment_type: PaymentOptionType = None) -> CBOPaymentOptionEntity:
        down_payment = "2000" if down_payment is None else down_payment
        term = "48" if term is None else term
        payment_type = PaymentOptionType.FINANCE if payment_type is None else payment_type
        credit_score = "700-719" if credit_score is None else credit_score
        payment_lender = "All lenders" if payment_lender is None else payment_lender
        mileage = "12,000 mi/yr" if mileage is None else mileage
        return CBOPaymentOptionEntity(down_payment=down_payment, credit_score=credit_score, term=term,
                                      payment_lender=payment_lender, mileage=mileage, payment_type=payment_type)
