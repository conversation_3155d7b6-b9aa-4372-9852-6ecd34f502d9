from functools import partial
from operator import is_not
from typing import List

from kom_framework.src.web import remote_execution
from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from test import get_resource_file_path


class MessageOwner:
    CONSUMER = "consumer"
    OPERATOR = "operator"


class QuickReplyEntity:
    def __init__(self, category, message, placeholder=None):
        self.category = category
        self.message = message
        self.placeholder = placeholder

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)


class MessageEntity:
    def __init__(self,
                 text: str = None,
                 quick_replies: List[QuickReplyEntity] = None,
                 vehicles: List[VehicleEntity] = None,
                 offers: List[str] = None,
                 working_hours: bool = None,
                 location: bool = None,
                 file: str = None,
                 dealership_hours=None,
                 dealership_info=None,
                 upcoming_holiday=None,
                 owner=None,
                 time_sent=None):
        self.text = text
        self.quick_replies = quick_replies
        self.vehicles = vehicles
        self.offers = offers
        self.working_hours = working_hours
        self.location = location
        self.file = file
        self.dealership_info = dealership_info
        self.dealership_hours = dealership_hours
        self.upcoming_holiday = upcoming_holiday
        self.owner = owner
        self.time_sent = time_sent

    def __str__(self):
        vehicles = f'vehicles {self.vehicles.__str__()}' if self.vehicles else None
        quick_replies = f'"{self.quick_replies.__str__()}"' if self.quick_replies else None
        offers = f'offers({", ".join(self.offers)})' if self.offers else None
        text = f'"{self.text}"' if self.text else None
        filter(partial(is_not, None), [text, quick_replies, vehicles, offers])
        return "_".join(filter(partial(is_not, None), [text, quick_replies, vehicles, offers]))

    def __repr__(self):
        vehicles = f'vehicles {self.vehicles.__str__()}' if self.vehicles else None
        quick_replies = f'"{self.quick_replies.__str__()}"' if self.quick_replies else None
        offers = f'offers({", ".join(self.offers)})' if self.offers else None
        text = f'"{self.text}"' if self.text else None
        filter(partial(is_not, None), [text, quick_replies, vehicles, offers])
        return "_".join(filter(partial(is_not, None), [text, quick_replies, vehicles, offers]))

    def get_list_of_stocks(self):
        if self.vehicles:
            return [v.stock for v in self.vehicles]


class MessageFactory:

    @classmethod
    def create(cls,
               text: str = None,
               quick_replies: List[QuickReplyEntity] = None,
               vehicles: List[VehicleEntity] = None,
               offers: List[str] = None,
               working_hours: bool = None,
               location: bool = None):
        return MessageEntity(text=text,
                             quick_replies=quick_replies,
                             vehicles=vehicles,
                             offers=offers,
                             working_hours=working_hours,
                             location=location)

    @classmethod
    def text(cls, text: str, owner=None):
        return MessageEntity(text=text, owner=owner)

    @classmethod
    def vehicles(cls, vehicles: List[VehicleEntity]):
        return MessageEntity(vehicles=vehicles)

    @classmethod
    def offers(cls, offers: List[str]):
        return MessageEntity(offers=offers)

    @classmethod
    def quick_replies(cls, quick_replies: List[QuickReplyEntity]):
        return MessageEntity(quick_replies=quick_replies)

    @classmethod
    def file(cls, path: str):
        return MessageEntity(file=path)

    @classmethod
    def file_from_resources(cls, file_name: str):
        return cls.file(path=get_resource_file_path(file_name, remote=remote_execution))

    @classmethod
    def dealership_info(cls, info: str):
        return MessageEntity(dealership_info=info)

    @classmethod
    def dealership_upcoming_holiday(cls, info: dict):
        return MessageEntity(upcoming_holiday=info)

    @classmethod
    def dealership_dealership_hours(cls, dealership_hours: str):
        return MessageEntity(dealership_hours=dealership_hours)
