class LeaseProgramBrowserEntity:
    def __init__(self,
                 monthly_payment: float = None,
                 lender: str = None,
                 term: str = None,
                 rate: str = None,
                 program: str = None
                 ):
        self.monthly_payment = monthly_payment
        self.lender = lender
        self.term = term
        self.rate = rate
        self.program = program

    def __str__(self):
        return str(self.__dict__)

    def __repr__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        if isinstance(other, LeaseProgramBrowserEntity):
            return (
                    self.lender == other.lender and
                    self.term == other.term and
                    self.program == other.program
            )
        return False
