from datetime import date

import names
from dateutil.relativedelta import relativedelta


class PropertyType:
    RENT = 0
    OWNED = 1
    FAMILY_MEMBER = 2


class EmploymentStatusTypes:
    FULL_TIME = "Full-time"
    PART_TIME = "Part-time"
    SELF_EMPLOYED = "Self employed"
    MILITARY = "Active military"
    RETIRED = "Retired"
    UNEMPLOYED = "Unemployed"
    STUDENT = "Student"
    OTHER = "Other"


class CoApplicantType:
    SPOUSE = "Spouse"
    CIVIL_UNION = "Civil union"
    RELATIVE = "Relative"
    OTHER = "Other"


class OtherIncomeType:
    AFDC = "Aid for Dependent Children"
    A = "Alimony"
    B = "Bonus"
    CS = "Child Support"
    COLA = "Cost of Living Allowance (COLA)"
    D = "Disability"
    FSSA = "Family Subsistence Supplemental Allowance (FSSA)"
    HA = "Housing Allowances"
    I = "Investments"
    MBAFS = "Military Basic Allowance for Subsistence (BAS)"
    MBI = "Municipal Bond Interest"
    ONTSOI = "Other Non-Taxable Sources of Income"
    O = "Other"
    PA = "Public Assistance"
    RP = "Railroad Pension"
    SE = "Second Employer"
    SSB = "Social Security Benefits"
    WC = "Workman's Compensation"


class WebCreditApplicationEntity:
    def __init__(self, first_name=None,
                 last_name=None,
                 email=None,
                 phone=None,
                 date_of_birth=None,
                 social_security_number=None,
                 street=None,
                 suite=None,
                 city=None,
                 state=None,
                 country=None,
                 zip=None,
                 move_in_date_month=None,
                 move_in_date_year=None,
                 monthly_payment=None,
                 previous_street=None,
                 previous_suite=None,
                 previous_city=None,
                 previous_state=None,
                 previous_country=None,
                 previous_zip=None,
                 previous_move_in_date_month=None,
                 previous_move_in_date_year=None,
                 employment_status=None,
                 job_title=None,
                 employer_name=None,
                 employer_phone_number=None,
                 job_start_date_month=None,
                 job_start_date_year=None,
                 annual_gross_income=None,
                 previous_employment_status=None,
                 previous_job_title=None,
                 previous_employer_name=None,
                 previous_employer_phone_number=None,
                 previous_job_start_date_month=None,
                 previous_job_start_date_year=None,
                 previous_annual_gross_income=None,
                 other_income_source=None,
                 other_income_amount=None,
                 education=None,
                 property_type=None,
                 co_applicant=None,
                 relationship=None):
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.phone = phone
        self.date_of_birth = date_of_birth
        self.social_security_number = social_security_number
        self.street = street
        self.suite = suite
        self.city = city
        self.zip = zip
        self.move_in_date_month = move_in_date_month
        self.move_in_date_year = move_in_date_year
        self.monthly_payment = monthly_payment
        self.previous_street = previous_street
        self.previous_suite = previous_suite
        self.previous_city = previous_city
        self.previous_state = previous_state
        self.previous_country = previous_country
        self.previous_zip = previous_zip
        self.previous_move_in_date_month = previous_move_in_date_month
        self.previous_move_in_date_year = previous_move_in_date_year
        self.job_title = job_title
        self.employer_name = employer_name
        self.employer_phone_number = employer_phone_number
        self.job_start_date_month = job_start_date_month
        self.job_start_date_year = job_start_date_year
        self.annual_gross_income = annual_gross_income
        self.previous_employment_status = previous_employment_status
        self.previous_job_title = previous_job_title
        self.previous_employer_name = previous_employer_name
        self.previous_employer_phone_number = previous_employer_phone_number
        self.previous_job_start_date_month = previous_job_start_date_month
        self.previous_job_start_date_year = previous_job_start_date_year
        self.previous_annual_gross_income = previous_annual_gross_income
        self.other_income_source = other_income_source
        self.other_income_amount = other_income_amount
        self.education = education
        self.state = state
        self.country = country
        self.employment_status = employment_status
        self.property_type = property_type
        self.relationship = relationship
        self.co_applicant = co_applicant

    def __str__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        """Overrides the default implementation"""
        if isinstance(other, self.__class__):
            return self.__dict__ == other.__dict__
        return False


class WebCreditApplicatioFactory:

    @classmethod
    def create(cls, first_name=None,
               last_name=None,
               email=None,
               phone=None,
               date_of_birth=None,
               social_security_number=None,
               street=None,
               suite=None,
               city=None,
               state=None,
               country=None,
               zip=None,
               move_in_date_month=None,
               move_in_date_year=None,
               monthly_payment=None,
               previous_street=None,
               previous_suite=None,
               previous_city=None,
               previous_state=None,
               previous_country=None,
               previous_zip=None,
               previous_move_in_date_month=None,
               previous_move_in_date_year=None,
               employment_status=None,
               job_title=None,
               employer_name=None,
               employer_phone_number=None,
               job_start_date_month=None,
               job_start_date_year=None,
               annual_gross_income=None,
               previous_employment_status=None,
               previous_job_title=None,
               previous_employer_name=None,
               previous_employer_phone_number=None,
               previous_job_start_date_month=None,
               previous_job_start_date_year=None,
               previous_annual_gross_income=None,
               other_income_source=None,
               other_income_amount=None,
               education=None,
               property_type=None,
               co_applicant=None,
               relationship=None) -> WebCreditApplicationEntity:
        return WebCreditApplicationEntity(
            first_name=first_name,
            last_name=last_name,
            email=email,
            phone=phone,
            date_of_birth=date_of_birth,
            social_security_number=social_security_number,
            street=street,
            suite=suite,
            city=city,
            zip=zip,
            move_in_date_month=move_in_date_month,
            move_in_date_year=move_in_date_year,
            monthly_payment=monthly_payment,
            previous_street=previous_street,
            previous_suite=previous_suite,
            previous_city=previous_city,
            previous_state=previous_state,
            previous_country=previous_country,
            previous_zip=previous_zip,
            previous_move_in_date_month=previous_move_in_date_month,
            previous_move_in_date_year=previous_move_in_date_year,
            job_title=job_title,
            employer_name=employer_name,
            employer_phone_number=employer_phone_number,
            job_start_date_month=job_start_date_month,
            job_start_date_year=job_start_date_year,
            annual_gross_income=annual_gross_income,
            previous_employment_status=previous_employment_status,
            previous_job_title=previous_job_title,
            previous_employer_name=previous_employer_name,
            previous_employer_phone_number=previous_employer_phone_number,
            previous_job_start_date_month=previous_job_start_date_month,
            previous_job_start_date_year=previous_job_start_date_year,
            previous_annual_gross_income=previous_annual_gross_income,
            other_income_source=other_income_source,
            other_income_amount=other_income_amount,
            education=education,
            state=state,
            country=country,
            employment_status=employment_status,
            property_type=property_type,
            co_applicant=co_applicant,
            relationship=relationship)

    @classmethod
    def default(cls) -> WebCreditApplicationEntity:
        return WebCreditApplicationEntity(
            date_of_birth="10261970",
            social_security_number="123456789",
            street="Palomino Pass",
            suite="180",
            city="Trumbull",
            move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            monthly_payment="1300",
            job_title="QA Analyst"
            , employer_name="Gubagoo",
            employer_phone_number="************",
            job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            other_income_source=OtherIncomeType.I,
            other_income_amount="1250",
            annual_gross_income="150000",
            education="4 year college/university grad",
            state="Connecticut", country="United States",
            employment_status=EmploymentStatusTypes.FULL_TIME,
            property_type=PropertyType.OWNED)

    @classmethod
    def co_applicant(cls) -> WebCreditApplicationEntity:
        first_name = names.get_first_name()
        last_name = names.get_last_name()
        return WebCreditApplicationEntity(
            first_name=first_name,
            last_name=last_name,
            email=f"{first_name}.{last_name}-<EMAIL>",
            phone="5103442660",
            date_of_birth="11261970",
            social_security_number="987654321",
            street="Palomino Pass",
            suite="180",
            city="Trumbull",
            zip="10001",
            move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            monthly_payment="1300",
            job_title="QA Analyst",
            employer_name="Gubagoo",
            employer_phone_number="************",
            job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            other_income_source=OtherIncomeType.CS,
            other_income_amount="1550",
            annual_gross_income="100000",
            education="4 year college/university grad",
            state="Connecticut",
            country="United States",
            employment_status=EmploymentStatusTypes.PART_TIME,
            property_type=PropertyType.OWNED,
            relationship=CoApplicantType.SPOUSE)

    @classmethod
    def co_applicant_two_places_for_work(cls) -> WebCreditApplicationEntity:
        first_name = names.get_first_name()
        last_name = names.get_last_name()
        return WebCreditApplicationEntity(
            first_name=first_name,
            last_name=last_name, email=f"{first_name}.{last_name}-<EMAIL>",
            phone="5103442660",
            date_of_birth="11261970",
            social_security_number="987654321",
            street="Palomino Pass",
            suite="180",
            city="Trumbull",
            zip="10001",
            move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            monthly_payment="1300",
            job_title="QA Analyst",
            employer_name="Gubagoo",
            employer_phone_number="************",
            job_start_date_month=(date.today() - relativedelta(months=+6)).strftime("%m"),
            job_start_date_year=(date.today() - relativedelta(months=+6)).strftime("%Y"),
            previous_employment_status=EmploymentStatusTypes.PART_TIME,
            previous_job_title="QA",
            previous_employer_name="Bubaboo",
            previous_employer_phone_number="************",
            previous_job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            previous_job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            previous_annual_gross_income="55000",
            other_income_source=OtherIncomeType.CS, other_income_amount="1550",
            annual_gross_income="100000",
            education="4 year college/university grad",
            state="Connecticut",
            country="United States",
            employment_status=EmploymentStatusTypes.PART_TIME,
            property_type=PropertyType.OWNED,
            relationship=CoApplicantType.SPOUSE)

    @classmethod
    def co_applicant_two_places_for_live(cls) -> WebCreditApplicationEntity:
        first_name = names.get_first_name()
        last_name = names.get_last_name()
        return WebCreditApplicationEntity(
            first_name=first_name,
            last_name=last_name,
            email=f"{first_name}.{last_name}-<EMAIL>",
            phone="5103442660",
            date_of_birth="11261970",
            social_security_number="987654321",
            street="Palomino Pass",
            suite="180",
            city="Trumbull",
            zip="10001",
            move_in_date_month=(date.today() - relativedelta(months=+6)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+6)).strftime("%Y"),
            monthly_payment="1300",
            previous_street="Washington",
            previous_suite="305",
            previous_city="Jewell",
            previous_state="Kansas",
            previous_country="United States",
            previous_zip="66949",
            previous_move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            previous_move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            job_title="QA Analyst",
            employer_name="Gubagoo",
            employer_phone_number="************",
            job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            other_income_source=OtherIncomeType.CS,
            other_income_amount="1550",
            annual_gross_income="100000",
            education="4 year college/university grad",
            state="Connecticut",
            country="United States",
            employment_status=EmploymentStatusTypes.PART_TIME,
            property_type=PropertyType.OWNED,
            relationship=CoApplicantType.SPOUSE)

    @classmethod
    def two_places_for_work(cls) -> WebCreditApplicationEntity:
        return WebCreditApplicationEntity(
            date_of_birth="10261970",
            social_security_number="123456789",
            street="Palomino Pass",
            suite="180",
            city="Trumbull",
            move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            monthly_payment="1300",
            job_title="QA Analyst",
            employer_name="Gubagoo",
            employer_phone_number="************",
            job_start_date_month=(date.today() - relativedelta(months=+6)).strftime("%m"),
            job_start_date_year=(date.today() - relativedelta(months=+6)).strftime("%Y"),
            previous_employment_status=EmploymentStatusTypes.PART_TIME,
            previous_job_title="QA",
            previous_employer_name="Bubaboo",
            previous_employer_phone_number="************",
            previous_job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            previous_job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            previous_annual_gross_income="55000",
            other_income_source=OtherIncomeType.I,
            other_income_amount="1250",
            annual_gross_income="150000",
            education="4 year college/university grad",
            state="Connecticut",
            country="United States",
            employment_status=EmploymentStatusTypes.FULL_TIME,
            property_type=PropertyType.OWNED)

    @classmethod
    def two_places_for_live(cls) -> WebCreditApplicationEntity:
        return WebCreditApplicationEntity(
            date_of_birth="10261970",
            social_security_number="123456789",
            street="Palomino Pass", suite="180",
            city="Trumbull",
            move_in_date_month=(date.today() - relativedelta(months=+6)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+6)).strftime("%Y"),
            monthly_payment="1300",
            previous_street="Washington",
            previous_suite="305",
            previous_city="Jewell",
            previous_state="Kansas",
            previous_country="United States",
            previous_zip="66949",
            previous_move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            previous_move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            job_title="QA Analyst",
            employer_name="Gubagoo",
            employer_phone_number="************",
            job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            other_income_source=OtherIncomeType.I,
            other_income_amount="1250",
            annual_gross_income="150000",
            education="4 year college/university grad",
            state="Connecticut",
            country="United States",
            employment_status=EmploymentStatusTypes.FULL_TIME,
            property_type=PropertyType.OWNED)

    @classmethod
    def unemployed(cls) -> WebCreditApplicationEntity:
        return WebCreditApplicationEntity(
            date_of_birth="10261970",
            social_security_number="123456789",
            street="Palomino Pass",
            suite="180",
            city="Trumbull",
            move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            monthly_payment="1300",
            previous_employment_status=EmploymentStatusTypes.PART_TIME,
            previous_job_title="QA",
            previous_employer_name="Bubaboo",
            previous_employer_phone_number="************",
            previous_job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            previous_job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            previous_annual_gross_income="55000",
            other_income_source=OtherIncomeType.I,
            other_income_amount="1250",
            education="4 year college/university grad",
            state="Connecticut",
            country="United States",
            employment_status=EmploymentStatusTypes.UNEMPLOYED,
            property_type=PropertyType.OWNED)

    @classmethod
    def default_with_co_applicant(cls, co_applicant) -> WebCreditApplicationEntity:
        return WebCreditApplicationEntity(
            date_of_birth="10261970",
            social_security_number="123456789",
            street="Palomino Pass",
            suite="180",
            city="Trumbull",
            move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            monthly_payment="1300",
            job_title="QA Analyst",
            employer_name="Gubagoo",
            employer_phone_number="************",
            job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            other_income_source=OtherIncomeType.I,
            other_income_amount="1250",
            annual_gross_income="150000",
            education="4 year college/university grad",
            state="Connecticut",
            country="United States",
            employment_status=EmploymentStatusTypes.FULL_TIME,
            property_type=PropertyType.OWNED,
            co_applicant=co_applicant
        )

    @classmethod
    def default_secure_credit_app(cls) -> WebCreditApplicationEntity:
        first_name = names.get_first_name()
        last_name = names.get_last_name()
        return WebCreditApplicationEntity(
            first_name=first_name,
            last_name=last_name,
            email=f"{first_name}.{last_name}-<EMAIL>",
            phone="5103442660",
            date_of_birth="11261970",
            social_security_number="334454354",
            street="300 Palomino Pass",
            suite="180",
            city="Trumbull",
            zip="06611",
            state="Connecticut",
            country="United States",
            move_in_date_month=(date.today() - relativedelta(months=+72)).strftime("%m"),
            move_in_date_year=(date.today() - relativedelta(months=+72)).strftime("%Y"),
            monthly_payment="1300",
            job_title="QA Analyst",
            employer_name="Gubagoo",
            employer_phone_number="************",
            job_start_date_month=(date.today() - relativedelta(months=+36)).strftime("%m"),
            job_start_date_year=(date.today() - relativedelta(months=+36)).strftime("%Y"),
            annual_gross_income="100000",
            employment_status=EmploymentStatusTypes.FULL_TIME,
            property_type=PropertyType.OWNED
        )
