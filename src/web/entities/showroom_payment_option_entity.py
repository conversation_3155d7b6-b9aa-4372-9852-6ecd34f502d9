from src.web.chat_console import VehicleType
from src.web.consumer import PaymentOptionType


class ShowroomPaymentOptionEntity:
    def __init__(self, vehicle_type: VehicleType, payment_option: PaymentOptionType, monthly_payment_min: str = None,
                 monthly_payment_max: str = None, down_payment: str = None, price_min: str = None,
                 price_max: str = None, finance_terms: int = None, lease_terms: int = None, credit_score: int = None):
        self.vehicle_type = vehicle_type
        self.payment_option = payment_option
        self.monthly_payment_min = monthly_payment_min
        self.monthly_payment_max = monthly_payment_max
        self.down_payment = down_payment
        self.price_min = price_min
        self.price_max = price_max
        self.finance_terms = finance_terms
        self.lease_terms = lease_terms
        self.credit_score = credit_score


class ShowroomPaymentOptionFactory:

    @classmethod
    def create(cls, vehicle_type: VehicleType, payment_option: PaymentOptionType, monthly_payment_min: int = None,
               monthly_payment_max: int = None, down_payment: str = None, price_min: int = None,
               price_max: int = None, finance_terms: int = None, lease_terms: int = None, credit_score: int = None) \
            -> ShowroomPaymentOptionEntity:
        return ShowroomPaymentOptionEntity(vehicle_type, payment_option, monthly_payment_min, monthly_payment_max,
                                           down_payment, price_min, price_max, finance_terms, lease_terms, credit_score)
