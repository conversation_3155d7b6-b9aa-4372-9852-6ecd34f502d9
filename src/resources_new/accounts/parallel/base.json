{"_description": "Base configuration for all parallel accounts", "account_type": "parallel", "credentials": "standard_user", "access_token": "standard", "consumer": {"src": "https://cdn.gubagoo.io/development"}, "chat_console": {"url": {"chat_console/develop": "https://chat-alpha.gubagoo.com", "chat_console/pr-build": "link_is_not_provided", "chat_console/staging": "https://chat-staging.gubagoo.com"}, "users": {"manager": {"user_name": "chat_manager"}, "operator": {"user_name": "chat_operator"}, "admin": {"user_name": "chat_admin"}, "vr_specialist": {"user_name": "vr_specialist"}}}, "resq": {"url": {"resq-desktop/rc": "https://desktop-rc.resq.rocks/conversations/", "resq-desktop/glive-rc": "https://glive-rc.resq.rocks/conversations/", "resq-desktop/glive-dev": "https://glive-dev.resq.rocks/conversations/", "resq-desktop/pr-build": "link_is_not_provided"}, "api": "https://api-test.resq.rocks", "password": "Docker1234"}}