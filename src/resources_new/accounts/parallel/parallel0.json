{"_description": "Parallel0 specific configuration", "whatsapp_phone_number": "**********", "database": {"database": "client_gubagoo_202671"}, "consumer": {"url": "https://guba:<EMAIL>/index.html", "cbo_url": "https://guba:<EMAIL>/index_cbo.html"}, "chat_console": {"users": {"manager": {"user_id": "6284"}, "operator": {"user_id": "6283"}, "admin": {"user_id": "203279"}, "vr_specialist": {"user_id": "202692"}, "password": "fP4wV6gE9"}, "account_id": "202671", "account_name": "e2e-python", "token": "a7c37ee59f7971bc276e086359430634527efe54", "offer_id": "657", "db_trigger_id": "31", "da_trigger_id": "32", "bb_trigger_id": "33", "po_trigger_id": "34", "xtime_id": "1583", "dynamic_greeter_trigger_id": "27", "e_price_id": "7"}, "resq": {"users": {"manager": {"user_name": "nataliia.<PERSON><PERSON><PERSON><PERSON>+<EMAIL>", "id": "714", "uuid": "8f8ec9be-ffe6-11e9-b122-0e18253eb080"}, "service": {"user_name": "nataliia.k<PERSON><PERSON><PERSON>+<EMAIL>", "id": "716", "uuid": "c743fd98-ffe6-11e9-9ee6-0a945d774bc4"}, "sales": {"user_name": "nataliia.k<PERSON><PERSON><PERSON>+<EMAIL>", "id": "715", "uuid": "b287ea9a-ffe6-11e9-b122-0e18253eb080"}, "admin": {"user_name": "nataliia.k<PERSON><PERSON><PERSON>@gmail.com", "id": "713", "uuid": "0e26c318-ffe6-11e9-9ee6-0a945d774bc4"}}, "resq_account_id": "3081", "sub_dealer": {"consumer_url": "https://guba:<EMAIL>/index.html", "resq_account_id": "3098", "chat_console_account_id": "202691"}, "dealer_name": "e2e Python Parallel 0", "dealer_account_name": "e2e-parallel-0"}}