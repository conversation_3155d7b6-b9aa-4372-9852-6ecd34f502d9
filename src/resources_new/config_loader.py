"""
Configuration Loader for New Structure

This module demonstrates how to load and merge configurations
from the new hierarchical structure.
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigLoader:
    """Loads and merges configurations from the new structure."""
    
    def __init__(self, config_dir: str = None):
        """Initialize the config loader.
        
        Args:
            config_dir: Path to the configuration directory
        """
        if config_dir is None:
            config_dir = Path(__file__).parent
        self.config_dir = Path(config_dir)
        self.main_config = self._load_main_config()
    
    def _load_main_config(self) -> Dict[str, Any]:
        """Load the main configuration file."""
        config_path = self.config_dir / "config.json"
        with open(config_path, 'r') as f:
            return json.load(f)
    
    def _load_json_file(self, file_path: str) -> Dict[str, Any]:
        """Load a JSON file and return its contents."""
        full_path = self.config_dir / file_path
        if not full_path.exists():
            return {}
        
        with open(full_path, 'r') as f:
            return json.load(f)
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = base.copy()
        
        for key, value in override.items():
            if key.startswith('_'):  # Skip metadata keys
                continue
                
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_account_config(self, account_name: str, environment: str = "rc") -> Dict[str, Any]:
        """Get the complete configuration for an account.
        
        Args:
            account_name: Name of the account (e.g., "parallel0", "e2e-chat-console-env-1")
            environment: Environment name (e.g., "rc", "staging", "sandbox")
            
        Returns:
            Complete merged configuration for the account
        """
        # Find which account group this account belongs to
        account_group = self._find_account_group(account_name)
        if not account_group:
            raise ValueError(f"Account {account_name} not found in any group")
        
        # Start with base environment config
        config = self._load_json_file("environments/base.json")
        
        # Apply environment-specific overrides
        if environment != "base":
            env_config = self._load_json_file(f"environments/{environment}.json")
            config = self._deep_merge(config, env_config)
        
        # Apply service configurations
        for service_file in self.main_config.get("services", []):
            service_config = self._load_json_file(service_file)
            config = self._deep_merge(config, service_config)

        # Apply product configurations
        for product_file in self.main_config.get("products", []):
            product_config = self._load_json_file(product_file)
            config = self._deep_merge(config, product_config)
        
        # Apply account group base configuration
        group_base_config = self._load_json_file(f"accounts/{account_group}/base.json")
        config = self._deep_merge(config, group_base_config)
        
        # Apply specific account configuration
        account_config = self._load_json_file(f"accounts/{account_group}/{account_name}.json")
        config = self._deep_merge(config, account_config)
        
        # Resolve references (credentials, access_tokens, etc.)
        config = self._resolve_references(config)
        
        return config
    
    def _find_account_group(self, account_name: str) -> Optional[str]:
        """Find which group an account belongs to."""
        for group_name, group_info in self.main_config.get("account_groups", {}).items():
            if account_name in group_info.get("accounts", []):
                return group_name
        return None
    
    def _resolve_references(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve references like credential types and access token types."""
        result = config.copy()
        
        # Resolve credentials reference
        if "credentials" in result and isinstance(result["credentials"], str):
            cred_type = result["credentials"]
            if "credentials" in result and cred_type in result["credentials"]:
                # Replace the reference with actual credentials
                actual_creds = result["credentials"][cred_type]
                result.update(actual_creds)
        
        # Resolve access_token reference
        if "access_token" in result and isinstance(result["access_token"], str):
            token_type = result["access_token"]
            if "access_tokens" in result and token_type in result["access_tokens"]:
                result["access_token"] = result["access_tokens"][token_type]
        
        return result
    
    def list_accounts(self) -> Dict[str, list]:
        """List all available accounts by group."""
        return {
            group: info["accounts"] 
            for group, info in self.main_config.get("account_groups", {}).items()
        }
    
    def list_environments(self) -> list:
        """List all available environments."""
        return list(self.main_config.get("environments", {}).keys())


# Example usage
if __name__ == "__main__":
    loader = ConfigLoader()
    
    # Get configuration for parallel0 account
    config = loader.get_account_config("parallel0")
    print("Parallel0 config:")
    print(json.dumps(config, indent=2))
    
    # List all accounts
    print("\nAvailable accounts:")
    accounts = loader.list_accounts()
    for group, account_list in accounts.items():
        print(f"  {group}: {account_list}")
