{"_description": "Main configuration file that defines how configs are merged", "_version": "2.0", "_structure": {"inheritance_order": ["environments/base.json", "services/*.json", "products/*.json", "accounts/{account_group}/base.json", "accounts/{account_group}/{account_name}.json"]}, "account_groups": {"parallel": {"accounts": ["parallel0", "parallel1", "parallel2", "parallel3", "parallel4", "parallel5"], "description": "Basic parallel test accounts"}, "e2e-chat-console": {"accounts": ["env-1", "env-2", "env-3", "env-4", "env-5"], "description": "Chat console test environments"}, "e2e-cbo": {"accounts": ["env-1", "env-2", "env-3", "env-4", "env-5"], "description": "CBO test environments"}, "e2e-chat4": {"accounts": ["env-1", "env-2", "env-3", "env-4", "env-5", "env-6", "env-7"], "description": "Chat4 test environments"}, "e2e-glive": {"accounts": ["env-1", "env-2", "env-3", "env-4", "env-5"], "description": "GLive test environments"}, "e2e-gb1": {"accounts": ["env-1", "env-2", "env-3", "env-4", "env-5", "env-6", "env-7", "env-1-sandbox"], "description": "GB1 test environments"}, "special": {"accounts": ["default", "local"], "description": "Special purpose accounts"}}, "environments": {"rc": {"description": "Release Candidate environment", "config_file": "environments/rc.json"}, "staging": {"description": "Staging environment", "config_file": "environments/staging.json"}, "sandbox": {"description": "Sandbox environment", "config_file": "environments/sandbox.json"}, "local": {"description": "Local development environment", "config_file": "environments/local.json"}}, "services": ["services/testrail.json", "services/jira.json", "services/ftp.json", "services/database.json"], "products": ["products/consumer.json", "products/chat-console.json", "products/resq.json"]}