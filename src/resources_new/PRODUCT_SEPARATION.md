# Product Configuration Separation

## Overview
The 3 main product sections have been separated into individual files for better organization and maintainability.

## Product Files Created

### 🛒 Consumer Product (`products/consumer.json`)
**Purpose**: Consumer-facing product configurations
**Contains**:
- CDN source URLs for different environments
- Base URL patterns for consumer interfaces
- CBO (Chat Bot Optimization) URL patterns
- Environment-specific overrides

**Key Features**:
```json
{
  "consumer": {
    "src": "https://cdn.gubagoo.io/development",
    "base_url_pattern": "https://guba:gubaDM1@{subdomain}.resq.rocks/index.html",
    "cbo_url_pattern": "https://guba:gubaDM1@{subdomain}.resq.rocks/index_cbo.html"
  }
}
```

### 💬 Chat Console Product (`products/chat-console.json`)
**Purpose**: Chat Console product configurations
**Contains**:
- Chat Console URLs for different environments (develop, staging, RC, sandbox)
- User role definitions (manager, operator, admin, vr_specialist)
- Default settings (trigger IDs, offer IDs, etc.)

**Key Features**:
```json
{
  "chat_console": {
    "urls": {
      "develop": "https://chat-alpha.gubagoo.com",
      "staging": "https://chat-staging.gubagoo.com",
      "rc": "https://chat-rc.gubagoo.com"
    },
    "user_roles": {
      "manager": { "user_name": "chat_manager" },
      "operator": { "user_name": "chat_operator" }
    }
  }
}
```

### 🚀 Resq Product (`products/resq.json`)
**Purpose**: Resq product configurations
**Contains**:
- Desktop URLs for different environments
- API endpoints for different environments
- User role definitions
- Account naming patterns

**Key Features**:
```json
{
  "resq": {
    "urls": {
      "desktop": {
        "rc": "https://desktop-rc.resq.rocks/conversations/",
        "staging": "https://desktop-staging.resq.rocks/conversations/"
      }
    },
    "api": {
      "rc": "https://api-test.resq.rocks",
      "staging": "https://api-staging.resq.rocks"
    }
  }
}
```

## Configuration Inheritance

### New Inheritance Order
1. **Base Environment** (`environments/base.json`) - Core settings
2. **Services** (`services/*.json`) - TestRail, Jira, FTP configs
3. **Products** (`products/*.json`) - **NEW: Product-specific configs**
4. **Account Group Base** (`accounts/{group}/base.json`) - Group defaults
5. **Account Specific** (`accounts/{group}/{account}.json`) - Account overrides

### Benefits of This Structure

#### ✅ **No Duplication**
- Consumer URLs defined once, inherited by all accounts
- Chat Console settings shared across all environments
- Resq configurations centralized

#### ✅ **Environment Flexibility**
Each product can have environment-specific settings:
```json
// In products/consumer.json
"environments": {
  "rc": { "src": "https://cdn.gubagoo.io/development" },
  "staging": { "src": "https://cdn.gubagoo.io/staging" },
  "sandbox": { "src": "https://cdn.gubagoo.io/sandbox" }
}
```

#### ✅ **Easy Maintenance**
| Change Required | Old Way | New Way |
|----------------|---------|---------|
| Update Chat Console staging URL | Edit 25+ files | Edit 1 file |
| Add new Resq environment | Copy-paste 25+ times | Add to 1 file |
| Change Consumer CDN | Find/replace everywhere | Edit 1 file |

#### ✅ **Clear Separation of Concerns**
- **Base**: Core infrastructure settings
- **Services**: External service integrations
- **Products**: Product-specific configurations
- **Accounts**: Account-specific overrides

## Migration Impact

### Before (Monolithic)
```
environments/base.json (5KB)
├── common settings
├── consumer settings    } Duplicated
├── chat_console settings} across all
└── resq settings       } accounts
```

### After (Separated)
```
environments/base.json (2KB) - Core only
products/
├── consumer.json (1KB)      - Consumer only
├── chat-console.json (1.5KB) - Chat Console only
└── resq.json (1.5KB)        - Resq only
```

### File Size Reduction
- **Base file**: 5KB → 2KB (60% reduction)
- **Total product configs**: 4KB (vs duplicated across 25+ accounts)
- **Overall savings**: Massive reduction in duplication

## Usage Examples

### Loading Configuration
```python
loader = ConfigLoader()
config = loader.get_account_config("parallel0", "rc")

# All product configs automatically merged:
print(config["consumer"]["src"])           # From products/consumer.json
print(config["chat_console"]["urls"])      # From products/chat-console.json  
print(config["resq"]["api"])               # From products/resq.json
```

### Environment-Specific Loading
```python
# RC environment
config_rc = loader.get_account_config("parallel0", "rc")
print(config_rc["consumer"]["src"])  # Development CDN

# Staging environment  
config_staging = loader.get_account_config("parallel0", "staging")
print(config_staging["consumer"]["src"])  # Staging CDN
```

## Adding New Products

To add a new product (e.g., "analytics"):

1. Create `products/analytics.json`:
```json
{
  "analytics": {
    "urls": {
      "dashboard": "https://analytics-rc.gubagoo.com",
      "api": "https://api-analytics-rc.gubagoo.com"
    },
    "settings": {
      "tracking_enabled": true,
      "retention_days": 90
    }
  }
}
```

2. Add to `config.json`:
```json
"products": [
  "products/consumer.json",
  "products/chat-console.json", 
  "products/resq.json",
  "products/analytics.json"
]
```

3. All accounts automatically inherit the new product configuration!

## Conclusion

The product separation provides:
- **🎯 Clear organization** by product responsibility
- **⚡ Faster maintenance** with centralized product configs
- **🔄 Zero duplication** across 25+ accounts
- **📈 Easy scalability** for new products and environments
- **🛡️ Reduced errors** from copy-paste mistakes

This structure scales perfectly as you add more accounts, products, and environments!
