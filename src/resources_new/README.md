# New Configuration Structure

## Overview
This new structure eliminates duplication and provides better organization for 25+ test accounts.

## Structure
```
resources_new/
├── README.md                    # This file
├── config.json                 # Main configuration loader
├── environments/               # Environment-specific configs
│   ├── base.json               # Base/default configurations
│   ├── rc.json                 # RC environment overrides
│   ├── staging.json            # Staging environment overrides
│   └── sandbox.json            # Sandbox environment overrides
├── services/                   # Service-specific configs
│   ├── testrail.json           # TestRail configuration
│   ├── jira.json               # Jira configuration
│   ├── ftp.json                # FTP configuration
│   └── database.json           # Database configurations
├── products/                   # Product-specific configs
│   ├── consumer.json           # Consumer product configuration
│   ├── chat-console.json       # Chat Console product configuration
│   └── resq.json               # Resq product configuration
├── accounts/                   # Account-specific configs
│   ├── parallel/               # Parallel test accounts
│   │   ├── base.json           # Common parallel config
│   │   ├── parallel0.json      # Specific overrides
│   │   ├── parallel1.json
│   │   └── ...
│   ├── e2e-chat-console/       # Chat console accounts
│   │   ├── base.json
│   │   ├── env-1.json
│   │   └── ...
│   ├── e2e-cbo/               # CBO accounts
│   ├── e2e-chat4/             # Chat4 accounts
│   ├── e2e-glive/             # GLive accounts
│   └── e2e-gb1/               # GB1 accounts
└── templates/                  # Configuration templates
    ├── account-template.json   # Template for new accounts
    └── service-template.json   # Template for new services
```

## Benefits
1. **No Duplication**: Common configs defined once
2. **Easy Maintenance**: Change base config affects all accounts
3. **Clear Organization**: Logical grouping by purpose
4. **Easy to Find**: Predictable file locations
5. **Inheritance**: Accounts inherit from base configs
6. **Scalable**: Easy to add new accounts/services

## Usage
The main `config.json` file will merge configurations in this order:
1. Base environment config
2. Service configs
3. Product configs (Consumer, Chat Console, Resq)
4. Account base config
5. Specific account overrides

This ensures maximum reusability while allowing customization where needed.
