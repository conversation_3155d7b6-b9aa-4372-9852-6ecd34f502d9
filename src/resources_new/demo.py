#!/usr/bin/env python3
"""
Demo script showing the new configuration structure with products separation
"""

import json
from config_loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def demo_product_separation():
    """Demonstrate how the 3 main product sections are now separated."""
    
    print("🎯 New Configuration Structure Demo")
    print("=" * 50)
    
    loader = ConfigLoader()
    
    # Load configuration for parallel0
    config = loader.get_account_config("parallel0")
    
    print("\n📦 Product Configurations Loaded:")
    print("-" * 30)
    
    # Show Consumer product config
    if "consumer" in config:
        print("✅ Consumer Product:")
        print(f"   - Source: {config['consumer'].get('src', 'N/A')}")
        print(f"   - URL Pattern: {config['consumer'].get('base_url_pattern', 'N/A')}")
    
    # Show Chat Console product config  
    if "chat_console" in config:
        print("✅ Chat Console Product:")
        print(f"   - Develop URL: {config['chat_console']['urls'].get('develop', 'N/A')}")
        print(f"   - Staging URL: {config['chat_console']['urls'].get('staging', 'N/A')}")
        print(f"   - User Roles: {list(config['chat_console']['user_roles'].keys())}")
    
    # Show Resq product config
    if "resq" in config:
        print("✅ Resq Product:")
        print(f"   - API: {config['resq']['api'].get('rc', 'N/A')}")
        print(f"   - Desktop RC: {config['resq']['urls']['desktop'].get('rc', 'N/A')}")
        print(f"   - User Roles: {list(config['resq']['user_roles'].keys())}")
    
    print("\n🏗️ Configuration Inheritance Order:")
    print("-" * 35)
    print("1. environments/base.json        (Core settings)")
    print("2. services/*.json               (TestRail, Jira, FTP)")
    print("3. products/*.json               (Consumer, Chat Console, Resq)")
    print("4. accounts/parallel/base.json   (Parallel account defaults)")
    print("5. accounts/parallel/parallel0.json (Account-specific overrides)")
    
    print("\n📊 Benefits of Product Separation:")
    print("-" * 35)
    print("✅ No duplication across 25+ accounts")
    print("✅ Easy to update product URLs globally")
    print("✅ Clear separation of concerns")
    print("✅ Environment-specific product configs")
    print("✅ Maintainable and scalable")
    
    print("\n🔧 File Structure:")
    print("-" * 15)
    print("products/")
    print("├── consumer.json      (Consumer product settings)")
    print("├── chat-console.json  (Chat Console product settings)")
    print("└── resq.json          (Resq product settings)")
    
    print("\n💡 Example: Updating Chat Console staging URL")
    print("-" * 45)
    print("Old way: Edit 25+ account files")
    print("New way: Edit 1 file (products/chat-console.json)")
    print("Result: All accounts automatically get the update!")


if __name__ == "__main__":
    demo_product_separation()
