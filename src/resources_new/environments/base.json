{"_description": "Base configuration shared by all environments", "common": {"account_id": "99999", "access_user": "guba_dev", "floating_image": "1017002", "right_image": "1001388", "left_image": "1001389", "notification_access_token": "X-Gubagoo-Test"}, "urls": {"base": "https://gb1-rc.gubagoo.com", "notification": "https://notifications-test.gubagoo.io"}, "credentials": {"standard_user": {"user_name": "<PERSON><PERSON><PERSON>", "password": "njHppIdbNkqQi010hsUGJr"}, "test_admin": {"user_name": "test_admin", "password": "TKI734RKU1WLiTe7zStV"}}, "access_tokens": {"standard": "RKx^3Tz?pOf0Dn.H~aSH^PAk@GNVm~+eGVo", "chat_console": "GJE&4cMtA^^463hZwJ*oU*BRPY8SmGvMvuw4EJtjfZ1%3tB3@L"}, "database": {"server": "testdb.gubagoo.io", "user": "devdb", "password": "r67JvDddG9QjKYAw2KF", "port": 3307, "database_prefix": "client_gubagoo_"}, "phone_number_ranges": {"parallel": {"base": "202671", "increment": 2}, "chat_console": {"base": "813590", "increment": 1000}}}