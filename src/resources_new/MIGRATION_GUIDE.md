# Migration Guide: Old Config → New Structure

## Overview
This guide explains how to migrate from the old monolithic configuration files to the new hierarchical structure.

## Problems with Old Structure
- **Massive duplication**: Same URLs, credentials repeated 25+ times
- **Hard to maintain**: Changes require updating multiple places
- **Poor organization**: No logical grouping
- **Difficult to find**: Information scattered across huge files
- **No inheritance**: No way to share common configurations

## Benefits of New Structure
- **No duplication**: Common configs defined once
- **Easy maintenance**: Change base config affects all accounts
- **Clear organization**: Logical grouping by purpose
- **Easy to find**: Predictable file locations
- **Inheritance**: Accounts inherit from base configs
- **Scalable**: Easy to add new accounts/services

## Migration Steps

### 1. Identify Common Patterns
From the old files, we identified these common patterns:
- Base URLs and credentials (same for all accounts)
- Service configurations (TestRail, Jira, FTP)
- Account type patterns (parallel, chat-console, etc.)

### 2. Extract to Base Configurations
Common elements moved to:
- `environments/base.json` - Common URLs, credentials, database settings
- `services/*.json` - Service-specific configurations
- `accounts/{group}/base.json` - Common settings for account groups

### 3. Create Account-Specific Overrides
Only unique values stored in:
- `accounts/{group}/{account}.json` - Account-specific overrides

### 4. Update Code to Use New Loader
Replace old config loading:
```python
# Old way
with open('.qa.env.json') as f:
    config = json.load(f)
account_config = config[account_name]

# New way
loader = ConfigLoader()
account_config = loader.get_account_config(account_name, environment)
```

## Configuration Resolution Order
1. `environments/base.json` - Base environment settings
2. `environments/{env}.json` - Environment-specific overrides
3. `services/*.json` - Service configurations
4. `accounts/{group}/base.json` - Account group base
5. `accounts/{group}/{account}.json` - Account-specific overrides

## Adding New Accounts

### For Existing Account Groups
1. Copy the appropriate template from `templates/`
2. Fill in account-specific values
3. Save as `accounts/{group}/{new-account}.json`
4. Add to `config.json` account list

### For New Account Groups
1. Create `accounts/{new-group}/` directory
2. Create `base.json` with common settings
3. Create individual account files
4. Update `config.json` with new group

## Example: Adding parallel6

1. Copy `templates/account-template.json`
2. Fill in values:
```json
{
  "whatsapp_phone_number": "**********",
  "database": {
    "database": "client_gubagoo_202683"
  },
  "consumer": {
    "url": "https://guba:<EMAIL>/index.html"
  }
}
```
3. Save as `accounts/parallel/parallel6.json`
4. Add "parallel6" to config.json account list

## Testing Migration
Use the provided `config_loader.py` to test configurations:
```python
loader = ConfigLoader()
config = loader.get_account_config("parallel0")
# Verify all expected values are present
```

## Rollback Plan
If issues arise:
1. Keep old files as backup
2. Update code to use old structure temporarily
3. Fix issues in new structure
4. Re-migrate when ready

## File Size Comparison
- **Old**: 2 files, ~50KB each, massive duplication
- **New**: ~30 small files, ~2KB each, no duplication
- **Maintenance**: 1 change vs 25+ changes for common updates
