{"_description": "Resq product configuration", "resq": {"urls": {"desktop": {"rc": "https://desktop-rc.resq.rocks/conversations/", "glive-rc": "https://glive-rc.resq.rocks/conversations/", "glive-dev": "https://glive-dev.resq.rocks/conversations/", "staging": "https://desktop-staging.resq.rocks/conversations/", "sandbox": "https://desktop-sb.resq.rocks/conversations/", "pr-build": "link_is_not_provided"}}, "api": {"rc": "https://api-test.resq.rocks", "staging": "https://api-staging.resq.rocks", "sandbox": "https://api-sb.resq.rocks"}, "default_password": "Docker1234", "user_roles": {"manager": {"role": "manager"}, "service": {"role": "service"}, "sales": {"role": "sales"}, "admin": {"role": "admin"}}, "account_settings": {"dealer_name_pattern": "e2e Python {account_display_name}", "dealer_account_pattern": "e2e-{account_name}"}}}