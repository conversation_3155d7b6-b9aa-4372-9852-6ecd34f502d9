import allure
from pytest import fixture

from src import resq_password, resq_manager_user_name, cbo_url, resq_dealer_account_name
from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.web.consumer.cbo_page_behavior import CboCTAButton
from src.web.entities.pii_entity import PIIEntity, PIIFactory
from src.web.resq.offer_builder import offer_builder_page
from src.web.resq.offer_builder.offer_builder_page import OfferBuilderPage
from src.web.resq.resq_page import ResQPage
from src.web.resq.vr.virtualRetailing import VirtualRetailing
from test.web_tests.cbo import ConsumerPage


@fixture(scope="session")
@allure.title("Reset cbo settings")
def fx_ss_reset_cbo_settings():
    Gb1Settings.reset_cbo_settings()


@fixture(scope="class")
@allure.title("Open GLive page and sign in as a manager")
def fx_cl_resq_manager_page(request, fx_ss_reset_cbo_settings) -> VirtualRetailing:
    def finalizer():
        manager.sign_out()
        manager.quit()

    request.addfinalizer(finalizer)

    manager = ResQPage(resq_manager_user_name, resq_password).open()
    manager.change_account(resq_dealer_account_name)
    manager.vr.open()
    return manager


@fixture(scope="class")
@allure.title("Open Offer Builder via Create Deal flow")
def fx_cl_open_offer_builder(vehicle, fx_cl_resq_manager_page, lead_info, request) -> OfferBuilderPage:
    def finalizer():
        offer_builder_page.close_offer_builder()

    request.addfinalizer(finalizer)

    offer_builder_page = fx_cl_resq_manager_page.offer_builder_page.open(lead_info, vehicle)
    return offer_builder_page


@fixture(scope="class")
@allure.title("Change F&I Flow")
def fx_cl_set_set_fi_flow_setting(request):
    def finalizer():
        Gb1Settings.set_cbo_service_protection_setting()

    request.addfinalizer(finalizer)
    Gb1Settings.set_cbo_service_protection_setting(fi_value="bundles")


@fixture(scope="class")
@allure.title("Open Offer Builder with F&I Bundle flow")
def fx_cl_open_offer_builder_fi_bundle_flow(vehicle, fx_cl_resq_manager_page, fx_cl_set_set_fi_flow_setting,
                                            lead_info, request) -> OfferBuilderPage:
    def finalizer():
        offer_builder_page.close_offer_builder()

    request.addfinalizer(finalizer)

    offer_builder_page = fx_cl_resq_manager_page.offer_builder_page.open(lead_info, vehicle)
    return offer_builder_page


@fixture(scope="class")
@allure.title("Open Offer Builder with Trade-in flow")
def fx_cl_open_offer_builder_trade_in_flow(vehicle, fx_cl_resq_manager_page, fx_cl_set_cbo_trade_in_provider_setting,
                                           fx_cl_set_cbo_trade_in_provider_dealer_id_setting, lead_info, request)\
        -> OfferBuilderPage:
    def finalizer():
        offer_builder_page.close_offer_builder()

    request.addfinalizer(finalizer)

    offer_builder_page = fx_cl_resq_manager_page.offer_builder_page.open(lead_info, vehicle)
    return offer_builder_page


@fixture(scope="class")
@allure.title("Open Offer Builder via Update Deal flow")
def fx_cl_open_offer_builder_update_deal(fx_cl_resq_manager_page,  pii_entity: PIIEntity) -> OfferBuilderPage:
    offer_builder_page = fx_cl_resq_manager_page.offer_builder_page.open_offer_builder_update_deal(consumer_name=pii_entity.first_name + " " + pii_entity.last_name)
    return offer_builder_page


@fixture(scope="class")
@allure.title("Open Offer Builder via Update Deal flow with bundle setting enabled")
def fx_cl_open_offer_builder_update_deal_bundle(fx_cl_resq_manager_page, fx_cl_set_set_fi_flow_setting,
                                                pii_entity: PIIEntity) -> OfferBuilderPage:
    offer_builder_page = fx_cl_resq_manager_page.offer_builder_page.open_offer_builder_update_deal(consumer_name=pii_entity.first_name + " " + pii_entity.last_name)
    return offer_builder_page


@fixture(scope="class")
@allure.title("Open Offer Builder via Create Deal flow")
def fx_cl_open_offer_builder_without_finalizer(vehicle, fx_cl_resq_manager_page, lead_info) -> OfferBuilderPage:
    offer_builder_page = fx_cl_resq_manager_page.offer_builder_page.open(lead_info, vehicle)
    return offer_builder_page


@fixture(scope="class")
@allure.title("Vehicle info")
def vehicle() -> VehicleEntity:
    return VehicleDetails.get_vehicle_details(vin="2HKRS4H76SH425741")


@fixture(scope="class")
@allure.title("PII info")
def pii_entity() -> PIIEntity:
    return PIIFactory.random_pii()


@fixture(scope="class")
def cbo_cta_button() -> CboCTAButton:
    return CboCTAButton.CBO_UNLOCK_PAYMENTS


@fixture(scope="class")
@allure.title("Open Consumer page")
def fx_cl_consumer_page(request, consumer_name, fx_ss_reset_cbo_settings,
                        fx_cl_set_cbo_trade_in_provider_setting,
                        fx_cl_set_cbo_trade_in_provider_dealer_id_setting) -> ConsumerPage:
    def finalizer():
        consumer.quit()

    request.addfinalizer(finalizer)
    consumer = ConsumerPage(consumer_name, url=cbo_url).open()
    return consumer


@fixture(scope="class")
@allure.title("Open cbo page")
def open_cbo(fx_cl_consumer_page: ConsumerPage, vehicle: VehicleEntity, pii_entity: PIIEntity, cbo_cta_button,
             trade_in_provider):
    return fx_cl_consumer_page.open_cbo(vin=vehicle.vin, pii_entity=pii_entity, trade_in_provider=trade_in_provider,
                                        cbo_cta_button=cbo_cta_button)


@fixture(scope="class")
@allure.title("Open payments page")
def open_payments(open_cbo, cbo_payment_option_entity):
    return open_cbo.open_payment(cbo_payment_option_entity)
