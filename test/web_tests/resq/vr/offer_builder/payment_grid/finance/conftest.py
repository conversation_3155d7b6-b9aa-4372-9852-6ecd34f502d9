import allure
from pytest import fixture


from src.web.resq.offer_builder import PaymentType


@fixture(scope='class')
@allure.title("Offer Builder open finance program browser")
def payment_type():
    return PaymentType.FINANCE


@fixture(scope="function")
@allure.title("Offer Builder add one payment")
def fx_fn_add_one_payment(fx_fn_open_program_browser, request, payment_type):
    def finalizer():
        fx_fn_open_program_browser.ancestor.finance_payment_grid.remove_all.clear_all(payment_type)

    request.addfinalizer(finalizer)
    pb_payment = fx_fn_open_program_browser.add_finance_payment()
    return pb_payment


@fixture(scope="function")
@allure.title("Offer Builder add multiple payments")
def fx_fn_add_multiple_payments(fx_fn_open_program_browser):
    pb_payment = fx_fn_open_program_browser.add_finance_payment(2)
    return pb_payment
