import allure
from pytest import fixture

from test import pytestrail


@allure.feature('Offer Builder')
@allure.story('Add Payment')
class TestAddLeasePayments:

    @fixture(scope="function")
    @allure.title("Offer Builder add one payment")
    def fx_fn_add_one_payment(self, fx_fn_open_program_browser, request, payment_type):
        def finalizer():
            fx_fn_open_program_browser.ancestor.lease_payment_grid.remove_all.clear_all(payment_type)

        request.addfinalizer(finalizer)
        fx_fn_open_program_browser.select_lender('Ally Financial')
        pb_payment = fx_fn_open_program_browser.add_lease_payment()
        return pb_payment

    @pytestrail.case('********')
    def test_add_single_payment(self, fx_fn_add_one_payment, fx_cl_open_offer_builder):
        payment_grid_values = fx_cl_open_offer_builder.lease_payment_grid.get_pg_options_values()
        assert all(prg_br.monthly_payment == pmt_gr.monthly_payment and prg_br.rate == pmt_gr.rate and
                   prg_br.lender == pmt_gr.lender and
                   prg_br.term == pmt_gr.term
                   for prg_br, pmt_gr in zip(fx_fn_add_one_payment, payment_grid_values)), \
            "Values in the Payment Grid do not match Program Browser values"

    @pytestrail.case('C6203859')
    def test_add_multiple_payment(self, fx_fn_add_multiple_payments, fx_cl_open_offer_builder):
        payment_grid_values = fx_cl_open_offer_builder.lease_payment_grid.get_pg_options_values()
        assert all(prg_br.monthly_payment == pmt_gr.monthly_payment and prg_br.rate == pmt_gr.rate and
                   prg_br.lender == pmt_gr.lender and
                   prg_br.term == pmt_gr.term
                   for prg_br, pmt_gr in zip(fx_fn_add_multiple_payments, payment_grid_values)),\
            "Values in the Payment Grid do not match Program Browser values"

