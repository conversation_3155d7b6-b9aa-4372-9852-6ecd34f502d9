import allure

from test import pytestrail


@allure.feature('Offer Builder')
@allure.story('Price widget - Discount field')
class TestCashPriceWidgetDiscount:

    @pytestrail.case('C6559859')
    def test_change_discount_updates_selling_price(self, fx_cl_add_cash_payment, fx_cl_open_offer_builder):
        fx_cl_open_offer_builder.price_widget.change_discount("2000")
        values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        assert values.msrp - values.discount == values.selling_price, (f"Selling price '{values.selling_price}' is not"
                                                                       f"equal to msrp - discount "
                                                                       f"'{values.msrp - values.discount}'")

    @pytestrail.case('C6559860')
    def test_change_discount_updates_total_price(self, fx_cl_add_cash_payment, fx_cl_open_offer_builder):
        initial_values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        fx_cl_open_offer_builder.price_widget.change_discount("2500")
        updated_values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        assert initial_values.total_price > updated_values.total_price, \
            (f"The total price wasn't updated. Initial total price: '{initial_values.total_price}',"
             f" Updated total price: '{updated_values.total_price}'")

    @pytestrail.case('C6559861')
    def test_change_discount_updates_pg_total_price(self, fx_cl_add_cash_payment, fx_cl_open_offer_builder):
        initial_values = fx_cl_open_offer_builder.cash_payment_grid.get_pg_options_values()
        fx_cl_open_offer_builder.price_widget.change_discount("3000")
        updated_values = fx_cl_open_offer_builder.cash_payment_grid.get_pg_options_values()
        assert initial_values[0].total_price > updated_values[0].total_price, \
            (f"Total price in payment grid wasn't updated. Initial total price: '{initial_values.total_price}',"
             f" Updated total price: '{updated_values.total_price}'")
