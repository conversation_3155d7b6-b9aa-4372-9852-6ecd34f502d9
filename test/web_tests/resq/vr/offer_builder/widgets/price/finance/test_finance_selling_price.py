import allure
from pytest import fixture

from test import pytestrail


@allure.feature('Offer Builder')
@allure.story('Price widget - Selling Price field')
class TestFinancePriceWidgetSellingPrice:

    @pytestrail.case('C2894709')
    def test_discount_zero_when_selling_price_exceeds_msrp(self, fx_fn_add_one_payment, fx_cl_open_offer_builder):
        fx_cl_open_offer_builder.price_widget.change_selling_price("56000")
        values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        assert values.discount == 0, f"Discount '{values.discount}' is not equal to 0"

    @pytestrail.case('C2894710')
    def test_discount_calculated_as_msrp_minus_selling_price(self, fx_fn_add_one_payment, fx_cl_open_offer_builder):
        fx_cl_open_offer_builder.price_widget.change_selling_price("48500")
        values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        assert values.msrp - values.selling_price == values.discount, (f"Discount '{values.discount}' is not equal "
                                                                       f"to msrp - selling price total "
                                                                       f"'{values.msrp - values.selling_price}'")

    @pytestrail.case('C2894714')
    def test_change_selling_updates_discount(self, fx_fn_add_one_payment, fx_cl_open_offer_builder):
        initial_values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        fx_cl_open_offer_builder.price_widget.change_selling_price("48000")
        updated_values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        assert initial_values.discount < updated_values.discount, \
            (f"Discount wasn't updated. Initial discount: '{initial_values.discount}',"
             f" Updated discount: '{updated_values.discount}'")

    @pytestrail.case('C2894715')
    def test_change_selling_price_updates_total_price(self, fx_fn_add_one_payment, fx_cl_open_offer_builder):
        initial_values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        fx_cl_open_offer_builder.price_widget.change_selling_price("49000")
        updated_values = fx_cl_open_offer_builder.price_widget.get_price_widget_values()
        assert initial_values.total_price > updated_values.total_price, \
            (f"Total price wasn't updated. Initial total price: '{initial_values.total_price}',"
             f" Updated total price: '{updated_values.total_price}'")

    @pytestrail.case('C2894716')
    def test_change_selling_price_updates_pg_monthly_payment(self, fx_fn_add_one_payment, fx_cl_open_offer_builder):
        initial_values = fx_cl_open_offer_builder.finance_payment_grid.get_pg_options_values()
        fx_cl_open_offer_builder.price_widget.change_selling_price("48500")
        updated_values = fx_cl_open_offer_builder.finance_payment_grid.get_pg_options_values()
        assert initial_values[0].monthly_payment > updated_values[0].monthly_payment, \
            (f"Monthly payment in payment grid wasn't updated. Initial monthly payment: '{initial_values.monthly_payment}',"
             f" Updated monthly payment: '{updated_values.monthly_payment}'")

