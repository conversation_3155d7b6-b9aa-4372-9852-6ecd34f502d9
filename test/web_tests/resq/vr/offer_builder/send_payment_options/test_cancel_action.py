
import allure
from pytest import fixture

from test import pytestrail


@allure.feature('Offer Builder')
@allure.story('Cancel Send Payment Option')
class TestCancelPaymentOption:
    @fixture(scope="class")
    @allure.title("Offer Builder open payment option modal")
    def fx_cl_open_send_payment_option_modal(self, fx_cl_open_offer_builder, payment_type):
        fx_cl_open_offer_builder.program_browser.open(payment_type)
        fx_cl_open_offer_builder.program_browser.add_finance_payment()
        send_payment_option_modal = fx_cl_open_offer_builder.send_payment_options.open()
        return send_payment_option_modal

    @pytestrail.case('C2326080')
    def test_cancel_payment_option(self, fx_cl_open_send_payment_option_modal):
        with allure.step("CHECK - SPO Modal is closed when dealer click the 'Cancel' button"):
            fx_cl_open_send_payment_option_modal.check_cancel_acton()
