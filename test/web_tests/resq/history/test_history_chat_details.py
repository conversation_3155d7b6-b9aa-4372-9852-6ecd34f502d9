import allure
import pytest
from pytest import fixture
from time import sleep

from src.web.chat_console.chat_console_page import ChatConsolePage
from src.web.entities.message_entity import MessageFactory
from test import pytestrail
from test.web_tests import ConsumerPage


@allure.feature('Resq')
@allure.story('History')
class TestHTMLTagInTranscriptPortuguese:

    @fixture(scope='class')
    @allure.title("Message from operator")
    def message_from_operator(self):
        return MessageFactory.text("Are you interested in leasing?\n\nAre you interested in renting?\n\n")

    @fixture(scope='class')
    @allure.title("Message  from consumer")
    def message_from_consumer(self):
        return "Eu gostaria de ter um carro novo.\n\nOnde posso ver os modelos disponíveis?\n\n"

    @fixture(scope='class')
    def language(self):
        return "Portuguese"

    @fixture(scope='class')
    @allure.title("Make a conversation")
    def fx_cl_conversation(self, fx_cl_chat_manager_page: ChatConsolePage,
                           fx_cl_consumer_page: ConsumerPage,
                           fx_cl_chat_manager_change_language,
                           message_from_operator, message_from_consumer):
        fx_cl_chat_manager_page.chat_component.send_message(message_from_operator,
                                                            fx_cl_consumer_page.get_chat_id(wait_time=3))
        sleep(1)
        fx_cl_consumer_page.send_message(message_from_consumer)

    @fixture(scope="class")
    @allure.title("Open GLive page and sign in as a manager")
    def fx_cl_resq_manager_page(self, request):
        def finalizer():
            if manager.driver:
                manager.sign_out()
                manager.quit()

        request.addfinalizer(finalizer)

        from src.web.resq.resq_page import ResQPage
        from src import resq_manager_user_name
        from src import resq_password
        manager = ResQPage(resq_manager_user_name, resq_password).open()
        manager.conversations.open()
        return manager

    @pytest.mark.parametrize("expected_message_operator, expected_message_consumer", [
        pytest.param("I('d| would) like to have a new car\.\n\nWhere can I see the available models\?",
                     "Are you interested in leasing\?\n\nAre you interested in renting\?",
                     marks=[pytestrail.case('C781047'), pytest.mark.ktf(reason="GL-3834", strict=False)])])
    # TODO: Check the status, was moved from consumer.chat.receive_message.test_html_tag_in_transcript
    def test_glive_multiline_message_in_history(self,
                                                fx_cl_resq_manager_page,
                                                fx_cl_consumer_page: ConsumerPage,
                                                fx_cl_conversation,
                                                expected_message_operator, expected_message_consumer):
        fx_cl_resq_manager_page.history.open()
        chat_detail = fx_cl_resq_manager_page.history.open_chat_detail(fx_cl_consumer_page.get_chat_id(wait_time=3))
        with allure.step("CHECK - if consumer can receive multiline message "):
            assert chat_detail.get_message_by_regex(expected_message_operator) and \
                   chat_detail.get_message_by_regex(expected_message_consumer)
