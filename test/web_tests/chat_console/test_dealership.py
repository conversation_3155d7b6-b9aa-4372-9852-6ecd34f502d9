import json
from datetime import timed<PERSON>ta

import allure
import datetime
import dateutil.relativedelta as REL
import pytest
from pytest import fixture

from kom_framework.src.general import non_zero_padded
from src import chat_console_account_id, chat_console_manager_user_name, chat_console_password
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.utils.holidays_open_hours import UPCOMING_HOLIDAYS, WD, DWD, upcoming_holidays_dates
from src.web.chat_console.chat.dealership_component import DealershipWorkingHoursType
from src.web.chat_console.chat_console_page import ChatConsolePage
from src.web.consumer import AIBotResponse
from src.web.entities.message_entity import MessageFactory
from test import pytestrail


@allure.epic('Chat')
@allure.feature('Dealership')
class TestChatConsoleDealershipRegularHours:

    @pytest.mark.parametrize("message, expect_message", [
        pytest.param(MessageFactory.dealership_dealership_hours(DealershipWorkingHoursType.TODAY),
                     AIBotResponse.DEALERSHIP_TODAY(),
                     marks=[pytestrail.case('C176348')]),
        pytest.param(MessageFactory.dealership_dealership_hours(DealershipWorkingHoursType.SALES),
                     AIBotResponse.DEALERSHIP_DEPARTMENT(dep=DealershipWorkingHoursType.SALES),
                     marks=[pytestrail.case('C176457'), pytest.mark.ktf(reason="GL-5983")]),
        pytest.param(MessageFactory.dealership_dealership_hours(DealershipWorkingHoursType.SERVICE),
                     AIBotResponse.DEALERSHIP_DEPARTMENT(dep=DealershipWorkingHoursType.SERVICE),
                     marks=[pytestrail.case('C176458'), pytest.mark.ktf(reason="GL-5983")]),
        pytest.param(MessageFactory.dealership_dealership_hours(DealershipWorkingHoursType.PARTS),
                     AIBotResponse.DEALERSHIP_DEPARTMENT(dep=DealershipWorkingHoursType.PARTS),
                     marks=[pytestrail.case('C176459'), pytest.mark.ktf(reason="GL-5983")]),
        # pytest.param(MessageFactory.dealership_upcoming_holiday({"name": UPCOMING_HOLIDAYS[holidays_dates[0]],
        #                                                         "date": holidays_dates[0].strftime(f"%A,\n%b %{non_zero_padded}d")}),
        #              AIBotResponse.DEALERSHIP_TODAY(week_day_id=holidays_dates[0].weekday(),
        #                                             day_name=f"{UPCOMING_HOLIDAYS[holidays_dates[0]]} hours"),
        #              marks=[pytestrail.case('C2488236'),
        #                     pytest.mark.skipif(len(holidays_dates) == 0 or holidays_dates[0] == datetime.date.today(),
        #                                        reason="No upcoming Statutory Holiday")])
    ])
    def test_send_hours(self,
                        fx_fn_chat_manager_send_message,
                        fx_cl_consumer_page,
                        message,
                        expect_message):
        with allure.step("CHECK - if consumer can receive message with dealership "):
            fx_cl_consumer_page.chat_component.wait_for_message(expect_message)

    # @pytest.mark.parametrize("expected_holiday", [
    #     pytest.param([{"date": date.strftime(f"%b %{non_zero_padded}d"),
    #                    "name": UPCOMING_HOLIDAYS[date],
    #                    "hours": AIBotResponse.DEALERSHIP_TODAY(week_day_id=date.weekday(), dict=True)} for date in holidays_dates],
    #                  marks=[pytestrail.case('C2500485')])])
    # @pytest.mark.skipif(len(holidays_dates) == 0, reason="No Upcoming holidays")
    # def test_not_configured_upcoming_holidays(self,
    #                                         fx_cl_consumer_page,
    #                                         fx_cl_chat_manager_page,
    #                                         expected_holiday):
    #     actual_holiday_list = fx_cl_chat_manager_page.chat_component.dealer_component.holidays.get_holidays_list()
    #     assert DeepDiff(expected_holiday, actual_holiday_list, ignore_string_case=True) == {},\
    #         f"Expected to see not configured holidays {expected_holiday}, but {actual_holiday_list} was found"

    @pytestrail.case('C2500485')
    def test_no_upcoming_holidays(self, fx_cl_consumer_page, fx_cl_chat_manager_page):
        fx_cl_chat_manager_page.chat_component.dealer_component.holidays.open()  # may need adjustment
        with allure.step("CHECK - if expected response appears"):
            assert fx_cl_chat_manager_page.chat_component.dealer_component.wait_for.text_to_be_present_in_element("No holidays next 30 days", wait_time=1), \
                f"Expected to see 'No holidays next 30 days'"


@allure.epic('Chat')
@allure.feature('Dealership')
class TestChatConsoleDealershipHoliday:

    @fixture(scope="class")
    @allure.title("Set execution hours setting")
    def fx_cl_set_exclusion_hours_setting(self, request):
        def finalizer():
            Gb1Settings.set_exclusion_hours_setting(exclusion_hours_value="")

        request.addfinalizer(finalizer)

        exclusion_hours_value = f'{{"{datetime.datetime.today().strftime("%Y-%m-%d")}":{{"sales":{{"to":"14:00","from":"8:00","closed":false,"active":true}},"service":{{"to":"14:30","from":"8:00","closed":false,"active":true}},"parts":{{"to":"15:00","from":"8:00","closed":false,"active":true}},"bodyshop":{{"to":"6:30","from":"1:30","closed":true,"active":false}},"special_event":"SD {datetime.datetime.today().strftime("%Y-%m-%d")}", "re_apply":"0"}}}}'
        Gb1Settings.set_exclusion_hours_setting(exclusion_hours_value=exclusion_hours_value)

    @fixture(scope="class")
    @allure.title("Open Chat Console and sign in as a chat operator")
    def fx_cl_chat_manager_page(self, request, fx_cl_set_exclusion_hours_setting) -> ChatConsolePage:
        def finalizer():
            manager.sign_out()
            manager.quit()

        request.addfinalizer(finalizer)

        manager = ChatConsolePage(chat_console_account_id, chat_console_manager_user_name, chat_console_password).open()
        manager.go_online()
        return manager

    @pytest.mark.parametrize("message, expect_message", [
        pytest.param(MessageFactory.dealership_dealership_hours(DealershipWorkingHoursType.TODAY),
                     AIBotResponse.DEALERSHIP_TODAY(week_day_id="H"),
                     marks=[pytestrail.case('C176448')]),
        pytest.param(MessageFactory.dealership_dealership_hours(DealershipWorkingHoursType.SALES),
                     AIBotResponse.DEALERSHIP_DEPARTMENT(dep=DealershipWorkingHoursType.SALES),
                     marks=[pytestrail.case('C176460'), pytest.mark.ktf(reason="GL-5983")]),
        pytest.param(MessageFactory.dealership_dealership_hours(DealershipWorkingHoursType.SERVICE),
                     AIBotResponse.DEALERSHIP_DEPARTMENT(dep=DealershipWorkingHoursType.SERVICE),
                     marks=[pytestrail.case('C176461'), pytest.mark.ktf(reason="GL-5983")]),
        pytest.param(MessageFactory.dealership_dealership_hours(DealershipWorkingHoursType.PARTS),
                     AIBotResponse.DEALERSHIP_DEPARTMENT(dep=DealershipWorkingHoursType.PARTS),
                     marks=[pytestrail.case('C176462'), pytest.mark.ktf(reason="GL-5983")]),
    ])
    def test_send_holiday_hour(self,
                               fx_fn_chat_manager_send_message,
                               fx_cl_consumer_page,
                               message,
                               expect_message):
        with allure.step("CHECK - if consumer can receive message with dealership "):
            fx_cl_consumer_page.chat_component.wait_for_message(expect_message)

    @pytest.mark.parametrize("expected_holiday", [pytest.param([], marks=[pytestrail.case('C2533694')])])
    def test_no_today_special_event_in_upcoming_holidays(self,
                                                        fx_cl_consumer_page,
                                                        fx_cl_chat_manager_page,
                                                        expected_holiday):
        actual_holiday_list = fx_cl_chat_manager_page.chat_component.dealer_component.holidays.get_holidays_list()
        assert [] == actual_holiday_list, \
            f"Expected no upcoming holidays, but {actual_holiday_list} was found"

    @pytestrail.case('C2533694')
    def test_no_upcoming_holidays(self, fx_cl_consumer_page, fx_cl_chat_manager_page):
        fx_cl_chat_manager_page.chat_component.dealer_component.holidays.open()  # may need adjustment
        assert fx_cl_chat_manager_page.chat_component.dealer_component.wait_for.text_to_be_present_in_element("No holidays next 30 days", wait_time=1), \
            f"Expected to see 'No holidays next 30 days'"


@allure.epic('Chat')
@allure.feature('Dealership')
class TestChatConsoleDealershipUpcomingHoliday:

    @fixture(scope="class")
    @allure.title("Generate Holiday's Hours")
    def hoh(self):
        hdl = upcoming_holidays_dates.copy()
        i = 4
        j = 1
        while i > 0:
            if datetime.date.today() + REL.relativedelta(days=j) not in hdl:
                hdl.append(datetime.date.today() + REL.relativedelta(days=j))
                i -= 1
            j += 1

        working_schedule = [
            DWD(sales=WD("8:00", "14:00"), service=WD("8:00", "14:30"), parts=WD("8:00", "15:00"),
                special_event="SD {}", re_apply="0", note="SD"),
            DWD(sales=WD("9:00", "15:00"), service=WD("9:00", "15:30"), parts=WD("9:00", "16:00"),
                special_event="SDR {}", re_apply="1", note="SDR"),
            DWD(sales=WD("10:00", "16:00"), service=WD("10:00", "16:30"), parts=WD("10:00", "17:00"),
                holiday=UPCOMING_HOLIDAYS[upcoming_holidays_dates[0]], re_apply="0", note="H"),
            DWD(sales=WD("11:00", "17:00"), service=WD("11:00", "17:30"), parts=WD("11:00", "18:00"),
                holiday=UPCOMING_HOLIDAYS[upcoming_holidays_dates[0]], re_apply="1", note="HR")]
        hoh = {}
        for d in hdl:
            k = d.strftime("%Y-%m-%d")
            if upcoming_holidays_dates and d == upcoming_holidays_dates[0]:
                hoh[k] = DWD(sales=WD("12:00", "18:00"), service=WD("12:00", "18:30"), parts=WD("12:00", "19:00"),
                             holiday=UPCOMING_HOLIDAYS[upcoming_holidays_dates[0]], re_apply="0", note="HRD")
            elif d in upcoming_holidays_dates:
                pass  # add not configured holiday
            else:
                hoh[k] = working_schedule.pop(0)
                if hoh[k].special_event:
                    hoh[k].special_event = hoh[k].special_event.format(k)
        return hoh

    @fixture(scope="class")
    @allure.title("Set execution hours setting")
    def fx_cl_set_exclusion_hours_setting(self, request, hoh):
        def finalizer():
            Gb1Settings.set_exclusion_hours_setting(exclusion_hours_value="")

        request.addfinalizer(finalizer)

        exclusion_hours_value = {k: hoh[k].__dict__() for k in hoh.keys()}
        Gb1Settings.set_exclusion_hours_setting(exclusion_hours_value=json.dumps(exclusion_hours_value))

    @fixture(scope="class")
    @allure.title("Open Chat Console and sign in as a chat operator")
    def fx_cl_chat_manager_page(self, request, fx_cl_set_exclusion_hours_setting) -> ChatConsolePage:
        def finalizer():
            manager.sign_out()
            manager.quit()

        request.addfinalizer(finalizer)

        manager = ChatConsolePage(chat_console_account_id, chat_console_manager_user_name, chat_console_password).open()
        manager.go_online()
        return manager

    @pytest.mark.parametrize("expected_special_event_workin_hours, event", [
        pytest.param({"Sales": "8 am — 2 pm", "Service": "8 am — 2:30 pm", "Parts": "8 am — 3 pm"},
                     "SD",
                     marks=[pytestrail.case('C445186')]),
        pytest.param({"Sales": "9 am — 3 pm", "Service": "9 am — 3:30 pm", "Parts": "9 am — 4 pm"},
                     "SDR",
                     marks=[pytestrail.case('C445187')]),
        pytest.param({"Sales": "11 am — 5 pm", "Service": "11 am — 5:30 pm", "Parts": "11 am — 6 pm"},
                     "HR",
                     marks=[pytestrail.case('C445190'), pytest.mark.ktf(reason="GL-574")]),
        pytest.param({"Sales": "10 am — 4 pm", "Service": "10 am — 4:30 pm", "Parts": "10 am — 5 pm"},
                     "H",
                     marks=[pytestrail.case('C1137655'), pytest.mark.ktf(reason="GL-574")]),
        pytest.param({"Sales": "12 pm — 6 pm", "Service": "12 pm — 6:30 pm", "Parts": "12 pm — 7 pm"},
                     "HRD",
                     marks=[pytestrail.case('C445188')])
    ])
    @pytest.mark.ktf(reason='CC-1456', condition=(upcoming_holidays_dates[0] - datetime.date.today()) > timedelta(days=30))
    def test_holiday_hour_in_upcoming_holidays(self, fx_cl_chat_manager_page, fx_cl_consumer_page, hoh,
                                               expected_special_event_workin_hours, event):
        for date in hoh.keys():
            if hoh[date].note == event:
                break
        title = hoh[date].special_event if hoh[date].special_event else hoh[date].holiday
        date = datetime.datetime.strptime(date, '%Y-%m-%d').strftime(f'%b %{non_zero_padded}d')
        actual_holiday_list = fx_cl_chat_manager_page.chat_component.dealer_component.holidays.get_holidays_list()
        holiday_hours_in_upcoming_holiday = [i['hours'] for i in actual_holiday_list if i['name'].capitalize() == title.capitalize() and i['date'] == date]
        assert len(holiday_hours_in_upcoming_holiday) == 1, f'One {title} {date} was expected to be found in {actual_holiday_list}, but it was found {len(holiday_hours_in_upcoming_holiday)} times'
        assert len(holiday_hours_in_upcoming_holiday) >= 1 and holiday_hours_in_upcoming_holiday[0] == \
               expected_special_event_workin_hours, f'Hour of opening {holiday_hours_in_upcoming_holiday[0]} are not match to expected {expected_special_event_workin_hours}'
