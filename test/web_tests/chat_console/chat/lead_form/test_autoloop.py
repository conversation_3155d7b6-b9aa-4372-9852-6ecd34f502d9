import re
from datetime import datetime

import allure
import pytest
from pytest import fixture

from src import xtime_id
from src.rest_api.services.leads import Leads
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.web.consumer import ServicesType, ServiceProviderType
from src.web.entities.lead_entity import WebLeadFactory, WebLeadEntity
from src.web.entities.message_entity import MessageFactory
from test import pytestrail
from test.web_tests import xtime_message_pattern


@allure.epic('Chat')
@allure.feature('Lead Form')
@allure.story('Autoloop')
@pytest.mark.ktf(reason='GL-4081')
class TestChatConsoleAutoloop:

    @fixture(scope="class")
    @allure.title("Set Autoloop as service scheduling provider")
    def fx_cl_set_autoloop_service(self, request):
        def finalizer():
            Gb1Settings.set_service_scheduling_provider_setting()
            Gb1Settings.set_offer_form_setting()

        request.addfinalizer(finalizer)

        Gb1Settings.set_service_scheduling_provider_setting(service_scheduling_id_value="239",
                                                            service_scheduling_provider_value=ServiceProviderType.AUTOLOOP)
        Gb1Settings.set_offer_form_setting(offer_id=xtime_id, form_value='autoloop', name="Publisher Scheduling Offer with Autoloop")

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self):
        return WebLeadFactory.service_appointment_valid_phone(vehicle_year="2011",
                                                              vehicle_make="Toyota",
                                                              vehicle_model="Camry",
                                                              appointment_services=[ServicesType.TIRE_INSTALLATION],
                                                              notes="This is a test",
                                                              custom_type=ServiceProviderType.AUTOLOOP)

    @fixture(scope="class")
    @allure.title("Chat console operator submits Autoloop service appointment scheduling form")
    def submit_autoloop(self, fx_cl_set_autoloop_service, fx_cl_chat_manager_page, fx_cl_consumer_page, lead_info):
        pytest.xfail('GL-4081')
        fx_cl_chat_manager_page.chat_component.schedule_service_appointment(
            chat_id=fx_cl_consumer_page.get_chat_id(wait_time=3),
            lead=lead_info, service_provider=ServiceProviderType.AUTOLOOP)

    @fixture(scope="class")
    @allure.title("Chat console operator sends Autoloop service appointment scheduling form")
    def send_autoloop(self, fx_cl_chat_manager_page, submit_autoloop):
        fx_cl_chat_manager_page.chat_component.btn_send.click()

    @pytestrail.case('C1683457')
    def test_chat_console_autoloop_lead(self, submit_autoloop, lead_info: WebLeadEntity):
        lead_info.transport_type = "DROPOFF" ## TODO: remove workaround
        with allure.step("CHECK - if filled xtime appeared in leads"):
            Leads.find_service_appointment_lead(lead_info)

    @pytestrail.case('C1683458')
    def test_chat_console_autoloop_send(self, send_autoloop, fx_cl_consumer_page, lead_info: WebLeadEntity):
        with allure.step("CHECK - if xtime info can be send"):
            fx_cl_consumer_page.chat_component.wait_for_message_by_regex(MessageFactory.text(xtime_message_pattern))

    @pytestrail.case('C1683459')
    def test_chat_console_autoloop_send_code(self,
                                             send_autoloop,
                                             fx_cl_consumer_page,
                                             lead_info: WebLeadEntity):
        message = fx_cl_consumer_page.chat_component.get_message_by_regex(
            MessageFactory.text(xtime_message_pattern)).body.text
        with allure.step("CHECK - xtime code"):
            assert lead_info.appointment_id == re.search(xtime_message_pattern, message).groupdict().get('code')

    @pytestrail.case('C1683460')
    def test_chat_console_autoloop_send_date(self,
                                             send_autoloop,
                                             fx_cl_consumer_page,
                                             lead_info: WebLeadEntity):
        message = fx_cl_consumer_page.chat_component.get_message_by_regex(MessageFactory.text(xtime_message_pattern)).body.text
        appointment = re.sub(r'(\d)(st|nd|rd|th)', r'\1', re.search(xtime_message_pattern, message).groupdict().get('date')).lower()
        appointment_send = datetime.strptime(lead_info.appointment_date + " at " + lead_info.appointment_time, '%m/%d/%Y at %I:%M %p').strftime('%B %d, %Y at %I:%M %p').lower()
        with allure.step("CHECK - xtime date"):
            assert appointment_send == appointment

    @pytestrail.case('C1683461')
    def test_chat_console_autoloop_send_email(self,
                                              send_autoloop,
                                              fx_cl_consumer_page,
                                              lead_info: WebLeadEntity):
        message = fx_cl_consumer_page.chat_component.get_message_by_regex(
            MessageFactory.text(xtime_message_pattern)).body.text
        with allure.step("CHECK - xtime email"):
            assert lead_info.email == re.search(xtime_message_pattern, message).groupdict().get('email')
