import allure
import pytest
from pytest import fixture

from src.web.chat_console.chat_console_page import ChatConsolePage
from src.web.entities.message_entity import MessageFactory
from test import pytestrail
from test.web_tests import ConsumerPage


@allure.epic('Chat')
@allure.feature('Receive message')
@allure.story('Email')
class TestChatSendEmail:

    @pytestrail.case('C61')
    @pytest.mark.parametrize("email", [pytest.param("<EMAIL>")])
    def test_send_email(self,
                        fx_cl_chat_manager_page: ChatConsolePage,
                        fx_cl_consumer_page: ConsumerPage,
                        email):
        fx_cl_consumer_page.send_message(email)
        message = fx_cl_chat_manager_page.chat_component.get_message_by_pattern(email, fx_cl_consumer_page.get_chat_id(wait_time=3))
        email_from_lead = fx_cl_chat_manager_page.chat_component.get_lead_email(fx_cl_consumer_page.get_chat_id(wait_time=3))
        with allure.step("CHECK - if chat operator received message with email that is marked with eye icon. Received email is shown in email field in Lead panel."):
            assert email_from_lead == email and message.info.exists()

    @pytestrail.case('C62')
    @pytest.mark.parametrize("email", [pytest.param("<EMAIL>")])
    def test_update_email(self,
                          fx_cl_chat_manager_page: ChatConsolePage,
                          fx_cl_consumer_page: ConsumerPage,
                          email):
        fx_cl_consumer_page.send_message("<EMAIL>")
        fx_cl_consumer_page.send_message(email)
        message = fx_cl_chat_manager_page.chat_component.get_message_by_pattern(email, fx_cl_consumer_page.get_chat_id(wait_time=3))
        email_from_lead = fx_cl_chat_manager_page.chat_component.get_lead_email(fx_cl_consumer_page.get_chat_id(wait_time=3))
        with allure.step("CHECK - if chat operator received message with updated email that is marked with eye icon. Received updated email is shown in email field in Lead panel."):
            assert email == email_from_lead and message.info.exists()

    @pytest.mark.parametrize("message, email", [
        pytest.param("<EMAIL> (Joe Smith)", "<EMAIL>", marks=[pytestrail.case('C103')]),
        pytest.param("あいうえお@domain.com", "あいうえお@domain.com", marks=[pytestrail.case('C71'), pytest.mark.ktf(reason='CU-1782')]),
        pytest.param(".<EMAIL>", "<EMAIL>", marks=[pytestrail.case('C74')]),
        pytest.param("<EMAIL>", "<EMAIL>", marks=[pytestrail.case('C75')]),
        pytest.param("email@<EMAIL>", "<EMAIL>", marks=[pytestrail.case('C73')]),
        pytest.param("ronaldo de Guzman 818 360 5101 <EMAIL> give me your best deal", "<EMAIL>", marks=[pytestrail.case('C104')]),
        pytest.param("My <NAME_EMAIL> as i am working all weekend i wont be able to take to many calls thanks", "<EMAIL>",
                     marks=[pytestrail.case('C121')]),
        pytest.param("John <EMAIL>", "<EMAIL>", marks=[pytestrail.case('C105'), pytest.mark.ktf(reason="GL-4087")]),
    ])
    def test_send_formats(self,
                          fx_cl_chat_manager_page: ChatConsolePage,
                          fx_cl_consumer_page: ConsumerPage,
                          message, email):
        fx_cl_consumer_page.send_message(message)
        chat_id = fx_cl_consumer_page.get_chat_id(wait_time=3)
        with allure.step("CHECK - if message with correct email that is marked with eye icon. Correct part of received email is shown in email field in Lead panel."):
            message_received = fx_cl_chat_manager_page.chat_component.get_message_by_pattern(email, chat_id, 10)
            email_from_lead = fx_cl_chat_manager_page.chat_component.get_lead_email(chat_id)
            assert email_from_lead.lower() == email.lower() and message_received.info.exists(5)

    @pytest.mark.parametrize("email", [
        pytest.param("plainaddress", marks=[pytestrail.case('C63')]),
        pytest.param("#@%^%#$@#$@#.com", marks=[pytestrail.case('C64')]),
        pytest.param("@mydomain.com", marks=[pytestrail.case('C65')]),
        pytest.param("email.domain.com", marks=[pytestrail.case('C66')]),
        pytest.param("<EMAIL>", marks=[pytestrail.case('C68'),
                                                 pytest.mark.ktf(reason="GL-4087")]),
        pytest.param("email@111.222.333.44444", marks=[pytestrail.case('C69'),
                                                       pytest.mark.ktf(reason="OD-619"),
                                                       pytest.mark.ktf(reason="GL-4087")]),
        pytest.param("<EMAIL>", marks=[pytestrail.case('C70')]),
        pytest.param("Adrian Smith <<EMAIL>>", marks=[pytestrail.case('C67'),
                                                               pytest.mark.ktf(reason="GL-4087")]),
        pytest.param("myemail@domain", marks=[pytestrail.case('C72')]),
        pytest.param("<EMAIL>", marks=[pytestrail.case('C76'),
                                                 pytest.mark.ktf(reason="GL-4087")]),
    ])
    def test_send_invalid_email(self,
                                fx_cl_chat_manager_page: ChatConsolePage,
                                fx_cl_consumer_page: ConsumerPage,
                                email):
        fx_cl_consumer_page.send_message(email)
        chat_id = fx_cl_consumer_page.get_chat_id(wait_time=3)
        with allure.step("CHECK - if message with incorrect email is not marked with eye icon. Received email is not shown in email field in Lead panel."):
            message_received = fx_cl_chat_manager_page.chat_component.get_message_by_pattern(email.split(" ")[0],
                                                                                             chat_id, 10)
            email_from_lead = fx_cl_chat_manager_page.chat_component.get_lead_email(chat_id)
            assert email_from_lead != email and not message_received.info.exists(2)


@allure.epic('Chat')
@allure.feature('Receive message')
@allure.story('Email')
class TestChatSendEmailFromQueue:

    @fixture(scope="class")
    @allure.title("Fill operator queue with chats (4 chats are active)")
    def fx_cl_create_queue(self, request, fx_cl_chat_manager_page):
        def finalizer():
            fx_cl_chat_manager_page.chat_component.close_chat(consumer_2.get_chat_id(wait_time=3))
            fx_cl_chat_manager_page.chat_component.close_chat(consumer_3.get_chat_id(wait_time=3))
            fx_cl_chat_manager_page.chat_component.close_chat(consumer_4.get_chat_id(wait_time=3))
        request.addfinalizer(finalizer)

        consumer_1 = ConsumerPage("Customer_1").open()
        consumer_1.start_chat()
        fx_cl_chat_manager_page.chat_component.select_chat_by_id(consumer_1.get_chat_id(wait_time=3))
        consumer_2 = ConsumerPage("Customer_2").open()
        consumer_2.start_chat()
        fx_cl_chat_manager_page.chat_component.select_chat_by_id(consumer_2.get_chat_id(wait_time=3))
        consumer_3 = ConsumerPage("Customer_3").open()
        consumer_3.start_chat()
        fx_cl_chat_manager_page.chat_component.select_chat_by_id(consumer_3.get_chat_id(wait_time=3))
        consumer_4 = ConsumerPage("Customer_4").open()
        consumer_4.start_chat()
        fx_cl_chat_manager_page.chat_component.select_chat_by_id(consumer_4.get_chat_id(wait_time=3))
        return consumer_1

    @fixture(scope="class")
    @allure.title("Open Consumer Page")
    def fx_cl_consumer_page(self, request, fx_cl_create_queue, consumer_name, fx_cl_chat_manager_page) -> ConsumerPage:
        def finalizer():
            fx_cl_chat_manager_page.chat_component.close_chat(consumer.get_chat_id(wait_time=3))
        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name).open()
        consumer.start_chat()
        return consumer

    @pytestrail.case('C14993')
    @pytest.mark.ktf(reason='CC-1491')
    @pytest.mark.parametrize("message, email", [pytest.param("My <NAME_EMAIL> as i am working all weekend i wont be able to take to many calls thanks", "<EMAIL>")])
    def test_send_email_after_queue(self,
                                    fx_cl_chat_manager_page: ChatConsolePage,
                                    fx_cl_create_queue,
                                    fx_cl_consumer_page: ConsumerPage,
                                    message, email):
        fx_cl_consumer_page.send_message(message)
        chat_id = fx_cl_consumer_page.get_chat_id(wait_time=3)
        fx_cl_chat_manager_page.chat_component.send_message(MessageFactory.text("Closing this chat"),
                                                            fx_cl_create_queue.get_chat_id(wait_time=3))
        fx_cl_chat_manager_page.chat_component.close_chat(fx_cl_create_queue.get_chat_id(wait_time=3), auto_engage="1")
        message = fx_cl_chat_manager_page.chat_component.get_message_by_pattern(email, chat_id, wait_time=5)
        email_from_lead = fx_cl_chat_manager_page.chat_component.get_lead_email(chat_id)
        with allure.step("CHECK - if message with correct email that is marked with eye icon. Correct part of received email is shown in email field in Lead panel."):
            assert email_from_lead == email and message.info.exists()
