import allure
import names
from pytest import fixture

from src import cbo_url, resq_password, resq_manager_user_name, resq_dealer_account_name
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.utils.converter import Converter
from src.web.consumer.desktop.cbo.show_room_component import ShowRoom
from src.web.consumer.desktop.vdp_page import VDP3Page
from src.web.consumer.vdp_page_behavior import OpenVDPFrom
from src.web.resq.resq_page import ResQPage
from src.web.resq.vr.virtualRetailing import VirtualRetailing
from test.web_tests.cbo import ConsumerPage


@fixture(scope="class")
@allure.title("vdp flow")
def vdp_flow():
    return "full_vr"


@fixture(scope="session")
@allure.title("vdp version")
def vdp_version():
    return 3


@fixture(scope="class")
@allure.title("Change vdp flow setting in GB1")
def fx_cl_set_vdp_flow_setting(request, vdp_flow):
    def finalizer():
        Gb1Settings.set_vdp_flow_setting()

    request.addfinalizer(finalizer)
    Gb1Settings.set_vdp_flow_setting(vdp_flow_value=vdp_flow)


@fixture(scope="session")
@allure.title("Change vdp version setting in GB1")
def fx_ss_set_vdp_version_setting(request, vdp_version):
    def finalizer():
        Gb1Settings.set_vdp_version_setting()

    request.addfinalizer(finalizer)
    Gb1Settings.set_vdp_version_setting(vdp_version_value=vdp_version)


@fixture(scope="class")
@allure.title("Change cbo flow setting in GB1")
def fx_cl_set_cbo_flow_setting():
    Gb1Settings.set_cbo_flow_setting()


@fixture(scope="class")
@allure.title("Change cbo account package setting in GB1")
def fx_cl_cbo_account_package_setting():
    Gb1Settings.set_cbo_account_package_setting()


@fixture(scope="class")
@allure.title("Change cbo lease mileage setting in GB1")
def fx_cl_set_cbo_lease_mileage_setting(request, cbo_lease_mileage_value):
    def finalizer():
        Gb1Settings.set_cbo_lease_mileage()

    request.addfinalizer(finalizer)
    Gb1Settings.set_cbo_lease_mileage(cbo_lease_mileage_value=cbo_lease_mileage_value)


@fixture(scope="class")
@allure.title("Open VDP page")
def fx_cl_vdp3(request, fx_ss_reset_cbo_settings, fx_cl_set_vdp_flow_setting, fx_cl_cbo_account_package_setting,
               vehicle) -> VDP3Page:
    def finalizer():
        vdp3.quit()

    request.addfinalizer(finalizer)
    vdp3 = ConsumerPage(names.get_first_name(), url=cbo_url).open() \
        .vdp3.open(vin_stock=vehicle.stock, open_from=OpenVDPFrom.CHAT)
    return vdp3


@fixture(scope="class")
@allure.title("Open Virtual Retailing in Glive and sign in as manager")
def fx_cl_resq_manager_vr_page(request) -> VirtualRetailing:
    def finalizer():
        resq.sign_out()
        resq.quit()

    request.addfinalizer(finalizer)

    resq = ResQPage(resq_manager_user_name, resq_password).open()
    resq.change_account(resq_dealer_account_name)
    vr = resq.vr.open()
    return vr


@fixture(scope="class")
@allure.title("Vehicle stock number")
def stock_number(vehicle):
    return vehicle.stock


@fixture(scope='class')
@allure.title('Initial price')
def fx_cl_initial_price(fx_cl_open_vdp_logged):
    value = fx_cl_open_vdp_logged.txt_price.text if fx_cl_open_vdp_logged.txt_price.exists() \
        else fx_cl_open_vdp_logged.txt_locked_price.text
    return int(Converter.extract_digits(value))


@fixture(scope='class')
@allure.title('Showroom is logging')
def showroom_logging():
    return True


@fixture(scope="class")
def fx_cl_set_cbo_enabled_setting():
    Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=1)


@fixture(scope="class")
@allure.title("Apply trade-in")
def fx_cl_apply_trade_in(fx_cl_open_vdp_logged, trade_in_vehicle, trade_in_provider, lead_info):
    fx_cl_open_vdp_logged.btn_add_trade_in.click()
    trade_in = fx_cl_open_vdp_logged.consumer.cbo.trade_in
    trade_in.wait_for.presence_of_element_located(wait_time=5)
    trade_in.trade_in(trade_in_vehicle, trade_in_provider, lead_info)
    if trade_in_vehicle.continue_without_trade_in:
        trade_in.final_report.btn_cont_without_trade_in.click()
        trade_in.ancestor.consumer.vdp3.txt_trade_in.wait_for.invisibility_of_element_located(wait_time=10)
    else:
        trade_in.final_report.apply_trade_in()
    return trade_in.ancestor.consumer.vdp3


@fixture(scope="class")
@allure.title("Showroom sing up")
def fx_cl_consumer_show_room_login(request, fx_ss_reset_cbo_settings, fx_ss_set_vdp_version_setting,
                                   fx_cl_set_vdp_flow_setting, lead_info, fx_cl_set_cbo_trade_in_provider_setting,
                                   fx_cl_set_cbo_trade_in_provider_dealer_id_setting, fx_cl_cbo_account_package_setting,
                                   showroom_logging) -> ShowRoom:
    def finalizer():
        consumer.quit()

    request.addfinalizer(finalizer)
    consumer = ConsumerPage(lead_info.first_name, url=cbo_url).open()
    showroom = consumer.show_room.open()
    if showroom_logging:
        showroom.login(lead_info)
    return showroom


@fixture(scope="class")
@allure.title("Open vdp from showroom")
def fx_cl_open_vdp_logged(fx_cl_consumer_show_room_login, vehicle) -> VDP3Page:
    if not vehicle.vin:
        fx_cl_consumer_show_room_login.select_first_model_card()
    vdp = fx_cl_consumer_show_room_login.consumer.vdp3.open(vin_stock=vehicle.vin,
                                                            open_from=OpenVDPFrom.SHOWROOM)
    return vdp


@fixture(scope='class')
@allure.title('VDP Sign Up')
def vdp_sign_up():
    return True


@fixture(scope="class")
@allure.title("Open VDP page from chat")
def fx_cl_open_vdp_from_chat(request, fx_ss_reset_cbo_settings, fx_ss_set_vdp_version_setting,
                             fx_cl_set_vdp_flow_setting,
                             fx_cl_cbo_account_package_setting, fx_cl_set_cbo_enabled_setting,
                             stock_number, fx_cl_set_cbo_trade_in_provider_setting,
                             fx_cl_set_cbo_trade_in_provider_dealer_id_setting, lead_info, vdp_sign_up) -> VDP3Page:
    def finalizer():
        consumer.quit()

    request.addfinalizer(finalizer)
    consumer = ConsumerPage(names.get_first_name(), url=cbo_url).open()
    vdp3 = consumer.vdp3.open(vin_stock=stock_number, open_from=OpenVDPFrom.CHAT)
    if vdp_sign_up:
        vdp3.sign_up.sign_up(lead_info)
    return vdp3


@fixture(scope="class")
@allure.title("Click 'Explore Payments' button")
def open_vr_from_vdp(fx_cl_apply_trade_in):
    if fx_cl_apply_trade_in.btn_explore_payments.exists():
        fx_cl_apply_trade_in.btn_explore_payments.click()
    else:
        fx_cl_apply_trade_in.btn_buy_now.click()
    fx_cl_apply_trade_in.consumer.cbo.exists(wait_time=10)
    from src.web.consumer.blur_component import BlurPiiCountryComponent
    BlurPiiCountryComponent(fx_cl_apply_trade_in.consumer).next()
    fx_cl_apply_trade_in.consumer.cbo.payment.spn_skeleton.wait_for_appear_and_disappear()
    return fx_cl_apply_trade_in.consumer.cbo


@fixture(scope="class")
@allure.title("Open showroom")
def apply_trade_in_for_other_vehicle(fx_cl_apply_trade_in, second_vehicle):
    showroom = fx_cl_apply_trade_in.consumer.cbo.navigation_panel.switch_to_showroom()
    vdp = showroom.consumer.vdp3.open(vin_stock=second_vehicle.vin, open_from=OpenVDPFrom.SHOWROOM)
    return vdp


@fixture(scope="class")
def get_trade_in_default_value(fx_cl_apply_trade_in, trade_in_vehicle, lead_info):
    from src.utils.trade_in_services.trade_in_providers import get_trade_in_service
    from src.web.entities.trade_in_entity import TradeInProvider
    price = get_trade_in_service(TradeInProvider.KBB).get_default_price(
        uuid=fx_cl_apply_trade_in.consumer.cbo.uuid, zip_code=lead_info.zip,
        trade_in_vehicle=trade_in_vehicle)
    return price['value']


@fixture(scope="class")
@allure.title("Select payment option and open VDP")
def select_payment_and_open_vdp(open_vr, cbo_payment_option_entity):
    open_vr.payment.set_cbo_payment_option_entity(cbo_payment_option_entity)
    open_vr.payment.select_payment_options()
    open_vr.payment.btn_next.click()
    return open_vr.open_vdp()
