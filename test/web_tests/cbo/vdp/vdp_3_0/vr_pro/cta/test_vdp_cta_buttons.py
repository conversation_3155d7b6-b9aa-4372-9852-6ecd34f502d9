import allure
import pytest
from pytest import fixture
from selenium.webdriver.common.by import By

from src.rest_api.services.leads import Leads
from src.web.entities.dealership import DealershipFactory, DealershipEntity
from test import pytestrail


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPProCTAStartChat:

    @fixture(scope="class")
    @allure.title("Start chat on VDP page")
    def start_chat_on_vdp(self, fx_cl_open_vdp_logged):
        chat = fx_cl_open_vdp_logged.consumer.chat_component.open()
        return chat

    @pytestrail.case('********')
    def test_vdp_cta_start_chat(self, start_chat_on_vdp, fx_cl_chat_manager_page):
        with allure.step("CHECK - if manager is able see message in Chat Console"):
            assert fx_cl_chat_manager_page.chat_component.get_message_by_pattern(start_chat_on_vdp.initial_message), \
                f"Unable to find chat with {start_chat_on_vdp.initial_message} message"


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPProCTADealerShipDirection:

    @fixture(scope="class")
    @allure.title("Open dealership direction")
    def get_dealership_direction(self, fx_cl_open_vdp_logged):
        fx_cl_open_vdp_logged.btn_get_directions.js.scroll_into_view()
        fx_cl_open_vdp_logged.btn_get_directions.click()
        fx_cl_open_vdp_logged.consumer.switch_to_last_tab()
        direction_value = fx_cl_open_vdp_logged.consumer.driver.find_element(By.CSS_SELECTOR,"#directions-searchbox-1 input") \
            .get_attribute("value")
        return direction_value

    @pytest.mark.parametrize("dealership", [
        pytest.param(DealershipFactory.vdp_location(), marks=[pytestrail.case('********')])])
    def test_vdp_cta_get_direction(self, get_dealership_direction, dealership: DealershipEntity):
        expected_dealership_location = f"{dealership.company_address}, {dealership.company_city}, " \
                                       f"{dealership.company_state} {dealership.company_zip}"
        with allure.step("CHECK - if dealership direction displayed correctly on google map"):
            assert get_dealership_direction == expected_dealership_location, \
                f"Expected dealership location: {expected_dealership_location}\n" \
                f"doesn't much with actual {get_dealership_direction}"


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPProCTATestDriveLogged:

    @fixture(scope='class')
    @allure.title('Showroom is logging')
    def showroom_logging(self):
        return True

    @fixture(scope="class")
    @allure.title("Request test drive")
    def request_test_drive(self, fx_cl_open_vdp_logged, lead_info):
        request_test_drive = fx_cl_open_vdp_logged.test_drive_form.open()
        request_test_drive.fill_test_drive_form(lead_info)

    @pytestrail.case('C2557144')
    def test_vdp_cta_test_drive_logged(self, request_test_drive, lead_info):
        with allure.step("CHECK - lead was created"):
            Leads.find_web_lead(lead_info)


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPProCTATestDriveNotLogged:

    @fixture(scope="class")
    @allure.title("Request test drive")
    def request_test_drive(self, fx_cl_open_vdp_logged, lead_info):
        request_test_drive = fx_cl_open_vdp_logged.test_drive_form.open()
        request_test_drive.fill_test_drive_form(lead_info)

    @pytestrail.case('C2557143')
    def test_vdp_cta_test_drive_not_logged(self, request_test_drive, lead_info):
        with allure.step("CHECK - lead was created"):
            Leads.find_web_lead(lead_info)
