import allure
import names
import pytest
from pytest import fixture

from src import cbo_url
from src.web.consumer.desktop.vdp_page import VDP3Page
from src.web.consumer.vdp_page_behavior import OpenVDPFrom
from test import pytestrail
from test.web_tests.cbo import ConsumerPage


@allure.feature('VDP3')
@allure.story('VDP3Open')
class TestVDPProOpen:

    @fixture(scope="function")
    @allure.title("Open VDP page")
    def fx_fn_vdp3(self, request, fx_ss_reset_cbo_settings, fx_cl_set_vdp_flow_setting, vin, open_from,
                   pii_entity) -> VDP3Page:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(names.get_first_name(), url=cbo_url).open()
        if open_from == OpenVDPFrom.CHAT:
            consumer.start_chat(message=f"Is {vin} available?")
        vdp3 = consumer.vdp3.open(vin_stock=vin, open_from=open_from, pii_entity=pii_entity)
        return vdp3

    @pytest.mark.parametrize("open_from,vin", [
        pytest.param(OpenVDPFrom.SHOWROOM, "2HKRS4H76SH425741", marks=[pytestrail.case('C2399107')])])
    @pytest.mark.vdp3
    def test_open_vdp_via_showroom(self, vin, fx_fn_vdp3, open_from):
        with allure.step("CHECK - VDP page"):
            assert fx_fn_vdp3.exists(), "vdp page was not exists"

    @pytest.mark.parametrize("open_from,vin", [pytest.param(OpenVDPFrom.CBO, "2HKRS4H76SH425741",
                                                            marks=[pytestrail.case('C2399108')])])
    @pytest.mark.vdp3
    def test_open_vdp_via_cbo(self, vin, fx_fn_vdp3, open_from):
        with allure.step("CHECK - VDP page"):
            assert fx_fn_vdp3.exists(), "vdp page was not exists"

    @pytest.mark.parametrize("open_from,vin", [pytest.param(OpenVDPFrom.CHAT, "MHF250811",
                                                            marks=[pytestrail.case('C2399109')])])
    @pytest.mark.vdp3
    def test_open_vdp_via_chat(self, fx_fn_vdp3, open_from, vin):
        with allure.step("CHECK - VDP page"):
            assert fx_fn_vdp3.btn_buy_now.wait_for.element_to_be_clickable(wait_time=5), "vdp page was not exists"
