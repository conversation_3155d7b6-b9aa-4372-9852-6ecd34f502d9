import allure
import pytest
from pytest import fixture

from src.utils.admin_tools.gb1_settings import Gb1Settings
from test import pytestrail


@allure.feature('VDP3')
@allure.story('VDPSettings')
class TestVDPSelectShowroomEnabled:

    @fixture(scope="class")
    @allure.title("Change cbo flow setting in GB1")
    def fx_cl_set_enable_showroom_setting(self):
        Gb1Settings.set_enable_showroom_setting()

    @pytestrail.case('********')
    @pytest.mark.vdp3
    def test_vdp_buttons_in_headers(self, fx_cl_set_enable_showroom_setting, fx_cl_open_vdp_from_chat):
        with allure.step("CHECK - new and used buttons exists on vdp"):
            assert fx_cl_open_vdp_from_chat.ddl_shop.exists(), "shop dropdown doesn't exists in header"

    @pytestrail.case('********')
    @pytest.mark.vdp3
    def test_vdp_inventory_section(self, fx_cl_set_enable_showroom_setting, fx_cl_open_vdp_from_chat):
        navigation_panel = fx_cl_open_vdp_from_chat.consumer.cbo.navigation_panel.open()
        with allure.step("CHECK - inventory section exists in sidebar"):
            assert navigation_panel.txt_inventory.exists(), "Inventory section doesn't exists in sidebar"


@allure.feature('VDP3')
@allure.story('VDPSettings')
class TestVDPSelectShowroomDisabled:

    @fixture(scope="class")
    @allure.title("Change cbo flow setting in GB1")
    def fx_cl_set_enable_showroom_setting(self, request):
        def finalizer():
            Gb1Settings.set_enable_showroom_setting()

        request.addfinalizer(finalizer)
        Gb1Settings.set_enable_showroom_setting(enable_showroom=0)

    @pytestrail.case('********')
    @pytest.mark.vdp3
    def test_vdp_buttons_in_headers(self, fx_cl_set_enable_showroom_setting, fx_cl_open_vdp_from_chat):
        with allure.step("CHECK - new and used buttons didn't exists on vdp"):
            assert not fx_cl_open_vdp_from_chat.ddl_shop.exists(), "shop dropdown exists in header"

    @pytestrail.case('********')
    @pytest.mark.vdp3
    def test_vdp_inventory_section(self, fx_cl_set_enable_showroom_setting, fx_cl_open_vdp_from_chat):
        navigation_panel = fx_cl_open_vdp_from_chat.consumer.cbo.navigation_panel.open()
        with allure.step("CHECK - inventory section doesn't exists in sidebar"):
            assert not navigation_panel.txt_inventory.exists(), "Inventory section exists in sidebar"
