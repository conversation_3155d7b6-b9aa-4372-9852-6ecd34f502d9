import allure
import pytest
from pytest import fixture

from src.rest_api.services.leads import Leads
from src.utils.phone_number import PhoneNumber
from src.web.chat_console import VehicleType
from src.web.entities import AppointmentToSend, AppointmentExpected
from src.web.entities.dealership import DealershipFactory, DealershipEntity
from src.web.entities.lead_entity import WebLeadFactory, WebLeadEntity
from test import pytestrail


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPSelectCTAStartChat:

    @fixture(scope="class")
    @allure.title("Start chat on VDP page")
    def start_chat_on_vdp(self, fx_cl_open_vdp_from_chat):
        chat = fx_cl_open_vdp_from_chat.consumer.chat_component.open()
        return chat

    @pytestrail.case('C1456711')
    @pytest.mark.ktf(reason="VR-19602")
    def test_vdp_cta_start_chat(self, start_chat_on_vdp, fx_cl_chat_manager_page):
        with allure.step("CHECK - if manager is able see message in Cha<PERSON>e"):
            assert fx_cl_chat_manager_page.chat_component.get_message_by_pattern(start_chat_on_vdp.initial_message), \
                f"Unable to find chat with {start_chat_on_vdp.initial_message} message"


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPSelectCTADealerShipDirection:

    @fixture(scope="class")
    @allure.title("Open dealership direction")
    def get_dealership_direction(self, fx_cl_open_vdp_from_chat):
        fx_cl_open_vdp_from_chat.btn_get_directions.js.scroll_into_view()
        fx_cl_open_vdp_from_chat.btn_get_directions.click()
        fx_cl_open_vdp_from_chat.consumer.switch_to_last_tab()
        from selenium.webdriver.common.by import By
        direction_value = fx_cl_open_vdp_from_chat.consumer.driver.find_element(
            By.CSS_SELECTOR, "#directions-searchbox-1 input").get_attribute("value")
        return direction_value

    @pytest.mark.parametrize("dealership", [
        pytest.param(DealershipFactory.vdp_location(), marks=[pytestrail.case('********')])])
    def test_vdp_cta_get_direction(self, get_dealership_direction, dealership: DealershipEntity):
        expected_dealership_location = f"{dealership.company_address}, {dealership.company_city}, " \
                                       f"{dealership.company_state} {dealership.company_zip}"
        with allure.step("CHECK - if dealership direction displayed correctly on google map"):
            assert get_dealership_direction == expected_dealership_location, \
                f"Expected dealership location: {expected_dealership_location}\n" \
                f"doesn't much with actual {get_dealership_direction}"


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPSelectCTATestDriveLogged:

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self) -> WebLeadEntity:# refactor
        return WebLeadFactory.random_lead(department="Sales", zip="10001", phone=PhoneNumber.new_valid_phone(),
                                          appointment_to_send=AppointmentExpected[AppointmentToSend.MORNING],
                                          vehicle_type=VehicleType.NEW, vehicle_make="Honda",
                                          vehicle_model="civic", vehicle_year="2023",
                                          vehicle_vin='2HGFE1F95PH301295')

    @fixture(scope="class")
    @allure.title("Request test drive")
    def request_test_drive(self, fx_cl_open_vdp_logged, lead_info):
        request_test_drive = fx_cl_open_vdp_logged.test_drive_form.open()
        request_test_drive.fill_test_drive_form(lead_info)

    @pytestrail.case('C2398973')
    @pytest.mark.ktf(reason="VR-19602")
    def test_vdp_cta_test_drive_logged(self, request_test_drive, lead_info):
        with allure.step("CHECK - lead was created"):
            Leads.find_web_lead(lead_info)


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPSelectCTATestDriveNotLogged:

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self) -> WebLeadEntity: #refactor
        return WebLeadFactory.random_lead(department="Sales", zip="10001", phone=PhoneNumber.new_valid_phone(),
                                          appointment_to_send=AppointmentExpected[AppointmentToSend.MORNING],
                                          vehicle_type=VehicleType.NEW, vehicle_make="Honda",
                                          vehicle_model="civic", vehicle_year="2023",
                                          vehicle_vin='2HGFE1F95PH301295')

    @fixture(scope="class")
    @allure.title("Request test drive")
    def request_test_drive(self, fx_cl_open_vdp_from_chat, lead_info):
        request_test_drive_form = fx_cl_open_vdp_from_chat.test_drive_form.open()
        request_test_drive_form.fill_test_drive_form(lead_info)

    @pytestrail.case('C2005361')
    @pytest.mark.ktf(reason="VR-19602")
    def test_vdp_cta_test_drive_not_logged(self, request_test_drive, lead_info):
        with allure.step("CHECK - lead was created"):
            Leads.find_web_lead(lead_info)
