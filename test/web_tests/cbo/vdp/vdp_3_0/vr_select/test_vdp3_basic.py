import allure
import pytest
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from src.utils.phone_number import PhoneNumber
from src.web.entities.lead_entity import WebLeadFactory, WebLeadEntity
from test import pytestrail


@allure.feature('VDP3')
@allure.story('VDP3Basic')
class TestVDPSelectBasic:

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="1GKS2JKL0PR504011")

    @pytestrail.case('C171441')
    @pytest.mark.vdp3
    def test_vdp_stock_number(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - stock number"):
            assert fx_cl_open_vdp_from_chat.get_stock() == vehicle.stock and fx_cl_open_vdp_from_chat.txt_stock.is_displayed(), \
                f"Incorrect stock number is displayed,\nActual stock# = {fx_cl_open_vdp_from_chat.get_stock()}" \
                f"\nExpected stock# = {vehicle.stock}"

    @pytest.mark.parametrize("expected_tabs", [pytest.param(['Exterior', 'Interior', 'Entertainment', 'Mechanical',
                                                             'Safety'], marks=[pytestrail.case('C171445')])])
    @pytest.mark.vdp3
    def test_vdp_features_section_multi_tab(self, fx_cl_open_vdp_from_chat, expected_tabs, vehicle):
        with allure.step("CHECK - features section multi tabs"):
            assert fx_cl_open_vdp_from_chat.anl_features_tabs.elements_texts == expected_tabs, \
                "Feature tabs was not displayed\n" \
                f"Actual tabs = {fx_cl_open_vdp_from_chat.anl_features_tabs.elements_texts}\nExpected tabs = {expected_tabs}"

    @pytestrail.case('C2450447')
    @pytest.mark.vdp3
    def test_vdp_miles(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - miles"):
            assert fx_cl_open_vdp_from_chat.get_miles() == format(vehicle.info.mileage, ","), \
                f"Expected vehicle mileage = {format(vehicle.info.mileage, ',')}\n" \
                f"Actual vehicle mileage = {fx_cl_open_vdp_from_chat.get_miles()}"

    @pytestrail.case('C2056467')
    @pytest.mark.vdp3
    def test_vdp_description_section(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - description section"):
            actual_text = fx_cl_open_vdp_from_chat.get_dealer_message_summary()
            expected_text = vehicle.description.replace("<br>  ", "\n").replace("<br>", "\n").replace("  ", " ")
            assert actual_text == expected_text, f"Expected description text = {expected_text}\n" \
                                                 f"Actual vehicle mileage = {actual_text}"

    @pytestrail.case('C2500487')
    @pytest.mark.vdp3
    def test_vdp_similar_vehicles_less_than_3(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - similar vehicles"):
            fx_cl_open_vdp_from_chat.check_similar_vehicles(vehicle)
            assert not fx_cl_open_vdp_from_chat.btn_view_next_button.is_displayed() and not \
                fx_cl_open_vdp_from_chat.btn_view_previous_button.is_displayed(), "Next or view previous button is displayed"


@allure.feature('VDP3')
@allure.story('VDP3Basic')
class TestVDPSelectUnlockPrice:

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self) -> WebLeadEntity:
        return WebLeadFactory.random_lead(stock_number="MHF251128", zip="10001", phone=PhoneNumber.new_valid_phone())

    @fixture(scope="class")
    @allure.title("Unblock best price")
    def fx_cl_unlock_best_price(self, fx_cl_open_vdp_from_chat, lead_info):
        fx_cl_open_vdp_from_chat.unlock_best_price(lead_info)

    @pytestrail.case('C1456710')
    @pytest.mark.vdp3
    @pytest.mark.ktf(reason="VR-19602")
    def test_vdp_unlock_best_price(self, fx_cl_open_vdp_from_chat, fx_cl_unlock_best_price):
        with allure.step("CHECK - unlocked price"):
            assert fx_cl_open_vdp_from_chat.txt_unlocked_price.exists(), "Price was not unlocked after pii submission"


@allure.feature('VDP3')
@allure.story('VDP3Basic')
class TestVDPSelectOther:

    @pytestrail.case('C2056473')
    @pytest.mark.vdp3
    @pytest.mark.ktf(reason="VR-19602")
    def test_vdp_similar_vehicles_more_than_3(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - similar vehicles"):
            fx_cl_open_vdp_from_chat.check_similar_vehicles(vehicle)
            assert fx_cl_open_vdp_from_chat.btn_view_previous_button.is_displayed(), "View previous button is not displayed"


@allure.feature('VDP3')
@allure.story('VDP3Basic')
class TestVDPSelectSingleFeatureTab:

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="2HGFE2F20NH545314")

    @pytestrail.case('C2344403')
    @pytest.mark.vdp3
    @pytest.mark.ktf(reason="VR-19602")
    def test_vdp_features_section_single_list(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - features section single list"):
            assert fx_cl_open_vdp_from_chat.anl_features_tabs_body.size == 1, "Feature section single list was not displayed"
