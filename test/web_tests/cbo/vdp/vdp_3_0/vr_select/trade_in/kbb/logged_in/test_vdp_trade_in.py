import allure
import pytest
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from src.utils.converter import Converter
from src.web.consumer import OwnershipType
from src.web.entities.trade_in_entity import TradeInEntity
from test import pytestrail


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBB:

    @pytestrail.case('C2557126')
    def test_vdp_trade_in_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text)
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"

    @pytest.mark.parametrize("expected_text", [pytest.param("Trade-in applied", marks=[pytestrail.case('C2557116')])])
    def test_vdp_trade_in_indicator_and_value(self, fx_cl_apply_trade_in, get_trade_in_default_value, expected_text):
        expected_trade_in_value = Converter.int_to_currency(get_trade_in_default_value)
        applied_text = fx_cl_apply_trade_in.txt_trade_in.text.split(" ", 1)
        with allure.step("CHECK - indicators on VDP after applying trade in"):
            assert expected_text == applied_text[1] and expected_trade_in_value == applied_text[0], \
                f"Unable to find {expected_text} text and {expected_trade_in_value} value after applied trade-in"

    @pytestrail.case('C2557117')
    def test_vdp_trade_in_dismissed_modal_window(self, fx_cl_apply_trade_in):
        with allure.step("CHECK - trade-in modal window disappear after applying trade in"):
            assert not fx_cl_apply_trade_in.consumer.cbo.trade_in.exists(wait_time=0), \
                "trade-in modal window still exist after applying trade-in"

    @pytestrail.case('C2557109')
    def test_vdp_trade_in_remove(self, fx_cl_apply_trade_in):
        fx_cl_apply_trade_in.remove_trade_in()
        with allure.step("CHECK - vdp page after removing trade-in"):
            assert fx_cl_apply_trade_in.btn_add_trade_in.exists() and not fx_cl_apply_trade_in.txt_trade_in.exists()


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBNegative:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2019", make="BMW", model="X6 M", trim="Sport Utility 4D",
                             mileage="500", ownership=OwnershipType.OWNED)

    @pytest.mark.parametrize("expected_text", [pytest.param("Trade-in not applied",
                                                            marks=[pytestrail.case('C2557120')])])
    def test_vdp_trade_in_cannot_be_applied(self, fx_cl_apply_trade_in, expected_text):
        with allure.step("CHECK - that unable to apply negative trade-in"):
            assert fx_cl_apply_trade_in.txt_trade_in.text == expected_text, "Negative trade-in should not be applied"


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBLicencePlate:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2013", make="Volkswagen", model="Beetle", trim="Turbo Convertible 2D",
                             license_plate="GUBAGOO", state="Florida", mileage="12000", ownership=OwnershipType.OWNED)

    @pytestrail.case('********')
    @pytest.mark.ktf(reason="VR-19602")
    def test_vdp_trade_in_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text)
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBVinNumber:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2010", make="Chevrolet", model="Camaro", trim="1LT",
                             mileage="12000", vin="2G1FB1EV0A9165254", ownership=OwnershipType.OWNED)

    @pytestrail.case('C2557123')
    def test_vdp_trade_in_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text)
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBManualEstimation:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2010", make="Chevrolet", model="Camaro", trim="1LT",
                             mileage="12000", manual_estimation=10000,
                             ownership=OwnershipType.OWNED)

    @pytestrail.case("C2557124")
    @pytest.mark.ktf(reason="VR-10608")
    def test_vdp_trade_in_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text)
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBWithOutTradeIn:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2012", make="Toyota", model="Corolla", trim="LE Sedan 4D",
                             mileage="50000000", ownership=OwnershipType.OWNED, continue_without_trade_in=True)

    @pytestrail.case("C2557121")
    @pytest.mark.ktf(reason="VR-11495")
    def test_vdp_trade_in_continue_without_trade_in(self, fx_cl_apply_trade_in, vehicle):
        with allure.step("CHECK - consumer able to proceed with out trade in"):
            assert fx_cl_apply_trade_in.txt_price.text == Converter.int_to_currency(vehicle.price), \
                "vehicle price was updated, after continue without trade-in"


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBVR:

    @pytest.mark.parametrize("expected_text,expected_text_size",
                             [pytest.param("Trade-in applied", 3, marks=[pytestrail.case('C3751396')])])
    def test_vdp_trade_in_labels_in_vr(self, open_vr_from_vdp, expected_text, expected_text_size):
        with allure.step("CHECK - that trade-in labels is applied in VR"):
            assert open_vr_from_vdp.payment.lst_correction_status.size == expected_text_size \
                   and all(expected_text in item for item in open_vr_from_vdp.payment.lst_correction_status.elements_texts), \
                f"Expected text: {expected_text} is not found in {open_vr_from_vdp.payment.lst_correction_status.elements_texts}"

    @fixture(scope="class")
    @allure.title("get trade-in value from summary window")
    def get_trade_in_value(self, open_vr_from_vdp):
        open_vr_from_vdp.trade_in.open()
        return open_vr_from_vdp.vi_card.open().get_trade_in().replace("-", "")

    @pytestrail.case("C2557108")
    def test_vdp_trade_in_reflected_in_vr(self, get_trade_in_value, get_trade_in_default_value):
        trade_in_default_value = Converter.int_to_currency(get_trade_in_default_value)
        with allure.step("CHECK - that trade-in is applied"):
            assert get_trade_in_value == trade_in_default_value, \
                "incorrect trade-in value displayed in summary info window\n" \
                f"Expected value = {trade_in_default_value}\n" \
                f"Actual value = {get_trade_in_value}"


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBDiffVehicles:

    @fixture(scope="class")
    @allure.title("Second vehicle info")
    def second_vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="5FNRL6H96SB017927")

    @pytestrail.case('C2557119')
    def test_vdp_added_trade_in_diff_vehicles(self, get_trade_in_default_value, apply_trade_in_for_other_vehicle):
        applied_text = apply_trade_in_for_other_vehicle.txt_trade_in.text.split(" ", 1)
        with allure.step("CHECK - that trade-in is applied for the second vehicle"):
            assert Converter.int_to_currency(get_trade_in_default_value) == applied_text[0], \
                "Trade-in didn't apply on another vehicle"


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBGLW:

    @pytestrail.case('C2557115')
    def test_vdp_trade_in_session_visible_in_glw(self, fx_cl_apply_trade_in, fx_cl_resq_manager_vr_page, lead_info):
        customer = fx_cl_resq_manager_vr_page.get_customer(lead_info)
        with allure.step("CHECK - that customer appears in GLW after applying trade-in in VDP"):
            assert customer, f"unable to find {lead_info.email} customer after applying trade-in"
