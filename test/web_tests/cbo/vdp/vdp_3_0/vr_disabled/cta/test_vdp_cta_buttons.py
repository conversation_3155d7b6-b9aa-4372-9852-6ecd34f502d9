import allure
import pytest
from pytest import fixture
from selenium.webdriver.common.by import By

from src.rest_api.services.leads import Leads
from src.utils.phone_number import PhoneNumber
from src.web.chat_console import VehicleType
from src.web.entities import AppointmentToSend, AppointmentExpected
from src.web.entities.dealership import DealershipFactory, DealershipEntity
from src.web.entities.lead_entity import WebLeadFactory, WebLeadEntity
from test import pytestrail


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPDisabledCTAStartChat:

    @pytestrail.case('********')
    def test_vdp_cta_start_chat(self, fx_cl_open_vdp_from_chat, fx_cl_chat_manager_page):
        fx_cl_open_vdp_from_chat.send_message("I want to talk with manager")
        with allure.step("CHECK - if manager is able see message in Cha<PERSON>e"):
            assert fx_cl_chat_manager_page.chat_component.get_message_by_pattern(
                fx_cl_open_vdp_from_chat.consumer.initial_message), \
                f"Unable to find chat with {fx_cl_open_vdp_from_chat.consumer.initial_message} message"


@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPDisabledTADealerShipDirection:

    @fixture(scope="class")
    @allure.title("Open dealership direction")
    def get_dealership_direction(self, fx_cl_open_vdp_from_chat):
        fx_cl_open_vdp_from_chat.btn_get_directions.wait_for.presence_of_element_located(wait_time=5)
        fx_cl_open_vdp_from_chat.btn_get_directions.js.scroll_into_view()
        fx_cl_open_vdp_from_chat.btn_get_directions.click()
        fx_cl_open_vdp_from_chat.consumer.switch_to_last_tab()
        direction_value = fx_cl_open_vdp_from_chat.consumer.driver.find_element(
            By.CSS_SELECTOR, "#directions-searchbox-1 input").get_attribute("value")
        return direction_value

    @pytest.mark.parametrize("dealership", [
        pytest.param(DealershipFactory.vdp_location(), marks=[pytestrail.case('********')])])
    def test_vdp_cta_get_direction(self, get_dealership_direction, dealership: DealershipEntity):
        expected_dealership_location = f"{dealership.company_address}, {dealership.company_city}, " \
                                       f"{dealership.company_state} {dealership.company_zip}"
        with allure.step("CHECK - if dealership direction displayed correctly on google map"):
            assert get_dealership_direction == expected_dealership_location, \
                f"Expected dealership location: {expected_dealership_location}\n" \
                f"doesn't much with actual {get_dealership_direction}"


#
@allure.feature('VDP3')
@allure.story('CTA')
class TestVDPDisabledCTAContactUs:

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self, vehicle) -> WebLeadEntity:
        return WebLeadFactory.random_lead(department="Sales", zip="10001", phone=PhoneNumber.new_valid_phone(),
                                          appointment_to_send=AppointmentExpected[AppointmentToSend.MORNING],
                                          vehicle_type=VehicleType.NEW, vehicle_make=vehicle.make,
                                          vehicle_model=vehicle.model, vehicle_year=vehicle.year,
                                          vehicle_vin=vehicle.vin, notes="this is a test")

    @fixture(scope="class")
    @allure.title("Request test drive")
    def fill_contact_us_form(self, fx_cl_open_vdp_from_chat, lead_info):
        contact_us = fx_cl_open_vdp_from_chat.contact_us_form.open()
        contact_us.fill_form(lead_info)

    @pytestrail.case('C182229')
    def test_vdp_cta_test_drive_not_logged(self, fill_contact_us_form, lead_info):
        with allure.step("CHECK - lead was created"):
            Leads.find_web_lead(lead_info)
