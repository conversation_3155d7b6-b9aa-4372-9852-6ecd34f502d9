from time import sleep

import allure
import pytest
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from test import pytestrail


@allure.feature('VDP3')
@allure.story('VDP3Basic')
class TestVDPDisabledBasic:

    @fixture(scope="class")
    @allure.title("Vehicle stock number")
    def stock_number(self, vehicle):
        return vehicle.stock

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="1GKS2JKL0PR504011")

    @pytestrail.case('C2647898')
    @pytest.mark.vdp3
    def test_vdp_stock_number(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - stock number"):
            assert fx_cl_open_vdp_from_chat.get_stock() == vehicle.stock and fx_cl_open_vdp_from_chat.txt_stock.is_displayed(), \
                f"Incorrect stock number is displayed,\nActual stock# = {fx_cl_open_vdp_from_chat.get_stock()}" \
                f"\nExpected stock# = {vehicle.stock}"

    @pytest.mark.parametrize("expected_tabs", [pytest.param(['Exterior', 'Interior', 'Entertainment', 'Mechanical',
                                                             'Safety'], marks=[pytestrail.case('C2647900')])])
    @pytest.mark.vdp3
    def test_vdp_features_section_multi_tab(self, fx_cl_open_vdp_from_chat, expected_tabs, vehicle):
        with allure.step("CHECK - features section multi tabs"):
            assert fx_cl_open_vdp_from_chat.anl_features_tabs.elements_texts == expected_tabs, \
                "Feature tabs was not displayed\n" \
                f"Actual tabs = {fx_cl_open_vdp_from_chat.anl_features_tabs.elements_texts}\nExpected tabs = {expected_tabs}"

    @pytestrail.case('C2647904')
    @pytest.mark.vdp3
    def test_vdp_miles(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - miles"):
            assert fx_cl_open_vdp_from_chat.get_miles() == format(vehicle.info.mileage, ","), \
                f"Expected vehicle mileage = {format(vehicle.info.mileage, ',')}\n" \
                f"Actual vehicle mileage = {fx_cl_open_vdp_from_chat.get_miles()}"

    @pytestrail.case('C2647891')
    @pytest.mark.vdp3
    def test_vdp_description_section(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - description section"):
            actual_text = fx_cl_open_vdp_from_chat.get_dealer_message_summary()
            expected_text = vehicle.description.replace("<br>  ", "\n").replace("<br>", "\n").replace("  ", " ")
            assert actual_text == expected_text, f"Expected description text = {expected_text}\n" \
                                                 f"Actual vehicle mileage = {actual_text}"

    @pytestrail.case('C2647893')
    @pytest.mark.vdp3
    def test_vdp_similar_vehicles_less_than_3(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - similar vehicles"):
            fx_cl_open_vdp_from_chat.check_similar_vehicles(vehicle)
            assert not fx_cl_open_vdp_from_chat.btn_view_next_button.is_displayed() and not \
                fx_cl_open_vdp_from_chat.btn_view_previous_button.is_displayed(), "Next or view previous button is displayed"

    @pytestrail.case('C2056476')
    @pytest.mark.vdp3
    def test_vdp_close_icon(self, fx_cl_open_vdp_from_chat, vehicle):
        fx_cl_open_vdp_from_chat.btn_close.click()
        sleep(1)
        with allure.step("CHECK - previous window appears clicking on close icon on VDP"):
            assert not fx_cl_open_vdp_from_chat.exists(), "VDP window still exists after clicking on close button"
            fx_cl_open_vdp_from_chat.consumer.switch_to.default_content()
            assert fx_cl_open_vdp_from_chat.consumer.exists(), "Consumer desktop window doesn't exists after clicking on close button on VDP"


@allure.feature('VDP3')
@allure.story('VDP3Basic')
class TestVDPDisabledOther:

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="5FNRL6H9XSB017932")

    @fixture(scope="class")
    @allure.title("Vehicle stock number")
    def stock_number(self, vehicle):
        return vehicle.stock

    @pytestrail.case('C2647892')
    @pytest.mark.vdp3
    def test_vdp_similar_vehicles_more_than_3(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - similar vehicles"):
            fx_cl_open_vdp_from_chat.check_similar_vehicles(vehicle)
            assert fx_cl_open_vdp_from_chat.btn_view_previous_button.is_displayed(), "View previous button is not displayed"


@allure.feature('VDP3')
@allure.story('VDP3Basic')
class TestVDPDisabledSingleFeatureTab:

    @fixture(scope="class")
    @allure.title("Vehicle stock number")
    def stock_number(self):
        return "S190460A"

    @pytestrail.case('C2647901')
    @pytest.mark.vdp3
    def test_vdp_features_section_single_list(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - features section single list"):
            assert fx_cl_open_vdp_from_chat.anl_features_tabs_body.size == 1, "Feature section single list was not displayed"
