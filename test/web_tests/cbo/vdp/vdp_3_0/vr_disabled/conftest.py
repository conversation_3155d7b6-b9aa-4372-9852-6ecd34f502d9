import allure
from pytest import fixture

from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.utils.converter import Converter


@fixture(scope="class")
@allure.title("vdp flow")
def vdp_flow():
    return "vdp"


@fixture(scope="class")
@allure.title("Vehicle stock number")
def stock_number(vehicle):
    return vehicle.stock


@fixture(scope='class')
@allure.title('VDP Sign Up')
def vdp_sign_up():
    return False


@fixture(scope="class")
@allure.title("Change cbo account package setting in GB1")
def fx_cl_cbo_account_package_setting(request):
    def finalizer():
        Gb1Settings.set_cbo_account_package_setting()

    request.addfinalizer(finalizer)
    Gb1Settings.set_cbo_account_package_setting(vr_value="vr_select")


@fixture(scope="class")
def fx_cl_set_cbo_enabled_setting(request):
    def finalizer():
        Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=1)

    request.addfinalizer(finalizer)
    Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=0)


@fixture(scope='class')
@allure.title('Initial price')
def fx_cl_initial_price(fx_cl_open_vdp_from_chat):
    return int(Converter.extract_digits(fx_cl_open_vdp_from_chat.txt_price.text))


@fixture(scope="class")
@allure.title("Apply trade-in")
def fx_cl_apply_trade_in(fx_cl_open_vdp_from_chat, trade_in_vehicle, trade_in_provider, lead_info):
    fx_cl_open_vdp_from_chat.btn_add_trade_in.click()
    trade_in = fx_cl_open_vdp_from_chat.consumer.cbo.trade_in
    trade_in.wait_for.presence_of_element_located(wait_time=5)
    trade_in.trade_in(trade_in_vehicle, trade_in_provider, lead_info)
    if trade_in_vehicle.continue_without_trade_in:
        trade_in.final_report.btn_cont_without_trade_in.click()
    else:
        trade_in.final_report.apply_trade_in()
    return trade_in.ancestor.consumer.vdp3
