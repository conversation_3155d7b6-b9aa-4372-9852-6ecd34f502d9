import allure
import pytest

from test import pytestrail


@allure.feature('VDP3')
@allure.story('VDPSettings')
class TestVDPSettings:

    @pytestrail.case('C2557319')
    @pytest.mark.vdp3
    def test_vdp_default_cbo_flow(self, fx_cl_set_cbo_flow_setting, open_vr):
        with allure.step("CHECK - payments are shown as the first step in vr"):
            assert open_vr.payment.exists(), "payments screen is not shown as default step in vr"
