import allure
import names
import pytest
from pytest import fixture

from src import cbo_url
from src.web.consumer.desktop.vdp_page import VDP3Page
from src.web.consumer.vdp_page_behavior import OpenVDPFrom
from test import pytestrail
from test.web_tests.cbo import ConsumerPage


@allure.feature('VDP4')
@allure.story('VDP4Open')
class TestVDPProOpen:

    @fixture(scope="function")
    @allure.title("Open VDP page")
    def fx_fn_vdp4(self, request, fx_ss_reset_cbo_settings, fx_cl_set_vdp_flow_setting, fx_ss_set_vdp_version_setting,
                   vin, open_from, pii_entity) -> VDP3Page:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(names.get_first_name(), url=cbo_url).open()
        if open_from == OpenVDPFrom.CHAT:
            consumer.start_chat(message=f"Is {vin} available?")
        vdp4 = consumer.vdp3.open(vin_stock=vin, open_from=open_from, pii_entity=pii_entity)
        return vdp4

    @pytest.mark.parametrize("open_from,vin", [
        pytest.param(OpenVDPFrom.SHOWROOM, "2HKRS4H76SH425741", marks=[pytestrail.case('C5608243')])])
    @pytest.mark.vdp4
    def test_open_vdp_via_showroom(self, vin, fx_fn_vdp4, open_from):
        with allure.step("CHECK - VDP page"):
            assert fx_fn_vdp4.exists(), "vdp page was not exists"

    @pytest.mark.parametrize("open_from,vin", [pytest.param(OpenVDPFrom.CBO, "2HKRS4H76SH425741",
                                                            marks=[pytestrail.case('C5608244')])])
    @pytest.mark.vdp4
    def test_open_vdp_via_cbo(self, vin, fx_fn_vdp4, open_from):
        with allure.step("CHECK - VDP page"):
            assert fx_fn_vdp4.exists(), "vdp page was not exists"

    @pytest.mark.parametrize("open_from,vin", [pytest.param(OpenVDPFrom.CHAT, "MHF250811",
                                                            marks=[pytestrail.case('C5608245')])])
    @pytest.mark.vdp4
    def test_open_vdp_via_chat(self, fx_fn_vdp4, open_from, vin):
        with allure.step("CHECK - VDP page"):
            assert fx_fn_vdp4.btn_buy_now.wait_for.element_to_be_clickable(wait_time=5), "vdp page was not exists"
