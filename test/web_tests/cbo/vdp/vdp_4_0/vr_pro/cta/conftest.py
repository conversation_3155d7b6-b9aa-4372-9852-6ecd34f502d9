import allure
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.utils.phone_number import PhoneNumber
from src.web.consumer.desktop.cbo import VehicleType
from src.web.entities import AppointmentExpected, AppointmentToSend
from src.web.entities.lead_entity import WebLeadFactory, WebLeadEntity


@fixture(scope="class")
@allure.title("Lead info")
def lead_info(vehicle: VehicleEntity) -> WebLeadEntity:
    return WebLeadFactory.random_lead(department="Sales", zip="10001", phone=PhoneNumber.new_valid_phone(),
                                      appointment_to_send=AppointmentExpected[AppointmentToSend.MORNING],
                                      vehicle_type=VehicleType.NEW, vehicle_make=vehicle.make,
                                      vehicle_model=vehicle.model, vehicle_year=vehicle.year,
                                      vehicle_vin=vehicle.vin)


@fixture(scope='class')
@allure.title('Showroom is logging')
def showroom_logging():
    return False
