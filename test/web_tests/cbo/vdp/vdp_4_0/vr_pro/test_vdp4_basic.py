import allure
import names
import pytest
from pytest import fixture

from src import cbo_url
from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from src.utils.converter import Converter
from src.utils.phone_number import PhoneNumber
from src.web.consumer import PaymentOptionType
from src.web.consumer.desktop.cbo import VehicleType
from src.web.consumer.desktop.vdp_page import VDP3Page
from src.web.consumer.vdp_page_behavior import OpenVDPFrom
from src.web.entities.cbo_payment_option_entity import CBOPaymentOptionFactory, CBOPaymentOptionEntity
from src.web.entities.lead_entity import WebLeadEntity, WebLeadFactory
from src.web.entities.showroom_payment_option_entity import ShowroomPaymentOptionEntity, ShowroomPaymentOptionFactory
from test import pytestrail
from test.web_tests.cbo import ConsumerPage


@allure.feature('VDP4')
@allure.story('VDPBasic')
class TestVDPBasic:

    @fixture(scope='class')
    @allure.title('Showroom is logging')
    def showroom_logging(self):
        return False

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="1GKS2JKL0PR504011")

    @pytestrail.case('C5055296')
    @pytest.mark.vdp4
    def test_vdp_stock_number(self, fx_cl_open_vdp_logged, vehicle):
        with allure.step("CHECK - stock number"):
            assert fx_cl_open_vdp_logged.get_stock() == vehicle.stock and fx_cl_open_vdp_logged.txt_stock.is_displayed(), \
                f"Incorrect stock number is displayed,\nActual stock# = {fx_cl_open_vdp_logged.get_stock()}" \
                f"\nExpected stock# = {vehicle.stock}"

    @pytest.mark.parametrize("expected_tabs",
                             [pytest.param(['Options', 'Exterior', 'Interior', 'Entertainment', 'Mechanical', 'Safety'],
                                           marks=[pytestrail.case('C5568895')])])
    @pytest.mark.vdp4
    def test_vdp_features_section_multi_tab(self, fx_cl_open_vdp_logged, expected_tabs, vehicle):
        with allure.step("CHECK - features section multi tabs"):
            assert fx_cl_open_vdp_logged.anl_features_tabs.elements_texts == expected_tabs, \
                "Feature tabs was not displayed\n" \
                f"Actual tabs = {fx_cl_open_vdp_logged.anl_features_tabs.elements_texts}\nExpected tabs = {expected_tabs}"

    @pytestrail.case('C5568897')
    @pytest.mark.vdp4
    def test_vdp_miles(self, fx_cl_open_vdp_logged, vehicle):
        with allure.step("CHECK - miles"):
            assert fx_cl_open_vdp_logged.get_miles() == format(vehicle.info.mileage, ","), \
                f"Expected vehicle mileage = {format(vehicle.info.mileage, ',')}\n" \
                f"Actual vehicle mileage = {fx_cl_open_vdp_logged.get_miles()}"

    @pytestrail.case('C5568892')
    @pytest.mark.vdp4
    def test_vdp_description_section(self, fx_cl_open_vdp_logged, vehicle):
        with allure.step("CHECK - description section"):
            actual_text = fx_cl_open_vdp_logged.get_dealer_message_summary()
            import re
            expected_text = re.sub(' +', ' ', vehicle.description.replace("<br>  ", "\n").replace("<br>", "\n"))
            assert actual_text == expected_text, f"Expected description text = {expected_text}\n" \
                                                 f"Actual vehicle mileage = {actual_text}"


@allure.feature('VDP4')
@allure.story('VDP4Basic')
class TestVDPUnlockPrice:

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self) -> WebLeadEntity:
        return WebLeadFactory.random_lead(stock_number="MHF251128", zip="10001", phone=PhoneNumber.new_valid_phone())

    @fixture(scope="class")
    @allure.title("Open VDP page")
    def fx_cl_vdp4(self, request, fx_ss_reset_cbo_settings, fx_ss_set_vdp_version_setting, fx_cl_set_vdp_flow_setting,
                   lead_info: WebLeadEntity) -> VDP3Page:
        def finalizer():
            vdp4.quit()

        request.addfinalizer(finalizer)
        consumer = ConsumerPage(names.get_first_name(), url=cbo_url).open()
        consumer.start_chat(message=f"Is {lead_info.stock_number} available?")
        vdp4 = consumer.vdp3.open(vin_stock=lead_info.stock_number, open_from=OpenVDPFrom.CHAT)
        return vdp4

    @fixture(scope="class")
    @allure.title("Unblock best price")
    def fx_cl_unlock_best_price(self, fx_cl_vdp4, lead_info):
        fx_cl_vdp4.unlock_best_price(lead_info)

    @pytestrail.case('C5055751')
    @pytest.mark.vdp4
    def test_vdp_unlock_best_price(self, fx_cl_vdp4, fx_cl_unlock_best_price):
        with allure.step("CHECK - unlocked price"):
            assert fx_cl_vdp4.txt_unlocked_price.exists(), "Price was not unlocked after pii submission"


@allure.feature('VDP4')
@allure.story('VDP4Basic')
class TestVDPProLeaseInVRKBB:

    @fixture(scope="class")
    @allure.title("cbo payment option info")
    def cbo_payment_option_entity(self) -> CBOPaymentOptionEntity:
        return CBOPaymentOptionFactory.default_cbo_payment_option(payment_type=PaymentOptionType.LEASE)

    @pytestrail.case('C5055752')
    @pytest.mark.vdp4
    @pytest.mark.ktf(reason="VR-16452")
    def test_vdp_selected_lease_in_vr(self, open_vr, select_payment_and_open_vdp):
        with allure.step("CHECK - Selected lease option in VDP"):
            assert select_payment_and_open_vdp.txt_price.text_content == open_vr.payment.selected_lease, \
                f"selected lease option in VR = {open_vr.payment.selected_lease}\n" \
                f"Displayed lease option in VDP = {select_payment_and_open_vdp.txt_price.text_content}"


@allure.feature('VDP4')
@allure.story('VDPBasicLeaseWidget')
class TestVDPLeaseWidget:

    @fixture(scope="class")
    @allure.title("Lease mileage value")
    def cbo_lease_mileage_value(self):
        return 12000

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self) -> WebLeadEntity:
        return WebLeadFactory.random_lead(stock_number="MHF250852", zip="10001", phone=PhoneNumber.new_valid_phone())

    @fixture(scope="class")
    @allure.title("Payment options info")
    def payment_options(self) -> ShowroomPaymentOptionEntity:
        return ShowroomPaymentOptionFactory.create(vehicle_type=VehicleType.NEW, payment_option=PaymentOptionType.LEASE,
                                                   monthly_payment_min=500, monthly_payment_max=550,
                                                   down_payment="2500", lease_terms=60, credit_score=700)

    @pytestrail.case('C5055315')
    @pytest.mark.vdp4
    def test_vdp_payments_lease_widget(self, fx_cl_set_cbo_lease_mileage_setting, fx_cl_consumer_open_vdp,
                                       cbo_lease_mileage_value, payment_options):
        expected_text = f"* Monthly lease payment estimate based on " \
                        f"{Converter.int_to_currency(int(payment_options.down_payment))} down payment," \
                        f" {payment_options.lease_terms} month term, {format(cbo_lease_mileage_value, ',')}" \
                        f" miles per year, {payment_options.credit_score} credit score."
        with allure.step("CHECK - lease widget"):
            assert fx_cl_consumer_open_vdp.get_tooltip_text() == expected_text, \
                f"Expected text widget = {expected_text}\nActual text widget = " \
                f"{fx_cl_consumer_open_vdp.txt_payment_widget.text}"

    @pytestrail.case('C5568898')
    @pytest.mark.vdp4
    @pytest.mark.ktf(reason="VR-19601")
    def test_vdp_payments_transmitted_to_vr(self, fx_cl_consumer_open_vdp, payment_options):
        cbo = fx_cl_consumer_open_vdp.open_vr()
        actual_payment = cbo.payment.get_down_payment_value()
        with allure.step("CHECK - down payment on vr"):
            assert actual_payment == payment_options.down_payment, f"Actual down payment {actual_payment}\n" \
                                                                   f"Expected down payment {payment_options.down_payment}"


@allure.feature('VDP4')
@allure.story('VDPBasic')
class TestVDPOther:

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="5FNYF8H64RB017102")

    @pytestrail.case('C5568891')
    @pytest.mark.vdp4
    def test_vdp_similar_vehicles_less_than_3(self, fx_cl_open_vdp_logged, vehicle):
        with allure.step("CHECK - similar vehicles"):
            fx_cl_open_vdp_logged.check_similar_vehicles(vehicle)
            assert not fx_cl_open_vdp_logged.btn_view_next_button.is_displayed() and not \
                fx_cl_open_vdp_logged.btn_view_previous_button.is_displayed(), "Next or view previous button is displayed"


@allure.feature('VDP4')
@allure.story('VDPBasic')
class TestVDPMoreThan3:

    @pytestrail.case('C5568890')
    @pytest.mark.vdp4
    def test_vdp_similar_vehicles_more_than_3(self, fx_cl_open_vdp_logged, vehicle):
        with allure.step("CHECK - similar vehicles"):
            fx_cl_open_vdp_logged.check_similar_vehicles(vehicle)
            assert fx_cl_open_vdp_logged.btn_view_previous_button.is_displayed(), "View previous button is not displayed"
