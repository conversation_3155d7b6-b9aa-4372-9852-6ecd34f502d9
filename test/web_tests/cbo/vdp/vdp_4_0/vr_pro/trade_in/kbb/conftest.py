import allure
from pytest import fixture

from src.web.consumer import OwnershipType
from src.web.entities.trade_in_entity import TradeInProvider, TradeInEntity


@fixture(scope="class")
@allure.title("Trade-in provider")
def trade_in_provider():
    return TradeInProvider.KBB


@fixture(scope="class")
@allure.title("Trade-in info")
def trade_in_vehicle() -> TradeInEntity:
    return TradeInEntity(year="2012", make="Toyota", model="Corolla", trim="LE Sedan 4D",
                         mileage="50000", ownership=OwnershipType.OWNED)
