import allure
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails


@fixture(scope='class')
@allure.title('Showroom is logging')
def showroom_logging():
    return False


@fixture(scope="class")
@allure.title("Vehicle info")
def vehicle() -> VehicleEntity:
    return VehicleDetails.get_vehicle_details(vin="3CZRZ1H38SM725065")
