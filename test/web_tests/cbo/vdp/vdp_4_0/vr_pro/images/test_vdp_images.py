import allure
import pytest
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from test import pytestrail


@allure.feature('VDP4')
@allure.story('Images')
class TestVDPProImages:

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="1FM5K7D8XJGA88858")

    @fixture(scope="class")
    @allure.title("Start chat on VDP page")
    def open_full_screen(self, fx_cl_open_vdp_logged):
        return fx_cl_open_vdp_logged.slides_full_screen.open()

    @pytestrail.case('C5055340')
    @pytest.mark.vdp4
    def test_vdp_image_gallery(self, fx_cl_open_vdp_logged):
        errors = fx_cl_open_vdp_logged.images.check_bubbles()
        image_index = fx_cl_open_vdp_logged.images.get_current_image_index()
        is_displayed = fx_cl_open_vdp_logged.slides_full_screen.open().check_autoscroll_to_current_image(image_index)
        assert errors == [] and is_displayed, "\n".join(errors)

    @pytestrail.case('C5055341')
    @pytest.mark.vdp4
    def test_vdp_image_back_from_gallery(self, open_full_screen):
        open_full_screen.btn_close_full_screen.click()
        with allure.step("CHECK - vdp displayed after closing full screen gallery"):
            assert open_full_screen.ancestor.exists(), "VDP is not displayed"
