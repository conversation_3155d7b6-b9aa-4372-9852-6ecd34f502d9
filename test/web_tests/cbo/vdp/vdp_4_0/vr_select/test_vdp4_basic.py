import allure
import pytest
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from src.utils.phone_number import PhoneNumber
from src.web.entities.lead_entity import WebLeadFactory, WebLeadEntity
from test import pytestrail


@allure.feature('VDP4')
@allure.story('VDPBasic')
class TestVDPSelectBasic:

    @fixture(scope="class")
    @allure.title("Vehicle stock number")
    def stock_number(self, vehicle):
        return vehicle.stock

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="1GKS2JKL0PR504011")

    @pytestrail.case('5055324')
    @pytest.mark.vdp4
    def test_vdp_stock_number(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - stock number"):
            assert fx_cl_open_vdp_from_chat.get_stock() == vehicle.stock and fx_cl_open_vdp_from_chat.txt_stock.is_displayed(), \
                f"Incorrect stock number is displayed,\nActual stock# = {fx_cl_open_vdp_from_chat.get_stock()}" \
                f"\nExpected stock# = {vehicle.stock}"

    @pytest.mark.parametrize("expected_tabs", [pytest.param(['Exterior', 'Interior', 'Entertainment', 'Mechanical',
                                                             'Safety'], marks=[pytestrail.case('5568915')])])
    @pytest.mark.vdp4
    def test_vdp_features_section_multi_tab(self, fx_cl_open_vdp_from_chat, expected_tabs, vehicle):
        with allure.step("CHECK - features section multi tabs"):
            assert fx_cl_open_vdp_from_chat.anl_features_tabs.elements_texts == expected_tabs, \
                "Feature tabs was not displayed\n" \
                f"Actual tabs = {fx_cl_open_vdp_from_chat.anl_features_tabs.elements_texts}\nExpected tabs = {expected_tabs}"

    @pytestrail.case('5568910')
    @pytest.mark.vdp4
    def test_vdp_miles(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - miles"):
            assert fx_cl_open_vdp_from_chat.get_miles() == format(vehicle.info.mileage, ","), \
                f"Expected vehicle mileage = {format(vehicle.info.mileage, ',')}\n" \
                f"Actual vehicle mileage = {fx_cl_open_vdp_from_chat.get_miles()}"

    @pytestrail.case('C5568909')
    @pytest.mark.vdp4
    def test_vdp_description_section(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - description section"):
            actual_text = fx_cl_open_vdp_from_chat.get_dealer_message_summary()
            expected_text = vehicle.description.replace("<br>  ", "\n").replace("<br>", "\n").replace("  ", " ")
            assert actual_text == expected_text, f"Expected description text = {expected_text}\n" \
                                                 f"Actual vehicle mileage = {actual_text}"

    @pytestrail.case('5568908')
    @pytest.mark.vdp4
    def test_vdp_similar_vehicles_less_than_3(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - similar vehicles"):
            fx_cl_open_vdp_from_chat.check_similar_vehicles(vehicle)
            assert not fx_cl_open_vdp_from_chat.btn_view_next_button.is_displayed() and not \
                fx_cl_open_vdp_from_chat.btn_view_previous_button.is_displayed(), "Next or view previous button is displayed"


@allure.feature('VDP4')
@allure.story('VDPBasic')
class TestVDPSelectUnlockPrice:

    @fixture(scope='class')
    @allure.title('VDP Sign Up')
    def vdp_sign_up(self):
        return False

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self) -> WebLeadEntity:
        return WebLeadFactory.random_lead(stock_number="MHF251128", zip="10001", phone=PhoneNumber.new_valid_phone())

    @fixture(scope="class")
    @allure.title("Unblock best price")
    def fx_cl_unlock_best_price(self, fx_cl_open_vdp_from_chat, lead_info):
        fx_cl_open_vdp_from_chat.unlock_best_price(lead_info)

    @pytestrail.case('5055756')
    @pytest.mark.vdp4
    def test_vdp_unlock_best_price(self, fx_cl_open_vdp_from_chat, fx_cl_unlock_best_price):
        with allure.step("CHECK - unlocked price"):
            assert fx_cl_open_vdp_from_chat.txt_unlocked_price.exists(), "Price was not unlocked after pii submission"


@allure.feature('VDP4')
@allure.story('VDPBasic')
class TestVDPSelectOther:

    @pytestrail.case('5568907')
    @pytest.mark.vdp4
    def test_vdp_similar_vehicles_more_than_3(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - similar vehicles"):
            fx_cl_open_vdp_from_chat.check_similar_vehicles(vehicle)
            assert fx_cl_open_vdp_from_chat.btn_view_previous_button.is_displayed(), "View previous button is not displayed"


@allure.feature('VDP4')
@allure.story('VDPBasic')
class TestVDPSelectSingleFeatureTab:

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="2HGFE2F20NH545314")

    @pytestrail.case('C5568916')
    @pytest.mark.vdp4
    def test_vdp_features_section_single_list(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - features section single list"):
            assert fx_cl_open_vdp_from_chat.anl_features_tabs_body.size == 1, "Feature section single list was not displayed"
