import allure
import pytest
from pytest import fixture

from src.utils.converter import Converter
from test import pytestrail


@allure.feature('VDP3')
@allure.story('TradeIn')
class TestVDPSelectTradeInKBBNotLogged:
    @fixture(scope='class')
    @allure.title('VDP Sign Up')
    def vdp_sign_up(self):
        return False

    @pytestrail.case('C5302277')
    @pytest.mark.vdp4
    def test_vdp_trade_in_not_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text.split('\n')[0])
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"
