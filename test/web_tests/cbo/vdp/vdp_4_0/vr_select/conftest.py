import allure
from pytest import fixture

from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.utils.converter import Converter
from src.web.consumer.desktop.cbo.cbo_page import CBOPage


@fixture(scope="class")
@allure.title("vdp flow")
def vdp_flow():
    return "payments"


@fixture(scope="class")
@allure.title("Change cbo account package setting in GB1")
def fx_cl_cbo_account_package_setting(request):
    def finalizer():
        Gb1Settings.set_cbo_account_package_setting()

    request.addfinalizer(finalizer)
    Gb1Settings.set_cbo_account_package_setting(vr_value="vr_select")


@fixture(scope='class')
@allure.title('Initial price')
def fx_cl_initial_price(fx_cl_open_vdp_from_chat):
    value = fx_cl_open_vdp_from_chat.txt_price.text if fx_cl_open_vdp_from_chat.txt_price.exists() \
        else fx_cl_open_vdp_from_chat.txt_locked_price.text
    return int(Converter.extract_digits(value))


@fixture(scope="class")
@allure.title("Apply trade-in")
def fx_cl_apply_trade_in(fx_cl_open_vdp_from_chat, trade_in_vehicle, trade_in_provider, lead_info):
    fx_cl_open_vdp_from_chat.btn_add_trade_in.click()
    fx_cl_open_vdp_from_chat.consumer.cbo.trade_in.exists(wait_time=5)
    trade_in = fx_cl_open_vdp_from_chat.consumer.cbo.trade_in
    trade_in.wait_for.presence_of_element_located(wait_time=5)
    trade_in.trade_in(trade_in_vehicle, trade_in_provider, lead_info)
    if trade_in_vehicle.continue_without_trade_in:
        trade_in.final_report.btn_cont_without_trade_in.click()
        trade_in.ancestor.consumer.vdp3.txt_trade_in.wait_for.invisibility_of_element_located(wait_time=5)
    else:
        trade_in.final_report.apply_trade_in()
    return trade_in.ancestor.consumer.vdp3


@fixture(scope="class")
@allure.title("Open VR from VDP by clicking on 'Buy now' button")
def open_vr(fx_cl_open_vdp_from_chat) -> CBOPage:
    return fx_cl_open_vdp_from_chat.open_vr()
