import allure
import pytest
from pytest import fixture

from test import pytestrail


@allure.feature('VDP4')
@allure.story('VDPFavorites')
class TestVDP4SelectFavoritesLogged:

    @fixture(scope="class")
    @allure.title("Add vehicle to favorites")
    def fx_cl_add_to_favorites(self, fx_cl_open_vdp_from_chat):
        fx_cl_open_vdp_from_chat.add_to_favorites()

    @fixture(scope="class")
    @allure.title("Open showroom container")
    def fx_cl_open_showroom_container(self, fx_cl_open_vdp_from_chat):
        return fx_cl_open_vdp_from_chat.consumer.cbo.my_vehicles_container.open()

    @pytestrail.case('C5568912')
    @pytest.mark.vdp4
    def test_vdp_shown_in_favorites_section(self, fx_cl_add_to_favorites, fx_cl_open_showroom_container, vehicle):
        with allure.step("CHECK - the marked vehicle appears in My vehicles, favorites tab"):
            assert fx_cl_open_showroom_container.get_vehicles_by_vin_number(vehicle.vin), \
                "marked vehicles doesn't appears in favorites tab"

    @pytestrail.case('C5568913')
    @pytest.mark.vdp4
    def test_vdp_remove_from_favorites_section(self, fx_cl_add_to_favorites, fx_cl_open_showroom_container, vehicle):
        fx_cl_open_showroom_container.remove_from_favorites_by_vin(vehicle.vin)
        with allure.step("CHECK - the unmarked vehicle disappears from My vehicles, favorites tab"):
            assert not fx_cl_open_showroom_container.get_vehicles_by_vin_number(vehicle.vin), \
                "unmarked vehicles still appears in favorites tab"


@allure.feature('VDP4')
@allure.story('VDPFavorites')
class TestVDP4SelectFavoritesNotLogged:

    @fixture(scope='class')
    @allure.title('VDP Sign Up')
    def vdp_sign_up(self):
        return False

    @fixture(scope="class")
    @allure.title("Add vehicle to favorites")
    def fx_cl_add_to_favorites(self, fx_cl_open_vdp_from_chat):
        fx_cl_open_vdp_from_chat.add_to_favorites()

    @fixture(scope="class")
    @allure.title("Open showroom container")
    def fx_cl_open_showroom_container(self, fx_cl_open_vdp_from_chat):
        return fx_cl_open_vdp_from_chat.consumer.cbo.my_vehicles_container.open()

    @pytestrail.case('C5568914')
    @pytest.mark.vdp4
    def test_vdp_shown_in_favorites_section(self, fx_cl_add_to_favorites, fx_cl_open_showroom_container, vehicle):
        with allure.step("CHECK - the marked vehicle appears in My vehicles, favorites tab"):
            assert fx_cl_open_showroom_container.get_vehicles_by_vin_number(vehicle.vin), \
                "marked vehicles doesn't appears in favorites tab"
