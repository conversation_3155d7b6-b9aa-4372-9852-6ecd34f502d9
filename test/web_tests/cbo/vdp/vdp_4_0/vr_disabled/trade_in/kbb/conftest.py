import allure
from pytest import fixture

from src.utils.phone_number import PhoneNumber
from src.web.consumer import OwnershipType
from src.web.entities.lead_entity import WebLeadEntity, WebLeadFactory
from src.web.entities.trade_in_entity import TradeInProvider, TradeInEntity


@fixture(scope="class")
@allure.title("Trade-in provider")
def trade_in_provider():
    return TradeInProvider.KBB


@fixture(scope="class")
@allure.title("Trade-in info")
def trade_in_vehicle() -> TradeInEntity:
    return TradeInEntity(year="2012", make="Toyota", model="Corolla", trim="LE Sedan 4D",
                         mileage="50000", ownership=OwnershipType.OWNED)


@fixture(scope="class")
@allure.title("Lead info")
def lead_info() -> WebLeadEntity:
    return WebLeadFactory.random_lead(stock_number="MHF251128", zip="33431", phone=PhoneNumber.new_valid_phone())
