import allure
import pytest
from pytest import fixture

from src.utils.converter import Converter
from src.web.consumer import OwnershipType
from src.web.entities.trade_in_entity import TradeInEntity
from test import pytestrail


@allure.feature('VDP4')
@allure.story('TradeIn')
class TestVDPDisabledTradeInKBB:

    @pytestrail.case('C5302303')
    @pytest.mark.vdp4
    def test_vdp_trade_in_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text.split('\n')[0])
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"

    @pytest.mark.parametrize("expected_text", [pytest.param("Trade-in applied", marks=[pytestrail.case('C5302294')])])
    @pytest.mark.vdp4
    def test_vdp_trade_in_indicator_and_value(self, fx_cl_apply_trade_in, get_trade_in_default_value, expected_text):
        applied_text = fx_cl_apply_trade_in.txt_trade_in.text.split("\n")[2]
        with allure.step("CHECK - indicators on VDP after applying trade in"):
            assert expected_text == applied_text, f"Unable to find {expected_text} text"

    @pytestrail.case('C5302295')
    @pytest.mark.vdp4
    def test_vdp_trade_in_dismissed_modal_window(self, fx_cl_apply_trade_in):
        with allure.step("CHECK - trade-in modal window disappear after applying trade in"):
            assert not fx_cl_apply_trade_in.consumer.cbo.trade_in.exists(wait_time=0), \
                "trade-in modal window still exist after applying trade-in"

    @pytestrail.case('C5302297')
    @pytest.mark.vdp4
    def test_vdp_trade_in_remove(self, fx_cl_apply_trade_in):
        fx_cl_apply_trade_in.remove_trade_in()
        with allure.step("CHECK - vdp page after removing trade-in"):
            assert fx_cl_apply_trade_in.btn_add_trade_in.exists() and not fx_cl_apply_trade_in.txt_trade_in_status.exists()


@allure.feature('VDP4')
@allure.story('TradeIn')
class TestVDPDisabledTradeInKBBLicencePlate:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2013", make="Volkswagen", model="Beetle", trim="Turbo Convertible 2D",
                             license_plate="GUBAGOO", state="Florida", mileage="12000", ownership=OwnershipType.OWNED)

    @pytestrail.case('********')
    @pytest.mark.vdp4
    def test_vdp_trade_in_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text.split('\n')[0])
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"


@allure.feature('VDP4')
@allure.story('TradeIn')
class TestVDPDisabledTradeInKBBVinNumber:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2010", make="Chevrolet", model="Camaro", trim="1LT",
                             mileage="12000", vin="2G1FB1EV0A9165254", ownership=OwnershipType.OWNED)

    @pytestrail.case('C5302301')
    @pytest.mark.vdp4
    def test_vdp_trade_in_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text.split('\n')[0])
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"


@allure.feature('VDP4')
@allure.story('TradeIn')
class TestVDPDisabledTradeInKBBManualEstimation:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2010", make="Chevrolet", model="Camaro", trim="1LT",
                             mileage="12000", manual_estimation=10000,
                             ownership=OwnershipType.OWNED)

    @pytestrail.case("C5302317")
    def test_vdp_trade_in_logged(self, fx_cl_initial_price, fx_cl_apply_trade_in):
        trade_in_value = Converter.extract_digits(fx_cl_apply_trade_in.txt_trade_in.text.split('\n')[0])
        actual_price = Converter.extract_digits(fx_cl_apply_trade_in.txt_price.text)
        with allure.step("CHECK - that trade-in is applied"):
            assert int(actual_price) + int(trade_in_value) == fx_cl_initial_price, \
                f"Actual price = {actual_price}\n Actual trade-in value = {trade_in_value}\n" \
                f"Initial price = {fx_cl_initial_price}"


@allure.feature('VDP4')
@allure.story('TradeIn')
class TestVDPDisabledTradeInKBBNegative:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2019", make="BMW", model="X6 M", trim="Sport Utility 4D",
                             mileage="500", ownership=OwnershipType.OWNED)

    @pytest.mark.parametrize("expected_text", [pytest.param("Trade-in not applied",
                                                            marks=[pytestrail.case('C5568885'),
                                                                   pytest.mark.ktf(reason="VR-13709")])])
    def test_vdp_trade_in_cannot_be_applied(self, fx_cl_apply_trade_in, expected_text):
        with allure.step("CHECK - that unable to apply negative trade-in"):
            assert fx_cl_apply_trade_in.txt_trade_in_status.text == expected_text, "Negative trade-in should not be applied"


@allure.feature('VDP4')
@allure.story('TradeIn')
class TestVDPDisabledTradeInKBBWithOutTradeIn:

    @fixture(scope="class")
    @allure.title("Trade-in info")
    def trade_in_vehicle(self) -> TradeInEntity:
        return TradeInEntity(year="2012", make="Toyota", model="Corolla", trim="LE Sedan 4D",
                             mileage="50000000", ownership=OwnershipType.OWNED, continue_without_trade_in=True)

    @pytestrail.case("C5302298")
    def test_vdp_trade_in_continue_without_trade_in(self, fx_cl_apply_trade_in, vehicle):
        with allure.step("CHECK - consumer able to proceed with out trade in"):
            assert fx_cl_apply_trade_in.txt_price.text == Converter.int_to_currency(vehicle.price), \
                "vehicle price was updated, after continue without trade-in"
