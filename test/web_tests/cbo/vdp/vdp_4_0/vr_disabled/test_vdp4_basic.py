import allure
import pytest
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from test import pytestrail


@allure.feature('VDP4')
@allure.story('VDP4Basic')
class TestVDPDisabledBasic:

    @fixture(scope="class")
    @allure.title("Vehicle stock number")
    def stock_number(self, vehicle):
        return vehicle.stock

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="1GKS2JKL0PR504011")

    @pytestrail.case('C5055335')
    @pytest.mark.vdp4
    def test_vdp_stock_number(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - stock number"):
            assert fx_cl_open_vdp_from_chat.get_stock() == vehicle.stock and fx_cl_open_vdp_from_chat.txt_stock.is_displayed(), \
                f"Incorrect stock number is displayed,\nActual stock# = {fx_cl_open_vdp_from_chat.get_stock()}" \
                f"\nExpected stock# = {vehicle.stock}"

    @pytestrail.case('C5055336')
    @pytest.mark.vdp4
    def test_vdp_vin_number(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - VIN number"):
            assert fx_cl_open_vdp_from_chat.get_vin() == vehicle.vin and fx_cl_open_vdp_from_chat.txt_vin.is_displayed(), \
                f"Incorrect vin number is displayed,\nActual vin# = {fx_cl_open_vdp_from_chat.get_vin()}" \
                f"\nExpected vin# = {vehicle.vin}"

    @pytestrail.case('C6598443')
    @pytest.mark.vdp4
    def test_vdp_miles(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - miles"):
            assert fx_cl_open_vdp_from_chat.get_miles() == format(vehicle.info.mileage, ","), \
                f"Expected vehicle mileage = {format(vehicle.info.mileage, ',')}\n" \
                f"Actual vehicle mileage = {fx_cl_open_vdp_from_chat.get_miles()}"

    @pytestrail.case('C6598444')
    @pytest.mark.vdp4
    def test_vdp_description_section(self, fx_cl_open_vdp_from_chat, vehicle):
        with allure.step("CHECK - description section"):
            actual_text = fx_cl_open_vdp_from_chat.get_dealer_message_summary()
            expected_text = vehicle.description.replace("<br>  ", "\n").replace("<br>", "\n").replace("  ", " ")
            assert actual_text == expected_text, f"Expected description text = {expected_text}\n" \
                                                 f"Actual vehicle mileage = {actual_text}"

    @pytestrail.case('C6640326')
    @pytest.mark.vdp4
    def test_vdp_close_icon(self, fx_cl_open_vdp_from_chat, vehicle):
        fx_cl_open_vdp_from_chat.btn_close.click()
        from time import sleep
        sleep(1)
        with allure.step("CHECK - previous window appears clicking on close icon on VDP"):
            assert not fx_cl_open_vdp_from_chat.exists(), "VDP window still exists after clicking on close button"
            fx_cl_open_vdp_from_chat.consumer.switch_to.default_content()
            assert fx_cl_open_vdp_from_chat.consumer.exists(), "Consumer desktop window doesn't exists after clicking on close button on VDP"
