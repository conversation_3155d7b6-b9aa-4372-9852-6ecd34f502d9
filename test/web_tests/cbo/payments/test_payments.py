import allure
import pytest
from pytest import fixture

from src.web.consumer.desktop.cbo.payment_component import PaymentsComponent
from src.web.entities.cbo_payment_option_entity import CBOPaymentOptionFactory, CBOPaymentOptionEntity
from test import pytestrail
from test.web_tests import is_mobile_mode


@allure.feature('CBO')
@allure.story('CBOPaymentNew')
class TestCBOPaymentRefresh:

    @fixture(scope="class")
    @allure.title("cbo payment option info")
    def cbo_payment_option_entity(self) -> CBOPaymentOptionEntity:
        return CBOPaymentOptionFactory.default_cbo_payment_option(down_payment='3000',
                                                                  credit_score=f'680-699{" Credit" if is_mobile_mode() else ""}',
                                                                  mileage='14,000 mi/yr')

    @pytestrail.case('C7671582')
    @pytest.mark.ktf(reason="VR-18948")
    def test_cbo_refresh_payment(self, open_payments: PaymentsComponent, cbo_payment_option_entity):
        payment_before_refresh = open_payments.txt_selected_payment.text
        payment_option_entity_before = open_payments.get_selected_payment_options()
        open_payments.refresh_page()
        open_payments.txt_selected_payment.wait_for.presence_of_element_located(wait_time=30)
        with allure.step("CHECK - payment is still applied after refreshing the page"):
            payment_option_entity_after = open_payments.get_selected_payment_options()
            payment_after_refresh = open_payments.txt_selected_payment.text
            assert payment_after_refresh == payment_before_refresh, f'{payment_before_refresh=}\n\n{payment_after_refresh=}'
            assert payment_option_entity_before == payment_option_entity_after, f'{payment_option_entity_before=}\n\n{payment_option_entity_after=}'
