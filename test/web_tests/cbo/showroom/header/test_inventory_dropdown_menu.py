import allure
import pytest

from src.web.consumer.desktop.cbo import VehicleType
from test import pytestrail


@allure.feature('Showroom')
@allure.story('TestChangeVehicleTypeWithShopButton')
class TestChangeVehicleTypeShopButton:

    @pytest.mark.parametrize("vehicle_type_shop", [pytest.param(VehicleType.USED, marks=[pytestrail.case('C5216484')]),
                                                   pytest.param(VehicleType.CERTIFIED, marks=[pytestrail.case('C5216485')]),
                                                   pytest.param(VehicleType.NEW, marks=[pytestrail.case('C5216483')])])
    def test_inventory_dropdown_shop(self, fx_cl_open_showroom, vehicle_type_shop):
        fx_cl_open_showroom.select_vehicle_type_option(vehicle_type_shop)
        assert fx_cl_open_showroom.get_selected_tab() == vehicle_type_shop

    @pytestrail.case('C105558')
    def test_close_show_room(self, fx_cl_open_showroom):
        fx_cl_open_showroom.close_show_room()

