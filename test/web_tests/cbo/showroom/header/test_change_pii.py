import re

import allure

from pytest import fixture
from test import pytestrail


@allure.feature('Showroom')
@allure.story('UpdatePiiHeaderInfo')
class TestUpdatePiiHeaderInfo:

    @fixture(scope="class")
    @allure.title("Showroom - change PII info")
    def fx_cl_change_pii_info(self, fx_cl_consumer_show_room_sign_up, updated_lead_info):
        fx_cl_consumer_show_room_sign_up.open_pii()
        fx_cl_consumer_show_room_sign_up.sign_up.change_pii(updated_lead_info)

    @fixture(scope="class")
    @allure.title("Open VDP page")
    def fx_cl_open_vdp(self, request, fx_cl_consumer_show_room_sign_up):
        def finalizer():
            fx_cl_consumer_show_room_sign_up.quit()

        request.addfinalizer(finalizer)

        fx_cl_consumer_show_room_sign_up.find_vehicle_by_vin(vin="1HGCY2F58RA096263")
        fx_cl_consumer_show_room_sign_up.open_vdp_by_vin(vin="1HGCY2F58RA096263")
        return fx_cl_consumer_show_room_sign_up.consumer.vdp3

    @fixture(scope="class")
    @allure.title("Open CBO page and get PII data")
    def fx_cl_open_cbo_and_get_pii_data(self, fx_cl_open_vdp):
        fx_cl_open_vdp.sign_up.close_modal()
        cbo = fx_cl_open_vdp.open_vr()
        cbo.open_pii()
        return cbo.get_all_pii_values()

    @fixture(scope="class")
    @allure.title("Get PII values in VDP")
    def get_pii_values(self, request, fx_cl_open_vdp):
        fx_cl_open_vdp.open_pii()
        return fx_cl_open_vdp.sign_up.get_all_values()

    @pytestrail.case('C4278446')
    def test_name_displayed_when_consumer_logged_in(self, fx_cl_consumer_show_room_sign_up, lead_info):
        full_name = fx_cl_consumer_show_room_sign_up.get_consumer_name()
        with allure.step("CHECK - Name is displayed when consumer is logged in"):
            assert full_name == lead_info.first_name + " " + lead_info.last_name

    @pytestrail.case('C106028')
    def test_pii_name_updated_in_vdp(self, fx_cl_change_pii_info, fx_cl_open_vdp, updated_lead_info, get_pii_values):
        vdp_pii_name = get_pii_values['name']
        with allure.step("CHECK - PII name is updated in VDP"):
            assert vdp_pii_name == updated_lead_info.first_name + " " + updated_lead_info.last_name, \
                "Name in VDP PII wasn't changed"

    @pytestrail.case('C8549694')
    def test_pii_email_updated_in_vdp(self, fx_cl_change_pii_info, fx_cl_open_vdp, updated_lead_info, get_pii_values):
        vdp_pii_email = get_pii_values['email']
        with allure.step("CHECK - PII email is updated in VDP"):
            assert vdp_pii_email == updated_lead_info.email, "Email in VDP PII wasn't changed"

    @pytestrail.case('C8549695')
    def test_pii_phone_updated_in_vdp(self, fx_cl_change_pii_info, fx_cl_open_vdp, updated_lead_info, get_pii_values):
        vdp_pii_phone = re.sub(r"\D", "", get_pii_values['phone'])
        with allure.step("CHECK - PII phone number is updated in VDP"):
            assert vdp_pii_phone == updated_lead_info.phone, "Phone in VDP PII wasn't changed"

    @pytestrail.case('C8549696')
    def test_pii_zip_updated_in_vdp(self, fx_cl_change_pii_info, fx_cl_open_vdp, updated_lead_info, get_pii_values):
        vdp_pii_zip = get_pii_values['zip']
        with allure.step("CHECK - PII Zip code is updated in VDP"):
            assert vdp_pii_zip == updated_lead_info.zip, "Zip code in VDP PII wasn't changed"

    @pytestrail.case('C8500810')
    def test_pii_name_updated_in_vr(self, fx_cl_change_pii_info, updated_lead_info, fx_cl_open_cbo_and_get_pii_data):
        vdp_pii_name = fx_cl_open_cbo_and_get_pii_data['name']
        with allure.step("CHECK - PII name is updated in VR"):
            assert vdp_pii_name == updated_lead_info.first_name + " " + updated_lead_info.last_name, \
                "Name in VDP PII wasn't changed"

    @pytestrail.case('C8549697')
    def test_pii_email_updated_in_vr(self, fx_cl_change_pii_info, updated_lead_info, fx_cl_open_cbo_and_get_pii_data):
        vdp_pii_email = fx_cl_open_cbo_and_get_pii_data['email']
        with allure.step("CHECK - PII email is updated in VR"):
            assert vdp_pii_email == updated_lead_info.email, "Email in VDP PII wasn't changed"

    @pytestrail.case('C8549698')
    def test_pii_phone_updated_in_vr(self, fx_cl_change_pii_info, updated_lead_info, fx_cl_open_cbo_and_get_pii_data):
        vdp_pii_phone = re.sub(r"\D", "", fx_cl_open_cbo_and_get_pii_data['phone'])
        with allure.step("CHECK - PII phone number is updated in VR"):
            assert vdp_pii_phone == updated_lead_info.phone, "Phone in VDP PII wasn't changed"

    @pytestrail.case('C8549699')
    def test_pii_zip_updated_in_vr(self, fx_cl_change_pii_info, updated_lead_info, fx_cl_open_cbo_and_get_pii_data):
        vdp_pii_zip = fx_cl_open_cbo_and_get_pii_data['zip']
        with allure.step("CHECK - PII zip code is updated in VR"):
            assert vdp_pii_zip == updated_lead_info.zip, "ZIP in VDP PII wasn't changed"
