import allure
import pytest

from src.utils.converter import Converter
from src.web.consumer.desktop.cbo import SortingType
from src.web.consumer.desktop.cbo.show_room_component import VehicleCardType
from test import pytestrail


@allure.feature('Showroom')
@allure.story('NewSorting')
class TestShowRoomNewSorting:

    @pytest.mark.parametrize("expected_sort_elements", [pytest.param([SortingType.LOWEST_PRICE,
                                                                      SortingType.HIGHEST_PRICE],
                                                                     marks=[pytestrail.case('C106035')])])
    def test_showroom_sort_options(self, fx_cl_open_showroom, expected_sort_elements):
        selected_option = fx_cl_open_showroom.txt_sort_selected_option.text
        actual_option_list = fx_cl_open_showroom.get_sort_options_text()
        with allure.step("CHECK - sort options by default"):
            assert actual_option_list == expected_sort_elements and selected_option == actual_option_list[0], \
                f"wrong text of sorting options\nExpected:{expected_sort_elements}\n" \
                f"Actual:{actual_option_list} "

    @pytest.mark.parametrize("sorting_option, vehicle_card_type,column,replacement_value,value_to_replace",
                             [pytest.param(SortingType.LOWEST_PRICE, VehicleCardType.MODEL, "price", 'Call for price',
                                           '$999,999', marks=[pytestrail.case("C716601")]),
                              pytest.param(SortingType.HIGHEST_PRICE, VehicleCardType.MODEL, "price", 'Call for price',
                                           '$999,999', marks=[pytestrail.case("C716598")])])
    def test_showroom_sort_lowest_highest_price(self, fx_fn_select_sorting_option, sorting_option, vehicle_card_type,
                                                column, replacement_value, value_to_replace):
        with allure.step(f"CHECK - modal card sorted by {sorting_option}"):
            values = fx_fn_select_sorting_option.get_cards_column_values_with_replace(vehicle_card_type, column,
                                                                                      replacement_value, value_to_replace)
            assert Converter.is_sorted(any_lists=values, asc=sorting_option == SortingType.LOWEST_PRICE), \
                f"incorrect sorting of prices by cards\nActual:{values}"
