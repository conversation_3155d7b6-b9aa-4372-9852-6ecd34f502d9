import allure
import pytest
from pytest import fixture

from src.utils.converter import Converter
from src.web.consumer.desktop.cbo import SortingType
from src.web.consumer.desktop.cbo.show_room_component import VehicleCardType
from test import pytestrail
from test.web_tests.cbo.showroom import total_amount_error, allure_step_total_amount


@allure.feature("Showroom")
@allure.story("SingleFilterInteriorColorMultipleValue")
class TestShowRoomSingleFilterInteriorColorMultipleValue:

    @fixture(scope="class")
    def filters(self):
        return (("Interior Color", ("Brown", "Gray")),)

    @pytest.mark.parametrize("expected_count", [pytest.param("153", marks=[pytestrail.case("C716625")])])
    def test_showroom_single_filter_multiple_value_total_amount(self, fx_cl_apply_filter, expected_count):
        with allure.step(allure_step_total_amount):
            assert fx_cl_apply_filter.get_vehicle_total_amount() == expected_count, total_amount_error

    @pytest.mark.parametrize("vehicle_card_type", [pytest.param(VehicleCardType.MODEL,
                                                                marks=[pytestrail.case("C988995")])])
    def test_showroom_single_filter_multiple_value_grid_view(self, fx_cl_apply_filter, filters, vehicle_card_type):
        errors = fx_cl_apply_filter.check_grid_view(filters, vehicle_card_type)
        assert errors == "", errors

    @pytestrail.case("C989006")
    @pytest.mark.ktf(reason="VR-19566")
    def test_showroom_single_filter_multiple_value_unselected_filters(self, fx_ss_unselected_filters,
                                                                      fx_cl_apply_filter, filters):
        errors = fx_cl_apply_filter.check_unselected_filters(filters, fx_ss_unselected_filters)
        assert errors == "", errors

    @pytestrail.case("C989028")
    def test_showroom_single_filter_multiple_value_label_expanded(self, fx_cl_apply_filter, filters):
        errors = fx_cl_apply_filter.check_filter_label_expanded(filters)
        assert errors == [], "\n".join(errors)

    @pytestrail.case("C989017")
    def test_showroom_single_filter_multiple_value_label_collapsed(self, fx_cl_apply_filter, filters,
                                                                   fx_cl_collapse_filter):
        errors = fx_cl_apply_filter.check_filter_label_collapsed(filters)
        assert errors == [], "\n".join(errors)

    @pytest.mark.parametrize("sorting_option, vehicle_card_type, column, replacement_value,value_to_replace",
                             [pytest.param(SortingType.LOWEST_PRICE, VehicleCardType.MODEL, "price", 'Call for price',
                                           '$999,999',
                                           marks=[pytestrail.case("C989039")]),
                              pytest.param(SortingType.HIGHEST_PRICE, VehicleCardType.MODEL, "price", 'Call for price',
                                           '$999,999',
                                           marks=[pytestrail.case("C989050")])])
    def test_showroom_single_filter_multiple_value_sort(self, fx_cl_apply_filter, fx_fn_select_sorting_option,
                                                        sorting_option, vehicle_card_type, column, replacement_value,
                                                        value_to_replace):
        with allure.step(f"CHECK - modal card sorted by {sorting_option}"):
            values = fx_fn_select_sorting_option.get_cards_column_value(vehicle_card_type, column)
            if value_to_replace:
                values = Converter.replace_in_list(values, replacement_value, value_to_replace)
            assert Converter.is_sorted(any_lists=values, asc=sorting_option == SortingType.LOWEST_PRICE), \
                f"incorrect sorting of prices by cards\nActual:{values}"
