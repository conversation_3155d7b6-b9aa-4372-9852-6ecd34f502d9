import allure
import pytest
from pytest import fixture

from src.web.consumer.desktop.cbo import SortingType
from src.web.consumer.desktop.cbo.show_room_component import VehicleCardType
from test import pytestrail
from test.web_tests.cbo.showroom import allure_step_total_amount, total_amount_error


@allure.feature('Showroom')
@allure.story('SingleFilterHighlights')
class TestShowRoomSingleFilterHighlights:

    @fixture(scope="class")
    def filters(self):
        return (('Highlights', ('Panoramic Roof',)),)

    @pytest.mark.parametrize("expected_count", [pytest.param("128", marks=[pytestrail.case('C730208')])])
    def test_showroom_single_filter_total_amount(self, fx_cl_apply_filter, expected_count):
        with allure.step(allure_step_total_amount):
            assert fx_cl_apply_filter.get_vehicle_total_amount() == expected_count, total_amount_error

    @pytest.mark.parametrize("vehicle_card_type", [pytest.param(VehicleCardType.MODEL,
                                                                marks=[pytestrail.case('C730198')])])
    def test_showroom_single_filter_grid_view(self, fx_cl_apply_filter, filters, vehicle_card_type):
        errors = fx_cl_apply_filter.check_grid_view(filters, vehicle_card_type)
        assert errors == "", errors

    @pytestrail.case('C179875')
    @pytest.mark.ktf(reason="VR-19566")
    def test_showroom_single_filter_unselected_filters(self, fx_ss_unselected_filters, fx_cl_apply_filter, filters):
        errors = fx_cl_apply_filter.check_unselected_filters(filters, fx_ss_unselected_filters)
        assert errors == "", errors

    @pytestrail.case('C893672')
    def test_showroom_single_filter_label_expanded(self, fx_cl_apply_filter, filters):
        errors = fx_cl_apply_filter.check_filter_label_expanded(filters)
        assert errors == [], "\n".join(errors)

    @pytestrail.case('C730218')
    def test_showroom_single_filter_label_collapsed(self, fx_cl_apply_filter, filters, fx_cl_collapse_filter):
        errors = fx_cl_apply_filter.check_filter_label_collapsed(filters)
        assert errors == [], "\n".join(errors)

    @pytest.mark.parametrize(
        "sorting_option, vehicle_card_type", [pytest.param(SortingType.LOWEST_PRICE, VehicleCardType.MODEL,
                                                           marks=[pytestrail.case('C972547'),
                                                                  pytest.mark.ktf(reason="VR-8570")]),
                                              pytest.param(SortingType.HIGHEST_PRICE, VehicleCardType.MODEL,
                                                           marks=[pytestrail.case('C972558'),
                                                                  pytest.mark.ktf(reason="VR-8570")])])
    def test_showroom_sort_lowest_highest_price(self, fx_cl_apply_filter, fx_fn_select_sorting_option, sorting_option,
                                                vehicle_card_type):
        with allure.step(f"CHECK - modal card sorted by {sorting_option}"):
            assert fx_fn_select_sorting_option \
                .get_all_vehicle_cards(vehicle_card_type).is_sorted("price", "int",
                                                                    asc=sorting_option == SortingType.LOWEST_PRICE,
                                                                    regex=r"\D"), \
                f"incorrect sorting of prices by cards\nActual:{fx_fn_select_sorting_option.get_all_vehicle_cards(vehicle_card_type).get_column_values('price')}"
