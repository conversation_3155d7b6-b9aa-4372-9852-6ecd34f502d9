import allure
import pytest
from pytest import fixture

from src.web.consumer.desktop.cbo import VehicleType
from test import pytestrail
from test.web_tests.cbo.showroom import total_amount_error


@allure.feature('Showroom')
@allure.story('NewFinanceDefaultState')
class TestShowRoomNewFinanceDefaultState:

    @fixture(scope="class")
    def filters(self, vehicle_type: VehicleType):
        return ((vehicle_type, ("",)),)

    @pytest.mark.parametrize("expected_count", [pytest.param("698", marks=[pytestrail.case('C5234542'),
                                                                           pytest.mark.ktf(reason="GG-11336")])])
    def test_showroom_default_total_amount(self, fx_cl_consumer_show_room_filtered, expected_count):
        with allure.step("CHECK - total amount when no one filter is selected"):
            assert fx_cl_consumer_show_room_filtered.get_vehicle_total_amount() == expected_count, total_amount_error

    @pytest.mark.parametrize("default_tab", [pytest.param(VehicleType.NEW, marks=[pytestrail.case('C5234540')])])
    def test_showroom_default_tab(self, fx_cl_consumer_show_room_filtered, default_tab):
        actual_tab = fx_cl_consumer_show_room_filtered.get_selected_tab()
        with allure.step("CHECK - the new tab is selected by default"):
            assert actual_tab == default_tab, f"unexpected tab selected by default\nexpected tab: {default_tab}\n " \
                                              f"actual tab: {actual_tab}"

    @pytestrail.case('C5234541')
    def test_showroom_unselected_filters_default_state(self, fx_cl_consumer_show_room_filtered,
                                                    fx_ss_unselected_filters, filters):
        errors = fx_cl_consumer_show_room_filtered.check_unselected_filters(filters, fx_ss_unselected_filters)
        with allure.step("CHECK - default state of unselected filters"):
            assert errors == "", errors
