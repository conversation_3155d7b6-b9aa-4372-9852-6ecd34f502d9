import allure
import pytest
from pytest import fixture

from src.web.consumer.desktop.cbo import SortingType
from src.web.consumer.desktop.cbo.show_room_component import VehicleCardType
from test import pytestrail
from test.web_tests.cbo.showroom import allure_step_total_amount, total_amount_error, \
    get_ordered_vin_numbers_by_price


@allure.feature('Showroom')
@allure.story('FinanceSingleFilterModel')
class TestShowRoomFinanceSingleFilterModel:

    @fixture(scope="class")
    def filters(self):
        return (('Model', ('Pilot',)),)

    @pytest.mark.parametrize("expected_count", [pytest.param("117", marks=[pytestrail.case('C5234569')])])
    def test_showroom_single_filter_total_amount(self, fx_cl_apply_filter_finance, expected_count):
        with allure.step(allure_step_total_amount):
            assert fx_cl_apply_filter_finance.get_vehicle_total_amount() == expected_count, total_amount_error

    @pytest.mark.parametrize("vehicle_card_type",
                             [pytest.param(VehicleCardType.VIN, marks=[pytestrail.case('C5234554')])])
    def test_showroom_single_filter_grid_view(self, fx_cl_apply_filter_finance, filters, vehicle_card_type):
        errors = fx_cl_apply_filter_finance.check_grid_view(filters, vehicle_card_type)
        assert errors == "", errors

    @pytestrail.case('C5234629')
    def test_showroom_single_filter_unselected_filters(self, fx_ss_unselected_filters, fx_cl_apply_filter_finance, filters):
        errors = fx_cl_apply_filter_finance.check_unselected_filters(filters, fx_ss_unselected_filters)
        assert errors == "", errors

    @pytestrail.case('C5234599')
    def test_showroom_single_filter_label_expanded(self, fx_cl_apply_filter_finance, filters):
        errors = fx_cl_apply_filter_finance.check_filter_label_expanded(filters)
        assert errors == [], "\n".join(errors)

    @pytestrail.case('C5234584')
    def test_showroom_single_filter_label_collapsed(self, fx_cl_apply_filter_finance, filters, fx_cl_collapse_filter):
        errors = fx_cl_apply_filter_finance.check_filter_label_collapsed(filters)
        assert errors == [], "\n".join(errors)

    @pytest.mark.parametrize("sorting_option",
                             [pytest.param(SortingType.LOWEST_PRICE,
                                           marks=[pytestrail.case('C5234644'),
                                                  pytest.mark.ktf(reason="VR-13191")]),
                              pytest.param(SortingType.HIGHEST_PRICE,
                                           marks=[pytestrail.case('C5234659'),
                                                  pytest.mark.ktf(reason="VR-13191")])])
    def test_showroom_sort_lowest_highest_price(self, fx_cl_apply_filter_finance, fx_fn_select_sorting_option, sorting_option):
        actual_vin_numbers = fx_fn_select_sorting_option.get_all_vin_numbers()
        expected_vin_numbers = get_ordered_vin_numbers_by_price(actual_vin_numbers, sorting_option)
        with allure.step(f"CHECK - modal card sorted by {sorting_option}"):
            assert actual_vin_numbers == expected_vin_numbers, \
                f"incorrect sorting of prices by vin cards\nActual:{actual_vin_numbers}" \
                f"\nExpected:{expected_vin_numbers}"
