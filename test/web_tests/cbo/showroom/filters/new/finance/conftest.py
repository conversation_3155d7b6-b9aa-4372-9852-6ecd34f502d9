import allure
from pytest import fixture

from src import cbo_url
from src.web.consumer import PaymentOptionType
from src.web.consumer.desktop.cbo import VehicleType, SortingType
from src.web.consumer.desktop.cbo.show_room_component import ShowRoom
from src.web.entities.showroom_payment_option_entity import ShowroomPaymentOptionFactory
from test.web_tests.cbo import ConsumerPage


@fixture(scope="class")
def vehicle_type():
    return VehicleType.NEW


@fixture(scope='class')
@allure.title('Showroom login')
def showroom_login():
    return True


@fixture(scope="class")
@allure.title("Select payment option and vehicle type")
def fx_cl_consumer_show_room_filtered(fx_cl_consumer_show_room_sign_up) -> ShowRoom:
    payment_options = ShowroomPaymentOptionFactory.create(vehicle_type=VehicleType.NEW,
                                                          payment_option=PaymentOptionType.FINANCE)
    fx_cl_consumer_show_room_sign_up.apply_payments(payment_options)
    return fx_cl_consumer_show_room_sign_up


@fixture(scope="class")
@allure.title("Apply filters for finance payment type")
def fx_cl_apply_filter_finance(request, fx_cl_consumer_show_room_filtered, filters):
    def finalizer():
        fx_cl_consumer_show_room_filtered.clear_all_filters()

    request.addfinalizer(finalizer)
    fx_cl_consumer_show_room_filtered.apply_filters(filters)
    return fx_cl_consumer_show_room_filtered


@fixture(scope="class")
@allure.title("Collapse filter")
def fx_cl_collapse_filter(fx_cl_consumer_show_room_filtered, filters):
    fx_cl_consumer_show_room_filtered.collapse_filters(filters)
    return fx_cl_consumer_show_room_filtered


@fixture(scope="function")
@allure.title("Select sorting option")
def fx_fn_select_sorting_option(fx_cl_consumer_show_room_filtered, sorting_option: SortingType):
    fx_cl_consumer_show_room_filtered.select_sorting_option(sorting_option)
    return fx_cl_consumer_show_room_filtered
