import allure
import pytest
from pytest import fixture

from src.utils.converter import Converter
from src.web.consumer.desktop.cbo import SortingType, VehicleType
from src.web.consumer.desktop.cbo.show_room_component import VehicleCardType
from test import pytestrail


@allure.feature('Showroom')
@allure.story('UsedSorting')
class TestShowRoomUsedSorting:

    @fixture(scope="class")
    def filters(self):
        return ((VehicleType.USED, ("",)),)

    @pytest.mark.parametrize("expected_sort_elements",
                             [pytest.param([SortingType.LOWEST_PRICE, SortingType.HIGHEST_PRICE, SortingType.NEWEST,
                                            SortingType.OLDEST, SortingType.LOWEST_MILEAGE,
                                            SortingType.HIGHEST_MILEAGE],
                                           marks=[pytestrail.case('C1750167')])])
    def test_showroom_sort_options(self, fx_cl_open_showroom, expected_sort_elements):
        selected_option = fx_cl_open_showroom.txt_sort_selected_option.text
        actual_option_list = fx_cl_open_showroom.get_sort_options_text()
        with allure.step("CHECK - sort options by default"):
            assert actual_option_list == expected_sort_elements \
                   and selected_option == actual_option_list[0], \
                f"wrong text of sorting options\nExpected:{expected_sort_elements}\n" \
                f"Actual:{actual_option_list}"

    @pytest.mark.parametrize("sorting_option, vehicle_card_type,column,replacement_value,value_to_replace",
                             [pytest.param(SortingType.LOWEST_PRICE, VehicleCardType.VIN, "price", 'Call for price',
                                           '$999,999', marks=[pytestrail.case('C716410')]),
                              pytest.param(SortingType.HIGHEST_PRICE, VehicleCardType.VIN, "price", 'Call for price',
                                           '$0', marks=[pytestrail.case('C716407')]),
                              pytest.param(SortingType.NEWEST, VehicleCardType.VIN, "year", '', '',
                                           marks=[pytestrail.case('C716408')]),
                              pytest.param(SortingType.OLDEST, VehicleCardType.VIN, "year", '', '',
                                           marks=[pytestrail.case('C716409')]),
                              pytest.param(SortingType.LOWEST_MILEAGE, VehicleCardType.VIN, "mileage", None,
                                           '999,999mi', marks=[pytestrail.case('C1750192')]),
                              pytest.param(SortingType.HIGHEST_MILEAGE, VehicleCardType.VIN, "mileage", None, '0mi',
                                           marks=[pytestrail.case('C1750193')])])
    def test_showroom_used_sorting(self, fx_fn_select_sorting_option, sorting_option, vehicle_card_type, column,
                                   replacement_value, value_to_replace):
        with allure.step(f"CHECK - vin card sorted by {sorting_option}"):
            values = fx_fn_select_sorting_option.get_cards_column_value(vehicle_card_type, column)
            if value_to_replace:
                values = Converter.replace_in_list(values, replacement_value, value_to_replace)
            assert Converter.is_sorted(any_lists=values, asc=sorting_option in (SortingType.LOWEST_PRICE,
                                                                                SortingType.OLDEST,
                                                                                SortingType.LOWEST_MILEAGE)), \
                f"incorrect sorting of prices by cards\nActual:{values}"
