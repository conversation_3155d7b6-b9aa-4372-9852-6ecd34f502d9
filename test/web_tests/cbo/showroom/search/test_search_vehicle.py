import allure
import pytest

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from src.web.consumer.desktop.cbo.show_room_component import VehicleCardType
from test import pytestrail
from pytest import fixture


@allure.feature('Showroom')
@allure.story('TestSearchVehicles')
class TestSearchVehicles:

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="5FNYG2H35SB013369")

    @fixture(scope="function")
    @allure.title("Showroom - Search vehicle by")
    def fx_fn_search_vehicle_by(self,  fx_cl_open_showroom, search_text):
        fx_cl_open_showroom.search_action_with_enter(search_text)
        return fx_cl_open_showroom

    @fixture(scope="class")
    @allure.title("Showroom - Search vehicle by year, make, model")
    def fx_cl_search_vehicle_by_name(self,  fx_cl_open_showroom, vehicle):
        fx_cl_open_showroom.search_action(vehicle.name)
        return fx_cl_open_showroom.get_search_item_info(vehicle.stock)

    @fixture(scope="class")
    @allure.title("Showroom - Open VDP from search")
    def fx_cl_open_vdp_from_search_suggestions(self, fx_cl_open_showroom, vehicle):
        fx_cl_open_showroom.search_action(vehicle.name)
        fx_cl_open_showroom.open_vdp_from_search(vehicle.stock)
        return fx_cl_open_showroom.consumer.vdp3

    @pytest.mark.parametrize("search_text, vehicle_card_type, error_message", [
        pytest.param('Passport', VehicleCardType.VIN, "Some vehicles are NOT Passport models!",
                     marks=[pytestrail.case('C407798')]),
        pytest.param('RTL', VehicleCardType.VIN, "Some vehicles don't have RTL trim!",
                     marks=[pytestrail.case('C407799')]),
        pytest.param('2024', VehicleCardType.VIN, "Some vehicles don't have 2024 year!",
                     marks=[pytestrail.case('C407801')]),
        pytest.param('3CZRZ1H30SM724413', VehicleCardType.VIN, "The vehicle wasn't found",
                     marks=[pytestrail.case('C8648561')])
    ])
    def test_showroom_search_by(self, fx_fn_search_vehicle_by, search_text, vehicle_card_type, error_message):
        vehicles_data = fx_fn_search_vehicle_by.get_all_vehicle_cards(vehicle_card_type)
        assert all(search_text in vehicle for vehicle in vehicles_data.elements_texts), error_message

    @pytestrail.case('C8688166')
    def test_vehicle_info_year(self, fx_cl_search_vehicle_by_name, vehicle):
        year = fx_cl_search_vehicle_by_name['year']
        with allure.step("CHECK - Vehicle year is correct for the found vehicles"):
            assert year == vehicle.year, 'Vehicle year is incorrect'

    @pytestrail.case('C8688167')
    def test_vehicle_info_make(self, fx_cl_search_vehicle_by_name, vehicle):
        make = fx_cl_search_vehicle_by_name['make']
        with allure.step("CHECK - Vehicle make is correct for the found vehicles"):
            assert make == vehicle.make, 'Vehicle make is incorrect'

    @pytestrail.case('C8688168')
    def test_vehicle_info_model(self, fx_cl_search_vehicle_by_name, vehicle):
        model = fx_cl_search_vehicle_by_name['model']
        with allure.step("CHECK - Vehicle model is correct for the found vehicles"):
            assert model == vehicle.model, 'Vehicle model is incorrect'

    @pytestrail.case('C8688169')
    def test_vehicle_info_trim(self, fx_cl_search_vehicle_by_name, vehicle):
        trim = fx_cl_search_vehicle_by_name['trim']
        with allure.step("CHECK - Vehicle trim is correct for the found vehicles"):
            assert trim == vehicle.trim, 'Vehicle trim is incorrect'

    @pytestrail.case('C5794420')
    def test_open_vdp_from_search(self, fx_cl_open_vdp_from_search_suggestions, vehicle):
        vdp_vin = fx_cl_open_vdp_from_search_suggestions.get_vin()
        with allure.step("CHECK - Vehicle vin in VDP is the same as in the showroom"):
            assert vdp_vin == vehicle.vin, "Incorrect VIN displayed in VDP"

