import allure

from src.web.consumer.desktop.cbo import VehicleType
from src.web.consumer.desktop.cbo.show_room_component import VehicleCardType

from test import pytestrail
from pytest import fixture


@allure.feature('Showroom')
@allure.story('Test Used VIN Vehicle Cards')
class TestVINVehicleCardsUsed:

    @fixture(scope="class")
    @allure.title("Showroom - Open VIN level cards and return showroom")
    def fx_cl_open_vin_lvl_cards(self,  fx_cl_consumer_show_room_sign_up):
        fx_cl_consumer_show_room_sign_up.select_vehicle_type(VehicleType.USED)
        filters = (("Model", ("Accord",)),)
        fx_cl_consumer_show_room_sign_up.apply_filters(filters)
        return fx_cl_consumer_show_room_sign_up

    @pytestrail.case('C8726150')
    def test_vehicle_vin_engine(self, fx_cl_open_vin_lvl_cards, vehicle):
        vehicles_engine = fx_cl_open_vin_lvl_cards.get_cards_column_value(VehicleCardType.VIN, 'txt_engine')
        with allure.step("CHECK - Each VIN-level vehicle card for the expected model displays engine information"):
            assert all(engine for engine in vehicles_engine), "List contains an empty string"

    @pytestrail.case('C8726151')
    def test_vehicle_vin_drivetrain(self, fx_cl_open_vin_lvl_cards, vehicle):
        vehicles_drivetrain = fx_cl_open_vin_lvl_cards.get_cards_column_value(VehicleCardType.VIN, 'txt_drivetrain')
        with allure.step("CHECK - Each VIN-level vehicle card for the expected model displays drivetrain information"):
            assert all(drivetrain for drivetrain in vehicles_drivetrain), "List contains an empty string"

    @pytestrail.case('C8726152')
    def test_vehicle_vin_body(self, fx_cl_open_vin_lvl_cards, vehicle):
        vehicles_body = fx_cl_open_vin_lvl_cards.get_cards_column_value(VehicleCardType.VIN, 'txt_body')
        with allure.step("CHECK - Each VIN-level vehicle card for the expected model displays body style information"):
            assert all(body for body in vehicles_body), "List contains an empty string"

    @pytestrail.case('C8726153')
    def test_vehicle_vin_horsepower(self, fx_cl_open_vin_lvl_cards, vehicle):
        vehicles_horsepower = fx_cl_open_vin_lvl_cards.get_cards_column_value(VehicleCardType.VIN, 'txt_horsepower')
        with allure.step("CHECK - Each VIN-level vehicle card for the expected model displays horsepower information"):
            assert all(horsepower for horsepower in vehicles_horsepower), "List contains an empty string"
