import allure

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from src.web.consumer.desktop.cbo.show_room_component import VehicleCardType

from test import pytestrail
from pytest import fixture


@allure.feature('Showroom')
@allure.story('Test vehicle cards Stock, Favorites and CTAs')
class TestVINVehicleCardsStockAndCTAs:

    @fixture(scope="class")
    @allure.title("Vehicle info")
    def vehicle(self) -> VehicleEntity:
        return VehicleDetails.get_vehicle_details(vin="1HGCY2F59SA003322")

    @fixture(scope="class")
    @allure.title("Showroom - Open VIN level cards from model lvl card")
    def fx_cl_open_vin_lvl_cards_from_model(self,  fx_cl_open_showroom):
        return fx_cl_open_showroom.select_model_card('2025 Accord Hybrid')

    @fixture(scope="class")
    @allure.title("Showroom - Open VDP from search")
    def fx_cl_open_vdp_from_vehicle_card(self, request, fx_cl_open_vin_lvl_cards_from_model, fx_cl_open_showroom,
                                         vehicle):
        def finalizer():
            fx_cl_open_showroom.close_last_tab()
        request.addfinalizer(finalizer)
        fx_cl_open_showroom.open_vdp_by_vin(vehicle.vin)
        return fx_cl_open_showroom.consumer.vdp3

    @pytestrail.case('C232323')
    def test_vehicles_in_stock(self, fx_cl_open_vin_lvl_cards_from_model, fx_cl_open_showroom):
        stock_modal = int(fx_cl_open_vin_lvl_cards_from_model.split()[0])
        total_vehicles_label = int(fx_cl_open_showroom.get_vehicle_total_amount())
        total_amount = fx_cl_open_showroom.get_vehicles_card_data(VehicleCardType.VIN)
        with allure.step("CHECK - Model level vehicle in stock number"):
            assert stock_modal == len(total_amount) and total_vehicles_label == len(total_amount), \
                "Total vehicle amount in modal lvl card is not the same as in VIN lvl card"

    @pytestrail.case('C3751470')
    def test_mark_as_favorite(self, fx_cl_open_vin_lvl_cards_from_model, fx_cl_open_showroom, vehicle):
        fx_cl_open_showroom.toggle_favorite_status(vehicle.vin)

    @pytestrail.case('C3751471')
    def test_unmark_from_favorite(self, fx_cl_open_vin_lvl_cards_from_model, fx_cl_open_showroom, vehicle):
        fx_cl_open_showroom.toggle_favorite_status(vehicle.vin, mark=False)

    @pytestrail.case('C106031')
    def test_open_vdp(self, fx_cl_open_vdp_from_vehicle_card, vehicle):
        vdp_vin = fx_cl_open_vdp_from_vehicle_card.get_vin()
        with allure.step("CHECK - Vehicle vin in VDP is the same as in the showroom"):
            assert vdp_vin == vehicle.vin, "Incorrect VIN displayed in VDP"
