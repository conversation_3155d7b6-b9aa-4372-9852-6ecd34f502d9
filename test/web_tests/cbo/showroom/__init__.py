import re
from copy import copy

from kom_framework.src.utils.database import MySql
from src import database_configuration
from src.web.consumer.desktop.cbo import SortingType, VehicleType

filter_item_to_replace = {
    "Brown": ("Cinnamon", "<PERSON>", "Dragon Brown", "Bronze Dune Metallic", "Dark Mocha Metallic"),
    "Gray": ("Tungsten Metallic", "Khaki", "Gray"),
    "Blue": ("Frostbite", "Indigo", "Azure", "Deep Cerulean", "Velvet", "Atlantic Metallic", "Blue"),
    "4x4": ("4WD",),
}


def get_replaced_item(filter_item: tuple):
    result = []
    for item in filter_item:
        if item in filter_item_to_replace.keys():
            for i in filter_item_to_replace.get(item):
                result.append(i)
        else:
            result.append(item)
    return tuple(result)


class FilterEntity:
    def __init__(self, make=None, model=None, year=None, exterior=None, trim=None, interior=None, highlights=None,
                 body_style=None, engine=None, transmission=None, drivetrain=None, fuel_type=None):
        self.make = make
        self.model = model
        self.year = year
        self.exterior = exterior
        self.trim = trim
        self.interior = interior
        self.highlights = highlights
        self.body_style = body_style
        self.engine = engine
        self.transmission = transmission
        self.drivetrain = drivetrain
        self.fuel_type = fuel_type

    def set_filter_value_by_filter_name(self, filter_name, filter_value: tuple):
        if filter_name == "Make":
            self.make = filter_value
        elif filter_name == "Model":
            self.model = filter_value
        elif filter_name == "Year":
            self.year = filter_value
        elif filter_name == "Exterior Color":
            self.exterior = get_replaced_item(filter_value)
        elif filter_name == "Trim":
            self.trim = filter_value
        elif filter_name == "Interior Color":
            self.interior = get_replaced_item(filter_value)
        elif filter_name == "Highlights":
            self.highlights = filter_value
        elif filter_name == "Body Style":
            self.body_style = filter_value
        elif filter_name == "Engine":
            self.engine = filter_value
        elif filter_name == "Transmission":
            self.transmission = filter_value
        elif filter_name == "Drivetrain":
            self.drivetrain = get_replaced_item(filter_value)
        elif filter_name == "Fuel Type":
            self.fuel_type = filter_value


unselected_filters = {
    (("Make", ("Subaru",)),): {'Model': {'Ascent': '22', 'BRZ': '1', 'Crosstrek': '9', 'Forester': '13', 'Impreza': '15', 'Legacy': '15', 'Outback': '33', 'WRX': '1'}, 'Year': {'2019': '105', '2018': '4'}, 'Exterior Color': {'Black': '18', 'Blue': '7', 'Gray': '18', 'Green': '5', 'Orange': '3', 'Red': '15', 'Silver': '18', 'White': '25'}, 'Interior Color': {'Black': '48', 'Brown': '5', 'Gray': '12', 'Ivory': '10', 'Java': '8', 'Slate Black': '12', 'Titanium Gray': '1', 'Warm Ivory': '13'}, 'Highlights': {'Climate Control': '82', 'Heated Mirrors': '86', 'Heated Seats': '87', 'Keyless Access': '109', 'Moon Roof': '52', 'Rear Camera': '109', 'Rear Climate Control': '22', 'Rear Wiper': '89', 'Roof Rack': '75', 'Satellite Radio': '99', 'Sun Roof': '1'}, 'Body Style': {'2D Coupe': '1', '4D Hatchback': '12', '4D Sedan': '19', '4D Sport Utility': '77'}, 'Engine': {'4 2': '26', '4 2.4': '22', '4 2.5': '56', '6 3.6': '5'}, 'Transmission': {'5-Speed Manual': '2', '6-Speed Manual': '1', 'CVT Lineartronic': '61', 'Close-Ratio 6-Speed Manual': '1', 'Lineartronic CVT': '44'}, 'Drivetrain': {'AWD': '108', 'RWD': '1'}, 'Fuel Type': {'Gasoline': '109'}},
    (('Make', ('Subaru', 'Dodge')),): {'Model': {'Ascent': '22', 'BRZ': '1', 'Challenger': '2', 'Charger': '1', 'Crosstrek': '9', 'Forester': '13', 'Impreza': '15', 'Legacy': '15', 'Outback': '33', 'WRX': '1'}, 'Year': {'2021': '3', '2019': '105', '2018': '4'}, 'Exterior Color': {'Black': '18', 'Blue': '7', 'Gray': '18', 'Green': '5', 'Orange': '6', 'Red': '15', 'Silver': '18', 'White': '25'}, 'Interior Color': {'Black': '49', 'Blk Clth Performance Sts': '1', 'Blk Houndstooth Clth Sprt': '1', 'Brown': '5', 'Gray': '12', 'Ivory': '10', 'Java': '8', 'Slate Black': '12', 'Titanium Gray': '1', 'Warm Ivory': '13'}, 'Highlights': {'AUX': '3', 'Bluetooth': '2', 'Climate Control': '84', 'Heated Mirrors': '89', 'Heated Seats': '89', 'Keyless Access': '112', 'Moon Roof': '52', 'Navigation': '2', 'Rear Camera': '112', 'Rear Climate Control': '22', 'Rear Wiper': '89', 'Remote Start': '3', 'Roof Rack': '75', 'Satellite Radio': '102', 'Sun Roof': '1'}, 'Body Style': {'2D Coupe': '3', '4D Hatchback': '12', '4D Sedan': '20', '4D Sport Utility': '77'}, 'Engine': {'3.6 liters / V6 Cylinder': '2', '4 2': '26', '4 2.4': '22', '4 2.5': '56', '5.7 liters / 8 Cylinder': '1', '6 3.6': '5'}, 'Transmission': {'5-Speed Manual': '2', '6-Speed Manual': '1', '6-Speed Manual Tremec': '1', '8-Speed Automatic (8HP50)': '2', 'CVT Lineartronic': '61', 'Close-Ratio 6-Speed Manual': '1', 'Lineartronic CVT': '44'}, 'Drivetrain': {'AWD': '108', 'RWD': '4'}, 'Fuel Type': {'Gasoline': '112'}},
    (('Make', ('Honda',)),): {'Model': {'Accord': '11', 'CR-V': '8', 'CR-V Hybrid': '1', 'Civic': '9', 'Civic Type R': '1', 'Insight': '1', 'Odyssey': '8', 'Pilot': '8', 'Ridgeline': '2'}, 'Year': {'2025': '1', '2024': '1', '2023': '4', '2022': '11', '2021': '5', '2020': '5', '2019': '4', '2018': '6', '2017': '3', '2016': '1', '2015': '3', '2014': '1', '2013': '1', '2012': '2', '2008': '1'}, 'Exterior Color': {'Basque Red Pearl Ii': '1', 'Black': '15', 'Blue': '4', 'Gray': '10', 'Grigio Metallic': '1', 'Red': '3', 'Silver': '6', 'Tan': '1', 'White': '7'}, 'Interior Color': {'Beige': '3', 'Black': '26', 'Brown': '1', 'Gray': '11', 'Ivory': '3', 'Mocha': '1', 'Red': '1', 'Truffle': '1'}, 'Highlights': {'AUX': '49', 'Bluetooth': '48', 'Climate Control': '46', 'Heated Mirrors': '29', 'Heated Seats': '28', 'Keyless Access': '49', 'Leather Seats': '21', 'Moon Roof': '24', 'Panoramic Roof': '1', 'Parking Sensors': '5', 'Rear Camera': '47', 'Rear Climate Control': '18', 'Rear Wiper': '27', 'Remote Start': '29', 'Roof Rack': '7', 'Satellite Radio': '33', 'Sun Roof': '10', 'Third Row Seats': '1', 'Tinted Glass': '46', 'USB': '45'}, 'Body Style': {'2D Coupe': '1', '4D Crew Cab': '2', '4D Hatchback': '2', '4D Passenger Van': '8', '4D Sedan': '19', '4D Sport Utility': '17'}, 'Engine': {'1.5 liters / 4 Cylinder': '12', '1.8 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '11', '2.4 liters / 4 Cylinder': '6', '3.5 liters / V6 Cylinder': '19'}, 'Transmission': {'1-Speed CVT w/OD': '23', '10-Speed Automatic w/OD': '7', '5-Speed Automatic': '3', '5-Speed Automatic w/OD': '1', '6-Speed Automatic w/OD': '6', '6-Speed Manual w/OD': '1', '9-Speed Automatic w/OD': '7', 'Continuously variable': '1'}, 'Drivetrain': {'AWD': '8', 'FWD': '41'}, 'Fuel Type': {'Gasoline': '48', 'Hybrid': '1'}},
    (('Make', ('Lexus', 'Toyota')),): {'Model': {'4Runner': '1', 'Avalon': '1', 'Camry': '4', 'Corolla': '2', 'ES': '3', 'GX': '1', 'IS': '1', 'LX': '1', 'NX': '1', 'RAV4': '1', 'RAV4 Hybrid': '2', 'RX': '3', 'Sequoia': '1', 'Sienna': '2', 'Tacoma': '2', 'Tundra': '2'}, 'Year': {'2024': '1', '2023': '1', '2022': '5', '2021': '4', '2020': '1', '2019': '1', '2018': '3', '2017': '2', '2016': '2', '2015': '2', '2014': '2', '2011': '1', '2010': '1', '2009': '1', '2007': '1'}, 'Exterior Color': {'Beige': '1', 'Black': '6', 'Blue': '2', 'Gray': '4', 'Obsidian': '1', 'Red': '6', 'Silver': '2', 'Sunset Bronze Mica': '1', 'White': '4'}, 'Interior Color': {'Ash': '2', 'Black': '8', 'Boulder': '1', 'Cement': '2', 'Creme': '1', 'Flaxen': '2', 'Moonstone': '1', 'Noble Brown': '1', 'Parchment': '4', 'Red': '1', 'Sand Beige': '1', 'Sepia': '1', 'Stone': '1'}, 'Highlights': {'AUX': '28', 'Bluetooth': '26', 'Climate Control': '25', 'Heated Mirrors': '22', 'Heated Seats': '11', 'Keyless Access': '26', 'Leather Seats': '10', 'Moon Roof': '11', 'Panoramic Roof': '1', 'Parking Sensors': '4', 'Rear Camera': '24', 'Rear Climate Control': '4', 'Rear Wiper': '13', 'Roof Rack': '9', 'Satellite Radio': '25', 'Sun Roof': '1', 'Tinted Glass': '27', 'USB': '17'}, 'Body Style': {'4D CrewMax': '2', '4D Double Cab': '2', '4D Passenger Van': '2', '4D Sedan': '11', '4D Sport Utility': '11'}, 'Engine': {'1.8 liters / 4 Cylinder': '2', '2.0 liters / 4 Cylinder': '2', '2.4 liters / 4 Cylinder': '1', '2.5 liters / 4 Cylinder': '7', '2.7 liters / 4 Cylinder': '1', '3.4 liters / V6 Cylinder': '2', '3.5 liters / V6 Cylinder': '9', '4.0 liters / V6 Cylinder': '1', '4.6 liters / 8 Cylinder': '1', '5.7 liters / 8 Cylinder': '2'}, 'Transmission': {'1-Speed CVT w/OD': '5', '10-Speed Automatic w/OD': '2', '5-Speed': '1', '5-Speed Automatic w/OD': '1', '6-Speed Automatic': '2', '6-Speed Automatic w/OD': '9', '8-Speed Automatic w/OD': '8'}, 'Drivetrain': {'4x4': '4', 'AWD': '3', 'FWD': '16', 'RWD': '5'}, 'Fuel Type': {'Gasoline': '26', 'Hybrid': '2'}},
    (('Make', ('Chevrolet',)),): {'Model': {'Camaro': '2', 'Captiva Sport': '1', 'Colorado': '3', 'Corvette': '2', 'Cruze': '7', 'Equinox': '3', 'Impala': '5', 'Impala Limited': '1', 'Malibu': '4', 'Silverado 1500': '7', 'Silverado 2500HD': '1', 'Tahoe': '2', 'Traverse': '1', 'Trax': '2'}, 'Year': {'2019': '2', '2018': '4', '2017': '20', '2016': '8', '2015': '4', '2014': '3'}, 'Exterior Color': {'Arctic Blue Metallic': '2', 'Black': '10', 'Blue': '4', 'Brown': '2', 'Gray': '4', 'Red': '4', 'Silver Ice Metallic': '8', 'White': '5', 'Yellow': '1'}, 'Interior Color': {'Black': '1', 'Cocoa Dune': '2', 'Dark Ash Jet Black': '2', 'Ebony': '2', 'Jet Black': '32', 'Jet Black Medium Titanium': '1'}, 'Highlights': {'AUX': '8', 'Bluetooth': '20', 'Climate Control': '19', 'Heated Mirrors': '35', 'Heated Seats': '10', 'Keyless Access': '41', 'Leather Seats': '14', 'Moon Roof': '4', 'Parking Sensors': '3', 'Rear Camera': '26', 'Rear Climate Control': '3', 'Rear Wiper': '10', 'Remote Start': '9', 'Roof Rack': '9', 'Satellite Radio': '39', 'Sun Roof': '4', 'Tinted Glass': '7', 'USB': '11'}, 'Body Style': {'2D Coupe': '4', '4D Crew Cab': '6', '4D Double Cab': '5', '4D Sedan': '17', '4D Sport Utility': '9'}, 'Engine': {'1.4 liters / 4 Cylinder': '8', '1.5 liters / 4 Cylinder': '5', '1.8 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '1', '2.4 liters / 4 Cylinder': '3', '3.6 liters / V6 Cylinder': '11', '5.3 liters / 8 Cylinder': '9', '6.0 liters / 8 Cylinder': '1', '6.2 liters / 8 Cylinder': '2'}, 'Transmission': {'Transmission, 6-speed automatic': '13', 'Transmission, 6-speed automatic (Included and only available with (LCV) 2.5L engine.)': '5', 'Transmission, 6-speed automatic (Included and only available with CR14526 FWD models.)': '1', 'Transmission, 6-speed automatic with overdrive': '2', 'Transmission, 6-speed automatic, HMD, 6L50': '1', 'Transmission, 6-speed automatic, electronically controlled with overdrive': '2', 'Transmission, 6-speed automatic, electronically controlled with overdrive and tow/haul mode': '2', 'Transmission, 6-speed automatic, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking': '5', 'Transmission, 6-speed automatic, electronically-controlled with overdrive includes Driver Shift Control': '1', 'Transmission, 6-speed automatic, heavy-duty, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking (Requires (L96) Vortec 6.0L V8 SFI engine or (LC8) 6.0L V8 SFI Gaseous engine.)': '1', 'Transmission, 7-speed manual with Active Rev Matching': '2', 'Transmission, 8-speed automatic (Requires (LGZ) 3.6L DI DOHC V6 engine. Standard on Crew Cab models. Available on Extended Cab models.)': '1', 'Transmission, 8-speed automatic (Requires (LGZ) 3.6L DI DOHC V6 engine.)': '1', 'Transmission, 8-speed automatic includes transmission oil cooler and (BTV) remote vehicle starter system (When ordered with (LGX) 3.6L V6 engine, also includes Active Fuel Management. Included with (PCL) 1LT Preferred Package with 20" Black Wheels and Auto Trans.)': '2', 'Transmission, 8-speed automatic, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking': '1', 'Transmission, 8-speed automatic, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking (Standard on Crew Cab models.)': '1'}, 'Drivetrain': {'4x4': '12', 'AWD': '3', 'FWD': '21', 'RWD': '5'}, 'Fuel Type': {'Flex Fuel Capability': '1', 'Gasoline': '3', 'Gasoline Fuel': '37'}},
    (('Make', ('Kia', 'Subaru')),): {'Model': {'Crosstrek': '2', 'Forester': '2', 'Forte': '2', 'Impreza': '1', 'Legacy': '1', 'Optima': '5', 'Outback': '2', 'Rio': '1', 'Sorento': '9', 'Soul': '3', 'Sportage': '4'}, 'Year': {'2018': '4', '2017': '4', '2016': '20', '2015': '3', '2014': '1'}, 'Exterior Color': {'Black': '6', 'Blue': '4', 'Brown': '1', 'Gray': '9', 'Purple': '1', 'Red': '3', 'Silver': '1', 'White': '6', 'Yellow': '1'}, 'Interior Color': {'Beige': '1', 'Black': '24', 'Dark Gray': '1', 'Gray': '3', 'Ivory': '1', 'Slate Black': '1', 'Warm Ivory': '1'}, 'Highlights': {'Bluetooth': '6', 'Climate Control': '11', 'Heated Mirrors': '20', 'Heated Seats': '17', 'Keyless Access': '31', 'Leather Seats': '3', 'Moon Roof': '10', 'Navigation': '1', 'Parking Sensors': '7', 'Rear Camera': '26', 'Rear Climate Control': '5', 'Rear Wiper': '22', 'Remote Start': '3', 'Roof Rack': '16', 'Satellite Radio': '32', 'Sun Roof': '6', 'USB': '1'}, 'Body Style': {'4D Hatchback': '3', '4D Sedan': '10', '4D Sport Utility': '19'}, 'Engine': {'1.6 liters / 4 Cylinder': '3', '2.0 liters / 4 Cylinder': '5', '2.4 liters / 4 Cylinder': '10', '3.3 liters / V6 Cylinder': '6', '4 2': '3', '4 2.5': '5'}, 'Transmission': {'6-Speed Automatic': '9', '6-Speed Automatic w/Sportmatic': '14', '6-Speed Manual': '1', 'CVT Lineartronic': '4', 'Lineartronic CVT': '4'}, 'Drivetrain': {'AWD': '16', 'FWD': '16'}, 'Fuel Type': {'Gasoline': '8', 'Gasoline Fuel': '24'}},
    (("Model", ("Accord Hybrid",)),): {'Trim': {'EX-L': '21', 'Sport': '15', 'Sport-L': '23', 'Touring': '17'}, 'Year': {'2025': '25', '2024': '51'}, 'Exterior Color': {'Crystal Black Pearl': '8', 'Meteorite Gray Metallic': '9', 'Platinum White Pearl': '25', 'Radiant Red Metallic': '7', 'Solar Silver Metallic': '12', 'Still Night Pearl': '3', 'Urban Gray Pearl': '12'}, 'Interior Color': {'Black': '70', 'Gray': '6'}, 'Highlights': {'AUX': '76', 'Bluetooth': '76', 'Climate Control': '76', 'Heated Mirrors': '55', 'Heated Seats': '70', 'Keyless Access': '76', 'Leather Seats': '61', 'Moon Roof': '76', 'Parking Sensors': '38', 'Rear Camera': '76', 'Remote Start': '62', 'Tinted Glass': '76'}, 'Body Style': {'4D Sedan': '76'}, 'Engine': {'2.0 liters / 4 Cylinder': '76'}, 'Transmission': {'1-Speed CVT w/OD': '76'}, 'Drivetrain': {'FWD': '76'}, 'Fuel Type': {'Hybrid': '76'}},
    (('Model', ("Accord", "HR-V")),): {'Trim': {'EX': '1', 'EX-L': '14', 'LX': '33', 'SE': '17', 'Sport': '8'}, 'Year': {'2025': '63', '2024': '10'}, 'Exterior Color': {'Canyon River Blue Metallic': '2', 'Crystal Black Pearl': '5', 'Lunar Silver Metallic': '3', 'Meteorite Gray Metallic': '7', 'Milano Red': '3', 'Modern Steel Metallic': '5', 'Nordic Forest Pearl': '7', 'Platinum White Pearl': '23', 'Radiant Red Metallic': '6', 'Solar Silver Metallic': '7', 'Urban Gray Pearl': '5'}, 'Interior Color': {'Black': '44', 'Gray': '29'}, 'Highlights': {'AUX': '73', 'Bluetooth': '73', 'Climate Control': '73', 'Heated Mirrors': '39', 'Heated Seats': '40', 'Keyless Access': '73', 'Leather Seats': '14', 'Moon Roof': '32', 'Parking Sensors': '14', 'Rear Camera': '73', 'Rear Wiper': '35', 'Remote Start': '31', 'Satellite Radio': '14', 'Tinted Glass': '73'}, 'Body Style': {'4D Sedan': '38', '4D Sport Utility': '35'}, 'Engine': {'1.5 liters / 4 Cylinder': '38', '2.0 liters / 4 Cylinder': '35'}, 'Transmission': {'1-Speed CVT w/OD': '73'}, 'Drivetrain': {'AWD': '4', 'FWD': '69'}, 'Fuel Type': {'Gasoline': '73'}},
    (('Model', ('CR-V',)),): {'Trim': {'EX': '1', 'EX-L': '3', 'LX': '3', 'Special Edition': '1'}, 'Year': {'2024': '1', '2021': '1', '2019': '1', '2018': '2', '2015': '1', '2014': '1', '2012': '1'}, 'Exterior Color': {'Black': '3', 'Blue': '1', 'Gray': '1', 'Red': '1', 'Silver': '1', 'White': '1'}, 'Interior Color': {'Black': '5', 'Gray': '3'}, 'Highlights': {'AUX': '8', 'Bluetooth': '8', 'Climate Control': '6', 'Heated Mirrors': '4', 'Heated Seats': '5', 'Keyless Access': '8', 'Leather Seats': '3', 'Moon Roof': '4', 'Rear Camera': '8', 'Rear Wiper': '8', 'Remote Start': '4', 'Roof Rack': '1', 'Satellite Radio': '4', 'Tinted Glass': '8', 'USB': '7'}, 'Body Style': {'4D Sport Utility': '8'}, 'Engine': {'1.5 liters / 4 Cylinder': '4', '2.4 liters / 4 Cylinder': '4'}, 'Transmission': {'1-Speed CVT w/OD': '6', '5-Speed Automatic': '1', '5-Speed Automatic w/OD': '1'}, 'Drivetrain': {'AWD': '4', 'FWD': '4'}, 'Fuel Type': {'Gasoline': '8'}},
    (('Model', ('CR-V', 'Wrangler')),): {'Make': {'Honda': '8', 'Jeep': '4'}, 'Trim': {'EX': '1', 'EX-L': '3', 'LX': '3', 'Special Edition': '1', 'Unlimited Rubicon': '1', 'Unlimited Sport': '2', 'Unlimited Sport S': '1'}, 'Year': {'2024': '1', '2021': '1', '2020': '1', '2019': '1', '2018': '3', '2016': '1', '2015': '1', '2014': '1', '2012': '2'}, 'Exterior Color': {'Black': '3', 'Blue': '1', 'Gray': '2', 'Red': '2', 'Silver': '1', 'White': '2', 'Yellow': '1'}, 'Interior Color': {'Black': '9', 'Gray': '3'}, 'Highlights': {'AUX': '12', 'Bluetooth': '12', 'Climate Control': '7', 'Heated Mirrors': '8', 'Heated Seats': '7', 'Keyless Access': '12', 'Leather Seats': '3', 'Moon Roof': '4', 'Navigation': '1', 'Rear Camera': '10', 'Rear Wiper': '12', 'Remote Start': '5', 'Roof Rack': '1', 'Satellite Radio': '8', 'Tinted Glass': '12', 'USB': '10'}, 'Body Style': {'4D Sport Utility': '12'}, 'Engine': {'1.5 liters / 4 Cylinder': '4', '2.4 liters / 4 Cylinder': '4', '3.6 liters / V6 Cylinder': '4'}, 'Transmission': {'1-Speed CVT w/OD': '6', '5-Speed Automatic': '2', '5-Speed Automatic w/OD': '2', '8-Speed Automatic w/OD': '2'}, 'Drivetrain': {'4x4': '4', 'AWD': '4', 'FWD': '4'}, 'Fuel Type': {'Gasoline': '12'}},
    (('Model', ('Civic',)),): {'Trim': {'LX': '1', 'Sport': '2'}, 'Year': {'2023': '1', '2022': '2'}, 'Exterior Color': {'Black': '3'}, 'Interior Color': {'Black': '3'}, 'Highlights': {'AUX': '3', 'Bluetooth': '3', 'Climate Control': '3', 'Keyless Access': '3', 'Rear Camera': '3', 'Rear Wiper': '1', 'Remote Start': '2', 'Satellite Radio': '3', 'Tinted Glass': '3', 'USB': '3'}, 'Body Style': {'4D Hatchback': '1', '4D Sedan': '2'}, 'Engine': {'2.0 liters / 4 Cylinder': '3'}, 'Transmission': {'1-Speed CVT w/OD': '3'}, 'Drivetrain': {'FWD': '3'}, 'Fuel Type': {'Gasoline': '3'}},
    (('Model', ('Odyssey', 'Civic')),): {'Trim': {'EX-L': '1', 'LX': '1', 'Sport': '2', 'Touring': '1'}, 'Year': {'2023': '1', '2022': '3', '2019': '1'}, 'Exterior Color': {'Black': '3', 'Gray': '1', 'Red': '1'}, 'Interior Color': {'Beige': '1', 'Black': '4'}, 'Highlights': {'AUX': '5', 'Bluetooth': '5', 'Climate Control': '5', 'Heated Mirrors': '2', 'Heated Seats': '2', 'Keyless Access': '5', 'Leather Seats': '2', 'Moon Roof': '2', 'Parking Sensors': '1', 'Rear Camera': '5', 'Rear Climate Control': '2', 'Rear Wiper': '3', 'Remote Start': '3', 'Satellite Radio': '5', 'Sun Roof': '1', 'Tinted Glass': '5', 'USB': '5'}, 'Body Style': {'4D Hatchback': '1', '4D Passenger Van': '2', '4D Sedan': '2'}, 'Engine': {'2.0 liters / 4 Cylinder': '3', '3.5 liters / V6 Cylinder': '2'}, 'Transmission': {'1-Speed CVT w/OD': '3', '10-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '1'}, 'Drivetrain': {'FWD': '5'}, 'Fuel Type': {'Gasoline': '5'}},
    (('Model', ('Pilot',)),): {'Trim': {'Black Edition': '6', 'EX-L': '39', 'Elite': '13', 'Sport': '4', 'Touring': '27', 'Touring+': '1', 'TrailSport': '27'}, 'Year': {'2025': '116', '2024': '1'}, 'Exterior Color': {'Crystal Black Pearl': '21', 'Diffused Sky Pearl': '8', 'Lunar Silver Metallic': '11', 'Modern Steel Metallic': '10', 'Obsidian Blue Pearl': '15', 'Platinum White Pearl': '37', 'Radiant Red Metallic Ii': '11', 'Sonic Gray Pearl': '4'}, 'Interior Color': {'Black': '81', 'Brown': '2', 'Gray': '34'}, 'Highlights': {'AUX': '117', 'Bluetooth': '117', 'Climate Control': '117', 'Heated Mirrors': '117', 'Heated Seats': '117', 'Keyless Access': '117', 'Leather Seats': '86', 'Moon Roof': '74', 'Panoramic Roof': '74', 'Parking Sensors': '113', 'Rear Camera': '117', 'Rear Climate Control': '117', 'Rear Wiper': '117', 'Remote Start': '83', 'Roof Rack': '78', 'Satellite Radio': '113', 'Third Row Seats': '36', 'Tinted Glass': '117', 'USB': '117'}, 'Body Style': {'4D Sport Utility': '117'}, 'Engine': {'3.5 liters / V6 Cylinder': '117'}, 'Transmission': {'10-Speed Automatic w/OD': '117'}, 'Drivetrain': {'AWD': '84', 'FWD': '33'}, 'Fuel Type': {'Gasoline': '117'}},
    (("Year", ("2025",)),): {'Model': {'Accord': '28', 'Accord Hybrid': '25', 'CR-V': '86', 'CR-V Hybrid': '104', 'Civic': '48', 'HR-V': '35', 'Odyssey': '61', 'Passport': '17', 'Pilot': '116', 'Ridgeline': '22'}, 'Exterior Color': {'Black': '88', 'Blue': '82', 'Brown': '1', 'Gray': '119', 'Green': '7', 'Red': '58', 'Silver': '59', 'White': '128'}, 'Interior Color': {'Black': '422', 'Brown': '8', 'Gray': '109', 'Red': '3'}, 'Highlights': {'AUX': '542', 'Bluetooth': '542', 'Climate Control': '542', 'Heated Mirrors': '468', 'Heated Seats': '460', 'Keyless Access': '542', 'Leather Seats': '334', 'Moon Roof': '409', 'Panoramic Roof': '73', 'Parking Sensors': '255', 'Rear Camera': '542', 'Rear Climate Control': '216', 'Rear Wiper': '439', 'Remote Start': '401', 'Roof Rack': '193', 'Satellite Radio': '318', 'Third Row Seats': '56', 'Tinted Glass': '542', 'USB': '177'}, 'Body Style': {'4D Crew Cab': '22', '4D Hatchback': '20', '4D Passenger Van': '61', '4D Sedan': '81', '4D Sport Utility': '358'}, 'Engine': {'1.5 liters / 4 Cylinder': '117', '2.0 liters / 4 Cylinder': '209', '3.5 liters / V6 Cylinder': '216'}, 'Transmission': {'1-Speed CVT w/OD': '323', '10-Speed Automatic w/OD': '177', '6-Speed Manual w/OD': '3', '9-Speed Automatic w/OD': '39'}, 'Drivetrain': {'AWD': '210', 'FWD': '332'}, 'Fuel Type': {'Gasoline': '413', 'Hybrid': '129'}},
    (('Year', ('2024', '2025')),): {'Model': {'Accord': '38', 'Accord Hybrid': '76', 'CR-V': '87', 'CR-V Hybrid': '104', 'Civic': '50', 'HR-V': '35', 'Odyssey': '62', 'Passport': '21', 'Pilot': '117', 'Prologue': '72', 'Ridgeline': '36'}, 'Exterior Color': {'Black': '106', 'Blue': '109', 'Brown': '1', 'Gray': '147', 'Green': '7', 'Red': '82', 'Silver': '74', 'White': '172'}, 'Interior Color': {'Black': '542', 'Brown': '13', 'Gray': '140', 'Red': '3'}, 'Highlights': {'AUX': '698', 'Bluetooth': '698', 'Climate Control': '698', 'Heated Mirrors': '606', 'Heated Seats': '599', 'Keyless Access': '698', 'Leather Seats': '453', 'Moon Roof': '536', 'Panoramic Roof': '128', 'Parking Sensors': '366', 'Rear Camera': '698', 'Rear Climate Control': '236', 'Rear Wiper': '520', 'Remote Start': '548', 'Roof Rack': '198', 'Satellite Radio': '410', 'Third Row Seats': '56', 'Tinted Glass': '698', 'USB': '181'}, 'Body Style': {'4D Crew Cab': '36', '4D Hatchback': '22', '4D Passenger Van': '62', '4D Sedan': '142', '4D Sport Utility': '436'}, 'Engine': {'1.5 liters / 4 Cylinder': '129', '2.0 liters / 4 Cylinder': '261', '3.5 liters / V6 Cylinder': '236', 'Electric Motor': '72'}, 'Transmission': {'1-Speed Automatic': '72', '1-Speed CVT w/OD': '387', '10-Speed Automatic w/OD': '179', '6-Speed Manual w/OD': '3', '9-Speed Automatic w/OD': '57'}, 'Drivetrain': {'AWD': '279', 'FWD': '419'}, 'Fuel Type': {'Electric': '72', 'Gasoline': '446', 'Hybrid': '180'}},
    (('Year', ('2022',)),): {'Make': {'BMW': '1', 'Dodge': '1', 'Honda': '11', 'Tesla': '1', 'Toyota': '5'}, 'Model': {'3 Series': '1', 'Accord': '2', 'Challenger': '1', 'Civic': '3', 'Corolla': '1', 'Insight': '1', 'Model Y': '1', 'Odyssey': '1', 'Pilot': '4', 'RAV4 Hybrid': '2', 'Tacoma': '1', 'Tundra': '1'}, 'Exterior Color': {'Black': '5', 'Blue': '1', 'Gray': '3', 'Red': '1', 'Silver': '2', 'White': '5'}, 'Interior Color': {'Black': '13', 'Boulder': '1', 'Cement': '1', 'Ivory': '1', 'Moonstone': '1'}, 'Highlights': {'AUX': '19', 'Bluetooth': '19', 'Climate Control': '19', 'Heated Mirrors': '13', 'Heated Seats': '8', 'Keyless Access': '19', 'Leather Seats': '4', 'Moon Roof': '6', 'Panoramic Roof': '1', 'Parking Sensors': '3', 'Rear Camera': '19', 'Rear Climate Control': '6', 'Rear Wiper': '8', 'Remote Start': '8', 'Roof Rack': '6', 'Satellite Radio': '15', 'Sun Roof': '1', 'Tinted Glass': '19', 'USB': '17'}, 'Body Style': {'2D Coupe': '1', '4D CrewMax': '1', '4D Double Cab': '1', '4D Hatchback': '1', '4D Passenger Van': '1', '4D Sedan': '7', '4D Sport Utility': '7'}, 'Engine': {'1.5 liters / 4 Cylinder': '3', '1.8 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '3', '2.5 liters / 4 Cylinder': '2', '3.0 liters / Straight 6 Cylinder': '1', '3.4 liters / V6 Cylinder': '1', '3.5 liters / V6 Cylinder': '6', '6.4 liters / 8 Cylinder': '1', 'Electric Motor': '1'}, 'Transmission': {'1-Speed Automatic': '1', '1-Speed CVT w/OD': '9', '10-Speed Automatic w/OD': '2', '6-Speed Automatic w/OD': '1', '8-Speed Automatic w/OD': '2', '9-Speed Automatic w/OD': '4'}, 'Drivetrain': {'4x4': '1', 'AWD': '3', 'FWD': '12', 'RWD': '3'}, 'Fuel Type': {'Electric': '1', 'Gasoline': '16', 'Hybrid': '2'}},
    (('Year', ('2017', '2018')),): {'Make': {'Audi': '1', 'Buick': '2', 'Cadillac': '2', 'Chevrolet': '2', 'Ford': '5', 'GMC': '1', 'Honda': '9', 'INFINITI': '1', 'Jeep': '3', 'Kia': '1', 'Lexus': '4', 'Nissan': '1', 'Toyota': '1'}, 'Model': {'Accord': '2', 'C-Max Energi': '1', 'CR-V': '2', 'Camry': '1', 'Civic': '2', 'Enclave': '2', 'Escalade': '2', 'Expedition': '1', 'Explorer': '2', 'IS': '1', 'LX': '1', 'Mustang': '1', 'New Compass': '1', 'Pathfinder': '1', 'Pilot': '2', 'Q3': '1', 'QX80': '1', 'RX': '2', 'Renegade': '1', 'Ridgeline': '1', 'Sierra 1500': '1', 'Silverado 1500': '1', 'Sportage': '1', 'Tahoe': '1', 'Wrangler': '1'}, 'Exterior Color': {'Black': '11', 'Blue': '3', 'Brown': '1', 'Gray': '5', 'Orange': '1', 'Red': '2', 'Silver': '5', 'White': '5'}, 'Interior Color': {'Beige': '1', 'Black': '11', 'Charcoal': '1', 'Chestnut Brown': '1', 'Cocoa Accents': '1', 'Dune': '1', 'Ebony': '2', 'Ebony Accents': '2', 'Ebony Black': '1', 'Flaxen': '1', 'Gray': '2', 'Ivory': '1', 'Jet Black': '3', 'Medium Light Stone': '1', 'Medium Stone': '1', 'Parchment': '1', 'Wheat': '1'}, 'Highlights': {'AUX': '33', 'Bluetooth': '33', 'Climate Control': '27', 'Heated Mirrors': '24', 'Heated Seats': '17', 'Keyless Access': '33', 'Leather Seats': '13', 'Moon Roof': '13', 'Navigation': '1', 'Panoramic Roof': '1', 'Parking Sensors': '8', 'Rear Camera': '33', 'Rear Climate Control': '14', 'Rear Wiper': '23', 'Remote Start': '14', 'Roof Rack': '14', 'Satellite Radio': '27', 'Sun Roof': '7', 'Third Row Seats': '1', 'Tinted Glass': '33', 'USB': '25', 'Xenon Headlights': '1'}, 'Body Style': {'2D Coupe': '1', '4D Crew Cab': '3', '4D Hatchback': '1', '4D Sedan': '6', '4D Sport Utility': '22'}, 'Engine': {'1.5 liters / 4 Cylinder': '4', '2.0 liters / 4 Cylinder': '5', '2.3 liters / 4 Cylinder': '1', '2.4 liters / 4 Cylinder': '3', '2.5 liters / 4 Cylinder': '1', '3.5 liters / V6 Cylinder': '8', '3.6 liters / V6 Cylinder': '3', '5.0 liters / 8 Cylinder': '1', '5.3 liters / 8 Cylinder': '3', '5.6 liters / 8 Cylinder': '1', '5.7 liters / 8 Cylinder': '1', '6.2 liters / 8 Cylinder': '2'}, 'Transmission': {'1-Speed CVT w/OD': '8', '6-Speed Automatic': '3', '6-Speed Automatic w/OD': '8', '6-Speed Manual': '1', '7-Speed Automatic w/OD': '1', '8-Speed Automatic': '2', '8-Speed Automatic w/OD': '6', '9-Speed Automatic': '2', '9-Speed Automatic w/OD': '2'}, 'Drivetrain': {'4x4': '5', 'AWD': '3', 'FWD': '17', 'FrontTrak': '1', 'RWD': '7'}, 'Fuel Type': {'Gasoline': '32', 'Hybrid': '1'}},
    (('Year', ('2020',)),): {'Model': {'Accord': '1', 'Pilot': '1'}, 'Exterior Color': {'Gray': '1', 'Silver': '1'}, 'Interior Color': {'Gray': '2'}, 'Highlights': {'AUX': '2', 'Bluetooth': '2', 'Climate Control': '2', 'Heated Mirrors': '2', 'Heated Seats': '2', 'Keyless Access': '2', 'Leather Seats': '1', 'Moon Roof': '1', 'Parking Sensors': '1', 'Rear Camera': '2', 'Rear Climate Control': '1', 'Rear Wiper': '1', 'Remote Start': '2', 'Satellite Radio': '2', 'Sun Roof': '1', 'Tinted Glass': '2', 'USB': '2'}, 'Body Style': {'4D Sedan': '1', '4D Sport Utility': '1'}, 'Engine': {'2.0 liters / 4 Cylinder': '1', '3.5 liters / V6 Cylinder': '1'}, 'Transmission': {'10-Speed Automatic w/OD': '1', '6-Speed Automatic w/OD': '1'}, 'Drivetrain': {'FWD': '2'}, 'Fuel Type': {'Gasoline': '2'}},
    (('Year', ('2020', '2022')),): {'Model': {'Accord': '1', 'Civic': '2', 'Odyssey': '1', 'Pilot': '3'}, 'Exterior Color': {'Black': '4', 'Gray': '2', 'Silver': '1'}, 'Interior Color': {'Black': '5', 'Gray': '2'}, 'Highlights': {'AUX': '7', 'Bluetooth': '7', 'Climate Control': '7', 'Heated Mirrors': '5', 'Heated Seats': '5', 'Keyless Access': '7', 'Leather Seats': '3', 'Moon Roof': '3', 'Parking Sensors': '2', 'Rear Camera': '7', 'Rear Climate Control': '4', 'Rear Wiper': '5', 'Remote Start': '5', 'Roof Rack': '2', 'Satellite Radio': '7', 'Sun Roof': '1', 'Tinted Glass': '7', 'USB': '7'}, 'Body Style': {'4D Hatchback': '1', '4D Passenger Van': '1', '4D Sedan': '2', '4D Sport Utility': '3'}, 'Engine': {'2.0 liters / 4 Cylinder': '3', '3.5 liters / V6 Cylinder': '4'}, 'Transmission': {'1-Speed CVT w/OD': '2', '10-Speed Automatic w/OD': '2', '6-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '2'}, 'Drivetrain': {'FWD': '7'}, 'Fuel Type': {'Gasoline': '7'}},
    (('Year', ('2024',)),): {'Model': {'Accord': '10', 'Accord Hybrid': '51', 'CR-V': '1', 'Civic': '2', 'Odyssey': '1', 'Passport': '4', 'Pilot': '1', 'Prologue': '72', 'Ridgeline': '14'}, 'Exterior Color': {'Black': '18', 'Blue': '27', 'Gray': '28', 'Red': '24', 'Silver': '15', 'White': '44'}, 'Interior Color': {'Black': '120', 'Brown': '5', 'Gray': '31'}, 'Highlights': {'AUX': '156', 'Bluetooth': '156', 'Climate Control': '156', 'Heated Mirrors': '138', 'Heated Seats': '139', 'Keyless Access': '156', 'Leather Seats': '119', 'Moon Roof': '127', 'Panoramic Roof': '55', 'Parking Sensors': '111', 'Rear Camera': '156', 'Rear Climate Control': '20', 'Rear Wiper': '81', 'Remote Start': '147', 'Roof Rack': '5', 'Satellite Radio': '92', 'Tinted Glass': '156', 'USB': '4'}, 'Body Style': {'4D Crew Cab': '14', '4D Hatchback': '2', '4D Passenger Van': '1', '4D Sedan': '61', '4D Sport Utility': '78'}, 'Engine': {'1.5 liters / 4 Cylinder': '12', '2.0 liters / 4 Cylinder': '52', '3.5 liters / V6 Cylinder': '20', 'Electric Motor': '72'}, 'Transmission': {'1-Speed Automatic': '72', '1-Speed CVT w/OD': '64', '10-Speed Automatic w/OD': '2', '9-Speed Automatic w/OD': '18'}, 'Drivetrain': {'AWD': '69', 'FWD': '87'}, 'Fuel Type': {'Electric': '72', 'Gasoline': '33', 'Hybrid': '51'}},
    (("Exterior Color", ("Red",)),): {'Model': {'Accord': '6', 'Accord Hybrid': '7', 'CR-V': '8', 'CR-V Hybrid': '13', 'Civic': '9', 'HR-V': '3', 'Odyssey': '7', 'Passport': '2', 'Pilot': '11', 'Prologue': '11', 'Ridgeline': '5'}, 'Year': {'2025': '58', '2024': '24'}, 'Interior Color': {'Black': '59', 'Brown': '1', 'Gray': '22'}, 'Highlights': {'AUX': '82', 'Bluetooth': '82', 'Climate Control': '82', 'Heated Mirrors': '70', 'Heated Seats': '69', 'Keyless Access': '82', 'Leather Seats': '47', 'Moon Roof': '60', 'Panoramic Roof': '15', 'Parking Sensors': '40', 'Rear Camera': '82', 'Rear Climate Control': '25', 'Rear Wiper': '58', 'Remote Start': '66', 'Roof Rack': '24', 'Satellite Radio': '42', 'Third Row Seats': '6', 'Tinted Glass': '82', 'USB': '19'}, 'Body Style': {'4D Crew Cab': '5', '4D Hatchback': '3', '4D Passenger Van': '7', '4D Sedan': '19', '4D Sport Utility': '48'}, 'Engine': {'1.5 liters / 4 Cylinder': '15', '2.0 liters / 4 Cylinder': '31', '3.5 liters / V6 Cylinder': '25', 'Electric Motor': '11'}, 'Transmission': {'1-Speed Automatic': '11', '1-Speed CVT w/OD': '46', '10-Speed Automatic w/OD': '18', '9-Speed Automatic w/OD': '7'}, 'Drivetrain': {'AWD': '29', 'FWD': '53'}, 'Fuel Type': {'Electric': '11', 'Gasoline': '51', 'Hybrid': '20'}},
    (('Exterior Color', ('Blue', 'Silver')),): {'Model': {'Accord': '9', 'Accord Hybrid': '15', 'CR-V': '22', 'CR-V Hybrid': '40', 'Civic': '12', 'HR-V': '3', 'Odyssey': '11', 'Passport': '5', 'Pilot': '34', 'Prologue': '30', 'Ridgeline': '2'}, 'Year': {'2025': '141', '2024': '42'}, 'Interior Color': {'Black': '145', 'Gray': '38'}, 'Highlights': {'AUX': '183', 'Bluetooth': '183', 'Climate Control': '183', 'Heated Mirrors': '160', 'Heated Seats': '160', 'Keyless Access': '183', 'Leather Seats': '109', 'Moon Roof': '142', 'Panoramic Roof': '45', 'Parking Sensors': '93', 'Rear Camera': '183', 'Rear Climate Control': '52', 'Rear Wiper': '152', 'Remote Start': '151', 'Roof Rack': '65', 'Satellite Radio': '109', 'Third Row Seats': '15', 'Tinted Glass': '183', 'USB': '45'}, 'Body Style': {'4D Crew Cab': '2', '4D Hatchback': '7', '4D Passenger Van': '11', '4D Sedan': '29', '4D Sport Utility': '134'}, 'Engine': {'1.5 liters / 4 Cylinder': '31', '2.0 liters / 4 Cylinder': '70', '3.5 liters / V6 Cylinder': '52', 'Electric Motor': '30'}, 'Transmission': {'1-Speed Automatic': '30', '1-Speed CVT w/OD': '101', '10-Speed Automatic w/OD': '45', '9-Speed Automatic w/OD': '7'}, 'Drivetrain': {'AWD': '78', 'FWD': '105'}, 'Fuel Type': {'Electric': '30', 'Gasoline': '98', 'Hybrid': '55'}},
    (('Exterior Color', ('Silver',)),): {'Make': {'Cadillac': '2', 'Ford': '1', 'Honda': '6', 'Hyundai': '1', 'Jeep': '1', 'Mercedes-Benz': '1', 'Toyota': '2'}, 'Model': {'Accord': '3', 'C-Max Energi': '1', 'CR-V': '1', 'Corolla': '1', 'EQS': '1', 'Escalade': '1', 'New Compass': '1', 'Pilot': '2', 'RAV4 Hybrid': '1', 'Sonata': '1', 'XT6': '1'}, 'Year': {'2023': '1', '2022': '2', '2021': '2', '2020': '2', '2019': '2', '2018': '1', '2017': '4'}, 'Interior Color': {'Black': '7', 'Gray': '3', 'Jet Black': '2', 'Medium Light Stone': '1'}, 'Highlights': {'AUX': '14', 'Bluetooth': '14', 'Climate Control': '13', 'Heated Mirrors': '9', 'Heated Seats': '8', 'Keyless Access': '14', 'Leather Seats': '7', 'Moon Roof': '7', 'Panoramic Roof': '2', 'Parking Sensors': '2', 'Rear Camera': '14', 'Rear Climate Control': '4', 'Rear Wiper': '9', 'Remote Start': '6', 'Roof Rack': '4', 'Satellite Radio': '11', 'Sun Roof': '5', 'Tinted Glass': '14', 'USB': '11'}, 'Body Style': {'4D Hatchback': '1', '4D Sedan': '5', '4D Sport Utility': '8'}, 'Engine': {'1.5 liters / 4 Cylinder': '3', '1.8 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '2', '2.4 liters / 4 Cylinder': '2', '2.5 liters / 4 Cylinder': '1', '3.5 liters / V6 Cylinder': '2', '3.6 liters / V6 Cylinder': '1', '6.2 liters / 8 Cylinder': '1', 'Electric Motor': '1'}, 'Transmission': {'1-Speed Automatic': '1', '1-Speed CVT w/OD': '6', '10-Speed Automatic w/OD': '1', '6-Speed Automatic w/OD': '3', '8-Speed Automatic': '1', '9-Speed Automatic': '1', '9-Speed Automatic w/OD': '1'}, 'Drivetrain': {'4x4': '1', 'AWD': '2', 'FWD': '9', 'RWD': '2'}, 'Fuel Type': {'Electric': '1', 'Gasoline': '11', 'Hybrid': '2'}},
    (('Exterior Color', ('Blue', 'Red')),): {'Make': {'Audi': '1', 'BMW': '1', 'Cadillac': '1', 'Chevrolet': '2', 'Ford': '2', 'Honda': '7', 'Jeep': '1', 'Lexus': '2', 'Mazda': '2', 'Toyota': '6'}, 'Model': {'Accord': '1', 'CR-V': '2', 'CX-5': '1', 'Camry': '3', 'Civic': '1', 'Cruze': '1', 'ES': '1', 'Expedition Max': '1', 'Explorer': '1', 'Mazda6': '1', 'Odyssey': '1', 'Pilot': '1', 'Q3': '1', 'RAV4': '1', 'RX': '1', 'Ridgeline': '1', 'SRX': '1', 'Tahoe': '1', 'Tundra': '2', 'Wrangler': '1', 'X3': '1'}, 'Year': {'2024': '1', '2023': '2', '2022': '2', '2021': '3', '2020': '1', '2019': '2', '2018': '4', '2017': '1', '2016': '4', '2015': '2', '2014': '3'}, 'Interior Color': {'Almond': '1', 'Ash': '1', 'Beige': '1', 'Black': '10', 'Boulder': '1', 'Brownstone Accents': '1', 'Chestnut Brown': '1', 'Ebony': '1', 'Gray': '3', 'Jet Black': '1', 'Medium Light Camel': '1', 'Medium Titanium': '1', 'Parchment': '1', 'Red': '1'}, 'Highlights': {'AUX': '25', 'Bluetooth': '25', 'Climate Control': '21', 'Heated Mirrors': '18', 'Heated Seats': '15', 'Keyless Access': '25', 'Leather Seats': '12', 'Moon Roof': '12', 'Panoramic Roof': '3', 'Parking Sensors': '5', 'Rear Camera': '21', 'Rear Climate Control': '6', 'Rear Wiper': '14', 'Remote Start': '8', 'Roof Rack': '7', 'Satellite Radio': '22', 'Sun Roof': '6', 'Third Row Seats': '1', 'Tinted Glass': '23', 'USB': '19', 'Xenon Headlights': '2'}, 'Body Style': {'4D Crew Cab': '1', '4D CrewMax': '2', '4D Passenger Van': '1', '4D Sedan': '8', '4D Sport Utility': '13'}, 'Engine': {'1.5 liters / 4 Cylinder': '2', '1.8 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '3', '2.4 liters / 4 Cylinder': '1', '2.5 liters / 4 Cylinder': '6', '3.4 liters / V6 Cylinder': '2', '3.5 liters / V6 Cylinder': '7', '3.6 liters / V6 Cylinder': '2', '6.2 liters / 8 Cylinder': '1'}, 'Transmission': {'1-Speed CVT w/OD': '4', '10-Speed Automatic': '1', '10-Speed Automatic w/OD': '3', '5-Speed Automatic w/OD': '1', '6-Speed Automatic': '2', '6-Speed Automatic w/OD': '7', '8-Speed Automatic w/OD': '5', '9-Speed Automatic w/OD': '2'}, 'Drivetrain': {'4x4': '3', 'AWD': '4', 'FWD': '15', 'FrontTrak': '1', 'RWD': '2'}, 'Fuel Type': {'Gasoline': '25'}},
    (('Exterior Color', ('Black',)),): {'Model': {'Civic': '3', 'Pilot': '2'}, 'Year': {'2023': '1', '2022': '4'}, 'Interior Color': {'Black': '5'}, 'Highlights': {'AUX': '5', 'Bluetooth': '5', 'Climate Control': '5', 'Heated Mirrors': '2', 'Heated Seats': '2', 'Keyless Access': '5', 'Leather Seats': '1', 'Moon Roof': '1', 'Rear Camera': '5', 'Rear Climate Control': '2', 'Rear Wiper': '3', 'Remote Start': '4', 'Roof Rack': '2', 'Satellite Radio': '5', 'Tinted Glass': '5', 'USB': '5'}, 'Body Style': {'4D Hatchback': '1', '4D Sedan': '2', '4D Sport Utility': '2'}, 'Engine': {'2.0 liters / 4 Cylinder': '3', '3.5 liters / V6 Cylinder': '2'}, 'Transmission': {'1-Speed CVT w/OD': '3', '9-Speed Automatic w/OD': '2'}, 'Drivetrain': {'FWD': '5'}, 'Fuel Type': {'Gasoline': '5'}},
    (('Exterior Color', ('Black', 'Gray')),): {'Model': {'Accord': '1', 'Civic': '3', 'Odyssey': '1', 'Pilot': '2'}, 'Year': {'2023': '1', '2022': '5', '2020': '1'}, 'Interior Color': {'Black': '6', 'Gray': '1'}, 'Highlights': {'AUX': '7', 'Bluetooth': '7', 'Climate Control': '7', 'Heated Mirrors': '4', 'Heated Seats': '4', 'Keyless Access': '7', 'Leather Seats': '3', 'Moon Roof': '3', 'Parking Sensors': '2', 'Rear Camera': '7', 'Rear Climate Control': '3', 'Rear Wiper': '4', 'Remote Start': '5', 'Roof Rack': '2', 'Satellite Radio': '7', 'Sun Roof': '1', 'Tinted Glass': '7', 'USB': '7'}, 'Body Style': {'4D Hatchback': '1', '4D Passenger Van': '1', '4D Sedan': '3', '4D Sport Utility': '2'}, 'Engine': {'2.0 liters / 4 Cylinder': '4', '3.5 liters / V6 Cylinder': '3'}, 'Transmission': {'1-Speed CVT w/OD': '3', '10-Speed Automatic w/OD': '2', '9-Speed Automatic w/OD': '2'}, 'Drivetrain': {'FWD': '7'}, 'Fuel Type': {'Gasoline': '7'}},
    (('Exterior Color', ('Gray',)),): {'Model': {'Accord': '11', 'Accord Hybrid': '21', 'CR-V': '21', 'CR-V Hybrid': '23', 'Civic': '11', 'HR-V': '6', 'Odyssey': '20', 'Passport': '4', 'Pilot': '14', 'Prologue': '5', 'Ridgeline': '11'}, 'Year': {'2025': '119', '2024': '28'}, 'Interior Color': {'Black': '121', 'Brown': '4', 'Gray': '22'}, 'Highlights': {'AUX': '147', 'Bluetooth': '147', 'Climate Control': '147', 'Heated Mirrors': '128', 'Heated Seats': '121', 'Keyless Access': '147', 'Leather Seats': '101', 'Moon Roof': '120', 'Panoramic Roof': '18', 'Parking Sensors': '69', 'Rear Camera': '147', 'Rear Climate Control': '49', 'Rear Wiper': '98', 'Remote Start': '107', 'Roof Rack': '40', 'Satellite Radio': '77', 'Third Row Seats': '11', 'Tinted Glass': '147', 'USB': '34'}, 'Body Style': {'4D Crew Cab': '11', '4D Hatchback': '5', '4D Passenger Van': '20', '4D Sedan': '38', '4D Sport Utility': '73'}, 'Engine': {'1.5 liters / 4 Cylinder': '32', '2.0 liters / 4 Cylinder': '61', '3.5 liters / V6 Cylinder': '49', 'Electric Motor': '5'}, 'Transmission': {'1-Speed Automatic': '5', '1-Speed CVT w/OD': '93', '10-Speed Automatic w/OD': '34', '9-Speed Automatic w/OD': '15'}, 'Drivetrain': {'AWD': '49', 'FWD': '98'}, 'Fuel Type': {'Electric': '5', 'Gasoline': '98', 'Hybrid': '44'}},
    (("Interior Color", ("Brown",)),): {'Model': {'Odyssey': '1', 'Passport': '1', 'Pilot': '2', 'Prologue': '2', 'Ridgeline': '7'}, 'Year': {'2025': '8', '2024': '5'}, 'Exterior Color': {'Black': '3', 'Gray': '4', 'Red': '1', 'White': '5'}, 'Highlights': {'AUX': '13', 'Bluetooth': '13', 'Climate Control': '13', 'Heated Mirrors': '13', 'Heated Seats': '13', 'Keyless Access': '13', 'Leather Seats': '13', 'Moon Roof': '13', 'Panoramic Roof': '4', 'Parking Sensors': '6', 'Rear Camera': '13', 'Rear Climate Control': '11', 'Rear Wiper': '6', 'Remote Start': '10', 'Roof Rack': '2', 'Satellite Radio': '13', 'Tinted Glass': '13', 'USB': '3'}, 'Body Style': {'4D Crew Cab': '7', '4D Passenger Van': '1', '4D Sport Utility': '5'}, 'Engine': {'3.5 liters / V6 Cylinder': '11', 'Electric Motor': '2'}, 'Transmission': {'1-Speed Automatic': '2', '10-Speed Automatic w/OD': '3', '9-Speed Automatic w/OD': '8'}, 'Drivetrain': {'AWD': '12', 'FWD': '1'}, 'Fuel Type': {'Electric': '2', 'Gasoline': '11'}},
    (('Interior Color', ('Brown', 'Gray')),): {'Model': {'Accord': '15', 'Accord Hybrid': '6', 'CR-V': '22', 'CR-V Hybrid': '5', 'Civic': '2', 'HR-V': '14', 'Odyssey': '22', 'Passport': '3', 'Pilot': '36', 'Prologue': '20', 'Ridgeline': '8'}, 'Year': {'2025': '117', '2024': '36'}, 'Exterior Color': {'Black': '11', 'Blue': '28', 'Gray': '26', 'Green': '5', 'Red': '23', 'Silver': '10', 'White': '50'}, 'Highlights': {'AUX': '153', 'Bluetooth': '153', 'Climate Control': '153', 'Heated Mirrors': '127', 'Heated Seats': '128', 'Keyless Access': '153', 'Leather Seats': '112', 'Moon Roof': '105', 'Panoramic Roof': '33', 'Parking Sensors': '98', 'Rear Camera': '153', 'Rear Climate Control': '69', 'Rear Wiper': '122', 'Remote Start': '105', 'Roof Rack': '21', 'Satellite Radio': '107', 'Third Row Seats': '21', 'Tinted Glass': '153', 'USB': '58'}, 'Body Style': {'4D Crew Cab': '8', '4D Passenger Van': '22', '4D Sedan': '23', '4D Sport Utility': '100'}, 'Engine': {'1.5 liters / 4 Cylinder': '37', '2.0 liters / 4 Cylinder': '27', '3.5 liters / V6 Cylinder': '69', 'Electric Motor': '20'}, 'Transmission': {'1-Speed Automatic': '20', '1-Speed CVT w/OD': '64', '10-Speed Automatic w/OD': '58', '9-Speed Automatic w/OD': '11'}, 'Drivetrain': {'AWD': '69', 'FWD': '84'}, 'Fuel Type': {'Electric': '20', 'Gasoline': '122', 'Hybrid': '11'}},
    (('Interior Color', ('Gray',)),): {'Make': {'Honda': '11', 'Hyundai': '2', 'Mercedes-Benz': '1', 'Ram': '1'}, 'Model': {'1500 Classic': '1', 'Accord': '3', 'CR-V': '3', 'EQS': '1', 'Odyssey': '3', 'Pilot': '2', 'Sonata': '2'}, 'Year': {'2023': '1', '2021': '1', '2020': '3', '2019': '3', '2018': '2', '2016': '1', '2015': '2', '2014': '1', '2012': '1'}, 'Exterior Color': {'Black': '1', 'Blue': '2', 'Gray': '5', 'Red': '1', 'Silver': '3', 'Tan': '1', 'White': '2'}, 'Highlights': {'AUX': '15', 'Bluetooth': '15', 'Climate Control': '12', 'Heated Mirrors': '11', 'Heated Seats': '10', 'Keyless Access': '15', 'Leather Seats': '7', 'Moon Roof': '8', 'Panoramic Roof': '2', 'Parking Sensors': '3', 'Rear Camera': '15', 'Rear Climate Control': '5', 'Rear Wiper': '9', 'Remote Start': '6', 'Roof Rack': '1', 'Satellite Radio': '12', 'Sun Roof': '5', 'Tinted Glass': '15', 'USB': '13'}, 'Body Style': {'4D Passenger Van': '3', '4D Quad Cab': '1', '4D Sedan': '5', '4D Sport Utility': '6'}, 'Engine': {'1.5 liters / 4 Cylinder': '2', '2.0 liters / 4 Cylinder': '2', '2.4 liters / 4 Cylinder': '4', '3.5 liters / V6 Cylinder': '5', '5.7 liters / 8 Cylinder': '1', 'Electric Motor': '1'}, 'Transmission': {'1-Speed Automatic': '1', '1-Speed CVT w/OD': '3', '10-Speed Automatic w/OD': '2', '5-Speed Automatic': '1', '5-Speed Automatic w/OD': '1', '6-Speed Automatic w/OD': '5', '8-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '1'}, 'Drivetrain': {'4x4': '1', 'AWD': '1', 'FWD': '12', 'RWD': '1'}, 'Fuel Type': {'Electric': '1', 'Gasoline': '14'}},
    (('Interior Color', ('Gray', 'Ash')),): {'Make': {'Honda': '11', 'Hyundai': '2', 'Mercedes-Benz': '1', 'Ram': '1', 'Toyota': '2'}, 'Model': {'1500 Classic': '1', 'Accord': '3', 'CR-V': '3', 'EQS': '1', 'Odyssey': '3', 'Pilot': '2', 'RAV4': '1', 'Sienna': '1', 'Sonata': '2'}, 'Year': {'2023': '1', '2021': '1', '2020': '3', '2019': '4', '2018': '2', '2016': '2', '2015': '2', '2014': '1', '2012': '1'}, 'Exterior Color': {'Black': '1', 'Blue': '2', 'Gray': '6', 'Red': '2', 'Silver': '3', 'Tan': '1', 'White': '2'}, 'Highlights': {'AUX': '17', 'Bluetooth': '17', 'Climate Control': '14', 'Heated Mirrors': '13', 'Heated Seats': '12', 'Keyless Access': '17', 'Leather Seats': '8', 'Moon Roof': '10', 'Panoramic Roof': '2', 'Parking Sensors': '4', 'Rear Camera': '17', 'Rear Climate Control': '6', 'Rear Wiper': '11', 'Remote Start': '6', 'Roof Rack': '3', 'Satellite Radio': '14', 'Sun Roof': '5', 'Tinted Glass': '17', 'USB': '14'}, 'Body Style': {'4D Passenger Van': '4', '4D Quad Cab': '1', '4D Sedan': '5', '4D Sport Utility': '7'}, 'Engine': {'1.5 liters / 4 Cylinder': '2', '2.0 liters / 4 Cylinder': '2', '2.4 liters / 4 Cylinder': '4', '2.5 liters / 4 Cylinder': '1', '3.5 liters / V6 Cylinder': '6', '5.7 liters / 8 Cylinder': '1', 'Electric Motor': '1'}, 'Transmission': {'1-Speed Automatic': '1', '1-Speed CVT w/OD': '3', '10-Speed Automatic w/OD': '2', '5-Speed Automatic': '1', '5-Speed Automatic w/OD': '1', '6-Speed Automatic w/OD': '6', '8-Speed Automatic w/OD': '2', '9-Speed Automatic w/OD': '1'}, 'Drivetrain': {'4x4': '1', 'AWD': '1', 'FWD': '14', 'RWD': '1'}, 'Fuel Type': {'Electric': '1', 'Gasoline': '16'}},
    (('Interior Color', ('Beige',)),): {'Model': {'Odyssey': '1'}, 'Year': {'2019': '1'}, 'Exterior Color': {'Red': '1'}, 'Highlights': {'AUX': '1', 'Bluetooth': '1', 'Climate Control': '1', 'Heated Mirrors': '1', 'Heated Seats': '1', 'Keyless Access': '1', 'Leather Seats': '1', 'Moon Roof': '1', 'Rear Camera': '1', 'Rear Climate Control': '1', 'Rear Wiper': '1', 'Remote Start': '1', 'Satellite Radio': '1', 'Sun Roof': '1', 'Tinted Glass': '1', 'USB': '1'}, 'Body Style': {'4D Passenger Van': '1'}, 'Engine': {'3.5 liters / V6 Cylinder': '1'}, 'Transmission': {'9-Speed Automatic w/OD': '1'}, 'Drivetrain': {'FWD': '1'}, 'Fuel Type': {'Gasoline': '1'}},
    (('Interior Color', ('Black', 'Beige')),): {'Model': {'Accord': '1', 'CR-V': '1', 'Civic': '3', 'Odyssey': '2', 'Pilot': '2'}, 'Year': {'2024': '1', '2023': '1', '2022': '5', '2021': '1', '2019': '1'}, 'Exterior Color': {'Black': '5', 'Blue': '1', 'Gray': '1', 'Red': '1', 'Silver': '1'}, 'Highlights': {'AUX': '9', 'Bluetooth': '9', 'Climate Control': '9', 'Heated Mirrors': '6', 'Heated Seats': '6', 'Keyless Access': '9', 'Leather Seats': '4', 'Moon Roof': '5', 'Parking Sensors': '2', 'Rear Camera': '9', 'Rear Climate Control': '4', 'Rear Wiper': '6', 'Remote Start': '7', 'Roof Rack': '2', 'Satellite Radio': '9', 'Sun Roof': '2', 'Tinted Glass': '9', 'USB': '8'}, 'Body Style': {'4D Hatchback': '1', '4D Passenger Van': '2', '4D Sedan': '3', '4D Sport Utility': '3'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '4', '3.5 liters / V6 Cylinder': '4'}, 'Transmission': {'1-Speed CVT w/OD': '4', '10-Speed Automatic w/OD': '2', '9-Speed Automatic w/OD': '3'}, 'Drivetrain': {'FWD': '9'}, 'Fuel Type': {'Gasoline': '9'}},
    (('Interior Color', ('Black',)),): {'Model': {'Accord': '23', 'Accord Hybrid': '70', 'CR-V': '65', 'CR-V Hybrid': '99', 'Civic': '45', 'HR-V': '21', 'Odyssey': '40', 'Passport': '18', 'Pilot': '81', 'Prologue': '52', 'Ridgeline': '28'}, 'Year': {'2025': '422', '2024': '120'}, 'Exterior Color': {'Black': '93', 'Blue': '81', 'Brown': '1', 'Gray': '121', 'Green': '2', 'Red': '59', 'Silver': '64', 'White': '121'}, 'Highlights': {'AUX': '542', 'Bluetooth': '542', 'Climate Control': '542', 'Heated Mirrors': '476', 'Heated Seats': '468', 'Keyless Access': '542', 'Leather Seats': '341', 'Moon Roof': '428', 'Panoramic Roof': '95', 'Parking Sensors': '268', 'Rear Camera': '542', 'Rear Climate Control': '167', 'Rear Wiper': '398', 'Remote Start': '443', 'Roof Rack': '177', 'Satellite Radio': '303', 'Third Row Seats': '35', 'Tinted Glass': '542', 'USB': '123'}, 'Body Style': {'4D Crew Cab': '28', '4D Hatchback': '22', '4D Passenger Van': '40', '4D Sedan': '116', '4D Sport Utility': '336'}, 'Engine': {'1.5 liters / 4 Cylinder': '89', '2.0 liters / 4 Cylinder': '234', '3.5 liters / V6 Cylinder': '167', 'Electric Motor': '52'}, 'Transmission': {'1-Speed Automatic': '52', '1-Speed CVT w/OD': '323', '10-Speed Automatic w/OD': '121', '9-Speed Automatic w/OD': '46'}, 'Drivetrain': {'AWD': '210', 'FWD': '332'}, 'Fuel Type': {'Electric': '52', 'Gasoline': '321', 'Hybrid': '169'}},
    (("Highlights", ("Panoramic Roof",)),): {'Model': {'Pilot': '74', 'Prologue': '54'}, 'Year': {'2025': '73', '2024': '55'}, 'Exterior Color': {'Black': '18', 'Blue': '32', 'Gray': '18', 'Red': '15', 'Silver': '13', 'White': '32'}, 'Interior Color': {'Black': '95', 'Brown': '4', 'Gray': '29'}, 'Body Style': {'4D Sport Utility': '128'}, 'Engine': {'3.5 liters / V6 Cylinder': '74', 'Electric Motor': '54'}, 'Transmission': {'1-Speed Automatic': '54', '10-Speed Automatic w/OD': '74'}, 'Drivetrain': {'AWD': '104', 'FWD': '24'}, 'Fuel Type': {'Electric': '54', 'Gasoline': '74'}},
    (("Highlights", ("Heated Mirrors", "Rear Climate Control")),): {'Model': {'Odyssey': '62', 'Passport': '21', 'Pilot': '117', 'Ridgeline': '33'}, 'Year': {'2025': '214', '2024': '19'}, 'Exterior Color': {'Black': '54', 'Blue': '34', 'Gray': '48', 'Red': '25', 'Silver': '18', 'White': '54'}, 'Interior Color': {'Black': '164', 'Brown': '11', 'Gray': '58'}, 'Body Style': {'4D Crew Cab': '33', '4D Passenger Van': '62', '4D Sport Utility': '138'}, 'Engine': {'3.5 liters / V6 Cylinder': '233'}, 'Transmission': {'10-Speed Automatic w/OD': '179', '9-Speed Automatic w/OD': '54'}, 'Drivetrain': {'AWD': '138', 'FWD': '95'}, 'Fuel Type': {'Gasoline': '233'}},
    (('Highlights', ('AUX',)),): {'Make': {'Audi': '4', 'BMW': '3', 'Buick': '2', 'Cadillac': '4', 'Chevrolet': '7', 'Chrysler': '1', 'Dodge': '2', 'Ford': '13', 'GMC': '5', 'Honda': '49', 'Hyundai': '2', 'INFINITI': '3', 'Jeep': '9', 'Kia': '1', 'Lexus': '10', 'Mazda': '2', 'Mercedes-Benz': '1', 'Nissan': '2', 'Ram': '2', 'Subaru': '2', 'Tesla': '2', 'Toyota': '18', 'Volkswagen': '2', 'Volvo': '1'}, 'Model': {'1500': '1', '1500 Classic': '1', '3 Series': '1', '4Runner': '1', 'Accord': '11', 'Ascent': '1', 'Avalon': '1', 'Bronco': '1', 'C-Max Energi': '1', 'CR-V': '8', 'CR-V Hybrid': '1', 'CX-5': '1', 'Camry': '4', 'Challenger': '1', 'Charger': '1', 'Civic': '9', 'Civic Type R': '1', 'Corolla': '2', 'Cruze': '1', 'EQS': '1', 'ES': '3', 'Edge': '1', 'Enclave': '2', 'Equinox': '1', 'Escalade': '2', 'Expedition': '1', 'Expedition Max': '2', 'Explorer': '3', 'F-150': '1', 'Fusion Hybrid': '1', 'G37': '1', 'GX': '1', 'Grand Cherokee': '2', 'Grand Cherokee L': '1', 'IS': '1', 'Insight': '1', 'Jetta': '1', 'LX': '1', 'Mazda6': '1', 'Model Y': '2', 'Mustang': '2', 'NX': '1', 'New Compass': '1', 'Odyssey': '8', 'Outback': '1', 'Pacifica': '1', 'Pathfinder': '1', 'Pilot': '8', 'Q3': '1', 'Q5': '2', 'Q7': '1', 'QX60': '1', 'QX80': '1', 'RAV4': '1', 'RAV4 Hybrid': '2', 'RX': '3', 'Renegade': '1', 'Ridgeline': '2', 'SRX': '1', 'Sequoia': '1', 'Sienna': '2', 'Sierra 1500': '1', 'Silverado 1500': '1', 'Sonata': '2', 'Sportage': '1', 'Tacoma': '2', 'Tahoe': '2', 'Terrain': '1', 'Tiguan': '1', 'Titan XD': '1', 'Traverse': '2', 'Tundra': '2', 'Wrangler': '4', 'X3': '2', 'XC60': '1', 'XT6': '1', 'Yukon': '1', 'Yukon XL': '2'}, 'Year': {'2025': '2', '2024': '4', '2023': '10', '2022': '19', '2021': '18', '2020': '12', '2019': '14', '2018': '19', '2017': '14', '2016': '7', '2015': '8', '2014': '5', '2013': '6', '2012': '3', '2011': '1', '2010': '2', '2009': '1', '2008': '1', '2007': '1'}, 'Exterior Color': {'Basque Red Pearl Ii': '1', 'Beige': '1', 'Black': '37', 'Blue': '10', 'Brown': '1', 'Gray': '26', 'Grigio Metallic': '1', 'Ice Silver Metallic': '1', 'Maroon': '1', 'Obsidian': '1', 'Orange': '1', 'Red': '15', 'Silver': '14', 'Sunset Bronze Mica': '1', 'Tan': '1', 'White': '32', 'Yellow': '1'}, 'Interior Color': {'Almond': '1', 'Ash': '2', 'Beige': '3', 'Black': '57', 'Black Onyx': '1', 'Boulder': '1', 'Brown': '2', 'Brownstone Accents': '1', 'Cement': '2', 'Charcoal': '1', 'Chestnut Brown': '1', 'Cocoa Accents': '1', 'Creme': '1', 'Dune': '1', 'Ebony': '7', 'Ebony Accents': '2', 'Ebony Black': '1', 'Flaxen': '2', 'Global Black': '1', 'Graphite': '2', 'Gray': '15', 'Ivory': '3', 'Jet Black': '10', 'Light Shale': '2', 'Medium Light Camel': '1', 'Medium Light Stone': '1', 'Medium Stone': '1', 'Medium Titanium': '1', 'Mocha': '1', 'Moonstone': '1', 'Noble Brown': '1', 'Parchment': '4', 'Red': '2', 'Sand Beige': '1', 'Sepia': '1', 'Slate Black': '1', 'Soft Beige': '1', 'Stone': '1', 'Titan Black': '2', 'Truffle': '1', 'Warm Ivory': '1', 'Wheat': '1'}, 'Body Style': {'2D Convertible': '1', '2D Coupe': '4', '2D Sport Utility': '1', '4D Crew Cab': '6', '4D CrewMax': '2', '4D Double Cab': '2', '4D Hatchback': '3', '4D Passenger Van': '11', '4D Quad Cab': '1', '4D Sedan': '38', '4D Sport Utility': '77', '4D SuperCrew': '1'}, 'Engine': {'1.5 liters / 4 Cylinder': '13', '1.8 liters / 4 Cylinder': '4', '2.0 liters / 4 Cylinder': '24', '2.3 liters / 4 Cylinder': '3', '2.4 liters / 4 Cylinder': '12', '2.5 liters / 4 Cylinder': '9', '2.7 liters / 4 Cylinder': '1', '3.0 liters / Straight 6 Cylinder': '1', '3.0 liters / V6 Cylinder': '1', '3.2 liters / V6 Cylinder': '1', '3.4 liters / V6 Cylinder': '2', '3.5 liters / V6 Cylinder': '36', '3.6 liters / V6 Cylinder': '15', '3.7 liters / V6 Cylinder': '1', '4 2.4 Intercooled Turbo Regular Unleaded H-4 2.4 L/146': '1', '4 2.5 Regular Unleaded H-4 2.5 L/152': '1', '4.0 liters / V6 Cylinder': '1', '4.6 liters / 8 Cylinder': '1', '5.0 liters / 8 Cylinder': '1', '5.3 liters / 8 Cylinder': '3', '5.6 liters / 8 Cylinder': '2', '5.7 liters / 8 Cylinder': '4', '6.2 liters / 8 Cylinder': '6', '6.4 liters / 8 Cylinder': '1', 'Electric Motor': '3'}, 'Transmission': {'1-Speed Automatic': '3', '1-Speed CVT w/OD': '32', '10-Speed Automatic': '3', '10-Speed Automatic w/OD': '12', '5-Speed': '1', '5-Speed Automatic': '4', '5-Speed Automatic w/OD': '3', '6-Speed Automatic': '10', '6-Speed Automatic w/OD': '25', '6-Speed Manual': '2', '6-Speed Manual w/OD': '1', '7-Speed Automatic w/OD': '2', '7-Speed Manual': '1', '7-Speed S tronic Dual-Clutch Auto': '1', '8-Speed Automatic': '4', '8-Speed Automatic w/OD': '22', '8-Speed CVT w/OD': '2', '8-speed Tiptronic automatic': '1', '9-Speed Automatic': '6', '9-Speed Automatic w/OD': '11', 'Continuously variable': '1'}, 'Drivetrain': {'4x4': '18', 'AWD': '19', 'FWD': '81', 'FrontTrak': '1', 'RWD': '25', 'quattro': '3'}, 'Fuel Type': {'Electric': '3', 'Gasoline': '139', 'Hybrid': '5'}},
    (('Highlights', ('AUX', 'Parking Sensors')),): {'Make': {'Audi': '1', 'Buick': '1', 'Chrysler': '1', 'Dodge': '2', 'Ford': '9', 'Honda': '5', 'Hyundai': '1', 'INFINITI': '1', 'Jeep': '2', 'Lexus': '1', 'Mercedes-Benz': '1', 'Nissan': '2', 'Ram': '1', 'Tesla': '2', 'Toyota': '3'}, 'Model': {'1500': '1', 'Accord': '2', 'CR-V Hybrid': '1', 'Challenger': '1', 'Charger': '1', 'EQS': '1', 'Edge': '1', 'Enclave': '1', 'Expedition': '1', 'Expedition Max': '2', 'Explorer': '3', 'F-150': '1', 'Fusion Hybrid': '1', 'Grand Cherokee': '1', 'Grand Cherokee L': '1', 'LX': '1', 'Model Y': '2', 'Odyssey': '1', 'Pacifica': '1', 'Pathfinder': '1', 'Pilot': '1', 'Q3': '1', 'QX80': '1', 'RAV4': '1', 'Sienna': '1', 'Sonata': '1', 'Titan XD': '1', 'Tundra': '1'}, 'Year': {'2025': '1', '2024': '2', '2023': '3', '2022': '3', '2021': '7', '2020': '2', '2019': '4', '2018': '5', '2017': '3', '2016': '2', '2015': '1'}, 'Exterior Color': {'Black': '7', 'Blue': '4', 'Gray': '10', 'Red': '1', 'Silver': '2', 'Sunset Bronze Mica': '1', 'White': '8'}, 'Interior Color': {'Ash': '1', 'Black': '12', 'Brown': '2', 'Charcoal': '1', 'Chestnut Brown': '1', 'Ebony': '5', 'Ebony Accents': '1', 'Ebony Black': '1', 'Global Black': '1', 'Gray': '3', 'Medium Light Camel': '1', 'Medium Stone': '1', 'Noble Brown': '1', 'Parchment': '1', 'Wheat': '1'}, 'Body Style': {'2D Coupe': '1', '4D Crew Cab': '2', '4D CrewMax': '1', '4D Passenger Van': '3', '4D Sedan': '5', '4D Sport Utility': '20', '4D SuperCrew': '1'}, 'Engine': {'2.0 liters / 4 Cylinder': '7', '2.3 liters / 4 Cylinder': '1', '2.5 liters / 4 Cylinder': '2', '3.4 liters / V6 Cylinder': '1', '3.5 liters / V6 Cylinder': '9', '3.6 liters / V6 Cylinder': '5', '5.6 liters / 8 Cylinder': '2', '5.7 liters / 8 Cylinder': '2', '6.4 liters / 8 Cylinder': '1', 'Electric Motor': '3'}, 'Transmission': {'1-Speed Automatic': '3', '1-Speed CVT w/OD': '4', '10-Speed Automatic w/OD': '8', '6-Speed Automatic w/OD': '7', '7-Speed Automatic w/OD': '1', '8-Speed Automatic w/OD': '7', '9-Speed Automatic': '1', '9-Speed Automatic w/OD': '2'}, 'Drivetrain': {'4x4': '5', 'AWD': '4', 'FWD': '13', 'FrontTrak': '1', 'RWD': '10'}, 'Fuel Type': {'Electric': '3', 'Gasoline': '28', 'Hybrid': '2'}},
    (('Highlights', ('Heated Seats',)),): {'Model': {'Accord': '2', 'CR-V': '1', 'Odyssey': '2', 'Pilot': '3'}, 'Year': {'2024': '1', '2022': '3', '2021': '1', '2020': '2', '2019': '1'}, 'Exterior Color': {'Black': '2', 'Blue': '1', 'Gray': '2', 'Red': '1', 'Silver': '2'}, 'Interior Color': {'Beige': '1', 'Black': '5', 'Gray': '2'}, 'Body Style': {'4D Passenger Van': '2', '4D Sedan': '2', '4D Sport Utility': '4'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '2', '3.5 liters / V6 Cylinder': '5'}, 'Transmission': {'1-Speed CVT w/OD': '1', '10-Speed Automatic w/OD': '3', '6-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '3'}, 'Drivetrain': {'FWD': '8'}, 'Fuel Type': {'Gasoline': '8'}},
    (('Highlights', ('Moon Roof', 'Rear Wiper')),): {'Model': {'CR-V': '1', 'Odyssey': '2', 'Pilot': '1'}, 'Year': {'2024': '1', '2022': '2', '2019': '1'}, 'Exterior Color': {'Black': '1', 'Blue': '1', 'Gray': '1', 'Red': '1'}, 'Interior Color': {'Beige': '1', 'Black': '3'}, 'Body Style': {'4D Passenger Van': '2', '4D Sport Utility': '2'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '3.5 liters / V6 Cylinder': '3'}, 'Transmission': {'1-Speed CVT w/OD': '1', '10-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '2'}, 'Drivetrain': {'FWD': '4'}, 'Fuel Type': {'Gasoline': '4'}},
    (('Highlights', ('Remote Start',)),): {'Model': {'Accord': '9', 'Accord Hybrid': '62', 'CR-V': '57', 'CR-V Hybrid': '104', 'Civic': '44', 'HR-V': '22', 'Odyssey': '38', 'Passport': '21', 'Pilot': '83', 'Prologue': '72', 'Ridgeline': '36'}, 'Year': {'2025': '401', '2024': '147'}, 'Exterior Color': {'Black': '84', 'Blue': '90', 'Brown': '1', 'Gray': '107', 'Green': '2', 'Red': '66', 'Silver': '61', 'White': '137'}, 'Interior Color': {'Black': '443', 'Brown': '10', 'Gray': '95'}, 'Body Style': {'4D Crew Cab': '36', '4D Hatchback': '22', '4D Passenger Van': '38', '4D Sedan': '93', '4D Sport Utility': '359'}, 'Engine': {'1.5 liters / 4 Cylinder': '67', '2.0 liters / 4 Cylinder': '231', '3.5 liters / V6 Cylinder': '178', 'Electric Motor': '72'}, 'Transmission': {'1-Speed Automatic': '72', '1-Speed CVT w/OD': '298', '10-Speed Automatic w/OD': '121', '9-Speed Automatic w/OD': '57'}, 'Drivetrain': {'AWD': '247', 'FWD': '301'}, 'Fuel Type': {'Electric': '72', 'Gasoline': '310', 'Hybrid': '166'}},
    (("Body Style", ("4D Crew Cab",)),): {'Model': {'Ridgeline': '36'}, 'Year': {'2025': '22', '2024': '14'}, 'Exterior Color': {'Black': '9', 'Blue': '1', 'Gray': '11', 'Red': '5', 'Silver': '1', 'White': '9'}, 'Interior Color': {'Black': '28', 'Brown': '7', 'Gray': '1'}, 'Highlights': {'AUX': '36', 'Bluetooth': '36', 'Climate Control': '36', 'Heated Mirrors': '33', 'Heated Seats': '33', 'Keyless Access': '36', 'Leather Seats': '30', 'Moon Roof': '33', 'Parking Sensors': '15', 'Rear Camera': '36', 'Rear Climate Control': '36', 'Remote Start': '36', 'Satellite Radio': '33', 'Tinted Glass': '36'}, 'Engine': {'3.5 liters / V6 Cylinder': '36'}, 'Transmission': {'9-Speed Automatic w/OD': '36'}, 'Drivetrain': {'AWD': '36'}, 'Fuel Type': {'Gasoline': '36'}},
    (('Body Style', ('4D Hatchback', "4D Sedan")),): {'Model': {'Accord': '38', 'Accord Hybrid': '76', 'Civic': '50'}, 'Year': {'2025': '101', '2024': '63'}, 'Exterior Color': {'Black': '18', 'Blue': '11', 'Brown': '1', 'Gray': '43', 'Red': '22', 'Silver': '25', 'White': '44'}, 'Interior Color': {'Black': '138', 'Gray': '23', 'Red': '3'}, 'Highlights': {'AUX': '164', 'Bluetooth': '164', 'Climate Control': '164', 'Heated Mirrors': '118', 'Heated Seats': '111', 'Keyless Access': '164', 'Leather Seats': '92', 'Moon Roof': '117', 'Parking Sensors': '49', 'Rear Camera': '164', 'Rear Wiper': '22', 'Remote Start': '115', 'Satellite Radio': '1', 'Tinted Glass': '164', 'USB': '2'}, 'Engine': {'1.5 liters / 4 Cylinder': '42', '2.0 liters / 4 Cylinder': '122'}, 'Transmission': {'1-Speed CVT w/OD': '161', '6-Speed Manual w/OD': '3'}, 'Drivetrain': {'FWD': '164'}, 'Fuel Type': {'Gasoline': '88', 'Hybrid': '76'}},
    (('Body Style', ('4D Sedan',)),): {'Make': {'BMW': '1', 'Chevrolet': '1', 'Dodge': '1', 'Ford': '1', 'Honda': '19', 'Hyundai': '2', 'Lexus': '4', 'Mazda': '1', 'Toyota': '7', 'Volkswagen': '1'}, 'Model': {'3 Series': '1', 'Accord': '10', 'Avalon': '1', 'Camry': '4', 'Charger': '1', 'Civic': '8', 'Corolla': '2', 'Cruze': '1', 'ES': '3', 'Fusion Hybrid': '1', 'IS': '1', 'Insight': '1', 'Jetta': '1', 'Mazda6': '1', 'Sonata': '2'}, 'Year': {'2023': '2', '2022': '7', '2021': '5', '2020': '3', '2019': '3', '2018': '5', '2017': '1', '2016': '1', '2015': '3', '2014': '4', '2013': '2', '2012': '1', '2009': '1'}, 'Exterior Color': {'Basque Red Pearl Ii': '1', 'Beige': '1', 'Black': '8', 'Blue': '3', 'Gray': '6', 'Grigio Metallic': '1', 'Red': '5', 'Silver': '5', 'Tan': '1', 'White': '7'}, 'Interior Color': {'Almond': '1', 'Beige': '1', 'Black': '19', 'Ebony': '1', 'Flaxen': '2', 'Gray': '5', 'Ivory': '2', 'Medium Titanium': '1', 'Moonstone': '1', 'Parchment': '2', 'Red': '1', 'Titan Black': '1'}, 'Highlights': {'AUX': '38', 'Bluetooth': '35', 'Climate Control': '32', 'Heated Mirrors': '16', 'Heated Seats': '13', 'Keyless Access': '36', 'Leather Seats': '9', 'Moon Roof': '13', 'Panoramic Roof': '1', 'Parking Sensors': '5', 'Rear Camera': '32', 'Rear Climate Control': '1', 'Remote Start': '10', 'Satellite Radio': '24', 'Sun Roof': '5', 'Tinted Glass': '33', 'USB': '33', 'Xenon Headlights': '1'}, 'Engine': {'1.5 liters / 4 Cylinder': '8', '1.8 liters / 4 Cylinder': '4', '2.0 liters / 4 Cylinder': '12', '2.4 liters / 4 Cylinder': '4', '2.5 liters / 4 Cylinder': '4', '3.0 liters / Straight 6 Cylinder': '1', '3.5 liters / V6 Cylinder': '4', '3.6 liters / V6 Cylinder': '1'}, 'Transmission': {'1-Speed CVT w/OD': '18', '10-Speed Automatic w/OD': '2', '5-Speed': '1', '5-Speed Automatic': '1', '6-Speed Automatic': '2', '6-Speed Automatic w/OD': '7', '8-Speed Automatic w/OD': '6', 'Continuously variable': '1'}, 'Drivetrain': {'FWD': '35', 'RWD': '3'}, 'Fuel Type': {'Gasoline': '37', 'Hybrid': '1'}},
    (('Body Style', ('2D Coupe', '4D CrewMax')),): {'Make': {'Dodge': '1', 'Ford': '2', 'Honda': '1', 'Toyota': '2'}, 'Model': {'Accord': '1', 'Challenger': '1', 'Mustang': '2', 'Tundra': '2'}, 'Year': {'2023': '1', '2022': '2', '2019': '1', '2017': '1', '2008': '1'}, 'Exterior Color': {'Black': '1', 'Blue': '1', 'Gray': '1', 'Red': '1', 'White': '2'}, 'Interior Color': {'Black': '2', 'Boulder': '1', 'Ebony': '2', 'Ivory': '1'}, 'Highlights': {'AUX': '6', 'Bluetooth': '6', 'Climate Control': '4', 'Heated Mirrors': '4', 'Heated Seats': '3', 'Keyless Access': '6', 'Leather Seats': '2', 'Moon Roof': '2', 'Panoramic Roof': '1', 'Parking Sensors': '2', 'Rear Camera': '5', 'Remote Start': '1', 'Satellite Radio': '5', 'Tinted Glass': '5', 'USB': '3'}, 'Engine': {'2.3 liters / 4 Cylinder': '1', '3.4 liters / V6 Cylinder': '2', '3.5 liters / V6 Cylinder': '1', '5.0 liters / 8 Cylinder': '1', '6.4 liters / 8 Cylinder': '1'}, 'Transmission': {'10-Speed Automatic w/OD': '2', '5-Speed Automatic': '1', '6-Speed Manual': '2', '8-Speed Automatic w/OD': '1'}, 'Drivetrain': {'4x4': '2', 'FWD': '1', 'RWD': '3'}, 'Fuel Type': {'Gasoline': '6'}},
    (('Body Style', ('4D Sport Utility',)),): {'Model': {'CR-V': '1', 'Pilot': '3'}, 'Year': {'2024': '1', '2022': '2', '2020': '1'}, 'Exterior Color': {'Black': '2', 'Blue': '1', 'Silver': '1'}, 'Interior Color': {'Black': '3', 'Gray': '1'}, 'Highlights': {'AUX': '4', 'Bluetooth': '4', 'Climate Control': '4', 'Heated Mirrors': '4', 'Heated Seats': '4', 'Keyless Access': '4', 'Leather Seats': '1', 'Moon Roof': '2', 'Rear Camera': '4', 'Rear Climate Control': '3', 'Rear Wiper': '4', 'Remote Start': '4', 'Roof Rack': '2', 'Satellite Radio': '4', 'Tinted Glass': '4', 'USB': '3'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '3.5 liters / V6 Cylinder': '3'}, 'Transmission': {'1-Speed CVT w/OD': '1', '6-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '2'}, 'Drivetrain': {'FWD': '4'}, 'Fuel Type': {'Gasoline': '4'}},
    (('Body Style', ('4D Sport Utility', '4D Passenger Van')),): {'Model': {'CR-V': '1', 'Odyssey': '2', 'Pilot': '3'}, 'Year': {'2024': '1', '2022': '3', '2020': '1', '2019': '1'}, 'Exterior Color': {'Black': '2', 'Blue': '1', 'Gray': '1', 'Red': '1', 'Silver': '1'}, 'Interior Color': {'Beige': '1', 'Black': '4', 'Gray': '1'}, 'Highlights': {'AUX': '6', 'Bluetooth': '6', 'Climate Control': '6', 'Heated Mirrors': '6', 'Heated Seats': '6', 'Keyless Access': '6', 'Leather Seats': '3', 'Moon Roof': '4', 'Parking Sensors': '1', 'Rear Camera': '6', 'Rear Climate Control': '5', 'Rear Wiper': '6', 'Remote Start': '5', 'Roof Rack': '2', 'Satellite Radio': '6', 'Sun Roof': '1', 'Tinted Glass': '6', 'USB': '5'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '3.5 liters / V6 Cylinder': '5'}, 'Transmission': {'1-Speed CVT w/OD': '1', '10-Speed Automatic w/OD': '1', '6-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '3'}, 'Drivetrain': {'FWD': '6'}, 'Fuel Type': {'Gasoline': '6'}},
    (('Body Style', ('4D Hatchback',)),): {'Model': {'Civic': '22'}, 'Year': {'2025': '20', '2024': '2'}, 'Exterior Color': {'Black': '2', 'Blue': '2', 'Brown': '1', 'Gray': '5', 'Red': '3', 'Silver': '5', 'White': '4'}, 'Interior Color': {'Black': '22'}, 'Highlights': {'AUX': '22', 'Bluetooth': '22', 'Climate Control': '22', 'Heated Mirrors': '21', 'Heated Seats': '8', 'Keyless Access': '22', 'Leather Seats': '9', 'Moon Roof': '8', 'Parking Sensors': '3', 'Rear Camera': '22', 'Rear Wiper': '22', 'Remote Start': '22', 'Satellite Radio': '1', 'Tinted Glass': '22', 'USB': '2'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '21'}, 'Transmission': {'1-Speed CVT w/OD': '22'}, 'Drivetrain': {'FWD': '22'}, 'Fuel Type': {'Gasoline': '22'}},
    (("Engine", ("1.5 liters / 4 Cylinder",)),): {'Model': {'Accord': '38', 'CR-V': '87', 'Civic': '4'}, 'Year': {'2025': '117', '2024': '12'}, 'Exterior Color': {'Black': '21', 'Blue': '19', 'Gray': '32', 'Red': '15', 'Silver': '12', 'White': '30'}, 'Interior Color': {'Black': '89', 'Gray': '37', 'Red': '3'}, 'Highlights': {'AUX': '129', 'Bluetooth': '129', 'Climate Control': '129', 'Heated Mirrors': '78', 'Heated Seats': '79', 'Keyless Access': '129', 'Leather Seats': '33', 'Moon Roof': '79', 'Parking Sensors': '33', 'Rear Camera': '129', 'Rear Wiper': '88', 'Remote Start': '67', 'Satellite Radio': '33', 'Tinted Glass': '129', 'USB': '1'}, 'Body Style': {'4D Hatchback': '1', '4D Sedan': '41', '4D Sport Utility': '87'}, 'Transmission': {'1-Speed CVT w/OD': '126', '6-Speed Manual w/OD': '3'}, 'Drivetrain': {'AWD': '37', 'FWD': '92'}, 'Fuel Type': {'Gasoline': '129'}},
    (("Engine", ("2.0 liters / 4 Cylinder", "Electric Motor")),): {'Model': {'Accord Hybrid': '76', 'CR-V Hybrid': '104', 'Civic': '46', 'HR-V': '35', 'Prologue': '72'}, 'Year': {'2025': '209', '2024': '124'}, 'Exterior Color': {'Black': '31', 'Blue': '56', 'Brown': '1', 'Gray': '66', 'Green': '7', 'Red': '42', 'Silver': '44', 'White': '86'}, 'Interior Color': {'Black': '286', 'Brown': '2', 'Gray': '45'}, 'Highlights': {'AUX': '333', 'Bluetooth': '333', 'Climate Control': '333', 'Heated Mirrors': '295', 'Heated Seats': '287', 'Keyless Access': '333', 'Leather Seats': '221', 'Moon Roof': '267', 'Panoramic Roof': '54', 'Parking Sensors': '150', 'Rear Camera': '333', 'Rear Wiper': '232', 'Remote Start': '303', 'Roof Rack': '104', 'Satellite Radio': '148', 'Tinted Glass': '333', 'USB': '1'}, 'Body Style': {'4D Hatchback': '21', '4D Sedan': '101', '4D Sport Utility': '211'}, 'Transmission': {'1-Speed Automatic': '72', '1-Speed CVT w/OD': '261'}, 'Drivetrain': {'AWD': '101', 'FWD': '232'}, 'Fuel Type': {'Electric': '72', 'Gasoline': '81', 'Hybrid': '180'}},
    (('Engine', ('2.4 liters / 4 Cylinder',)),): {'Make': {'Chevrolet': '1', 'Honda': '6', 'Hyundai': '1', 'Jeep': '2', 'Kia': '1', 'Toyota': '1'}, 'Model': {'Accord': '2', 'CR-V': '4', 'Camry': '1', 'Equinox': '1', 'New Compass': '1', 'Renegade': '1', 'Sonata': '1', 'Sportage': '1'}, 'Year': {'2019': '2', '2017': '3', '2015': '2', '2014': '1', '2013': '2', '2012': '1', '2009': '1'}, 'Exterior Color': {'Basque Red Pearl Ii': '1', 'Beige': '1', 'Black': '1', 'Brown': '1', 'Gray': '1', 'Orange': '1', 'Red': '1', 'Silver': '2', 'White': '3'}, 'Interior Color': {'Black': '6', 'Gray': '4', 'Jet Black': '1'}, 'Highlights': {'AUX': '12', 'Bluetooth': '11', 'Climate Control': '6', 'Heated Mirrors': '4', 'Heated Seats': '2', 'Keyless Access': '11', 'Leather Seats': '2', 'Moon Roof': '2', 'Rear Camera': '10', 'Rear Wiper': '8', 'Remote Start': '1', 'Roof Rack': '3', 'Satellite Radio': '5', 'Sun Roof': '1', 'Tinted Glass': '9', 'USB': '7'}, 'Body Style': {'4D Sedan': '4', '4D Sport Utility': '8'}, 'Transmission': {'1-Speed CVT w/OD': '3', '5-Speed': '1', '5-Speed Automatic': '1', '5-Speed Automatic w/OD': '1', '6-Speed Automatic': '1', '6-Speed Automatic w/OD': '2', '9-Speed Automatic w/OD': '2', 'Continuously variable': '1'}, 'Drivetrain': {'4x4': '1', 'AWD': '2', 'FWD': '9'}, 'Fuel Type': {'Gasoline': '12'}},
    (('Engine', ('6.2 liters / 8 Cylinder', '5.7 liters / 8 Cylinder')),): {'Make': {'Cadillac': '2', 'Chevrolet': '1', 'GMC': '3', 'Lexus': '1', 'Ram': '2', 'Toyota': '1'}, 'Model': {'1500': '1', '1500 Classic': '1', 'Escalade': '2', 'LX': '1', 'Sequoia': '1', 'Tahoe': '1', 'Yukon': '1', 'Yukon XL': '2'}, 'Year': {'2023': '2', '2019': '3', '2017': '3', '2016': '1', '2010': '1'}, 'Exterior Color': {'Black': '4', 'Gray': '1', 'Red': '1', 'Silver': '1', 'White': '3'}, 'Interior Color': {'Black': '1', 'Cocoa Accents': '1', 'Gray': '1', 'Jet Black': '3', 'Light Shale': '2', 'Parchment': '1', 'Sand Beige': '1'}, 'Highlights': {'AUX': '10', 'Bluetooth': '10', 'Climate Control': '8', 'Heated Mirrors': '10', 'Heated Seats': '9', 'Keyless Access': '10', 'Leather Seats': '8', 'Moon Roof': '4', 'Parking Sensors': '2', 'Rear Camera': '10', 'Rear Climate Control': '8', 'Rear Wiper': '8', 'Remote Start': '5', 'Roof Rack': '8', 'Satellite Radio': '10', 'Sun Roof': '7', 'Third Row Seats': '4', 'Tinted Glass': '10', 'USB': '10'}, 'Body Style': {'4D Crew Cab': '1', '4D Quad Cab': '1', '4D Sport Utility': '8'}, 'Transmission': {'10-Speed Automatic': '3', '6-Speed Automatic': '1', '8-Speed Automatic': '3', '8-Speed Automatic w/OD': '3'}, 'Drivetrain': {'4x4': '5', 'RWD': '5'}, 'Fuel Type': {'Gasoline': '10'}},
    (('Engine', ('2.0 liters / 4 Cylinder',)),): {'Model': {'Accord': '2', 'Civic': '3'}, 'Year': {'2023': '1', '2022': '2', '2021': '1', '2020': '1'}, 'Exterior Color': {'Black': '3', 'Gray': '1', 'Silver': '1'}, 'Interior Color': {'Black': '4', 'Gray': '1'}, 'Highlights': {'AUX': '5', 'Bluetooth': '5', 'Climate Control': '5', 'Heated Mirrors': '2', 'Heated Seats': '2', 'Keyless Access': '5', 'Leather Seats': '2', 'Moon Roof': '2', 'Parking Sensors': '2', 'Rear Camera': '5', 'Rear Wiper': '1', 'Remote Start': '4', 'Satellite Radio': '5', 'Sun Roof': '2', 'Tinted Glass': '5', 'USB': '5'}, 'Body Style': {'4D Hatchback': '1', '4D Sedan': '4'}, 'Transmission': {'1-Speed CVT w/OD': '3', '10-Speed Automatic w/OD': '2'}, 'Drivetrain': {'FWD': '5'}, 'Fuel Type': {'Gasoline': '5'}},
    (('Engine', ('3.5 liters / V6 Cylinder', '1.5 liters / 4 Cylinder')),): {'Model': {'CR-V': '1', 'Odyssey': '2', 'Pilot': '3'}, 'Year': {'2024': '1', '2022': '3', '2020': '1', '2019': '1'}, 'Exterior Color': {'Black': '2', 'Blue': '1', 'Gray': '1', 'Red': '1', 'Silver': '1'}, 'Interior Color': {'Beige': '1', 'Black': '4', 'Gray': '1'}, 'Highlights': {'AUX': '6', 'Bluetooth': '6', 'Climate Control': '6', 'Heated Mirrors': '6', 'Heated Seats': '6', 'Keyless Access': '6', 'Leather Seats': '3', 'Moon Roof': '4', 'Parking Sensors': '1', 'Rear Camera': '6', 'Rear Climate Control': '5', 'Rear Wiper': '6', 'Remote Start': '5', 'Roof Rack': '2', 'Satellite Radio': '6', 'Sun Roof': '1', 'Tinted Glass': '6', 'USB': '5'}, 'Body Style': {'4D Passenger Van': '2', '4D Sport Utility': '4'}, 'Transmission': {'1-Speed CVT w/OD': '1', '10-Speed Automatic w/OD': '1', '6-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '3'}, 'Drivetrain': {'FWD': '6'}, 'Fuel Type': {'Gasoline': '6'}},
    (('Engine', ('3.5 liters / V6 Cylinder',)),): {'Model': {'Odyssey': '62', 'Passport': '21', 'Pilot': '117', 'Ridgeline': '36'}, 'Year': {'2025': '216', '2024': '20'}, 'Exterior Color': {'Black': '54', 'Blue': '34', 'Gray': '49', 'Red': '25', 'Silver': '18', 'White': '56'}, 'Interior Color': {'Black': '167', 'Brown': '11', 'Gray': '58'}, 'Highlights': {'AUX': '236', 'Bluetooth': '236', 'Climate Control': '236', 'Heated Mirrors': '233', 'Heated Seats': '233', 'Keyless Access': '236', 'Leather Seats': '199', 'Moon Roof': '190', 'Panoramic Roof': '74', 'Parking Sensors': '183', 'Rear Camera': '236', 'Rear Climate Control': '236', 'Rear Wiper': '200', 'Remote Start': '178', 'Roof Rack': '94', 'Satellite Radio': '229', 'Third Row Seats': '56', 'Tinted Glass': '236', 'USB': '179'}, 'Body Style': {'4D Crew Cab': '36', '4D Passenger Van': '62', '4D Sport Utility': '138'}, 'Transmission': {'10-Speed Automatic w/OD': '179', '9-Speed Automatic w/OD': '57'}, 'Drivetrain': {'AWD': '141', 'FWD': '95'}, 'Fuel Type': {'Gasoline': '236'}},
    (("Transmission", ("10-Speed Automatic w/OD",)),): {'Model': {'Odyssey': '62', 'Pilot': '117'}, 'Year': {'2025': '177', '2024': '2'}, 'Exterior Color': {'Black': '37', 'Blue': '29', 'Gray': '34', 'Red': '18', 'Silver': '16', 'White': '45'}, 'Interior Color': {'Black': '121', 'Brown': '3', 'Gray': '55'}, 'Highlights': {'AUX': '179', 'Bluetooth': '179', 'Climate Control': '179', 'Heated Mirrors': '179', 'Heated Seats': '179', 'Keyless Access': '179', 'Leather Seats': '148', 'Moon Roof': '136', 'Panoramic Roof': '74', 'Parking Sensors': '147', 'Rear Camera': '179', 'Rear Climate Control': '179', 'Rear Wiper': '179', 'Remote Start': '121', 'Roof Rack': '78', 'Satellite Radio': '175', 'Third Row Seats': '56', 'Tinted Glass': '179', 'USB': '179'}, 'Body Style': {'4D Passenger Van': '62', '4D Sport Utility': '117'}, 'Engine': {'3.5 liters / V6 Cylinder': '179'}, 'Drivetrain': {'AWD': '84', 'FWD': '95'}, 'Fuel Type': {'Gasoline': '179'}},
    (("Transmission", ("6-Speed Manual w/OD", "9-Speed Automatic w/OD")),): {'Model': {'Civic': '3', 'Passport': '21', 'Ridgeline': '36'}, 'Year': {'2025': '42', '2024': '18'}, 'Exterior Color': {'Black': '19', 'Blue': '5', 'Gray': '15', 'Red': '7', 'Silver': '2', 'White': '12'}, 'Interior Color': {'Black': '46', 'Brown': '8', 'Gray': '3', 'Red': '3'}, 'Highlights': {'AUX': '60', 'Bluetooth': '60', 'Climate Control': '60', 'Heated Mirrors': '57', 'Heated Seats': '57', 'Keyless Access': '60', 'Leather Seats': '51', 'Moon Roof': '57', 'Parking Sensors': '36', 'Rear Camera': '60', 'Rear Climate Control': '57', 'Rear Wiper': '21', 'Remote Start': '57', 'Roof Rack': '16', 'Satellite Radio': '54', 'Tinted Glass': '60'}, 'Body Style': {'4D Crew Cab': '36', '4D Sedan': '3', '4D Sport Utility': '21'}, 'Engine': {'1.5 liters / 4 Cylinder': '3', '3.5 liters / V6 Cylinder': '57'}, 'Drivetrain': {'AWD': '57', 'FWD': '3'}, 'Fuel Type': {'Gasoline': '60'}},
    (('Transmission', ('8-Speed Automatic w/OD',)),): {'Make': {'BMW': '2', 'Dodge': '2', 'Ford': '1', 'Jeep': '5', 'Lexus': '4', 'Ram': '2', 'Toyota': '4', 'Volkswagen': '1', 'Volvo': '1'}, 'Model': {'1500': '1', '1500 Classic': '1', '3 Series': '1', 'Camry': '3', 'Challenger': '1', 'Charger': '1', 'Edge': '1', 'Grand Cherokee': '2', 'Grand Cherokee L': '1', 'IS': '1', 'LX': '1', 'RX': '2', 'Sienna': '1', 'Tiguan': '1', 'Wrangler': '2', 'X3': '1', 'XC60': '1'}, 'Year': {'2022': '2', '2021': '5', '2020': '3', '2019': '4', '2018': '4', '2017': '2', '2016': '1', '2015': '1'}, 'Exterior Color': {'Black': '4', 'Blue': '1', 'Gray': '5', 'Red': '4', 'White': '8'}, 'Interior Color': {'Ash': '1', 'Black': '13', 'Ebony': '1', 'Flaxen': '1', 'Global Black': '1', 'Gray': '1', 'Parchment': '1', 'Red': '1', 'Soft Beige': '1', 'Titan Black': '1'}, 'Highlights': {'AUX': '22', 'Bluetooth': '22', 'Climate Control': '16', 'Heated Mirrors': '18', 'Heated Seats': '13', 'Keyless Access': '22', 'Leather Seats': '6', 'Moon Roof': '6', 'Navigation': '3', 'Panoramic Roof': '2', 'Parking Sensors': '7', 'Rear Camera': '20', 'Rear Climate Control': '4', 'Rear Wiper': '13', 'Remote Start': '8', 'Roof Rack': '7', 'Satellite Radio': '19', 'Sun Roof': '4', 'Tinted Glass': '22', 'USB': '15', 'Xenon Headlights': '1'}, 'Body Style': {'2D Coupe': '1', '4D Crew Cab': '1', '4D Passenger Van': '1', '4D Quad Cab': '1', '4D Sedan': '6', '4D Sport Utility': '12'}, 'Engine': {'2.0 liters / 4 Cylinder': '5', '2.5 liters / 4 Cylinder': '3', '3.0 liters / Straight 6 Cylinder': '1', '3.5 liters / V6 Cylinder': '3', '3.6 liters / V6 Cylinder': '6', '5.7 liters / 8 Cylinder': '3', '6.4 liters / 8 Cylinder': '1'}, 'Drivetrain': {'4x4': '6', 'AWD': '4', 'FWD': '6', 'RWD': '6'}, 'Fuel Type': {'Gasoline': '22'}},
    (('Transmission', ('8-Speed Automatic w/OD', '6-Speed Manual')),): {'Make': {'BMW': '2', 'Dodge': '2', 'Ford': '3', 'Jeep': '5', 'Lexus': '4', 'Ram': '2', 'Toyota': '4', 'Volkswagen': '1', 'Volvo': '1'}, 'Model': {'1500': '1', '1500 Classic': '1', '3 Series': '1', 'Camry': '3', 'Challenger': '1', 'Charger': '1', 'Edge': '1', 'Grand Cherokee': '2', 'Grand Cherokee L': '1', 'IS': '1', 'LX': '1', 'Mustang': '2', 'RX': '2', 'Sienna': '1', 'Tiguan': '1', 'Wrangler': '2', 'X3': '1', 'XC60': '1'}, 'Year': {'2022': '2', '2021': '5', '2020': '3', '2019': '5', '2018': '4', '2017': '3', '2016': '1', '2015': '1'}, 'Exterior Color': {'Black': '5', 'Blue': '1', 'Gray': '5', 'Red': '4', 'White': '9'}, 'Interior Color': {'Ash': '1', 'Black': '13', 'Ebony': '3', 'Flaxen': '1', 'Global Black': '1', 'Gray': '1', 'Parchment': '1', 'Red': '1', 'Soft Beige': '1', 'Titan Black': '1'}, 'Highlights': {'AUX': '24', 'Bluetooth': '24', 'Climate Control': '16', 'Heated Mirrors': '18', 'Heated Seats': '13', 'Keyless Access': '24', 'Leather Seats': '6', 'Moon Roof': '6', 'Navigation': '3', 'Panoramic Roof': '2', 'Parking Sensors': '7', 'Rear Camera': '22', 'Rear Climate Control': '4', 'Rear Wiper': '13', 'Remote Start': '8', 'Roof Rack': '7', 'Satellite Radio': '20', 'Sun Roof': '4', 'Tinted Glass': '24', 'USB': '17', 'Xenon Headlights': '1'}, 'Body Style': {'2D Coupe': '3', '4D Crew Cab': '1', '4D Passenger Van': '1', '4D Quad Cab': '1', '4D Sedan': '6', '4D Sport Utility': '12'}, 'Engine': {'2.0 liters / 4 Cylinder': '5', '2.3 liters / 4 Cylinder': '1', '2.5 liters / 4 Cylinder': '3', '3.0 liters / Straight 6 Cylinder': '1', '3.5 liters / V6 Cylinder': '3', '3.6 liters / V6 Cylinder': '6', '5.0 liters / 8 Cylinder': '1', '5.7 liters / 8 Cylinder': '3', '6.4 liters / 8 Cylinder': '1'}, 'Drivetrain': {'4x4': '6', 'AWD': '4', 'FWD': '6', 'RWD': '8'}, 'Fuel Type': {'Gasoline': '24'}},
    (('Transmission', ('1-Speed CVT w/OD',)),): {'Model': {'CR-V': '1', 'Civic': '3'}, 'Year': {'2024': '1', '2023': '1', '2022': '2'}, 'Exterior Color': {'Black': '3', 'Blue': '1'}, 'Interior Color': {'Black': '4'}, 'Highlights': {'AUX': '4', 'Bluetooth': '4', 'Climate Control': '4', 'Heated Mirrors': '1', 'Heated Seats': '1', 'Keyless Access': '4', 'Moon Roof': '1', 'Rear Camera': '4', 'Rear Wiper': '2', 'Remote Start': '3', 'Satellite Radio': '4', 'Tinted Glass': '4', 'USB': '3'}, 'Body Style': {'4D Hatchback': '1', '4D Sedan': '2', '4D Sport Utility': '1'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '3'}, 'Drivetrain': {'FWD': '4'}, 'Fuel Type': {'Gasoline': '4'}},
    (('Transmission', ('9-Speed Automatic w/OD', '10-Speed Automatic w/OD')),): {'Model': {'Accord': '2', 'Odyssey': '2', 'Pilot': '2'}, 'Year': {'2022': '3', '2021': '1', '2020': '1', '2019': '1'}, 'Exterior Color': {'Black': '2', 'Gray': '2', 'Red': '1', 'Silver': '1'}, 'Interior Color': {'Beige': '1', 'Black': '4', 'Gray': '1'}, 'Highlights': {'AUX': '6', 'Bluetooth': '6', 'Climate Control': '6', 'Heated Mirrors': '6', 'Heated Seats': '6', 'Keyless Access': '6', 'Leather Seats': '5', 'Moon Roof': '5', 'Parking Sensors': '3', 'Rear Camera': '6', 'Rear Climate Control': '4', 'Rear Wiper': '4', 'Remote Start': '5', 'Roof Rack': '2', 'Satellite Radio': '6', 'Sun Roof': '3', 'Tinted Glass': '6', 'USB': '6'}, 'Body Style': {'4D Passenger Van': '2', '4D Sedan': '2', '4D Sport Utility': '2'}, 'Engine': {'2.0 liters / 4 Cylinder': '2', '3.5 liters / V6 Cylinder': '4'}, 'Drivetrain': {'FWD': '6'}, 'Fuel Type': {'Gasoline': '6'}},
    (('Transmission', ('9-Speed Automatic w/OD',)),): {'Model': {'Passport': '21', 'Ridgeline': '36'}, 'Year': {'2025': '39', '2024': '18'}, 'Exterior Color': {'Black': '17', 'Blue': '5', 'Gray': '15', 'Red': '7', 'Silver': '2', 'White': '11'}, 'Interior Color': {'Black': '46', 'Brown': '8', 'Gray': '3'}, 'Highlights': {'AUX': '57', 'Bluetooth': '57', 'Climate Control': '57', 'Heated Mirrors': '54', 'Heated Seats': '54', 'Keyless Access': '57', 'Leather Seats': '51', 'Moon Roof': '54', 'Parking Sensors': '36', 'Rear Camera': '57', 'Rear Climate Control': '57', 'Rear Wiper': '21', 'Remote Start': '57', 'Roof Rack': '16', 'Satellite Radio': '54', 'Tinted Glass': '57'}, 'Body Style': {'4D Crew Cab': '36', '4D Sport Utility': '21'}, 'Engine': {'3.5 liters / V6 Cylinder': '57'}, 'Drivetrain': {'AWD': '57'}, 'Fuel Type': {'Gasoline': '57'}},
    (("Drivetrain", ("AWD",)),): {'Model': {'CR-V': '37', 'CR-V Hybrid': '47', 'HR-V': '4', 'Passport': '21', 'Pilot': '84', 'Prologue': '50', 'Ridgeline': '36'}, 'Year': {'2025': '210', '2024': '69'}, 'Exterior Color': {'Black': '54', 'Blue': '57', 'Gray': '49', 'Red': '29', 'Silver': '21', 'White': '69'}, 'Interior Color': {'Black': '210', 'Brown': '12', 'Gray': '57'}, 'Highlights': {'AUX': '279', 'Bluetooth': '279', 'Climate Control': '279', 'Heated Mirrors': '268', 'Heated Seats': '268', 'Keyless Access': '279', 'Leather Seats': '199', 'Moon Roof': '236', 'Panoramic Roof': '104', 'Parking Sensors': '203', 'Rear Camera': '279', 'Rear Climate Control': '141', 'Rear Wiper': '243', 'Remote Start': '247', 'Roof Rack': '124', 'Satellite Radio': '235', 'Third Row Seats': '28', 'Tinted Glass': '279', 'USB': '84'}, 'Body Style': {'4D Crew Cab': '36', '4D Sport Utility': '243'}, 'Engine': {'1.5 liters / 4 Cylinder': '37', '2.0 liters / 4 Cylinder': '51', '3.5 liters / V6 Cylinder': '141', 'Electric Motor': '50'}, 'Transmission': {'1-Speed Automatic': '50', '1-Speed CVT w/OD': '88', '10-Speed Automatic w/OD': '84', '9-Speed Automatic w/OD': '57'}, 'Fuel Type': {'Electric': '50', 'Gasoline': '182', 'Hybrid': '47'}},
    (("Drivetrain", ("AWD", "FWD")),): {'Model': {'Accord': '38', 'Accord Hybrid': '76', 'CR-V': '87', 'CR-V Hybrid': '104', 'Civic': '50', 'HR-V': '35', 'Odyssey': '62', 'Passport': '21', 'Pilot': '117', 'Prologue': '72', 'Ridgeline': '36'}, 'Year': {'2025': '542', '2024': '156'}, 'Exterior Color': {'Black': '106', 'Blue': '109', 'Brown': '1', 'Gray': '147', 'Green': '7', 'Red': '82', 'Silver': '74', 'White': '172'}, 'Interior Color': {'Black': '542', 'Brown': '13', 'Gray': '140', 'Red': '3'}, 'Highlights': {'AUX': '698', 'Bluetooth': '698', 'Climate Control': '698', 'Heated Mirrors': '606', 'Heated Seats': '599', 'Keyless Access': '698', 'Leather Seats': '453', 'Moon Roof': '536', 'Panoramic Roof': '128', 'Parking Sensors': '366', 'Rear Camera': '698', 'Rear Climate Control': '236', 'Rear Wiper': '520', 'Remote Start': '548', 'Roof Rack': '198', 'Satellite Radio': '410', 'Third Row Seats': '56', 'Tinted Glass': '698', 'USB': '181'}, 'Body Style': {'4D Crew Cab': '36', '4D Hatchback': '22', '4D Passenger Van': '62', '4D Sedan': '142', '4D Sport Utility': '436'}, 'Engine': {'1.5 liters / 4 Cylinder': '129', '2.0 liters / 4 Cylinder': '261', '3.5 liters / V6 Cylinder': '236', 'Electric Motor': '72'}, 'Transmission': {'1-Speed Automatic': '72', '1-Speed CVT w/OD': '387', '10-Speed Automatic w/OD': '179', '6-Speed Manual w/OD': '3', '9-Speed Automatic w/OD': '57'}, 'Fuel Type': {'Electric': '72', 'Gasoline': '446', 'Hybrid': '180'}},
    (('Drivetrain', ('4x4',)),): {'Make': {'Chevrolet': '1', 'Ford': '1', 'GMC': '3', 'Jeep': '6', 'Lexus': '2', 'Nissan': '1', 'Ram': '2', 'Toyota': '2'}, 'Model': {'1500': '1', '1500 Classic': '1', 'Bronco': '1', 'GX': '1', 'Grand Cherokee L': '1', 'LX': '1', 'New Compass': '1', 'Sierra 1500': '1', 'Silverado 1500': '1', 'Titan XD': '1', 'Tundra': '2', 'Wrangler': '4', 'Yukon XL': '2'}, 'Year': {'2023': '4', '2022': '1', '2021': '2', '2020': '1', '2019': '2', '2018': '3', '2017': '2', '2016': '1', '2012': '1', '2011': '1'}, 'Exterior Color': {'Black': '6', 'Blue': '1', 'Gray': '3', 'Red': '2', 'Silver': '1', 'White': '4', 'Yellow': '1'}, 'Interior Color': {'Black': '7', 'Black Onyx': '1', 'Boulder': '1', 'Brown': '1', 'Dune': '1', 'Global Black': '1', 'Gray': '1', 'Jet Black': '1', 'Light Shale': '2', 'Parchment': '1', 'Sepia': '1'}, 'Highlights': {'AUX': '18', 'Bluetooth': '18', 'Climate Control': '11', 'Heated Mirrors': '18', 'Heated Seats': '11', 'Keyless Access': '18', 'Leather Seats': '8', 'Moon Roof': '4', 'Navigation': '2', 'Panoramic Roof': '1', 'Parking Sensors': '5', 'Rear Camera': '16', 'Rear Climate Control': '4', 'Rear Wiper': '11', 'Remote Start': '5', 'Roof Rack': '6', 'Satellite Radio': '17', 'Sun Roof': '5', 'Third Row Seats': '2', 'Tinted Glass': '18', 'USB': '12'}, 'Body Style': {'2D Sport Utility': '1', '4D Crew Cab': '4', '4D CrewMax': '2', '4D Quad Cab': '1', '4D Sport Utility': '10'}, 'Engine': {'2.3 liters / 4 Cylinder': '1', '2.4 liters / 4 Cylinder': '1', '3.4 liters / V6 Cylinder': '2', '3.6 liters / V6 Cylinder': '5', '4.6 liters / 8 Cylinder': '1', '5.3 liters / 8 Cylinder': '2', '5.6 liters / 8 Cylinder': '1', '5.7 liters / 8 Cylinder': '3', '6.2 liters / 8 Cylinder': '2'}, 'Transmission': {'10-Speed Automatic': '2', '10-Speed Automatic w/OD': '2', '5-Speed Automatic': '1', '5-Speed Automatic w/OD': '1', '6-Speed Automatic': '3', '7-Speed Manual': '1', '8-Speed Automatic w/OD': '6', '9-Speed Automatic w/OD': '2'}, 'Fuel Type': {'Gasoline': '18'}},
    (('Drivetrain', ('4x4', 'RWD')),): {'Make': {'BMW': '1', 'Cadillac': '2', 'Chevrolet': '3', 'Dodge': '2', 'Ford': '7', 'GMC': '4', 'INFINITI': '2', 'Jeep': '8', 'Lexus': '3', 'Mercedes-Benz': '1', 'Nissan': '1', 'Ram': '2', 'Tesla': '1', 'Toyota': '6'}, 'Model': {'1500': '1', '1500 Classic': '1', '3 Series': '1', '4Runner': '1', 'Bronco': '1', 'Challenger': '1', 'Charger': '1', 'EQS': '1', 'Escalade': '2', 'Expedition': '1', 'Expedition Max': '2', 'F-150': '1', 'G37': '1', 'GX': '1', 'Grand Cherokee': '2', 'Grand Cherokee L': '1', 'IS': '1', 'LX': '1', 'Model Y': '1', 'Mustang': '2', 'New Compass': '1', 'QX80': '1', 'Sequoia': '1', 'Sierra 1500': '1', 'Silverado 1500': '1', 'Tacoma': '2', 'Tahoe': '2', 'Titan XD': '1', 'Tundra': '2', 'Wrangler': '4', 'Yukon': '1', 'Yukon XL': '2'}, 'Year': {'2024': '1', '2023': '6', '2022': '4', '2021': '5', '2020': '2', '2019': '6', '2018': '6', '2017': '6', '2016': '2', '2013': '1', '2012': '1', '2011': '1', '2010': '1', '2007': '1'}, 'Exterior Color': {'Black': '11', 'Blue': '2', 'Gray': '7', 'Red': '3', 'Silver': '3', 'White': '16', 'Yellow': '1'}, 'Interior Color': {'Black': '14', 'Black Onyx': '1', 'Boulder': '1', 'Brown': '1', 'Cement': '2', 'Cocoa Accents': '1', 'Dune': '1', 'Ebony': '5', 'Flaxen': '1', 'Global Black': '1', 'Graphite': '1', 'Gray': '2', 'Jet Black': '5', 'Light Shale': '2', 'Parchment': '1', 'Sand Beige': '1', 'Sepia': '1', 'Stone': '1', 'Wheat': '1'}, 'Highlights': {'AUX': '43', 'Bluetooth': '42', 'Climate Control': '31', 'Heated Mirrors': '39', 'Heated Seats': '26', 'Keyless Access': '42', 'Leather Seats': '17', 'Moon Roof': '13', 'Navigation': '3', 'Panoramic Roof': '4', 'Parking Sensors': '15', 'Rear Camera': '40', 'Rear Climate Control': '15', 'Rear Wiper': '25', 'Remote Start': '13', 'Roof Rack': '17', 'Satellite Radio': '37', 'Sun Roof': '13', 'Third Row Seats': '5', 'Tinted Glass': '42', 'USB': '30', 'Xenon Headlights': '2'}, 'Body Style': {'2D Convertible': '1', '2D Coupe': '3', '2D Sport Utility': '1', '4D Crew Cab': '4', '4D CrewMax': '2', '4D Double Cab': '2', '4D Quad Cab': '1', '4D Sedan': '3', '4D Sport Utility': '25', '4D SuperCrew': '1'}, 'Engine': {'2.0 liters / 4 Cylinder': '1', '2.3 liters / 4 Cylinder': '2', '2.4 liters / 4 Cylinder': '1', '2.7 liters / 4 Cylinder': '1', '3.0 liters / Straight 6 Cylinder': '1', '3.4 liters / V6 Cylinder': '2', '3.5 liters / V6 Cylinder': '5', '3.6 liters / V6 Cylinder': '8', '3.7 liters / V6 Cylinder': '1', '4.0 liters / V6 Cylinder': '1', '4.6 liters / 8 Cylinder': '1', '5.0 liters / 8 Cylinder': '1', '5.3 liters / 8 Cylinder': '3', '5.6 liters / 8 Cylinder': '2', '5.7 liters / 8 Cylinder': '4', '6.2 liters / 8 Cylinder': '6', '6.4 liters / 8 Cylinder': '1', 'Electric Motor': '2'}, 'Transmission': {'1-Speed Automatic': '2', '10-Speed Automatic': '3', '10-Speed Automatic w/OD': '5', '5-Speed Automatic': '1', '5-Speed Automatic w/OD': '2', '6-Speed Automatic': '5', '6-Speed Automatic w/OD': '3', '6-Speed Manual': '2', '7-Speed Automatic w/OD': '2', '7-Speed Manual': '1', '8-Speed Automatic': '3', '8-Speed Automatic w/OD': '12', '9-Speed Automatic w/OD': '2'}, 'Fuel Type': {'Electric': '2', 'Gasoline': '41'}},
    (('Drivetrain', ('FWD',)),): {'Model': {'Accord': '2', 'CR-V': '1', 'Civic': '3', 'Odyssey': '2', 'Pilot': '3'}, 'Year': {'2024': '1', '2023': '1', '2022': '5', '2021': '1', '2020': '2', '2019': '1'}, 'Exterior Color': {'Black': '5', 'Blue': '1', 'Gray': '2', 'Red': '1', 'Silver': '2'}, 'Interior Color': {'Beige': '1', 'Black': '8', 'Gray': '2'}, 'Highlights': {'AUX': '11', 'Bluetooth': '11', 'Climate Control': '11', 'Heated Mirrors': '8', 'Heated Seats': '8', 'Keyless Access': '11', 'Leather Seats': '5', 'Moon Roof': '6', 'Parking Sensors': '3', 'Rear Camera': '11', 'Rear Climate Control': '5', 'Rear Wiper': '7', 'Remote Start': '9', 'Roof Rack': '2', 'Satellite Radio': '11', 'Sun Roof': '3', 'Tinted Glass': '11', 'USB': '10'}, 'Body Style': {'4D Hatchback': '1', '4D Passenger Van': '2', '4D Sedan': '4', '4D Sport Utility': '4'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '5', '3.5 liters / V6 Cylinder': '5'}, 'Transmission': {'1-Speed CVT w/OD': '4', '10-Speed Automatic w/OD': '3', '6-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '3'}, 'Fuel Type': {'Gasoline': '11'}},
    (("Fuel Type", ("Hybrid",)),): {'Model': {'Accord Hybrid': '76', 'CR-V Hybrid': '104'}, 'Year': {'2025': '129', '2024': '51'}, 'Exterior Color': {'Black': '14', 'Blue': '26', 'Gray': '44', 'Red': '20', 'Silver': '29', 'White': '47'}, 'Interior Color': {'Black': '169', 'Gray': '11'}, 'Highlights': {'AUX': '180', 'Bluetooth': '180', 'Climate Control': '180', 'Heated Mirrors': '159', 'Heated Seats': '174', 'Keyless Access': '180', 'Leather Seats': '123', 'Moon Roof': '180', 'Parking Sensors': '54', 'Rear Camera': '180', 'Rear Wiper': '104', 'Remote Start': '166', 'Roof Rack': '104', 'Satellite Radio': '62', 'Tinted Glass': '180'}, 'Body Style': {'4D Sedan': '76', '4D Sport Utility': '104'}, 'Engine': {'2.0 liters / 4 Cylinder': '180'}, 'Transmission': {'1-Speed CVT w/OD': '180'}, 'Drivetrain': {'AWD': '47', 'FWD': '133'}},
    (("Fuel Type", ("Electric", "Hybrid")),): {'Model': {'Accord Hybrid': '76', 'CR-V Hybrid': '104', 'Prologue': '72'}, 'Year': {'2025': '129', '2024': '123'}, 'Exterior Color': {'Black': '22', 'Blue': '50', 'Gray': '49', 'Red': '31', 'Silver': '35', 'White': '65'}, 'Interior Color': {'Black': '221', 'Brown': '2', 'Gray': '29'}, 'Highlights': {'AUX': '252', 'Bluetooth': '252', 'Climate Control': '252', 'Heated Mirrors': '231', 'Heated Seats': '246', 'Keyless Access': '252', 'Leather Seats': '177', 'Moon Roof': '234', 'Panoramic Roof': '54', 'Parking Sensors': '126', 'Rear Camera': '252', 'Rear Wiper': '176', 'Remote Start': '238', 'Roof Rack': '104', 'Satellite Radio': '134', 'Tinted Glass': '252'}, 'Body Style': {'4D Sedan': '76', '4D Sport Utility': '176'}, 'Engine': {'2.0 liters / 4 Cylinder': '180', 'Electric Motor': '72'}, 'Transmission': {'1-Speed Automatic': '72', '1-Speed CVT w/OD': '180'}, 'Drivetrain': {'AWD': '97', 'FWD': '155'}},
    (('Fuel Type', ('Gasoline',)),): {'Make': {'Audi': '4', 'BMW': '3', 'Buick': '2', 'Cadillac': '4', 'Chevrolet': '7', 'Chrysler': '1', 'Dodge': '2', 'Ford': '11', 'GMC': '5', 'Honda': '48', 'Hyundai': '2', 'INFINITI': '3', 'Jeep': '9', 'Kia': '1', 'Lexus': '10', 'Mazda': '2', 'Nissan': '2', 'Ram': '2', 'Subaru': '2', 'Toyota': '16', 'Volkswagen': '2', 'Volvo': '1'}, 'Model': {'1500': '1', '1500 Classic': '1', '3 Series': '1', '4Runner': '1', 'Accord': '11', 'Ascent': '1', 'Avalon': '1', 'Bronco': '1', 'CR-V': '8', 'CX-5': '1', 'Camry': '4', 'Challenger': '1', 'Charger': '1', 'Civic': '9', 'Civic Type R': '1', 'Corolla': '2', 'Cruze': '1', 'ES': '3', 'Edge': '1', 'Enclave': '2', 'Equinox': '1', 'Escalade': '2', 'Expedition': '1', 'Expedition Max': '2', 'Explorer': '3', 'F-150': '1', 'G37': '1', 'GX': '1', 'Grand Cherokee': '2', 'Grand Cherokee L': '1', 'IS': '1', 'Insight': '1', 'Jetta': '1', 'LX': '1', 'Mazda6': '1', 'Mustang': '2', 'NX': '1', 'New Compass': '1', 'Odyssey': '8', 'Outback': '1', 'Pacifica': '1', 'Pathfinder': '1', 'Pilot': '8', 'Q3': '1', 'Q5': '2', 'Q7': '1', 'QX60': '1', 'QX80': '1', 'RAV4': '1', 'RX': '3', 'Renegade': '1', 'Ridgeline': '2', 'SRX': '1', 'Sequoia': '1', 'Sienna': '2', 'Sierra 1500': '1', 'Silverado 1500': '1', 'Sonata': '2', 'Sportage': '1', 'Tacoma': '2', 'Tahoe': '2', 'Terrain': '1', 'Tiguan': '1', 'Titan XD': '1', 'Traverse': '2', 'Tundra': '2', 'Wrangler': '4', 'X3': '2', 'XC60': '1', 'XT6': '1', 'Yukon': '1', 'Yukon XL': '2'}, 'Year': {'2025': '2', '2024': '3', '2023': '9', '2022': '16', '2021': '17', '2020': '12', '2019': '13', '2018': '19', '2017': '13', '2016': '7', '2015': '8', '2014': '5', '2013': '6', '2012': '3', '2011': '1', '2010': '2', '2009': '1', '2008': '1', '2007': '1'}, 'Exterior Color': {'Basque Red Pearl Ii': '1', 'Beige': '1', 'Black': '36', 'Blue': '10', 'Brown': '1', 'Gray': '25', 'Grigio Metallic': '1', 'Ice Silver Metallic': '1', 'Maroon': '1', 'Obsidian': '1', 'Orange': '1', 'Red': '15', 'Silver': '11', 'Sunset Bronze Mica': '1', 'Tan': '1', 'White': '30', 'Yellow': '1'}, 'Interior Color': {'Almond': '1', 'Ash': '2', 'Beige': '3', 'Black': '53', 'Black Onyx': '1', 'Boulder': '1', 'Brown': '2', 'Brownstone Accents': '1', 'Cement': '2', 'Charcoal': '1', 'Chestnut Brown': '1', 'Cocoa Accents': '1', 'Creme': '1', 'Dune': '1', 'Ebony': '6', 'Ebony Accents': '2', 'Ebony Black': '1', 'Flaxen': '2', 'Global Black': '1', 'Graphite': '2', 'Gray': '14', 'Ivory': '3', 'Jet Black': '10', 'Light Shale': '2', 'Medium Light Camel': '1', 'Medium Stone': '1', 'Medium Titanium': '1', 'Mocha': '1', 'Moonstone': '1', 'Noble Brown': '1', 'Parchment': '4', 'Red': '2', 'Sand Beige': '1', 'Sepia': '1', 'Slate Black': '1', 'Soft Beige': '1', 'Stone': '1', 'Titan Black': '2', 'Truffle': '1', 'Warm Ivory': '1', 'Wheat': '1'}, 'Highlights': {'AUX': '139', 'Bluetooth': '135', 'Climate Control': '116', 'Heated Mirrors': '103', 'Heated Seats': '81', 'Heated Windshield Washer': '1', 'Keyless Access': '136', 'Leather Seats': '61', 'Moon Roof': '55', 'Navigation': '3', 'Panoramic Roof': '11', 'Parking Sensors': '28', 'Rear Camera': '124', 'Rear Climate Control': '49', 'Rear Wiper': '85', 'Remote Start': '52', 'Roof Rack': '48', 'Satellite Radio': '114', 'Sun Roof': '31', 'Third Row Seats': '6', 'Tinted Glass': '130', 'USB': '108', 'Xenon Headlights': '8'}, 'Body Style': {'2D Convertible': '1', '2D Coupe': '4', '2D Sport Utility': '1', '4D Crew Cab': '6', '4D CrewMax': '2', '4D Double Cab': '2', '4D Hatchback': '2', '4D Passenger Van': '11', '4D Quad Cab': '1', '4D Sedan': '37', '4D Sport Utility': '71', '4D SuperCrew': '1'}, 'Engine': {'1.5 liters / 4 Cylinder': '13', '1.8 liters / 4 Cylinder': '4', '2.0 liters / 4 Cylinder': '21', '2.3 liters / 4 Cylinder': '3', '2.4 liters / 4 Cylinder': '12', '2.5 liters / 4 Cylinder': '7', '2.7 liters / 4 Cylinder': '1', '3.0 liters / Straight 6 Cylinder': '1', '3.0 liters / V6 Cylinder': '1', '3.2 liters / V6 Cylinder': '1', '3.4 liters / V6 Cylinder': '2', '3.5 liters / V6 Cylinder': '36', '3.6 liters / V6 Cylinder': '15', '3.7 liters / V6 Cylinder': '1', '4 2.4 Intercooled Turbo Regular Unleaded H-4 2.4 L/146': '1', '4 2.5 Regular Unleaded H-4 2.5 L/152': '1', '4.0 liters / V6 Cylinder': '1', '4.6 liters / 8 Cylinder': '1', '5.0 liters / 8 Cylinder': '1', '5.3 liters / 8 Cylinder': '3', '5.6 liters / 8 Cylinder': '2', '5.7 liters / 8 Cylinder': '4', '6.2 liters / 8 Cylinder': '6', '6.4 liters / 8 Cylinder': '1'}, 'Transmission': {'1-Speed CVT w/OD': '27', '10-Speed Automatic': '3', '10-Speed Automatic w/OD': '12', '5-Speed': '1', '5-Speed Automatic': '4', '5-Speed Automatic w/OD': '3', '6-Speed Automatic': '10', '6-Speed Automatic w/OD': '25', '6-Speed Manual': '2', '6-Speed Manual w/OD': '1', '7-Speed Automatic w/OD': '2', '7-Speed Manual': '1', '7-Speed S tronic Dual-Clutch Auto': '1', '8-Speed Automatic': '4', '8-Speed Automatic w/OD': '22', '8-Speed CVT w/OD': '2', '8-speed Tiptronic automatic': '1', '9-Speed Automatic': '6', '9-Speed Automatic w/OD': '11', 'Continuously variable': '1'}, 'Drivetrain': {'4x4': '18', 'AWD': '15', 'FWD': '79', 'FrontTrak': '1', 'RWD': '23', 'quattro': '3'}},
    (('Fuel Type', ('Gasoline', 'Hybrid')),): {'Make': {'Audi': '4', 'BMW': '3', 'Buick': '2', 'Cadillac': '4', 'Chevrolet': '7', 'Chrysler': '1', 'Dodge': '2', 'Ford': '13', 'GMC': '5', 'Honda': '49', 'Hyundai': '2', 'INFINITI': '3', 'Jeep': '9', 'Kia': '1', 'Lexus': '10', 'Mazda': '2', 'Nissan': '2', 'Ram': '2', 'Subaru': '2', 'Toyota': '18', 'Volkswagen': '2', 'Volvo': '1'}, 'Model': {'1500': '1', '1500 Classic': '1', '3 Series': '1', '4Runner': '1', 'Accord': '11', 'Ascent': '1', 'Avalon': '1', 'Bronco': '1', 'C-Max Energi': '1', 'CR-V': '8', 'CR-V Hybrid': '1', 'CX-5': '1', 'Camry': '4', 'Challenger': '1', 'Charger': '1', 'Civic': '9', 'Civic Type R': '1', 'Corolla': '2', 'Cruze': '1', 'ES': '3', 'Edge': '1', 'Enclave': '2', 'Equinox': '1', 'Escalade': '2', 'Expedition': '1', 'Expedition Max': '2', 'Explorer': '3', 'F-150': '1', 'Fusion Hybrid': '1', 'G37': '1', 'GX': '1', 'Grand Cherokee': '2', 'Grand Cherokee L': '1', 'IS': '1', 'Insight': '1', 'Jetta': '1', 'LX': '1', 'Mazda6': '1', 'Mustang': '2', 'NX': '1', 'New Compass': '1', 'Odyssey': '8', 'Outback': '1', 'Pacifica': '1', 'Pathfinder': '1', 'Pilot': '8', 'Q3': '1', 'Q5': '2', 'Q7': '1', 'QX60': '1', 'QX80': '1', 'RAV4': '1', 'RAV4 Hybrid': '2', 'RX': '3', 'Renegade': '1', 'Ridgeline': '2', 'SRX': '1', 'Sequoia': '1', 'Sienna': '2', 'Sierra 1500': '1', 'Silverado 1500': '1', 'Sonata': '2', 'Sportage': '1', 'Tacoma': '2', 'Tahoe': '2', 'Terrain': '1', 'Tiguan': '1', 'Titan XD': '1', 'Traverse': '2', 'Tundra': '2', 'Wrangler': '4', 'X3': '2', 'XC60': '1', 'XT6': '1', 'Yukon': '1', 'Yukon XL': '2'}, 'Year': {'2025': '2', '2024': '3', '2023': '9', '2022': '18', '2021': '18', '2020': '12', '2019': '14', '2018': '19', '2017': '14', '2016': '7', '2015': '8', '2014': '5', '2013': '6', '2012': '3', '2011': '1', '2010': '2', '2009': '1', '2008': '1', '2007': '1'}, 'Exterior Color': {'Basque Red Pearl Ii': '1', 'Beige': '1', 'Black': '37', 'Blue': '10', 'Brown': '1', 'Gray': '26', 'Grigio Metallic': '1', 'Ice Silver Metallic': '1', 'Maroon': '1', 'Obsidian': '1', 'Orange': '1', 'Red': '15', 'Silver': '13', 'Sunset Bronze Mica': '1', 'Tan': '1', 'White': '30', 'Yellow': '1'}, 'Interior Color': {'Almond': '1', 'Ash': '2', 'Beige': '3', 'Black': '55', 'Black Onyx': '1', 'Boulder': '1', 'Brown': '2', 'Brownstone Accents': '1', 'Cement': '2', 'Charcoal': '1', 'Chestnut Brown': '1', 'Cocoa Accents': '1', 'Creme': '1', 'Dune': '1', 'Ebony': '7', 'Ebony Accents': '2', 'Ebony Black': '1', 'Flaxen': '2', 'Global Black': '1', 'Graphite': '2', 'Gray': '14', 'Ivory': '3', 'Jet Black': '10', 'Light Shale': '2', 'Medium Light Camel': '1', 'Medium Light Stone': '1', 'Medium Stone': '1', 'Medium Titanium': '1', 'Mocha': '1', 'Moonstone': '1', 'Noble Brown': '1', 'Parchment': '4', 'Red': '2', 'Sand Beige': '1', 'Sepia': '1', 'Slate Black': '1', 'Soft Beige': '1', 'Stone': '1', 'Titan Black': '2', 'Truffle': '1', 'Warm Ivory': '1', 'Wheat': '1'}, 'Highlights': {'AUX': '144', 'Bluetooth': '140', 'Climate Control': '121', 'Heated Mirrors': '106', 'Heated Seats': '82', 'Heated Windshield Washer': '1', 'Keyless Access': '141', 'Leather Seats': '62', 'Moon Roof': '56', 'Navigation': '3', 'Panoramic Roof': '11', 'Parking Sensors': '30', 'Rear Camera': '129', 'Rear Climate Control': '49', 'Rear Wiper': '89', 'Remote Start': '53', 'Roof Rack': '51', 'Satellite Radio': '119', 'Sun Roof': '32', 'Third Row Seats': '6', 'Tinted Glass': '135', 'USB': '113', 'Xenon Headlights': '8'}, 'Body Style': {'2D Convertible': '1', '2D Coupe': '4', '2D Sport Utility': '1', '4D Crew Cab': '6', '4D CrewMax': '2', '4D Double Cab': '2', '4D Hatchback': '3', '4D Passenger Van': '11', '4D Quad Cab': '1', '4D Sedan': '38', '4D Sport Utility': '74', '4D SuperCrew': '1'}, 'Engine': {'1.5 liters / 4 Cylinder': '13', '1.8 liters / 4 Cylinder': '4', '2.0 liters / 4 Cylinder': '24', '2.3 liters / 4 Cylinder': '3', '2.4 liters / 4 Cylinder': '12', '2.5 liters / 4 Cylinder': '9', '2.7 liters / 4 Cylinder': '1', '3.0 liters / Straight 6 Cylinder': '1', '3.0 liters / V6 Cylinder': '1', '3.2 liters / V6 Cylinder': '1', '3.4 liters / V6 Cylinder': '2', '3.5 liters / V6 Cylinder': '36', '3.6 liters / V6 Cylinder': '15', '3.7 liters / V6 Cylinder': '1', '4 2.4 Intercooled Turbo Regular Unleaded H-4 2.4 L/146': '1', '4 2.5 Regular Unleaded H-4 2.5 L/152': '1', '4.0 liters / V6 Cylinder': '1', '4.6 liters / 8 Cylinder': '1', '5.0 liters / 8 Cylinder': '1', '5.3 liters / 8 Cylinder': '3', '5.6 liters / 8 Cylinder': '2', '5.7 liters / 8 Cylinder': '4', '6.2 liters / 8 Cylinder': '6', '6.4 liters / 8 Cylinder': '1'}, 'Transmission': {'1-Speed CVT w/OD': '32', '10-Speed Automatic': '3', '10-Speed Automatic w/OD': '12', '5-Speed': '1', '5-Speed Automatic': '4', '5-Speed Automatic w/OD': '3', '6-Speed Automatic': '10', '6-Speed Automatic w/OD': '25', '6-Speed Manual': '2', '6-Speed Manual w/OD': '1', '7-Speed Automatic w/OD': '2', '7-Speed Manual': '1', '7-Speed S tronic Dual-Clutch Auto': '1', '8-Speed Automatic': '4', '8-Speed Automatic w/OD': '22', '8-Speed CVT w/OD': '2', '8-speed Tiptronic automatic': '1', '9-Speed Automatic': '6', '9-Speed Automatic w/OD': '11', 'Continuously variable': '1'}, 'Drivetrain': {'4x4': '18', 'AWD': '18', 'FWD': '81', 'FrontTrak': '1', 'RWD': '23', 'quattro': '3'}},
    (('Fuel Type', ('Gasoline Fuel',)),): {'Make': {'Buick': '2', 'Cadillac': '4', 'Chevrolet': '37', 'GMC': '4', 'Honda': '3', 'Kia': '24'}, 'Model': {'ATS': '3', 'Acadia': '1', 'CR-V': '1', 'Camaro': '2', 'Captiva Sport': '1', 'Civic Sedan': '1', 'Colorado': '2', 'Corvette': '2', 'Cruze': '7', 'Equinox': '3', 'Forte': '2', 'Impala': '5', 'Impala Limited': '1', 'LaCrosse': '1', 'Malibu': '4', 'Odyssey': '1', 'Optima': '5', 'Rio': '1', 'SRX': '1', 'Sierra 1500': '2', 'Silverado 1500': '5', 'Sorento': '9', 'Soul': '3', 'Sportage': '4', 'Tahoe': '2', 'Terrain': '1', 'Traverse': '1', 'Trax': '2', 'Verano': '1'}, 'Year': {'2022': '1', '2021': '1', '2019': '2', '2018': '7', '2017': '20', '2016': '32', '2015': '6', '2014': '5'}, 'Exterior Color': {'Arctic Blue Metallic': '2', 'Black': '14', 'Blue': '8', 'Brown': '3', 'Gray': '16', 'Purple': '1', 'Red': '6', 'Silver': '3', 'Silver Ice Metallic': '8', 'White': '11', 'Yellow': '2'}, 'Interior Color': {'Beige': '1', 'Black': '23', 'Cocoa Dune': '2', 'Dark Ash Jet Black': '1', 'Dark Gray': '1', 'Ebony': '3', 'Gray': '2', 'Jet Black': '34', 'Jet Black Medium Titanium': '1', 'Kona Brown With Jet Black Accents': '1', 'Light Neutral With Medium Cashmere Accents': '1', 'Light Titanium With Ebony Accents': '1', 'Medium Titanium': '1', 'Mocha': '1', 'Morello Red With Jet Black Accents': '1'}, 'Highlights': {'AUX': '10', 'Bluetooth': '30', 'Climate Control': '37', 'Heated Mirrors': '57', 'Heated Seats': '27', 'Keyless Access': '72', 'Leather Seats': '18', 'Moon Roof': '13', 'Navigation': '1', 'Parking Sensors': '11', 'Rear Camera': '55', 'Rear Climate Control': '10', 'Rear Wiper': '30', 'Remote Start': '13', 'Roof Rack': '22', 'Satellite Radio': '72', 'Sun Roof': '16', 'Tinted Glass': '10', 'USB': '14', 'Xenon Headlights': '1'}, 'Body Style': {'2D Coupe': '5', '2D Standard Cab': '1', '4D Crew Cab': '3', '4D Double Cab': '4', '4D Hatchback': '3', '4D Passenger Van': '1', '4D Sedan': '30', '4D Sport Utility': '26', 'Extended Cab': '1'}, 'Engine': {'1.4 liters / 4 Cylinder': '8', '1.5 liters / 4 Cylinder': '6', '1.6 liters / 4 Cylinder': '3', '1.8 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '9', '2.4 liters / 4 Cylinder': '14', '3.3 liters / V6 Cylinder': '6', '3.5 liters / V6 Cylinder': '1', '3.6 liters / V6 Cylinder': '15', '5.3 liters / 8 Cylinder': '9', '6.2 liters / 8 Cylinder': '2'}, 'Transmission': {'1-Speed CVT w/OD': '2', '10-Speed Automatic w/OD': '1', '6-Speed Automatic': '27', '6-Speed Automatic Electronic with Overdrive': '19', '6-Speed Automatic with Overdrive': '2', '6-Speed Automatic with Sportmatic': '14', '6-Speed Manual': '2', '7-Speed Manual with Active Rev Matching': '2', '8-Speed Automatic': '5'}, 'Drivetrain': {'4x4': '10', 'AWD': '16', 'FWD': '43', 'RWD': '5'}},
    (('Fuel Type', ('Electric',)),): {'Model': {'Prologue': '72'}, 'Year': {'2024': '72'}, 'Exterior Color': {'Black': '8', 'Blue': '24', 'Gray': '5', 'Red': '11', 'Silver': '6', 'White': '18'}, 'Interior Color': {'Black': '52', 'Brown': '2', 'Gray': '18'}, 'Highlights': {'AUX': '72', 'Bluetooth': '72', 'Climate Control': '72', 'Heated Mirrors': '72', 'Heated Seats': '72', 'Keyless Access': '72', 'Leather Seats': '54', 'Moon Roof': '54', 'Panoramic Roof': '54', 'Parking Sensors': '72', 'Rear Camera': '72', 'Rear Wiper': '72', 'Remote Start': '72', 'Satellite Radio': '72', 'Tinted Glass': '72'}, 'Body Style': {'4D Sport Utility': '72'}, 'Engine': {'Electric Motor': '72'}, 'Transmission': {'1-Speed Automatic': '72'}, 'Drivetrain': {'AWD': '50', 'FWD': '22'}},
    #(('New', ("",)),): {'Make': {'Buick': '87', 'Cadillac': '63', 'Chevrolet': '344', 'Dodge': '3', 'GMC': '143', 'Honda': '377', 'Jeep': '31', 'Kia': '209', 'Mitsubishi': '99', 'Ram': '31', 'Subaru': '109'}, 'Model': {'1500': '31', 'ATS': '1', 'Acadia': '38', 'Accord': '94', 'Accord Hybrid': '14', 'Ascent': '22', 'BRZ': '1', 'Blazer': '18', 'Bolt EV': '1', 'CR-V': '32', 'CT6': '1', 'CTS': '6', 'Camaro': '11', 'Canyon': '11', 'Challenger': '2', 'Charger': '1', 'Cherokee': '7', 'Civic': '36', 'Civic Type R': '1', 'Colorado': '12', 'Compass': '3', 'Corvette': '5', 'Crosstrek': '9', 'Cruze': '2', 'Eclipse Cross': '13', 'Enclave': '36', 'Encore': '34', 'Envision': '11', 'Equinox': '65', 'Escalade': '6', 'Escalade ESV': '5', 'Express 2500': '4', 'Express 3500': '7', 'Forester': '13', 'Forte': '37', 'Gladiator': '2', 'Grand Cherokee': '4', 'HR-V': '15', 'Impala': '2', 'Impreza': '15', 'Legacy': '15', 'Malibu': '28', 'Mirage': '5', 'Mirage G4': '15', 'Niro': '20', 'Odyssey': '58', 'Optima': '23', 'Outback': '33', 'Outlander': '35', 'Outlander PHEV': '4', 'Outlander Sport': '27', 'Passport': '28', 'Pilot': '71', 'Regal': '3', 'Regal TourX': '3', 'Renegade': '2', 'Ridgeline': '28', 'Rio': '3', 'Savana 2500': '1', 'Sedona': '5', 'Sierra 1500': '39', 'Sierra 1500 Limited': '7', 'Sierra 2500HD': '7', 'Sierra 3500HD': '7', 'Silverado 1500': '72', 'Silverado 1500 LD': '5', 'Silverado 2500HD': '8', 'Silverado 3500HD': '5', 'Sonic': '4', 'Sorento': '29', 'Soul': '53', 'Spark': '1', 'Sportage': '28', 'Stinger': '11', 'Suburban': '2', 'Tahoe': '5', 'Terrain': '10', 'Traverse': '56', 'Trax': '31', 'WRX': '1', 'Wrangler': '13', 'XT4': '14', 'XT5': '27', 'XTS': '3', 'Yukon': '13', 'Yukon XL': '10'}, 'Year': {'2023': '172', '2022': '205', '2021': '65', '2020': '58', '2019': '932', '2018': '57', '2017': '7'}, 'Exterior Color': {'Black': '350', 'Blue': '96', 'Blue Metallic': '11', 'Brown': '22', 'Cherry': '2', 'Fire': '6', 'Gray': '267', 'Gray Metallic': '4', 'Green': '7', 'Grey': '3', 'Neptune Blue': '1', 'Orange': '12', 'Quartz': '14', 'Quicksilver Metallic': '14', 'Radiant Silver Metallic': '4', 'Red': '140', 'Silver': '98', 'Silver Ice Metallic': '35', 'Sky': '8', 'Steel Metallic': '1', 'White': '401'}, 'Interior Color': {'Adrenaline Red': '2', 'Beige': '49', 'Black': '798', 'Black Gray': '1', 'Black New Saddle': '1', 'Black Red': '6', 'Black Ski Gray': '2', 'Blk Clth Performance Sts': '1', 'Blk Houndstooth Clth Sprt': '1', 'Brandy': '1', 'Brown': '5', 'Camel': '1', 'Ceramic White': '2', 'Charcoal': '14', 'Chestnut': '2', 'Cocoa': '1', 'Cocoa Light Ash Gray': '3', 'Cocoa Shale': '7', 'Cocoa Dark Sand': '4', 'Cocoa Dune': '2', 'D Brown': '1', 'Dark Ash': '2', 'Dark Ash Seats With Jet Black Interior Accents': '6', 'Dark Ash With Jet Black Interior Accents': '12', 'Dark Galvanized': '15', 'Dark Galvanized Light Galvanized': '1', 'Dark Galvanized Sky Gray': '1', 'Dark Walnut Slate': '1', 'Diesel Gray Black': '1', 'Ebony': '34', 'Gray': '62', 'Gray 2 Tone': '1', 'Ivory': '13', 'Java': '8', 'Jet Black': '253', 'Jet Black Mahogany': '1', 'Jet Black Kalahari': '1', 'Jet Black Dark Ash': '4', 'Jet Black Dark Titanium': '3', 'Jet Black Loft Brown': '2', 'Jet Black Medium Ash Gray Piping Stitching': '2', 'Jet Black With Cinnamon Accents': '2', 'Jet Black With Jet Black Accents': '4', 'Jet Black With Kalahari Accents': '2', 'Jet Black With Red Accents': '1', 'Light Neutral': '7', 'Light Platinum Jet Black': '6', 'Maple Sugar With Jet Black Accents': '1', 'Medium Ash Gray': '29', 'Medium Pewter': '12', 'Mocha': '6', 'Red': '1', 'Saddle': '2', 'Sahara Beige': '7', 'Satin Black': '17', 'Seats Front Bucket': '2', 'Shale': '26', 'Shale With Jet Black Accents': '5', 'Ski Gray Black': '1', 'Slate Black': '12', 'Stone Beige': '2', 'Titanium Gray': '1', 'Very Light Cashmere With Jet Black Accents': '3', 'Warm Ivory': '13', 'Whisper Beige': '1'}, 'Highlights': {'AUX': '654', 'Bluetooth': '607', 'Climate Control': '1134', 'Heated Mirrors': '1162', 'Heated Seats': '833', 'Keyless Access': '1485', 'Leather Seats': '333', 'Moon Roof': '402', 'Navigation': '34', 'Panoramic Roof': '12', 'Parking Sensors': '256', 'Rear Camera': '1259', 'Rear Climate Control': '407', 'Rear Wiper': '916', 'Remote Start': '168', 'Roof Rack': '485', 'Satellite Radio': '1082', 'Sun Roof': '464', 'Third Row Seats': '26', 'Tinted Glass': '528', 'USB': '563', 'Xenon Headlights': '2'}, 'Body Style': {'2D Chassis': '5', '2D Convertible': '5', '2D Coupe': '15', '2D Standard Cab': '4', '3D Cargo Van': '7', '4D Crew Cab': '164', '4D Double Cab': '1', '4D Extended Cab': '2', '4D Hatchback': '86', '4D Passenger Van': '63', '4D Sedan': '287', '4D Sport Utility': '782', '4D Wagon': '7', '5D Hatchback': '1', '5D Wagon': '4', 'Double Cab': '63'}, 'Engine': {'1.2 liters / 3 Cylinder': '20', '1.4 liters / 4 Cylinder': '72', '1.5 liters / 4 Cylinder': '231', '1.6 liters / 4 Cylinder': '35', '2.0 liters / 4 Cylinder': '235', '2.4 liters / 4 Cylinder': '94', '2.5 liters / 4 Cylinder': '28', '3.0 liters / V6 Cylinder': '7', '3.2 liters / V6 Cylinder': '5', '3.3 liters / V6 Cylinder': '29', '3.5 liters / V6 Cylinder': '185', '3.6 liters / V6 Cylinder': '201', '4 2': '26', '4 2.4': '22', '4 2.5': '56', '4.3 liters / V6 Cylinder': '8', '5.3 liters / 8 Cylinder': '111', '5.7 liters / 8 Cylinder': '32', '6 3.6': '5', '6.0 liters / 8 Cylinder': '22', '6.2 liters / 8 Cylinder': '54', '6.6 liters / 8 Cylinder': '17', 'Electric Motor': '1'}, 'Transmission': {'1-Speed Automatic': '5', '1-Speed CVT w/OD': '173', '10-Speed Automatic': '42', '10-Speed Automatic w/OD': '69', '10-Speed Automatic with Overdrive': '2', '5-Speed Manual': '19', '6-Speed Aisin Automatic': '3', '6-Speed Automatic': '260', '6-Speed Automatic Electronic with Overdrive': '36', '6-Speed Automatic with Sportmatic': '11', '6-Speed Dual Clutch': '20', '6-Speed Manual': '2', '6-Speed Manual w/OD': '8', '7 Speed Dual Clutch DCT': '6', '7-Speed Automatic': '2', '7-Speed Manual': '1', '8 Speed Paddle Shift with Automatic Modes': '4', '8-Speed Automatic': '216', '8-Speed Automatic with Overdrive': '1', '9-Speed 948TE Automatic': '9', '9-Speed Automatic': '150', '9-Speed Automatic w/OD': '127', '9-Speed Automatic with Overdrive': '1', 'Allison 1000 6-Speed Automatic': '17', 'Automatic': '23', 'CVT': '179', 'CVT Lineartronic': '61', 'Close-Ratio 6-Speed Manual': '1', 'Lineartronic CVT': '44', 'Manual': '4'}, 'Drivetrain': {'4x4': '304', 'AWD': '459', 'FWD': '679', 'RWD': '54'}, 'Fuel Type': {'Diesel': '5', 'Diesel Fuel': '17', 'Electric Fuel System': '1', 'Flex Fuel Capability': '13', 'Gas/Electric Hybrid': '20', 'Gasoline': '532', 'Gasoline Fuel': '889', 'Gasoline/Mild Electric Hybrid': '1', 'Hybrid': '14', 'Plug-In Electric/Gas': '4'}},
    (("New", ("",)),): {'Model': {'Accord': '38', 'Accord Hybrid': '76', 'CR-V': '87', 'CR-V Hybrid': '104', 'Civic': '50', 'HR-V': '35', 'Odyssey': '62', 'Passport': '21', 'Pilot': '117', 'Prologue': '72', 'Ridgeline': '36'}, 'Year': {'2025': '542', '2024': '156'}, 'Exterior Color': {'Black': '106', 'Blue': '109', 'Brown': '1', 'Gray': '147', 'Green': '7', 'Red': '82', 'Silver': '74', 'White': '172'}, 'Interior Color': {'Black': '542', 'Brown': '13', 'Gray': '140', 'Red': '3'}, 'Highlights': {'AUX': '698', 'Bluetooth': '698', 'Climate Control': '698', 'Heated Mirrors': '606', 'Heated Seats': '599', 'Keyless Access': '698', 'Leather Seats': '453', 'Moon Roof': '536', 'Panoramic Roof': '128', 'Parking Sensors': '366', 'Rear Camera': '698', 'Rear Climate Control': '236', 'Rear Wiper': '520', 'Remote Start': '548', 'Roof Rack': '198', 'Satellite Radio': '410', 'Third Row Seats': '56', 'Tinted Glass': '698', 'USB': '181'}, 'Body Style': {'4D Crew Cab': '36', '4D Hatchback': '22', '4D Passenger Van': '62', '4D Sedan': '142', '4D Sport Utility': '436'}, 'Engine': {'1.5 liters / 4 Cylinder': '129', '2.0 liters / 4 Cylinder': '261', '3.5 liters / V6 Cylinder': '236', 'Electric Motor': '72'}, 'Transmission': {'1-Speed Automatic': '72', '1-Speed CVT w/OD': '387', '10-Speed Automatic w/OD': '179', '6-Speed Manual w/OD': '3', '9-Speed Automatic w/OD': '57'}, 'Drivetrain': {'AWD': '279', 'FWD': '419'}, 'Fuel Type': {'Electric': '72', 'Gasoline': '446', 'Hybrid': '180'}},
    (('Monthly Payment', ("",)),): {'Make': {'Buick': '87', 'Cadillac': '63', 'Chevrolet': '344', 'Dodge': '3', 'GMC': '143', 'Honda': '377', 'Jeep': '31', 'Kia': '209', 'Mitsubishi': '99', 'Ram': '31', 'Subaru': '109'}, 'Model': {'1500': '31', 'ATS': '1', 'Acadia': '38', 'Accord': '94', 'Accord Hybrid': '14', 'Ascent': '22', 'BRZ': '1', 'Blazer': '18', 'Bolt EV': '1', 'CR-V': '32', 'CT6': '1', 'CTS': '6', 'Camaro': '11', 'Canyon': '11', 'Challenger': '2', 'Charger': '1', 'Cherokee': '7', 'Civic': '36', 'Civic Type R': '1', 'Colorado': '12', 'Compass': '3', 'Corvette': '5', 'Crosstrek': '9', 'Cruze': '2', 'Eclipse Cross': '13', 'Enclave': '36', 'Encore': '34', 'Envision': '11', 'Equinox': '65', 'Escalade': '6', 'Escalade ESV': '5', 'Express 2500': '4', 'Express 3500': '7', 'Forester': '13', 'Forte': '37', 'Gladiator': '2', 'Grand Cherokee': '4', 'HR-V': '15', 'Impala': '2', 'Impreza': '15', 'Legacy': '15', 'Malibu': '28', 'Mirage': '5', 'Mirage G4': '15', 'Niro': '20', 'Odyssey': '58', 'Optima': '23', 'Outback': '33', 'Outlander': '35', 'Outlander PHEV': '4', 'Outlander Sport': '27', 'Passport': '28', 'Pilot': '71', 'Regal': '3', 'Regal TourX': '3', 'Renegade': '2', 'Ridgeline': '28', 'Rio': '3', 'Savana 2500': '1', 'Sedona': '5', 'Sierra 1500': '39', 'Sierra 1500 Limited': '7', 'Sierra 2500HD': '7', 'Sierra 3500HD': '7', 'Silverado 1500': '72', 'Silverado 1500 LD': '5', 'Silverado 2500HD': '8', 'Silverado 3500HD': '5', 'Sonic': '4', 'Sorento': '29', 'Soul': '53', 'Spark': '1', 'Sportage': '28', 'Stinger': '11', 'Suburban': '2', 'Tahoe': '5', 'Terrain': '10', 'Traverse': '56', 'Trax': '31', 'WRX': '1', 'Wrangler': '13', 'XT4': '14', 'XT5': '27', 'XTS': '3', 'Yukon': '13', 'Yukon XL': '10'}, 'Year': {'2023': '172', '2022': '205', '2021': '65', '2020': '58', '2019': '932', '2018': '57', '2017': '7'}, 'Exterior Color': {'Black': '350', 'Blue': '96', 'Blue Metallic': '11', 'Brown': '22', 'Cherry': '2', 'Fire': '6', 'Gray': '267', 'Gray Metallic': '4', 'Green': '7', 'Grey': '3', 'Neptune Blue': '1', 'Orange': '12', 'Quartz': '14', 'Quicksilver Metallic': '14', 'Radiant Silver Metallic': '4', 'Red': '140', 'Silver': '98', 'Silver Ice Metallic': '35', 'Sky': '8', 'Steel Metallic': '1', 'White': '401'}, 'Interior Color': {'Adrenaline Red': '2', 'Beige': '49', 'Black': '798', 'Black Gray': '1', 'Black New Saddle': '1', 'Black Red': '6', 'Black Ski Gray': '2', 'Blk Clth Performance Sts': '1', 'Blk Houndstooth Clth Sprt': '1', 'Brandy': '1', 'Brown': '5', 'Camel': '1', 'Ceramic White': '2', 'Charcoal': '14', 'Chestnut': '2', 'Cocoa': '1', 'Cocoa Light Ash Gray': '3', 'Cocoa Shale': '7', 'Cocoa Dark Sand': '4', 'Cocoa Dune': '2', 'D Brown': '1', 'Dark Ash': '2', 'Dark Ash Seats With Jet Black Interior Accents': '6', 'Dark Ash With Jet Black Interior Accents': '12', 'Dark Galvanized': '15', 'Dark Galvanized Light Galvanized': '1', 'Dark Galvanized Sky Gray': '1', 'Dark Walnut Slate': '1', 'Diesel Gray Black': '1', 'Ebony': '34', 'Gray': '62', 'Gray 2 Tone': '1', 'Ivory': '13', 'Java': '8', 'Jet Black': '253', 'Jet Black Mahogany': '1', 'Jet Black Kalahari': '1', 'Jet Black Dark Ash': '4', 'Jet Black Dark Titanium': '3', 'Jet Black Loft Brown': '2', 'Jet Black Medium Ash Gray Piping Stitching': '2', 'Jet Black With Cinnamon Accents': '2', 'Jet Black With Jet Black Accents': '4', 'Jet Black With Kalahari Accents': '2', 'Jet Black With Red Accents': '1', 'Light Neutral': '7', 'Light Platinum Jet Black': '6', 'Maple Sugar With Jet Black Accents': '1', 'Medium Ash Gray': '29', 'Medium Pewter': '12', 'Mocha': '6', 'Red': '1', 'Saddle': '2', 'Sahara Beige': '7', 'Satin Black': '17', 'Seats Front Bucket': '2', 'Shale': '26', 'Shale With Jet Black Accents': '5', 'Ski Gray Black': '1', 'Slate Black': '12', 'Stone Beige': '2', 'Titanium Gray': '1', 'Very Light Cashmere With Jet Black Accents': '3', 'Warm Ivory': '13', 'Whisper Beige': '1'}, 'Highlights': {'AUX': '654', 'Bluetooth': '607', 'Climate Control': '1134', 'Heated Mirrors': '1162', 'Heated Seats': '833', 'Keyless Access': '1485', 'Leather Seats': '333', 'Moon Roof': '402', 'Navigation': '34', 'Panoramic Roof': '12', 'Parking Sensors': '256', 'Rear Camera': '1259', 'Rear Climate Control': '407', 'Rear Wiper': '916', 'Remote Start': '168', 'Roof Rack': '485', 'Satellite Radio': '1082', 'Sun Roof': '464', 'Third Row Seats': '26', 'Tinted Glass': '528', 'USB': '563', 'Xenon Headlights': '2'}, 'Body Style': {'2D Chassis': '5', '2D Convertible': '5', '2D Coupe': '15', '2D Standard Cab': '4', '3D Cargo Van': '7', '4D Crew Cab': '164', '4D Double Cab': '1', '4D Extended Cab': '2', '4D Hatchback': '86', '4D Passenger Van': '63', '4D Sedan': '287', '4D Sport Utility': '782', '4D Wagon': '7', '5D Hatchback': '1', '5D Wagon': '4', 'Double Cab': '63'}, 'Engine': {'1.2 liters / 3 Cylinder': '20', '1.4 liters / 4 Cylinder': '72', '1.5 liters / 4 Cylinder': '231', '1.6 liters / 4 Cylinder': '35', '2.0 liters / 4 Cylinder': '235', '2.4 liters / 4 Cylinder': '94', '2.5 liters / 4 Cylinder': '28', '3.0 liters / V6 Cylinder': '7', '3.2 liters / V6 Cylinder': '5', '3.3 liters / V6 Cylinder': '29', '3.5 liters / V6 Cylinder': '185', '3.6 liters / V6 Cylinder': '201', '4 2': '26', '4 2.4': '22', '4 2.5': '56', '4.3 liters / V6 Cylinder': '8', '5.3 liters / 8 Cylinder': '111', '5.7 liters / 8 Cylinder': '32', '6 3.6': '5', '6.0 liters / 8 Cylinder': '22', '6.2 liters / 8 Cylinder': '54', '6.6 liters / 8 Cylinder': '17', 'Electric Motor': '1'}, 'Transmission': {'1-Speed Automatic': '4', '10-Speed Automatic': '69', '5-Speed Manual': '20', '6-Speed Aisin F21-250 Gen 3 Auto': '3', '6-Speed Automatic': '5', '6-Speed Automatic w/Sportmatic': '31', '6-Speed Dual-Clutch': '20', '6-Speed Manual': '26', '6-Speed Manual Tremec': '1', '6-Speed Manual w/Rev-Match Control': '1', '7-Speed Dual Clutch': '2', '7-Speed Dual Clutch (DCT)': '6', '8-Speed Automatic (850RE)': '30', '8-Speed Automatic (8HP50)': '3', '8-Speed Automatic (8HP75)': '5', '8-Speed Automatic w/Sportmatic': '23', '8-Speed CVT w/Sport Mode': '12', '9-Speed 948TE Automatic': '9', '9-Speed Automatic': '127', 'CVT Lineartronic': '61', 'Close-Ratio 6-Speed Manual': '1', 'Continuously Variable': '87', 'Continuously Variable (CVT)': '62', 'Continuously Variable (LL-CVT)': '4', 'Continuously Variable (M-CVT)': '36', 'Continuously Variable Automatic': '21', 'Continuously Variable Transmission': '1', 'Continuously Variable w/Sport Mode': '11', 'Electronic 6-Speed Automatic w/OD': '28', 'Electronic Continuously Variable': '14', 'Electronic Precision Shift, electronic transmission range selector with Low selection for throttle off regenerative braking': '1', 'Electronically Controlled 8-Spd Auto': '11', 'Engine, 4.3L EcoTec3 V6 (285 hp [212 kW] @ 5300 rpm, 305 lb-ft of torque [413 Nm] @ 3900 rpm) (Requires (MYC) 6-speed automatic transmission.)': '6', 'Engine, Vortec 6.0L V8 (341 hp [254.3 kW] @ 5400 rpm, 373 lb-ft of torque [503.6 N-m] @ 4200 rpm) (Includes external oil cooler. Requires (MYD) 6-speed heavy-duty automatic transmission. Reference the Engine/Axle page for availability.)': '2', 'Intelligent Variable': '35', 'Intelligent Variable Automatic': '46', 'Lineartronic CVT': '44', 'Transmission, 10-speed automatic': '1', 'Transmission, 10-speed automatic electronically controlled with overdrive, tow/haul mode and tap up/tap down shifting': '25', 'Transmission, 6-speed automatic': '42', 'Transmission, 6-speed automatic (AWD models only and (LGX) 3.6L V6 engine.)': '10', 'Transmission, 6-speed automatic (AWD models only.)': '10', 'Transmission, 6-speed automatic (Included and only available with (LCV) 2.5L engine.)': '1', 'Transmission, 6-speed automatic (Included with (LCV) 2.5L 4-cylinder engine.)': '17', 'Transmission, 6-speed automatic (Standard on Crew Cab Short Box and Extended Cabs models.)': '2', 'Transmission, 6-speed automatic (TNL26 AWD model and (LGX) 3.6L V6 engine.)': '1', 'Transmission, 6-speed automatic electronically controlled': '3', 'Transmission, 6-speed automatic, HMD, 6L50 (Standard on Extended Cab and Crew Cab Short Box models. Requires (LCV) 2.5L I4 engine or (LWN) 2.8L Duramax Turbo-Diesel engine. Available on Crew Cab Long Box models.)': '1', 'Transmission, 6-speed automatic, electronically controlled': '21', 'Transmission, 6-speed automatic, electronically controlled with overdrive and tow/haul mode': '7', 'Transmission, 6-speed automatic, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking': '19', 'Transmission, 6-speed automatic, electronically controlled with overdrive, tow/haul mode and tap up/tap down shifting': '8', 'Transmission, 6-speed automatic, electronically-controlled with Driver Shift Control': '6', 'Transmission, 6-speed automatic, electronically-controlled with overdrive': '1', 'Transmission, 6-speed automatic, electronically-controlled with overdrive includes Driver Shift Control': '92', 'Transmission, 6-speed automatic, heavy-duty electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking (Requires (L96) Vortec 6.0L V8 SFI engine or (LC8) 6.0L V8 SFI Gaseous CNG/LPG capable engine.)': '4', 'Transmission, 6-speed automatic, heavy-duty, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking (Requires (L96) Vortec 6.0L V8 SFI engine or (LC8) 6.0L V8 SFI Gaseous CNG/LPG capable engine.)': '12', 'Transmission, 6-speed automatic, heavy-duty, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking (Requires (L96) Vortec 6.0L V8 SFI engine.)': '7', 'Transmission, 6-speed automatic, heavy-duty, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking. (Requires (L96) Vortec 6.0L V8 SFI engine or (LC8) 6.0L V8 SFI Gaseous CNG/LPG capable engine.)': '1', 'Transmission, 6-speed automatic, heavy-duty, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking. (Requires (L96) Vortec 6.0L V8 SFI engine.)': '3', 'Transmission, 6-speed manual': '4', 'Transmission, 6-speed manual (Includes Active Rev Matching.)': '4', 'Transmission, 7-speed manual with Active Rev Matching': '1', 'Transmission, 7-speed manual with Active Rev Matching with Z51 performance ratios': '4', 'Transmission, 8-speed automatic': '46', 'Transmission, 8-speed automatic (Requires (LGZ) 3.6L DI DOHC V6 engine. Standard on Crew Cab models. Available on Extended Cab models.)': '11', 'Transmission, 8-speed automatic electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking': '3', 'Transmission, 8-speed automatic electronically controlled with overdrive, tow/haul mode and tap up/tap down shifting': '1', 'Transmission, 8-speed automatic includes transmission oil cooler and (BTV) remote vehicle starter system': '3', 'Transmission, 8-speed automatic, electronically controlled with overdrive and tow/haul mode, includes Cruise Grade Braking and Powertrain Grade Braking (Requires (LV1) 4.3L V6 SIDI engine or (LWN) 2.8L Duramax Turbo-Diesel engine.)': '7', 'Transmission, 8-speed automatic, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking': '54', 'Transmission, 8-speed automatic, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking (Included and only available with (L84) 5.3L EcoTec3 V8 engine.)': '22', 'Transmission, 8-speed automatic, electronically controlled with overdrive and tow/haul mode. Includes Cruise Grade Braking and Powertrain Grade Braking (Standard on Crew Cab models.)': '1', 'Transmission, 9-speed automatic': '102', 'Transmission, 9-speed automatic 9T45, electronically-controlled with overdrive (Standard with (LYX) 1.5L Turbo 4-cylinder engine only.)': '9', 'Transmission, 9-speed automatic 9T50, electronically-controlled with overdrive': '1', 'Transmission, 9-speed automatic electronically-controlled': '14', 'Transmission, 9-speed automatic, electronically-controlled with overdrive includes Driver Shift Control': '13', 'Transmission, 9-speed automatic, electronically-controlled with overdrive, includes Driver Shift Control': '12', 'Transmission, Continuous Variable (CVT)': '1', 'Transmission, Continuously Variable (CVT)': '21'}, 'Drivetrain': {'4x4': '304', 'AWD': '459', 'FWD': '679', 'RWD': '54'}, 'Fuel Type': {'Diesel': '5', 'Diesel Fuel': '17', 'Electric Fuel System': '1', 'Flex Fuel Capability': '13', 'Gas/Electric Hybrid': '20', 'Gasoline': '532', 'Gasoline Fuel': '889', 'Gasoline/Mild Electric Hybrid': '1', 'Hybrid': '14', 'Plug-In Electric/Gas': '4'}},
    (('Used', ("",)),): {'Make': {'Audi': '4', 'BMW': '3', 'Buick': '2', 'Cadillac': '4', 'Chevrolet': '7', 'Chrysler': '1', 'Dodge': '2', 'Ford': '13', 'GMC': '5', 'Honda': '49', 'Hyundai': '2', 'INFINITI': '3', 'Jeep': '9', 'Kia': '1', 'Lexus': '10', 'Mazda': '2', 'Mercedes-Benz': '1', 'Nissan': '2', 'Ram': '2', 'Subaru': '2', 'Tesla': '2', 'Toyota': '18', 'Volkswagen': '2', 'Volvo': '1'}, 'Model': {'1500': '1', '1500 Classic': '1', '3 Series': '1', '4Runner': '1', 'Accord': '11', 'Ascent': '1', 'Avalon': '1', 'Bronco': '1', 'C-Max Energi': '1', 'CR-V': '8', 'CR-V Hybrid': '1', 'CX-5': '1', 'Camry': '4', 'Challenger': '1', 'Charger': '1', 'Civic': '9', 'Civic Type R': '1', 'Corolla': '2', 'Cruze': '1', 'EQS': '1', 'ES': '3', 'Edge': '1', 'Enclave': '2', 'Equinox': '1', 'Escalade': '2', 'Expedition': '1', 'Expedition Max': '2', 'Explorer': '3', 'F-150': '1', 'Fusion Hybrid': '1', 'G37': '1', 'GX': '1', 'Grand Cherokee': '2', 'Grand Cherokee L': '1', 'IS': '1', 'Insight': '1', 'Jetta': '1', 'LX': '1', 'Mazda6': '1', 'Model Y': '2', 'Mustang': '2', 'NX': '1', 'New Compass': '1', 'Odyssey': '8', 'Outback': '1', 'Pacifica': '1', 'Pathfinder': '1', 'Pilot': '8', 'Q3': '1', 'Q5': '2', 'Q7': '1', 'QX60': '1', 'QX80': '1', 'RAV4': '1', 'RAV4 Hybrid': '2', 'RX': '3', 'Renegade': '1', 'Ridgeline': '2', 'SRX': '1', 'Sequoia': '1', 'Sienna': '2', 'Sierra 1500': '1', 'Silverado 1500': '1', 'Sonata': '2', 'Sportage': '1', 'Tacoma': '2', 'Tahoe': '2', 'Terrain': '1', 'Tiguan': '1', 'Titan XD': '1', 'Traverse': '2', 'Tundra': '2', 'Wrangler': '4', 'X3': '2', 'XC60': '1', 'XT6': '1', 'Yukon': '1', 'Yukon XL': '2'}, 'Year': {'2025': '2', '2024': '4', '2023': '10', '2022': '19', '2021': '18', '2020': '12', '2019': '14', '2018': '19', '2017': '14', '2016': '7', '2015': '8', '2014': '5', '2013': '6', '2012': '3', '2011': '1', '2010': '2', '2009': '1', '2008': '1', '2007': '1'}, 'Exterior Color': {'Basque Red Pearl Ii': '1', 'Beige': '1', 'Black': '37', 'Blue': '10', 'Brown': '1', 'Gray': '26', 'Grigio Metallic': '1', 'Ice Silver Metallic': '1', 'Maroon': '1', 'Obsidian': '1', 'Orange': '1', 'Red': '15', 'Silver': '14', 'Sunset Bronze Mica': '1', 'Tan': '1', 'White': '32', 'Yellow': '1'}, 'Interior Color': {'Almond': '1', 'Ash': '2', 'Beige': '3', 'Black': '57', 'Black Onyx': '1', 'Boulder': '1', 'Brown': '2', 'Brownstone Accents': '1', 'Cement': '2', 'Charcoal': '1', 'Chestnut Brown': '1', 'Cocoa Accents': '1', 'Creme': '1', 'Dune': '1', 'Ebony': '7', 'Ebony Accents': '2', 'Ebony Black': '1', 'Flaxen': '2', 'Global Black': '1', 'Graphite': '2', 'Gray': '15', 'Ivory': '3', 'Jet Black': '10', 'Light Shale': '2', 'Medium Light Camel': '1', 'Medium Light Stone': '1', 'Medium Stone': '1', 'Medium Titanium': '1', 'Mocha': '1', 'Moonstone': '1', 'Noble Brown': '1', 'Parchment': '4', 'Red': '2', 'Sand Beige': '1', 'Sepia': '1', 'Slate Black': '1', 'Soft Beige': '1', 'Stone': '1', 'Titan Black': '2', 'Truffle': '1', 'Warm Ivory': '1', 'Wheat': '1'}, 'Highlights': {'AUX': '147', 'Bluetooth': '143', 'Climate Control': '124', 'Heated Mirrors': '109', 'Heated Seats': '85', 'Heated Windshield Washer': '1', 'Keyless Access': '144', 'Leather Seats': '63', 'Moon Roof': '59', 'Navigation': '3', 'Panoramic Roof': '14', 'Parking Sensors': '33', 'Rear Camera': '132', 'Rear Climate Control': '49', 'Rear Wiper': '90', 'Remote Start': '53', 'Roof Rack': '51', 'Satellite Radio': '120', 'Sun Roof': '35', 'Third Row Seats': '6', 'Tinted Glass': '138', 'USB': '113', 'Xenon Headlights': '8'}, 'Body Style': {'2D Convertible': '1', '2D Coupe': '4', '2D Sport Utility': '1', '4D Crew Cab': '6', '4D CrewMax': '2', '4D Double Cab': '2', '4D Hatchback': '3', '4D Passenger Van': '11', '4D Quad Cab': '1', '4D Sedan': '38', '4D Sport Utility': '77', '4D SuperCrew': '1'}, 'Engine': {'1.5 liters / 4 Cylinder': '13', '1.8 liters / 4 Cylinder': '4', '2.0 liters / 4 Cylinder': '24', '2.3 liters / 4 Cylinder': '3', '2.4 liters / 4 Cylinder': '12', '2.5 liters / 4 Cylinder': '9', '2.7 liters / 4 Cylinder': '1', '3.0 liters / Straight 6 Cylinder': '1', '3.0 liters / V6 Cylinder': '1', '3.2 liters / V6 Cylinder': '1', '3.4 liters / V6 Cylinder': '2', '3.5 liters / V6 Cylinder': '36', '3.6 liters / V6 Cylinder': '15', '3.7 liters / V6 Cylinder': '1', '4 2.4 Intercooled Turbo Regular Unleaded H-4 2.4 L/146': '1', '4 2.5 Regular Unleaded H-4 2.5 L/152': '1', '4.0 liters / V6 Cylinder': '1', '4.6 liters / 8 Cylinder': '1', '5.0 liters / 8 Cylinder': '1', '5.3 liters / 8 Cylinder': '3', '5.6 liters / 8 Cylinder': '2', '5.7 liters / 8 Cylinder': '4', '6.2 liters / 8 Cylinder': '6', '6.4 liters / 8 Cylinder': '1', 'Electric Motor': '3'}, 'Transmission': {'1-Speed Automatic': '3', '1-Speed CVT w/OD': '32', '10-Speed Automatic': '3', '10-Speed Automatic w/OD': '12', '5-Speed': '1', '5-Speed Automatic': '4', '5-Speed Automatic w/OD': '3', '6-Speed Automatic': '10', '6-Speed Automatic w/OD': '25', '6-Speed Manual': '2', '6-Speed Manual w/OD': '1', '7-Speed Automatic w/OD': '2', '7-Speed Manual': '1', '7-Speed S tronic Dual-Clutch Auto': '1', '8-Speed Automatic': '4', '8-Speed Automatic w/OD': '22', '8-Speed CVT w/OD': '2', '8-speed Tiptronic automatic': '1', '9-Speed Automatic': '6', '9-Speed Automatic w/OD': '11', 'Continuously variable': '1'}, 'Drivetrain': {'4x4': '18', 'AWD': '19', 'FWD': '81', 'FrontTrak': '1', 'RWD': '25', 'quattro': '3'}, 'Fuel Type': {'Electric': '3', 'Gasoline': '139', 'Hybrid': '5'}},
    (('Certified', ("",)),): {'Model': {'Accord': '2', 'CR-V': '1', 'Civic': '3', 'Odyssey': '2', 'Pilot': '3'}, 'Year': {'2024': '1', '2023': '1', '2022': '5', '2021': '1', '2020': '2', '2019': '1'}, 'Exterior Color': {'Black': '5', 'Blue': '1', 'Gray': '2', 'Red': '1', 'Silver': '2'}, 'Interior Color': {'Beige': '1', 'Black': '8', 'Gray': '2'}, 'Highlights': {'AUX': '11', 'Bluetooth': '11', 'Climate Control': '11', 'Heated Mirrors': '8', 'Heated Seats': '8', 'Keyless Access': '11', 'Leather Seats': '5', 'Moon Roof': '6', 'Parking Sensors': '3', 'Rear Camera': '11', 'Rear Climate Control': '5', 'Rear Wiper': '7', 'Remote Start': '9', 'Roof Rack': '2', 'Satellite Radio': '11', 'Sun Roof': '3', 'Tinted Glass': '11', 'USB': '10'}, 'Body Style': {'4D Hatchback': '1', '4D Passenger Van': '2', '4D Sedan': '4', '4D Sport Utility': '4'}, 'Engine': {'1.5 liters / 4 Cylinder': '1', '2.0 liters / 4 Cylinder': '5', '3.5 liters / V6 Cylinder': '5'}, 'Transmission': {'1-Speed CVT w/OD': '4', '10-Speed Automatic w/OD': '3', '6-Speed Automatic w/OD': '1', '9-Speed Automatic w/OD': '3'}, 'Drivetrain': {'FWD': '11'}, 'Fuel Type': {'Gasoline': '11'}},
    (('Monthly Payment', ("",)),): {},
    (('Down Payment', ("",)),): {}}


def build_query(filter_entity: FilterEntity, vehicle_type=VehicleType.NEW):
    query = [f"Select count(*) from {database_configuration['database']}.gg_inventory_vehicles where "]
    if vehicle_type == VehicleType.NEW:
        query.append("type = 2")
    if vehicle_type == VehicleType.USED:
        query.append("type = 1")
        query.append("certified = 0")
    if vehicle_type == VehicleType.CERTIFIED:
        query.append("certified = 1")

    if filter_entity.model:
        q = [f"model = '{m}'" for m in filter_entity.model]
        query.append(f'({" or ".join(q)})')
    if filter_entity.make:
        q = [f"make = '{m}'" for m in filter_entity.make]
        query.append(f'({" or ".join(q)})')
    if filter_entity.year:
        q = [f"year = {m}" for m in filter_entity.year]
        query.append(f'({" or ".join(q)})')
    if filter_entity.exterior:
        q = [query_like("color", f'{m}') for m in filter_entity.exterior]
        query.append(f'({" or ".join(q)})')
    if filter_entity.body_style:
        q = [query_like("options", m) for m in filter_entity.body_style]
        query.append(f'({" or ".join(q)})')
    if filter_entity.trim:
        q = [f"trim = '{m}'" for m in filter_entity.trim]
        query.append(f'({" or ".join(q)})')

    if filter_entity.engine:
        q = []
        for m in filter_entity.engine:
            print(m)
            match = re.search(r"(?P<engine_displacement>\S+) liters / (\S* )?\S?(?P<engine_cylinder>\d+) Cylinder", m)
            if match is None:
                q.append(query_like("options", "Electric Drive Unit"))
            else:
                engine_displacement = match.group('engine_displacement')
                if float(engine_displacement) % 1 == 0:
                    engine_displacement = str(int(float(engine_displacement)))
                engine_cylinder = match.group('engine_cylinder')
                q.append("(" + " and ".join([query_like("options", f'"engine_cylinder":"{engine_cylinder}"'),
                                             query_like("options",
                                                        f'"engine_displacement":"{engine_displacement}"')]) + ")")
        query.append(f'({" or ".join(q)})')
    if filter_entity.transmission:
        q = [query_like("options", f'"transmission":"{m}"') for m in filter_entity.transmission]
        query.append(f'({" or ".join(q)})')
    if filter_entity.drivetrain:
        q = [query_like("options", f'"drivetrain":"{m}"') for m in filter_entity.drivetrain]
        query.append(f'({" or ".join(q)})')
    if filter_entity.interior:
        q = [query_like("options", f'"interior_color":"{m}"') for m in filter_entity.interior]
        query.append(f'({" or ".join(q)})')
    if filter_entity.highlights:
        query = None
    if filter_entity.fuel_type:
        query = None
    return " and ".join(query).replace(" and ", "", 1) if query is not None else query


def query_like(key, value):
    return f"{key} like '%{value.replace(' ', '%')}%'"


def init_unselected_filters():
    for filters, values in unselected_filters.items():
        filter_entity = FilterEntity()
        for filter_name, filter_value in filters:
            filter_entity.set_filter_value_by_filter_name(filter_name, filter_value)
        for filter_name in values:
            filter_entity_temp = copy(filter_entity)
            for unselected_filter_value in values[filter_name].keys():
                filter_entity_temp.set_filter_value_by_filter_name(filter_name, (unselected_filter_value,))
                query = build_query(filter_entity_temp)
                print(query)
                if query is not None:
                    unselected_filters[filters][filter_name][unselected_filter_value] = str(MySql.execute_query(
                        database_configuration, query)[0][0])
    return unselected_filters


allure_step_total_amount = "CHECK -  total amount of vehicle based on selected filter"
total_amount_error = "Incorrect number of total vehicles"


def get_ordered_vin_numbers_by_price(vin_numbers, sorting_type: SortingType):
    order_type = "ASC" if sorting_type == SortingType.LOWEST_PRICE else "DESC"
    query = f"Select vin from {database_configuration['database']}.gg_inventory_vehicles where vin in ({str(vin_numbers)[1:-1]}) Order by price {order_type}, vin DESC"
    return [row[0] for row in MySql.execute_query(database_configuration, query)]
