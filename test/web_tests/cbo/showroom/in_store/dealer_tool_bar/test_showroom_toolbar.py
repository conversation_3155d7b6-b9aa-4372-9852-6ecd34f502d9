import allure
from pytest import fixture

from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.rest_api.services.vehicle_details import VehicleDetails
from test import pytestrail


@allure.feature('In-Store')
@allure.story('Dealer Toolbar collapsed in the showroom')
class TestInStoreDealerToolbarShowroom:

    @fixture(scope="class")
    @allure.title("In Store - Consumer Sign Up")
    def fx_cl_in_store_sign_up(self, fx_cl_open_in_store_welcome, lead_info):
        fx_cl_open_in_store_welcome.open_sign_up_pii()
        fx_cl_open_in_store_welcome.sign_up.submit_pii(lead_info, not_in_store=False)
        return fx_cl_open_in_store_welcome.consumer.show_room

    @pytestrail.case('C3502913')
    def test_expand_dealer_toolbar(self, fx_cl_in_store_sign_up):
        fx_cl_in_store_sign_up.expand_dealer_toolbar()
