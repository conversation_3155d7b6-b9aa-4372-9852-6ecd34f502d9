import allure
from pytest import fixture
from test import pytestrail


@allure.feature('In-Store')
@allure.story('Dealer Toolbar')
class TestInStoreDealerToolbars:

    @fixture(scope="class")
    @allure.title("In Store - Collapse dealer toolbar")
    def fx_cl_collapse_dealer_toolbar(self, fx_cl_open_in_store_welcome):
        fx_cl_open_in_store_welcome.collapse_dealer_toolbar()
        return fx_cl_open_in_store_welcome

    @fixture(scope="class")
    @allure.title("In Store - Dealer Sign Out")
    def fx_cl_dealer_sign_out(self, fx_cl_open_in_store_welcome):
        fx_cl_open_in_store_welcome.open_dealer_info()
        fx_cl_open_in_store_welcome.dealer_sign_out()
        return fx_cl_open_in_store_welcome

    @pytestrail.case('C9261556')
    def test_dealer_toolbar_expands_instore_launch(self, fx_cl_open_in_store_welcome):
        fx_cl_open_in_store_welcome.check_expanded_toolbar()

    @pytestrail.case('C3451434')
    def test_collapse_dealer_toolbar(self, fx_cl_collapse_dealer_toolbar):
        fx_cl_collapse_dealer_toolbar.check_collapsed_toolbar()

    @pytestrail.case('C3451433')
    def test_expand_dealer_toolbar(self, fx_cl_collapse_dealer_toolbar):
        fx_cl_collapse_dealer_toolbar.expand_dealer_toolbar()

    @pytestrail.case('C4847699')
    def test_dealer_toolbar_expands_dealer_log_in_welcome(self, fx_cl_dealer_sign_out):
        fx_cl_dealer_sign_out.hamburger_menu.open()
        fx_cl_dealer_sign_out.hamburger_menu.open_dealer_sign_in()
        fx_cl_dealer_sign_out.dealer_log_in.dealer_sigh_in(already_in_store=True)
        fx_cl_dealer_sign_out.check_expanded_toolbar()
