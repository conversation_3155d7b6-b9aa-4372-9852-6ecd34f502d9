import allure
from pytest import fixture
from test import pytestrail


@allure.feature('In-Store')
@allure.story('Dealer Info')
class TestInStoreDealerInfo:

    @fixture(scope="class")
    @allure.title("In Store - Dealer info")
    def fx_cl_get_dealer_info(self, fx_cl_open_in_store_welcome):
        fx_cl_open_in_store_welcome.open_dealer_info()
        return fx_cl_open_in_store_welcome

    @pytestrail.case('C3436981')
    def test_dealer_info_name(self, fx_cl_get_dealer_info):
        dealer_name = fx_cl_get_dealer_info.get_dealer_name()
        with allure.step("CHECK - Dealer has expected name"):
            assert dealer_name == "Dealer Manager"

    @pytestrail.case('C9160604')
    def test_dealer_info_role(self, fx_cl_get_dealer_info):
        dealer_role = fx_cl_get_dealer_info.get_dealer_role()
        with allure.step("CHECK - Dealer has expected role"):
            assert dealer_role == "Manager"
