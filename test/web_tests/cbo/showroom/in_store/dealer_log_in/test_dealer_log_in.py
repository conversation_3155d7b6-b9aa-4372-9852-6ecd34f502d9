import allure
from pytest import fixture
from test import pytestrail


@allure.feature('In-Store')
@allure.story('Dealer Log in')
class TestInStoreDealerLogIn:

    @fixture(scope="class")
    @allure.title("In Store - Dealer Sign Out")
    def fx_cl_dealer_sign_out(self, fx_cl_open_in_store_welcome):
        fx_cl_open_in_store_welcome.open_dealer_info()
        fx_cl_open_in_store_welcome.dealer_sign_out()
        return fx_cl_open_in_store_welcome

    @pytestrail.case('C3436982')
    def test_dealer_sign_out_welcome_screen_toolbar(self, fx_cl_dealer_sign_out):
        pass  # The assert for this test is in dealer_sign_out()

    @pytestrail.case('C4847699')
    def test_dealer_sign_in_welcome_screen_expanded_toolbar(self, fx_cl_dealer_sign_out):
        fx_cl_dealer_sign_out.hamburger_menu.open()
        fx_cl_dealer_sign_out.hamburger_menu.open_dealer_sign_in()
        fx_cl_dealer_sign_out.dealer_log_in.dealer_sign_in_welcome_page()


