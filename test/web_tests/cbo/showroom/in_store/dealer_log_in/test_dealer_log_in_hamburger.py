import allure
from pytest import fixture
from test import pytestrail


@allure.feature('In-Store')
@allure.story('Sign up/Sign out')
class TestInStoreSignUPHamburger:

    @fixture(scope="class")
    @allure.title("Dealer Sign Out")
    def fx_cl_dealer_sign_out(self, fx_cl_open_in_store_welcome):
        fx_cl_open_in_store_welcome.hamburger_menu.open()
        fx_cl_open_in_store_welcome.hamburger_menu.select_dealer_sign_out()
        fx_cl_open_in_store_welcome.dealer_sign_out()
        return fx_cl_open_in_store_welcome

    @pytestrail.case('C9160610')
    def test_dealer_sign_out_hamburger_menu(self, fx_cl_dealer_sign_out):
        fx_cl_dealer_sign_out.hamburger_menu.check_dealer_log_in_button()

    @pytestrail.case('C9160611')
    def test_log_out_button_present_after_dealer_log_in(self, fx_cl_dealer_sign_out):
        fx_cl_dealer_sign_out.hamburger_menu.open_dealer_sign_in()
        fx_cl_dealer_sign_out.dealer_log_in.dealer_sigh_in(already_in_store=True)
