import allure
from pytest import fixture
from test import pytestrail


@allure.feature('In-Store')
@allure.story('Dealer Log in')
class TestInStoreDealerShowroomLogIn:

    @fixture(scope="class")
    @allure.title("In Store - Dealer Sign Out")
    def fx_cl_dealer_sign_out(self, fx_cl_open_in_store_welcome):
        fx_cl_open_in_store_welcome.open_dealer_info()
        fx_cl_open_in_store_welcome.dealer_sign_out()
        return fx_cl_open_in_store_welcome

    @fixture(scope="class")
    @allure.title("In Store - Sign Up")
    def fx_cl_in_store_sign_up(self, fx_cl_dealer_sign_out, lead_info):
        fx_cl_dealer_sign_out.open_sign_up_pii()
        fx_cl_dealer_sign_out.sign_up.submit_pii(lead_info, not_in_store=False)
        return fx_cl_dealer_sign_out.consumer.show_room

    @pytestrail.case('C3451424')
    def test_dealer_logged_in_in_showroom(self, fx_cl_in_store_sign_up):
        fx_cl_in_store_sign_up.hamburger_menu.open()
        fx_cl_in_store_sign_up.hamburger_menu.open_dealer_sign_in()
        fx_cl_in_store_sign_up.dealer_log_in.dealer_sign_in_welcome_page(in_store_showroom=True)
