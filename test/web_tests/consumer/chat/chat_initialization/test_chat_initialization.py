import re

import allure
import pytest
from pytest import fixture

from src.web.chat_console.chat_console_page import ChatConsolePage
from src.web.consumer import message_to_start_chat, AIBotResponse
from src.web.entities.message_entity import MessageFactory
from test import pytestrail
from test.web_tests import ConsumerPage


@allure.feature('Consumer')
@allure.story('ChatInitialization')
class TestChatInitialization:

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request, consumer_name, fx_cl_chat_manager_page) -> ConsumerPage:
        def finalizer():
            fx_cl_chat_manager_page.chat_component.close_chat(consumer.get_chat_id(wait_time=3))
            consumer.quit()
        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @pytestrail.case('C24')
    @pytest.mark.ktf(reason='GL-5279', strict=False)
    def test_consumer_can_initialize_chat(self,
                                          fx_cl_chat_manager_page: ChatConsolePage,
                                          fx_fn_consumer_page: ConsumerPage):
        fx_fn_consumer_page.start_chat()
        chat_context = fx_cl_chat_manager_page.chat_component.get_chat_content(fx_fn_consumer_page.get_chat_id(wait_time=3)).get_column_values("body")
        with allure.step("CHECK - if consumer can initialize a chat"):
            assert chat_context.index(fx_fn_consumer_page.initial_message) == 0, \
                f"The first message is {chat_context[0]}, not {fx_fn_consumer_page.initial_message} as expected"

    @pytestrail.case('C131')
    @pytest.mark.ktf(reason='GL-5279', strict=False)
    def test_consumer_can_initialize_chat_from_2_message(self,
                                                         fx_cl_chat_manager_page: ChatConsolePage,
                                                         fx_fn_consumer_page: ConsumerPage):
        fx_fn_consumer_page.start_chat("Hi")
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.GREETING_NEUTRAL())
        fx_fn_consumer_page.send_message(f"Hello, my name is {fx_fn_consumer_page.consumer_name}. {message_to_start_chat}.")
        chat_context = fx_cl_chat_manager_page.chat_component.get_chat_content(fx_fn_consumer_page.get_chat_id(wait_time=5)).get_column_values("body")
        with allure.step("CHECK - if consumer can initialize a chat"):
            assert chat_context.index(f"Hello, my name is {fx_fn_consumer_page.consumer_name}. {message_to_start_chat}.") == 2, \
                f"The first message is {chat_context[0]}, not {fx_fn_consumer_page.initial_message} as expected"

    @pytest.mark.parametrize("phrase", [
        pytest.param("looking to speak with manage", marks=[pytestrail.case('C132')])])
    def test_consumer_can_initialize_chat_with_specific_phrase(self,
                                                               fx_cl_chat_manager_page: ChatConsolePage,
                                                               fx_fn_consumer_page: ConsumerPage,
                                                               phrase):
        fx_fn_consumer_page.start_chat(phrase)
        chat_context = fx_cl_chat_manager_page.chat_component.get_chat_content(fx_fn_consumer_page.get_chat_id(wait_time=3)).get_column_values("body")
        with allure.step("CHECK - if consumer can initialize a chat with specific phrase"):
            assert chat_context.index(phrase) == 0, \
                f"The first message is {chat_context[0]}, not {phrase} as expected"


@allure.feature('Consumer')
@allure.story('ChatInitialization')
class TestChatInitializationSpanish:

    @fixture(scope="class", params=[{"message": "Hola, mi nombre es Lili, quiero comprar un auto nuevo.",
                                     "language": "Spanish",
                                     "expected": "Hello, my name is Lili, I want to buy a new car.",
                                     "reply_consumer": "Hola, (me llamo|mi nombre es) Alice. ¡Es (estupendo|genial) tenerte con nosotros!",
                                     "reply_operator": "Hi my name is Alice. It’s great to have you with us!"
                                    }])
    def init(self, request):
        return request.param

    @fixture(scope="class")
    def initial_message(self, init):
        return init["message"]

    @fixture(scope="class")
    def language(self, init):
        return init["language"]

    @fixture(scope="class")
    def expected(self, init):
        return init["expected"]

    @fixture(scope="class")
    def reply_for_consumer(self, init):
        return MessageFactory.text(init["reply_consumer"])

    @fixture(scope="class")
    def reply_for_operator(self, init):
        return init["reply_operator"]

    @pytestrail.case('C83')
    def test_consumer_chat_language_auto_detect(self,
                                                fx_cl_chat_manager_page: ChatConsolePage,
                                                fx_cl_consumer_page_init: ConsumerPage,
                                                language,
                                                expected):
        lang = fx_cl_chat_manager_page.chat_component.get_chat_language(fx_cl_consumer_page_init.get_chat_id(wait_time=3))
        with allure.step("CHECK - if language was auto-detected"):
            assert lang == language, \
                f"Language is {lang}, but expected {language}."

    @pytestrail.case('C87')
    def test_consumer_initial_message_translate(self,
                                                fx_cl_chat_manager_page: ChatConsolePage,
                                                fx_cl_consumer_page_init: ConsumerPage,
                                                language,
                                                expected):
        chat_context = fx_cl_chat_manager_page.chat_component.get_chat_content(fx_cl_consumer_page_init.get_chat_id(wait_time=3)).get_column_values("body")
        with allure.step("CHECK - if initial message was translated"):
            assert chat_context.index(expected) == 0, \
                f"The first message is {chat_context[0]}, not {fx_cl_consumer_page_init.initial_message} as expected"

    @pytestrail.case('C96')
    def test_chat_operator_system_reply(self,
                                        fx_cl_chat_manager_page: ChatConsolePage,
                                        fx_cl_consumer_page_init: ConsumerPage,
                                        language,
                                        reply_for_operator):
        with allure.step("CHECK - if chat operator can see correct system reply"):
            assert fx_cl_chat_manager_page.chat_component.get_message_by_pattern(reply_for_operator,
                                                                  fx_cl_consumer_page_init.get_chat_id(wait_time=3)), \
                f"System reply {reply_for_operator} was not found in " \
                    f"{fx_cl_chat_manager_page.chat_component.get_chat_content(fx_cl_consumer_page_init.get_chat_id(wait_time=3)).get_column_values('body')}"

    @pytestrail.case('C91')
    def test_consumer_system_reply(self,
                                   fx_cl_chat_manager_page: ChatConsolePage,
                                   fx_cl_consumer_page_init: ConsumerPage,
                                   language,
                                   reply_for_consumer):
        with allure.step("CHECK - if consumer can see correct system reply"):
            fx_cl_consumer_page_init.chat_component.wait_for_message_by_regex(reply_for_consumer)


@allure.feature('Consumer')
@allure.story('ChatInitialization')
class TestChatInitializationFrench:

    @fixture(scope="class", params=[{"message": "Bonjour, je m'appelle Lili, je veux acheter une nouvelle voiture.",
                                     "language": "French",
                                     "expected": "(Hi|Hello), my name is Lili, I want to buy a new car.",
                                     "reply_consumer": "Bonjour, je m'appelle Alice. C'est (génial|un plaisir|super) de (vous |t')(avoir|compter) parmi nous !",
                                     #"reply_consumer": "Salut, je m'appelle Alice. C'est (génial|un plaisir) de vous avoir avec nous",
                                     "reply_operator": "Hi my name is Alice. It’s great to have you with us!"
                                    }])
    def init(self, request):
        return request.param

    @fixture(scope="class")
    def initial_message(self, init):
        return init["message"]

    @fixture(scope="class")
    def language(self, init):
        return init["language"]

    @fixture(scope="class")
    def expected(self, init):
        return init["expected"]

    @fixture(scope="class")
    def reply_for_consumer(self, init):
        return MessageFactory.text(init["reply_consumer"])

    @fixture(scope="class")
    def reply_for_operator(self, init):
        return init["reply_operator"]

    @pytestrail.case('C85')
    def test_consumer_chat_language_auto_detect(self,
                                                fx_cl_chat_manager_page: ChatConsolePage,
                                                fx_cl_consumer_page_init: ConsumerPage,
                                                language,
                                                expected):
        lang = fx_cl_chat_manager_page.chat_component.get_chat_language(fx_cl_consumer_page_init.get_chat_id(wait_time=3))
        with allure.step("CHECK - if language was auto-detected"):
            assert lang == language, \
                f"Language is {lang}, but expected {language}."

    @pytestrail.case('C89')
    def test_consumer_initial_message_translate(self,
                                                fx_cl_chat_manager_page: ChatConsolePage,
                                                fx_cl_consumer_page_init: ConsumerPage,
                                                language,
                                                expected):
        chat_context = fx_cl_chat_manager_page.chat_component.get_chat_content(fx_cl_consumer_page_init.get_chat_id(wait_time=3)).get_column_values("body")
        with allure.step("CHECK - if initial message was translated"):
            assert re.search(expected, chat_context[0]), \
                f"The first message is {chat_context[0]}, does not mach {expected} as expected"

    @pytestrail.case('C98')
    def test_chat_operator_system_reply(self,
                                        fx_cl_chat_manager_page: ChatConsolePage,
                                        fx_cl_consumer_page_init: ConsumerPage,
                                        language,
                                        reply_for_operator):
        with allure.step("CHECK - if chat operator can see correct system reply"):
            assert fx_cl_chat_manager_page.chat_component.get_message_by_pattern(reply_for_operator, fx_cl_consumer_page_init.get_chat_id(wait_time=3)), \
                f"System reply {reply_for_operator} was not found in {fx_cl_chat_manager_page.chat_component.get_chat_content(fx_cl_consumer_page_init.get_chat_id()).get_column_values('body')}"

    @pytestrail.case('C93')
    def test_consumer_system_reply(self,
                                   fx_cl_chat_manager_page: ChatConsolePage,
                                   fx_cl_consumer_page_init: ConsumerPage,
                                   language,
                                   reply_for_consumer):
        with allure.step("CHECK - if consumer can see correct system reply"):
            fx_cl_consumer_page_init.chat_component.wait_for_message_by_regex(reply_for_consumer)


@allure.feature('Consumer')
@allure.story('ChatInitialization')
class TestChatInitializationPortuguese:

    @fixture(scope="class", params=[{"message": "Olá, meu nome é Lili, quero comprar um carro novo.",
                                     "language": "Portuguese",
                                     "expected": "(Hi|Hello), my name is Lili, I want to buy a new car.",
                                     "reply_consumer": "(Oi|Olá), meu nome é Alice. É ótimo (ter você|tê-la) conosco!",
                                     "reply_operator": "Hi my name is Alice. It’s great to have you with us!"
                                    }])
    def init(self, request):
        return request.param

    @fixture(scope="class")
    def initial_message(self, init):
        return init["message"]

    @fixture(scope="class")
    def language(self, init):
        return init["language"]

    @fixture(scope="class")
    def expected(self, init):
        return init["expected"]

    @fixture(scope="class")
    def reply_for_consumer(self, init):
        return MessageFactory.text(init["reply_consumer"])

    @fixture(scope="class")
    def reply_for_operator(self, init):
        return init["reply_operator"]

    @pytestrail.case('C84')
    def test_consumer_chat_language_auto_detect(self,
                                                fx_cl_chat_manager_page: ChatConsolePage,
                                                fx_cl_consumer_page_init: ConsumerPage,
                                                language,
                                                expected):
        lang = fx_cl_chat_manager_page.chat_component.get_chat_language(fx_cl_consumer_page_init.get_chat_id(wait_time=3))
        with allure.step("CHECK - if language was auto-detected"):
            assert lang == language, \
                f"Language is {lang}, but expected {language}."

    @pytestrail.case('C88')
    def test_consumer_initial_message_translate(self,
                                                fx_cl_chat_manager_page: ChatConsolePage,
                                                fx_cl_consumer_page_init: ConsumerPage,
                                                language,
                                                expected):
        chat_context = fx_cl_chat_manager_page.chat_component.get_chat_content(fx_cl_consumer_page_init.get_chat_id(wait_time=3)).get_column_values("body")
        with allure.step("CHECK - if initial message was translated"):
            assert re.search(expected, chat_context[0]), \
                f"The first message is {chat_context[0]}, does not mach {expected} as expected"

    @pytestrail.case('C97')
    def test_chat_operator_system_reply(self,
                                        fx_cl_chat_manager_page: ChatConsolePage,
                                        fx_cl_consumer_page_init: ConsumerPage,
                                        language,
                                        reply_for_operator):
        with allure.step("CHECK - if chat operator can see correct system reply"):
            assert fx_cl_chat_manager_page.chat_component.get_message_by_pattern(reply_for_operator, fx_cl_consumer_page_init.get_chat_id(wait_time=3)), \
                f"System reply {reply_for_operator} was not found in {fx_cl_chat_manager_page.chat_component.get_chat_content(fx_cl_consumer_page_init.chat_id).get_column_values('body')}"

    @pytestrail.case('C92')
    def test_consumer_system_reply(self,
                                   fx_cl_chat_manager_page: ChatConsolePage,
                                   fx_cl_consumer_page_init: ConsumerPage,
                                   language,
                                   reply_for_consumer):
        with allure.step("CHECK - if consumer can see correct system reply"):
            fx_cl_consumer_page_init.chat_component.wait_for_message_by_regex(reply_for_consumer)


@allure.feature('Consumer')
@allure.story('ChatInitialization')
class TestChatInitializationEnglish:

    @fixture(scope="class", params=[{"message": f"Hi, my name is Lili, {message_to_start_chat}.",
                                     "language": "English",
                                     "expected": f"Hi, my name is Lili, {message_to_start_chat}.",
                                     "reply_consumer": "Hi my name is Alice. It’s great to have you with us!",
                                     "reply_operator": "Hi my name is Alice. It’s great to have you with us!"
                                    }])
    def init(self, request):
        return request.param

    @fixture(scope="class")
    def initial_message(self, init):
        return init["message"]

    @fixture(scope="class")
    def language(self, init):
        return init["language"]

    @fixture(scope="class")
    def expected(self, init):
        return init["expected"]

    @fixture(scope="class")
    def reply_for_consumer(self, init):
        return MessageFactory.text(init["reply_consumer"])

    @fixture(scope="class")
    def reply_for_operator(self, init):
        return init["reply_operator"]

    @pytestrail.case('C86')
    def test_consumer_chat_language_auto_detect(self,
                                                fx_cl_chat_manager_page: ChatConsolePage,
                                                fx_cl_consumer_page_init: ConsumerPage,
                                                language,
                                                expected):
        lang = fx_cl_chat_manager_page.chat_component.get_chat_language(fx_cl_consumer_page_init.get_chat_id(wait_time=3))
        with allure.step("CHECK - if language was auto-detected"):
            assert lang == language, \
                f"Language is {lang}, but expected {language}."

    @pytestrail.case('C90')
    def test_consumer_initial_message_translate(self,
                                                fx_cl_chat_manager_page: ChatConsolePage,
                                                fx_cl_consumer_page_init: ConsumerPage,
                                                language,
                                                expected):
        chat_context = fx_cl_chat_manager_page.chat_component.get_chat_content(fx_cl_consumer_page_init.chat_id).get_column_values("body")
        with allure.step("CHECK - if initial message was translated"):
            assert chat_context.index(expected) == 0, \
                f"The first message is {chat_context[0]}, not {fx_cl_consumer_page_init.initial_message} as expected"

    @pytestrail.case('C99')
    def test_chat_operator_system_reply(self,
                                        fx_cl_chat_manager_page: ChatConsolePage,
                                        fx_cl_consumer_page_init: ConsumerPage,
                                        language,
                                        reply_for_operator):
        with allure.step("CHECK - if chat operator can see correct system reply"):
            assert fx_cl_chat_manager_page.chat_component.get_message_by_pattern(reply_for_operator, fx_cl_consumer_page_init.get_chat_id(wait_time=3)), \
                f"System reply {reply_for_operator} was not found in" \
                    f" {fx_cl_chat_manager_page.chat_component.get_chat_text_content(fx_cl_consumer_page_init.get_chat_id(wait_time=3))}"

    @pytestrail.case('C94')
    def test_consumer_system_reply(self,
                                   fx_cl_chat_manager_page: ChatConsolePage,
                                   fx_cl_consumer_page_init: ConsumerPage,
                                   language,
                                   reply_for_consumer):
        with allure.step("CHECK - if consumer can see correct system reply"):
            fx_cl_consumer_page_init.chat_component.wait_for_message_by_regex(reply_for_consumer)


@allure.feature('Consumer')
@allure.story('ChatInitialization')
class TestSpecialCharacters:

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request, consumer_name) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @pytest.mark.parametrize("message", [pytest.param("<hello", marks=[pytestrail.case("C1001031")]),
                                         pytest.param("#hello", marks=[pytestrail.case("C4211702")])])
    def test_special_character_start_chat(self, fx_fn_consumer_page, message):
        fx_fn_consumer_page.start_chat(message)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.GREETING_NEUTRAL())
        get_chat = fx_fn_consumer_page.chat_component.get_messages_text()
        with allure.step("CHECK - if message exists in chat"):
            assert all(item in get_chat for item in [message, AIBotResponse.GREETING_NEUTRAL().text])

    @pytest.mark.parametrize("message", [pytest.param("<hello>", marks=[pytestrail.case('C4131435')])])
    def test_html_tag_start_chat(self, fx_fn_consumer_page, message):
        fx_fn_consumer_page.start_chat(message)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.GREETING_NEUTRAL())
        get_chat = fx_fn_consumer_page.chat_component.get_messages_text()
        with allure.step("CHECK - if message does not exist in chat"):
            assert message not in get_chat and AIBotResponse.GREETING_NEUTRAL().text in get_chat
