import allure
import pytest
from pytest import fixture

from src.rest_api.services.leads import Leads
from src.web.chat_console import VehicleType, LeadType
from src.web.consumer import AIBotResponse
from src.web.entities.lead_entity import WebLeadFactory
from test import pytestrail
from test.web_tests import ConsumerPage


@allure.epic('AI Bot')
@allure.feature('Vehicle Availability Skill')
class TestAIBotAnyInStock:

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request, consumer_name) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name,
                                initial_message="do you have any cars in stock?").reopen()
        consumer.start_chat()
        consumer.ai_wait_for_greeting()
        consumer.chat_component.wait_for_message(AIBotResponse.VEHICLES_MODEL_OR_VIN_REQUEST)
        return consumer

    @pytest.mark.parametrize("message, stock, lead_info", [
        pytest.param("5XYPG4A35JG350147", "K180016",
                     WebLeadFactory.random_lead(vehicle_type=VehicleType.NEW,
                                                vehicle_vin="5XYPG4A35JG350147",
                                                department="Sales",
                                                vehicle_make="Kia",
                                                vehicle_model="Sorento",
                                                vehicle_year="2018",
                                                stock_number="K180016",
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C47770')]),
        pytest.param("C170193", "C170193",
                     WebLeadFactory.random_lead(vehicle_type=VehicleType.NEW,
                                                stock_number="C170193",
                                                vehicle_vin="1G1Y12D70H5108614",
                                                vehicle_make="Chevrolet",
                                                vehicle_model="Corvette",
                                                vehicle_year="2017",
                                                department="Sales",
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C47771')]),
        pytest.param("190578", "190578",
                     WebLeadFactory.random_lead(vehicle_type=VehicleType.NEW,
                                                stock_number="190578",
                                                vehicle_vin="1GKKNXLS9KZ274557",
                                                vehicle_make="GMC",
                                                vehicle_model="Acadia",
                                                vehicle_year="2019",
                                                department="Sales",
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C47772')]),
        pytest.param("S190216A", "S190216A",
                     WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                stock_number="S190216A".lower(),
                                                vehicle_vin="WBA3B9G59ENR99139",
                                                vehicle_make="BMW",
                                                vehicle_model="3 series",
                                                vehicle_year="2014",
                                                department="Sales",
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C47773')]),
        pytest.param("CP6411", "CP6411",
                     WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                stock_number="CP6411".lower(),
                                                vehicle_vin="1C6RR7GG4JS106571",
                                                vehicle_make="Ram",
                                                vehicle_model="1500",
                                                vehicle_year="2018",
                                                department="Sales",
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C47774')])
    ])
    def test_availability(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.send_message(message)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock, message=AIBotResponse.VEHICLES_SINGLE_VEHICLE_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_CONFIRM)
        fx_fn_consumer_page.process_lead(lead_info)

    @pytest.mark.parametrize("message, response, lead_info", [
        pytest.param("CP9999",
                     AIBotResponse.VEHICLES_NO_VEHICLE_FOUND,
                     WebLeadFactory.random_lead(department="Sales",
                                                stock_number='CP9999'.lower(),
                                                vehicle_type=VehicleType.USED,
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C47775')]),
        pytest.param("cayenne",
                     AIBotResponse.VEHICLES_SINGLE_VEHICLE_MISSING,
                     WebLeadFactory.random_lead(vehicle_model="cayenne",
                                                vehicle_make="porsche",
                                                department="Sales",
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C379485')])])
    def test_availability_wrong_stock(self, fx_fn_consumer_page, message, response, lead_info):
        fx_fn_consumer_page.send_message(message)
        fx_fn_consumer_page.ai_wait_for_vehicle_not_available(message=response)
        fx_fn_consumer_page.process_lead(lead_info)


@allure.epic('AI Bot')
@allure.feature('Vehicle Availability Skill')
class TestAIBotInStock:

    @pytest.mark.parametrize("message, stocks",
                             [pytest.param("Any 2018 camaro in stock?",
                                           ["C180564", "C181259", "C181008", "C181228"],
                                           marks=[pytestrail.case('C51737')])])
    def test_in_stock_year_model(self, fx_fn_consumer_page, message, stocks):
        fx_fn_consumer_page.ai_wait_for_greeting()
        with allure.step("CHECK - if vehicles were pushed"):
            fx_fn_consumer_page.ai_wait_for_multiple_vehicles_available(stocks)
        fx_fn_consumer_page.provide_vehicle_which_one()
        # TODO: not finished

    @pytest.mark.parametrize("message, stocks",
                             [pytest.param("Any Chevrolet in stock?",
                                           ["C150046", "C170392", "C170822", "C180564"],
                                           marks=[pytestrail.case('C172569')])])
    def test_in_stock_make(self, fx_fn_consumer_page, message, stocks):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLES_MANY_VEHICLES_AVAIL(468),
                                                            wait_time=10)
        fx_fn_consumer_page.ai_wait_for_many_vehicles_push(stocks)
        fx_fn_consumer_page.provide_vehicle_which_one()

    @pytest.mark.parametrize("message, response, lead_info",
                             [pytest.param("any 2016 GMC Yukon in stock?",
                                           AIBotResponse.VEHICLES_SINGLE_VEHICLE_MISSING,
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_year="2016",
                                                                      vehicle_make="gmc",
                                                                      vehicle_model="yukon",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES),
                                           marks=[pytestrail.case('C51738')])])
    def test_in_stock_year_model_not_found(self, fx_fn_consumer_page, message, response, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.chat_component.wait_for_message(response)
        fx_fn_consumer_page.process_lead(lead_info)

    @pytest.mark.parametrize("message, stocks, which, lead_info", [
        pytest.param("Do you have any 2010 or 2011 Impreza or Forester in stock?",
                     ["K200013A", "S180695B", "K200069A"],
                     "Dark Gray Metallic",
                     WebLeadFactory.random_lead(vehicle_year="2011",
                                                vehicle_make="Subaru",
                                                vehicle_model="Impreza",
                                                vehicle_vin="JF1GE6A65BH518461",
                                                department="Sales",
                                                stock_number="K200069A",
                                                vehicle_type=VehicleType.USED,
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C51739')])])
    def test_in_stock_year_or_year_model_or_model(self, fx_fn_consumer_page, message, stocks, which, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_multiple_vehicles_available(stocks)
        fx_fn_consumer_page.provide_vehicle_which_one(which)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLE_COLOR_MATCH(which))
        fx_fn_consumer_page.process_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("Do you have 2016 Accord available?",
                                           "MAP230246A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="1HGCR3F97GA035201",
                                                                      vehicle_year="2016",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Accord",
                                                                      department="Sales",
                                                                      stock_number="MAP230246A",
                                                                      lead_type=LeadType.SALES),
                                           marks=[pytestrail.case('C51740')])])
    def test_availability_year_model_one_found(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock, message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.process_lead(lead_info)

    @pytest.mark.parametrize("message, stocks",
                             [pytest.param("Got any 2016 Sierra 1500 available?",
                                           ["KP1634", "C180614A"],
                                           marks=[pytestrail.case('C51741')])])
    def test_availability_year_model_several_found(self, fx_fn_consumer_page, message, stocks):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_multiple_vehicles_available(stocks)
        # TODO: not finished

    @pytest.mark.parametrize("message, stocks",
                             [pytest.param("is 2019 Chevrolet available?",
                                           ["C190013", "C190025", "C190015", "C190011"],
                                           marks=[pytestrail.case('C51742')])])
    def test_availability_year_make(self, fx_fn_consumer_page, message, stocks):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLES_MANY_VEHICLES_AVAIL(found=347), wait_time=10)
        fx_fn_consumer_page.ai_wait_for_many_vehicles_push(stocks)
        fx_fn_consumer_page.provide_vehicle_which_one()

    @pytest.mark.parametrize("message, lead_info",
                             [pytest.param("I need Nissan 1993 pick up",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_year="1993",
                                                                      vehicle_make="nissan",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES),
                                           marks=[pytestrail.case('C508774')])])
    def test_unavailability_year_make(self, fx_fn_consumer_page, message, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_vehicle_not_available()
        fx_fn_consumer_page.provide_full_name(lead_info.first_name + " " + lead_info.last_name,
                                              lead_info.phone + " " + lead_info.email)
        fx_fn_consumer_page.ai_wait_for_vehicle_lead_followup()
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)


@allure.epic('AI Bot')
@allure.feature('Vehicle Availability Skill')
class TestAIBotURL:

    @fixture(scope="class")
    @allure.title("Open Consumer Page")
    def fx_cl_consumer_page(self, request, consumer_name) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name,
                                initial_message="is this car still available?",
                                url_query={"year": "2018", "make": "kia", "model": "Sorento"}).open()
        consumer.start_chat()
        return consumer

    @pytest.mark.parametrize("stock, lead_info", [
        pytest.param("K180016",
                     WebLeadFactory.random_lead(vehicle_type=VehicleType.NEW,
                                                stock_number="K180016".lower(),
                                                vehicle_vin="5XYPG4A35JG350147",
                                                vehicle_year="2018",
                                                vehicle_make="Kia",
                                                vehicle_model="Sorento",
                                                department="Sales",
                                                lead_type=LeadType.SALES),
                     marks=[pytestrail.case('C47768')])])
    def test_availability(self, fx_cl_consumer_page, stock, lead_info):
        fx_cl_consumer_page.ai_wait_for_greeting()
        fx_cl_consumer_page.ai_wait_for_single_vehicle_available(stock, message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_cl_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_cl_consumer_page.process_lead(lead_info)


@allure.epic('AIBot')
@allure.feature('NewVehicle')
class TestNewVehicle:

    @fixture(scope="class")
    @allure.title("OpenConsumerPage")
    def fx_cl_consumer_page(self, request, consumer_name) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name,initial_message='I want to buy a new vehicle').open()
        consumer.start_chat()
        return consumer

    @pytestrail.case('C1683473')
    def test_new_vehicle_cards(self, fx_cl_consumer_page):
        fx_cl_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLES_MODEL_OR_VIN_REQUEST)
        fx_cl_consumer_page.chat_component.send_message('Honda Accord')
        fx_cl_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLES_MANY_VEHICLES_AVAIL(found=94))
        with allure.step('CHECK-if vehicle slider is present and NEW vehicles are shown'):
            assert fx_cl_consumer_page.chat_component.get_vehicle(text='HondaAccord').get_vehicle_type() == "New"
