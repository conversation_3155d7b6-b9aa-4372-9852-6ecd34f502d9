import allure
import pytest
from pytest import fixture

from src.rest_api.services.leads import Leads
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.web.chat_console import VehicleType, LeadType
from src.web.consumer import ai_wait_time, AIBotResponse, TradeInCondition
from src.web.entities.lead_entity import WebLeadFactory
from src.web.entities.trade_in_entity import TradeInProvider
from test import pytestrail
from test.web_tests import ConsumerPage


@allure.epic('AI Bot')
@allure.feature('Trade In Skill: KBB')
class TestAIKBBTradeIn:

    @fixture(scope="class")
    def fx_cl_set_cbo_trade_in_provider_setting(self, request):
        def finalizer():
            Gb1Settings.set_ai_trade_in_provider_setting()

        request.addfinalizer(finalizer)

        Gb1Settings.set_ai_trade_in_provider_setting(trade_in_provider=TradeInProvider.KBB)

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request,
                            fx_cl_set_cbo_trade_in_provider_setting,
                            consumer_name, message) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name, initial_message=message).open()
        consumer.start_chat()
        return consumer

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my 2015 corolla",
                                           "190442A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="SHHFK8G71JU200893",
                                                                      stock_number='190442a',
                                                                      vehicle_year="2018",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Civic Type R",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES,
                                                                      trade_in_trim="LE Sedan 4D",
                                                                      trade_in_year="2015",
                                                                      trade_in_model="Corolla",
                                                                      trade_in_make="Toyota",
                                                                      trade_in_mileage="65000",
                                                                      trade_in_condition="Good",
                                                                      custom_type="KBB"),
                                           marks=[pytestrail.case('C354566')]),
                              ])
    def test_trade_in_basic_info(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim,
                                         lead_info.trade_in_year,
                                         lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage,
                                            lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition,
                                              lead_info.trade_in_year,
                                              lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my 2015 corolla",
                                           "190442A",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="SHHFK8G71JU200893",
                                               stock_number='190442a',
                                               vehicle_make="Honda",
                                               vehicle_model="Civic Type R",
                                               vehicle_year="2018",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",  # "Fair",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354567')]),
                              ])
    def test_trade_in_basic_vin_kbb(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year,
                                         lead_info.trade_in_model.capitalize())
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_vin, "", "",
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_VEHICLE_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_CONFIRM)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year="2018", model="Civic", trim="Type R")
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stocks, lead_info",
                             [pytest.param("I want to trade-in my 2015 corolla",
                                           ["190152", "190197", "190203", "190179"],
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.NEW,
                                               vehicle_make="Cadillac",
                                               vehicle_year="2019",
                                               vehicle_model="Escalade",
                                               vehicle_vin="1gys4bkj4kr181028",
                                               stock_number='190197',
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C1215874')]),
                              ])
    def test_trade_in_basic_vehicles_search(self, fx_fn_consumer_page, message, stocks, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year,
                                         lead_info.trade_in_model.capitalize())
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_many_vehicles_push(stocks)
        fx_fn_consumer_page.provide_vehicle_which_one("Steel Metallic")
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available("190197",
                                                                 message=AIBotResponse.VEHICLES_SINGLE_VEHICLE_MATCH)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my car",
                                           "B5005",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="2C4RC1DG5LR151390",
                                               stock_number='b5005',
                                               vehicle_year="2020",
                                               vehicle_make="Chrysler",
                                               vehicle_model="Voyager",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354568')]),
                              ])
    def test_trade_in_no_initial_info_full(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, lead_info.trade_in_make,
                                                 lead_info.trade_in_model)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year,
                                         lead_info.trade_in_model.capitalize())
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my car",
                                           "B5005",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="2C4RC1DG5LR151390",
                                               stock_number='b5005',
                                               vehicle_year="2020",
                                               vehicle_make="Chrysler",
                                               vehicle_model="Voyager",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",  # "Fair",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354569')]),
                              ])
    def test_trade_in_no_initial_info_year_make(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, lead_info.trade_in_make, "")
        fx_fn_consumer_page.provide_vehicle_model(lead_info.trade_in_model, lead_info.trade_in_make)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year,
                                         lead_info.trade_in_model.capitalize())
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my car",
                                           "B5005",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="2C4RC1DG5LR151390",
                                               stock_number='b5005',
                                               vehicle_year="2020",
                                               vehicle_make="Chrysler",
                                               vehicle_model="Voyager",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354570')]),
                              ])
    def test_trade_in_no_initial_info_year_model(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, "", lead_info.trade_in_model)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year,
                                         lead_info.trade_in_model.capitalize())
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model.capitalize() + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my car",
                                           "B5005",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="2C4RC1DG5LR151390",
                                               stock_number='b5005',
                                               vehicle_year="2020",
                                               vehicle_make="Chrysler",
                                               vehicle_model="Voyager",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354571')]),
                              ])
    def test_trade_in_no_initial_info_make_model(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_vehicle_info("", lead_info.trade_in_make, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_vehicle_year_with_qr(lead_info.trade_in_year,
                                                         f"{lead_info.trade_in_make.capitalize()} {lead_info.trade_in_model}")
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my car",
                                           "B5005",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="2C4RC1DG5LR151390",
                                               stock_number='b5005',
                                               vehicle_year="2020",
                                               vehicle_make="Chrysler",
                                               vehicle_model="Voyager",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354574')]),
                              ])
    def test_trade_in_no_initial_info_model(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_vehicle_info("", "", lead_info.trade_in_model)
        fx_fn_consumer_page.provide_vehicle_year_with_qr(lead_info.trade_in_year,
                                                         f"{lead_info.trade_in_make.capitalize()} {lead_info.trade_in_model}")
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year,
                                         lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my car",
                                           "B5005",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="2C4RC1DG5LR151390",
                                               stock_number='b5005',
                                               vehicle_year="2020",
                                               vehicle_make="Chrysler",
                                               vehicle_model="Voyager",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354573')]),
                              ])
    def test_trade_in_no_initial_info_make(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_vehicle_info("", lead_info.trade_in_make, "")
        fx_fn_consumer_page.provide_vehicle_year_with_qr(lead_info.trade_in_year,
                                                         lead_info.trade_in_make)
        fx_fn_consumer_page.provide_vehicle_model(lead_info.trade_in_model, lead_info.trade_in_make)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my car",
                                           "B5005",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="2C4RC1DG5LR151390",
                                               stock_number='b5005',
                                               vehicle_year="2020",
                                               vehicle_make="Chrysler",
                                               vehicle_model="Voyager",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="LE Sedan 4D",
                                               trade_in_year="2015",
                                               trade_in_model="Corolla",
                                               trade_in_make="Toyota",
                                               trade_in_mileage="65000",
                                               trade_in_condition="Good",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354572')]),
                              ])
    def test_trade_in_no_initial_info_year(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, "", "")
        fx_fn_consumer_page.provide_vehicle_model(lead_info.trade_in_model, "vehicle", qr=False)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("Do you have 2016 Accord available?",
                                           "MAP230246A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="1HGCR3F97GA035201",
                                                                      stock_number='MAP230246A',
                                                                      vehicle_year="2016",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Accord",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES,
                                                                      trade_in_trim="EX Sport Utility 4D",
                                                                      trade_in_year="2015",
                                                                      trade_in_model="CR-V",
                                                                      trade_in_make="Honda",
                                                                      trade_in_mileage="75000",
                                                                      trade_in_condition="Very Good",
                                                                      custom_type="KBB"),
                                           marks=[pytestrail.case('C354575')])])
    def test_trade_in_in_availability_flow(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(response="yes. And I want to trade-in my vehicle as well",
                                            message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, lead_info.trade_in_make,
                                                 lead_info.trade_in_model)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year,
                                         lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("Do you have 2016 Accord available?",
                                           "MAP230246A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="1HGCR3F97GA035201",
                                                                      stock_number='MAP230246A',
                                                                      vehicle_year="2016",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Accord",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES,
                                                                      trade_in_trim="EX Sport Utility 4D",
                                                                      trade_in_year="2015",
                                                                      trade_in_model="CR-V",
                                                                      trade_in_make="Honda",
                                                                      trade_in_mileage="75000",
                                                                      trade_in_condition="Very Good",
                                                                      custom_type="KBB"),
                                           marks=[pytestrail.case('C354576')])])
    def test_trade_in_in_availability_pii_name(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.PII_REQUEST_NAME, wait_time=ai_wait_time)
        fx_fn_consumer_page.send_message("I want to trade-in my vehicle as well")
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, lead_info.trade_in_make,
                                                 lead_info.trade_in_model)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("Do you have 2016 Accord available?",
                                           "MAP230246A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="1HGCR3F97GA035201",
                                                                      stock_number='MAP230246A',
                                                                      vehicle_year="2016",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Accord",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES,
                                                                      trade_in_trim="EX Sport Utility 4D",
                                                                      trade_in_year="2015",
                                                                      trade_in_model="CR-V",
                                                                      trade_in_make="Honda",
                                                                      trade_in_mileage="75000",
                                                                      trade_in_condition="Very Good",
                                                                      custom_type="KBB"),
                                           marks=[pytestrail.case('C354577')])])
    def test_trade_in_in_availability_pii_phone(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.PII_REQUEST_PHONE, wait_time=ai_wait_time,
                                                            typing=True)
        fx_fn_consumer_page.send_message("I want to trade-in my vehicle as well")
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, lead_info.trade_in_make,
                                                 lead_info.trade_in_model)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone, message=AIBotResponse.PII_REQUEST_PHONE)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("Do you have 2016 Accord available?",
                                           "MAP230246A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="1HGCR3F97GA035201",
                                                                      stock_number='MAP230246A',
                                                                      vehicle_year="2016",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Accord",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES,
                                                                      trade_in_trim="EX Sport Utility 4D",
                                                                      trade_in_year="2015",
                                                                      trade_in_model="CR-V",
                                                                      trade_in_make="Honda",
                                                                      trade_in_mileage="75000",
                                                                      trade_in_condition="Very Good",
                                                                      custom_type="KBB",
                                                                      email=""),
                                           marks=[pytestrail.case('C354578')])])
    def test_trade_in_in_availability_pii_email(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.PII_REQUEST_EMAIL, wait_time=ai_wait_time,
                                                            typing=True)
        fx_fn_consumer_page.send_message("I want to trade-in my vehicle as well")
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, lead_info.trade_in_make,
                                                 lead_info.trade_in_model)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim, respond=None)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("Do you have 2016 Accord available?",
                                           "MAP230246A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="1HGCR3F97GA035201",
                                                                      stock_number='MAP230246A',
                                                                      vehicle_year="2016",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Accord",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES,
                                                                      trade_in_trim="EX Sport Utility 4D",
                                                                      trade_in_year="2015",
                                                                      trade_in_model="CR-V",
                                                                      trade_in_make="Honda",
                                                                      trade_in_mileage="75000",
                                                                      trade_in_condition="Good",  # "Fair",
                                                                      custom_type="KBB"),
                                           marks=[pytestrail.case('C354579'), pytest.mark.ktf(reason='CU-4253')])])
    def test_trade_in_in_post_lead(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting()
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.provide_full_name(lead_info.first_name, lead_info.last_name)
        fx_fn_consumer_page.provide_phone_number(lead_info.phone)
        fx_fn_consumer_page.provide_email(lead_info.email)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLE_LEAD_FOLLOWUP(),
                                                            wait_time=ai_wait_time,
                                                            typing=True)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.ANYTHING_ELSE, wait_time=ai_wait_time)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info.no_trade_in())
        fx_fn_consumer_page.send_message("do you accept trade-ins?")
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, lead_info.trade_in_make,
                                                 lead_info.trade_in_model)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage, lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition, lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim,
                                              respond=None)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stock, lead_info",
                             [pytest.param("I want to trade-in my vehicle",
                                           "190442A",
                                           WebLeadFactory.random_lead(
                                               vehicle_type=VehicleType.USED,
                                               vehicle_vin="SHHFK8G71JU200893",
                                               stock_number='190442A',
                                               vehicle_year="2018",
                                               vehicle_make="Honda",
                                               vehicle_model="Civic Type R",
                                               department="Sales",
                                               lead_type=LeadType.SALES,
                                               trade_in_trim="XL Pickup 2D 8 ft",
                                               trade_in_body_type="F150 Regular Cab",
                                               trade_in_year="2015",
                                               trade_in_model="F-150",
                                               trade_in_make="Ford",
                                               trade_in_mileage="78500",
                                               trade_in_condition="Good",
                                               custom_type="KBB"),
                                           marks=[pytestrail.case('C354580')]),
                              ])
    def test_trade_in_complex(self, fx_fn_consumer_page, message, stock, lead_info):
        fx_fn_consumer_page.provide_vehicle_info("", "", lead_info.trade_in_model)
        fx_fn_consumer_page.provide_vehicle_year_with_qr(
            lead_info.trade_in_year, f"{lead_info.trade_in_make.capitalize()} {lead_info.trade_in_model}")
        fx_fn_consumer_page.provide_body_type(lead_info.trade_in_body_type, "",
                                              lead_info.trade_in_model,
                                              question=AIBotResponse.VEHICLES_VEHICLE_CLARIFY_YOUR_MODEL_Q)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim,
                                         lead_info.trade_in_year,
                                         lead_info.trade_in_body_type)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage,
                                            lead_info.trade_in_year,
                                            lead_info.trade_in_body_type + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition,
                                              lead_info.trade_in_year,
                                              lead_info.trade_in_body_type + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_full_pii(lead_info)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stock,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, lead_info", [pytest.param("I want to trade in my Honda Accord 2020",
                                                                WebLeadFactory.random_lead(
                                                                    trade_in_trim="LX Sedan 4D",
                                                                    trade_in_mileage="55000",
                                                                    trade_in_year="2020",
                                                                    trade_in_model="Accord"
                                                                            ),
                                                                marks=[pytestrail.case('C2172962')])])
    def test_trade_in_condition_page(self, fx_fn_consumer_page, message, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage,
                                            lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLES_VEHICLE_CONDITION_REQUEST(
            year=lead_info.trade_in_year, model=lead_info.trade_in_model + " " + lead_info.trade_in_trim))
        trade_in_text = fx_fn_consumer_page.chat_component.get_trade_in_conditions_text()
        with allure.step('CHECK - if trade in conditions is explained'):
            assert TradeInCondition.TRADE_IN_CONDITION_EXPLAINED in trade_in_text


@allure.epic('AI Bot')
@allure.feature('Trade In Skill: KBB With License Plate')
class TestAIKBBTradeInWithLicensePlate:

    @fixture(scope="class")
    def fx_cl_set_trade_in_with_license_play_setting(self, request):
        def finalizer():
            Gb1Settings.set_license_plate_trade_in_flow_setting(license_plate_trade_in_flow_value=0)

        request.addfinalizer(finalizer)
        Gb1Settings.set_license_plate_trade_in_flow_setting(license_plate_trade_in_flow_value=1)

    @fixture(scope="class")
    def fx_cl_set_cbo_enabled_setting(self, request):
        def finalizer():
            Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=0)
            Gb1Settings.set_cbo_version_2_enabled_setting(cbo_enabled_value=0)

        request.addfinalizer(finalizer)
        Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=1)
        Gb1Settings.set_cbo_version_2_enabled_setting(cbo_enabled_value=1)

    @fixture(scope="class")
    def fx_cl_set_cbo_trade_in_provider_setting(self, request, fx_cl_set_cbo_enabled_setting, fx_cl_set_trade_in_with_license_play_setting):
        def finalizer():
            Gb1Settings.set_ai_trade_in_provider_setting()

        request.addfinalizer(finalizer)

        Gb1Settings.set_ai_trade_in_provider_setting(trade_in_provider=TradeInProvider.KBB)

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request,
                            fx_cl_set_cbo_trade_in_provider_setting,
                            consumer_name, message) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name, initial_message=message).open()
        consumer.start_chat()
        return consumer

    @pytest.mark.parametrize("message, stocks, lead_info",
                             [pytest.param("I want to trade-in my vehicle",
                                           "MAP230246A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="1HGCR3F97GA035201",
                                                                      stock_number='MAP230246A',
                                                                      vehicle_year="2016",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Accord",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES,
                                                                      trade_in_trim="2.0T",
                                                                      trade_in_year="2013",
                                                                      trade_in_model="Beetle Convertible",
                                                                      trade_in_make="Volkswagen",
                                                                      trade_in_mileage="14000",
                                                                      trade_in_condition="Good",
                                                                      custom_type="KBB"),
                                           marks=[pytestrail.case('********'), pytest.mark.ktf('CU-4070')])])
    def test_vehicle_information_with_license_plate(self, fx_fn_consumer_page, message, stocks, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_license_plate('GUBAGOO')
        fx_fn_consumer_page.provide_vehicle_registration('Florida')
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage,
                                            lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition,
                                              lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLES_TRADE_IN_PII_TRANSITION_KBB)
        fx_fn_consumer_page.provide_full_pii(lead_info)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stocks,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)

    @pytest.mark.parametrize("message, stocks, lead_info",
                             [pytest.param("I want to trade-in my vehicle",
                                           "MAP230246A",
                                           WebLeadFactory.random_lead(vehicle_type=VehicleType.USED,
                                                                      vehicle_vin="1HGCR3F97GA035201",
                                                                      stock_number='MAP230246A',
                                                                      vehicle_year="2016",
                                                                      vehicle_make="Honda",
                                                                      vehicle_model="Accord",
                                                                      department="Sales",
                                                                      lead_type=LeadType.SALES,
                                                                      trade_in_trim="2.0i Sport Sedan 4D",
                                                                      trade_in_year="2017",
                                                                      trade_in_model="Impreza",
                                                                      trade_in_make="Subaru",
                                                                      trade_in_mileage="25500",
                                                                      trade_in_condition="Good",
                                                                      custom_type="KBB"),
                                           marks=[pytestrail.case('********'), pytest.mark.ktf('CU-4070', strict=False)])])
    def test_vehicle_information_with_invalid_license_plate(self, fx_fn_consumer_page, message, stocks, lead_info):
        fx_fn_consumer_page.ai_wait_for_greeting(AIBotResponse.TRADE_IN_GREETING)
        fx_fn_consumer_page.provide_license_plate('Test-test')
        fx_fn_consumer_page.provide_license_plate(license_plate="GUBAGOO",
                                                  message=AIBotResponse.VEHICLES_VEHICLE_LICENSE_PLATE_INVALID)
        fx_fn_consumer_page.provide_vehicle_registration('New York')
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLE_LICENSE_PLATE_FALLBACK)
        fx_fn_consumer_page.provide_vehicle_info(lead_info.trade_in_year, lead_info.trade_in_make,
                                                 lead_info.trade_in_model)
        fx_fn_consumer_page.provide_trim(lead_info.trade_in_trim, lead_info.trade_in_year, lead_info.trade_in_model)
        fx_fn_consumer_page.provide_mileage(lead_info.trade_in_mileage,
                                            lead_info.trade_in_year,
                                            lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.provide_condition(lead_info.trade_in_condition,
                                              lead_info.trade_in_year,
                                              lead_info.trade_in_model + " " + lead_info.trade_in_trim)
        fx_fn_consumer_page.chat_component.wait_for_message(AIBotResponse.VEHICLES_TRADE_IN_PII_TRANSITION_KBB)
        fx_fn_consumer_page.provide_full_pii(lead_info)
        fx_fn_consumer_page.get_trade_in_estimation_kbb()
        fx_fn_consumer_page.provide_vehicle_info(lead_info.vehicle_year, lead_info.vehicle_make,
                                                 lead_info.vehicle_model,
                                                 AIBotResponse.VEHICLES_TRADE_IN_VEHICLE_INFO_REQUEST)
        fx_fn_consumer_page.ai_wait_for_single_vehicle_available(stocks,
                                                                 message=AIBotResponse.VEHICLES_SINGLE_MODEL_AVAIL)
        fx_fn_consumer_page.confirm_vehicle(message=AIBotResponse.VEHICLES_VEHICLE_FOLLOWUP)
        fx_fn_consumer_page.ai_wait_for_trade_in_vehicle_lead_followup(year=lead_info.vehicle_year,
                                                                       model=lead_info.vehicle_model)
        fx_fn_consumer_page.ai_close_chat(name=lead_info.first_name)
        with allure.step("CHECK - if information appears in leads"):
            Leads.find_web_lead(lead_info)
