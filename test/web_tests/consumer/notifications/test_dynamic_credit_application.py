import allure
import pytest
from pytest import fixture

from src import resq_password, resq_admin_user_name, resq_sales_user_name
from src.web.resq.resq_page import ResQPage
from test import pytestrail
from test.web_tests import ConsumerPage
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.web.consumer import AIBotResponse
import names
from src.web.entities.credit_application_entity import WebCreditApplicationEntity, WebCreditApplicatioFactory


@allure.feature('Customer')
@allure.story('DynamicCreditAppCta')
class TestDynamicCreditAppCta:

    @fixture(scope="class")
    @allure.title("Credit Lead info")
    def credit_lead_info(self):
        return WebCreditApplicatioFactory.default_secure_credit_app()

    @fixture(scope='function')
    def fx_fn_enable_cbo(self, request):
        def finalizer():
            Gb1Settings.set_cbo_enabled_setting()
        request.addfinalizer(finalizer)
        Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=1)

    @fixture(scope='function')
    def fx_fn_enable_dynamic_credit_app(self, request):
        def finalizer():
            Gb1Settings.set_dynamic_credit_app()
        request.addfinalizer(finalizer)
        Gb1Settings.set_dynamic_credit_app(dynamic_credit_app_value=1)

    @fixture(scope='function')
    def fx_fn_open_showroom(self, request, fx_fn_enable_dynamic_credit_app, credit_lead_info):
        def finalizer():
            showroom.quit()

        request.addfinalizer(finalizer)

        showroom = ConsumerPage(names.get_first_name()).open().show_room.open()
        showroom.secure_credit_app.open()
        showroom.secure_credit_app.fill(credit_lead_info)
        return showroom

    @fixture(scope="function")
    @allure.title("Open GLive page and sign in as a sales")
    def fx_fn_resq_sales_page(self, request) -> ResQPage:
        def finalizer():
            sales.switch_to.default_content()
            sales.sign_out()
            sales.quit()

        request.addfinalizer(finalizer)

        sales = ResQPage(resq_sales_user_name, resq_password).open()
        sales.conversations.open()
        return sales

    @pytestrail.case('C6273034')
    def test_start_credit_app_cta(self, fx_fn_enable_cbo, fx_fn_resq_sales_page, fx_fn_open_showroom, credit_lead_info):
        resq_account = fx_fn_resq_sales_page.txt_account.text
        fx_fn_resq_sales_page.vr.open()
        fx_fn_resq_sales_page.vr.select_specific_customer_deal(credit_lead_info)
        fx_fn_resq_sales_page.notify_credit_app.open()
        fx_fn_resq_sales_page.notify_credit_app.notify_by_chat()
        fx_fn_resq_sales_page.notify_credit_app.summary_credit_app.close_summary()
        fx_fn_open_showroom.consumer.switch_to.default_content()
        with allure.step('CHECK - if CTA message is present in notification'):
            assert AIBotResponse.CREDIT_APP_CTA_MESSAGE(name=credit_lead_info.first_name, account=resq_account) == \
               fx_fn_open_showroom.consumer.notification_component.get_notification_message()
