import allure
from pytest import fixture
import pytest

from kom_framework.src.web.drivers import UserAgents
from src.rest_api.entities.vehicle.vehicle_entity import VehicleEntity
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.web.consumer.cbo_page_behavior import CboCTAButton
from src.web.entities.lead_entity import WebLeadFactory
from test import pytestrail
from test.web_tests import ConsumerPage, device_mode
from src import cbo_url
from src.web.entities.pii_entity import PIIFactory


@allure.feature('Consumer')
@allure.story('HideChatBubble')
class TestHideChatBubble:

    @fixture(scope="class")
    def fx_cl_set_auto_engage_setting(self, request):
        def finalizer():
            if device_mode in UserAgents.DESKTOP:
                Gb1Settings.set_auto_engage_setting()

        request.addfinalizer(finalizer)
        if device_mode in UserAgents.DESKTOP:
            Gb1Settings.set_auto_engage_setting(autoengage_value=1,
                                                autoengage_after="1",
                                                autoengage_repeat="2",
                                                autoengage_score="0",
                                                message="This is a test message in auto engage greeter")

    @fixture(scope="class")
    def fx_cl_set_cbo_enabled_setting(self, request):
        def finalizer():
            Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=0)

        request.addfinalizer(finalizer)
        Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=1)

    @fixture(scope='class')
    def fx_cl_set_hide_chat_desktop_setting(self, request):
        def finalizer():
            if device_mode in UserAgents.DESKTOP:
                Gb1Settings.set_hide_chat_desktop_setting()
            else:
                Gb1Settings.set_hide_chat_mobile_setting()
        request.addfinalizer(finalizer)
        if device_mode in UserAgents.DESKTOP:
            Gb1Settings.set_hide_chat_desktop_setting(desktop_value="1")
        else:
            Gb1Settings.set_hide_chat_mobile_setting(mobile_value="1")

    @fixture(scope='class')
    def fx_cl_consumer_page(self, request, consumer_name, fx_cl_set_hide_chat_desktop_setting, fx_cl_set_cbo_enabled_setting, fx_cl_set_auto_engage_setting):
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)
        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @pytestrail.case('C1683468')
    def test_hide_chat_on_desktop(self, fx_cl_consumer_page):
        with allure.step('CHECK - if the chat bubble disappeared'):
            assert not fx_cl_consumer_page.btn_chat_bubble.exists(2)

    @pytestrail.case('C1683469')
    @pytest.mark.skipif(device_mode not in [UserAgents.ANDROID, UserAgents.IOS], reason="Not applicable for desktop")
    def test_hide_chat_on_mobile(self, fx_cl_consumer_page):
        with allure.step('CHECK - if the chat bubble disappeared'):
            assert not fx_cl_consumer_page.btn_chat_bubble.exists(2)

    @pytestrail.case('C1683470')
    def test_hide_chat_and_auto_engage(self, fx_cl_consumer_page):
        with allure.step('CHECK - if the chat bubble disappeared and auto engage greeter doesn\'t display'):
            assert not fx_cl_consumer_page.btn_chat_bubble.exists(2) and not fx_cl_consumer_page.auto_engage_greeter.exists(3)

    @pytestrail.case('C3236350')
    def test_ask_about_offer_hide_chat(self, fx_cl_consumer_page):
        name = "$50 Towards Purchase Or Lease"
        fx_cl_consumer_page.view_offer_from_special(name)
        fx_cl_consumer_page.ask_about_offer(name)
        chat_window = fx_cl_consumer_page.chat_component.exists(3)
        fx_cl_consumer_page.chat_component.close_using_icon()
        chat_bubble = fx_cl_consumer_page.btn_chat_bubble.exists(3)
        with allure.step('CHECK - if chat window opens after asking about offer and cannot be accessed after closing'):
            assert chat_window and not chat_bubble


@allure.feature('Consumer')
@allure.story('HideChatBubble')
class TestHideChatInVR:

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self):
        return WebLeadFactory.vr_mock_zip_single_country(vehicle_year="2021",
                                                         vehicle_make="Jeep",
                                                         vehicle_model="Gladiator",
                                                         vehicle_vin="1C6HJTAG7ML524853",
                                                         department="Sales")

    @fixture(scope='class')
    @allure.title("PII info")
    def pii_info(self):
        return PIIFactory.random_pii()

    @fixture(scope="class")
    def fx_cl_set_cbo_enabled_setting(self, request):
        def finalizer():
            Gb1Settings.set_cbo_enabled_setting(cbo_enabled_value=0)
            Gb1Settings.set_cbo_version_2_enabled_setting(cbo_enabled_value=0)
        request.addfinalizer(finalizer)
        Gb1Settings.reset_cbo_settings()
        Gb1Settings.set_inventory_path()

    @fixture(scope='class')
    def fx_cl_set_hide_chat_desktop_setting(self, request):
        def finalizer():
            Gb1Settings.set_hide_chat_desktop_setting()
        request.addfinalizer(finalizer)
        Gb1Settings.set_hide_chat_desktop_setting(desktop_value="1")

    @fixture(scope='class')
    def fx_cl_consumer_page(self, request, consumer_name, fx_cl_set_hide_chat_desktop_setting,
                            fx_cl_set_cbo_enabled_setting):
        def finalizer():
            consumer.quit()
        request.addfinalizer(finalizer)
        consumer = ConsumerPage(consumer_name, url=cbo_url).open()
        return consumer

    @pytestrail.case('C1706777')
    def test_hide_chat_on_vr(self, fx_cl_consumer_page, lead_info, pii_info):
        fx_cl_consumer_page.open_cbo(vin=lead_info.vehicle_vin, cbo_cta_button=CboCTAButton.CBO_UNLOCK_PAYMENTS, pii_entity=pii_info)
        chat_bubble = fx_cl_consumer_page.btn_chat_bubble.exists(3)
        chat_greeter = fx_cl_consumer_page.chat_greeter.exists(3)
        with allure.step('CHECK - if payment page is displayed and chat bubble and greeter is nor displayed'):
            assert not chat_bubble and not chat_greeter


@allure.feature('Settings')
@allure.story('HideChatBubble')
class TestHideChatWithTriggerEnabled:

    @fixture(scope='class')
    def fx_cl_set_hide_chat_desktop_setting(self, request):
        def finalizer():
            if device_mode in UserAgents.DESKTOP:
                Gb1Settings.set_hide_chat_desktop_setting()
            else:
                Gb1Settings.set_hide_chat_mobile_setting()

        request.addfinalizer(finalizer)
        if device_mode in UserAgents.DESKTOP:
            Gb1Settings.set_hide_chat_desktop_setting(desktop_value="1")
        else:
            Gb1Settings.set_hide_chat_mobile_setting(mobile_value="1")

    @fixture(scope='class')
    def fx_cl_set_trigger_settings(self, request):
        def finalizer():
            Gb1Settings.set_trigger_chat_settings(tr_id=28)
        request.addfinalizer(finalizer)
        Gb1Settings.set_trigger_chat_settings(tr_id=28, enabled_value=1)

    @fixture(scope='class')
    def fx_cl_consumer_page(self, request, consumer_name, fx_cl_set_hide_chat_desktop_setting,
                            fx_cl_set_trigger_settings):
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)
        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @pytestrail.case('C1683471')
    def test_chat_is_hidden_with_trigger_enabled(self, fx_cl_consumer_page):
        with allure.step('CHECK - if chat bubble does not display'):
            assert not fx_cl_consumer_page.btn_chat_bubble.exists()
