import allure
import pytest
from _pytest.fixtures import fixture

from kom_framework.src.web.drivers import UserAgents
from src.utils.admin_tools.gb1_settings import Gb1Settings
from test import pytestrail
from test.web_tests import ConsumerPage, device_mode


@allure.feature('Consumer')
@allure.story('Setting')
class TestAppleBusinessChatIDSetting:

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request, consumer_name, fx_fn_set_apple_biz_id_setting) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)
        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @fixture(scope="function")
    def fx_fn_set_apple_biz_id_setting(self, request, apple_biz_id):
        def finalizer():
            Gb1Settings.set_apple_biz_id_setting(apple_biz_id_value="")

        request.addfinalizer(finalizer)

        Gb1Settings.set_apple_biz_id_setting(apple_biz_id_value=apple_biz_id)

    @pytest.mark.parametrize("apple_biz_id",
                             [pytest.param("", marks=[pytestrail.case('C50325')]),
                              pytest.param("ABCC", marks=[pytestrail.case('C50324')])])
    @pytest.mark.skipif(device_mode in [UserAgents.DESKTOP, UserAgents.ANDROID],
                        reason="Not Applicable for not Apple devices")
    def test_apple_biz_id_setting(self,
                                  apple_biz_id,
                                  fx_fn_consumer_page: ConsumerPage):
        fx_fn_consumer_page.welcome.open()
        if apple_biz_id == "ABCC":
            assert fx_fn_consumer_page.welcome.txt_abc.exists(2)
        else:
            assert fx_fn_consumer_page.welcome.txt_abc.exists(2) is False