import allure
import names
import pytest
from pytest import fixture

from src import offer_id, xtime_id
from src.rest_api.services.leads import Leads
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.web.consumer import ServicesType, TransportType
from src.web.entities.lead_entity import WebLeadFactory
from src.web.entities.message_entity import MessageFactory, MessageEntity
from test import pytestrail
from test.web_tests import ConsumerPage


@allure.feature('Consumer')
@allure.story('Setting')
class TestOfferTermsSetting:

    @fixture(scope="class")
    @allure.title("Message")
    def message(self):
        return MessageFactory.offers(["$50 Towards Purchase Or Lease"])

    @fixture(scope="function")
    @allure.title("Chat Console operator sends message")
    def fx_fn_chat_manager_send_message(self, fx_fn_consumer_page, fx_cl_chat_manager_page, message):
        fx_cl_chat_manager_page.chat_component.send_message(message, fx_fn_consumer_page.get_chat_id(wait_time=3))

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request, fx_fn_set_offer_terms_setting, consumer_name,
                            fx_cl_chat_manager_page) -> ConsumerPage:
        def finalizer():
            fx_cl_chat_manager_page.chat_component.close_chat(consumer.get_chat_id(wait_time=3))
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name).open()
        consumer.start_chat()
        return consumer

    @fixture(scope="function")
    def fx_fn_set_offer_terms_setting(self, request, offer_terms_setting_value):
        def finalizer():
            if offer_terms_setting_value == 1:
                Gb1Settings.set_offer_terms_setting(offer_id=offer_id, offer_terms_value=0)

        request.addfinalizer(finalizer)

        Gb1Settings.set_offer_terms_setting(offer_id=offer_id, offer_terms_value=offer_terms_setting_value)

    @pytest.mark.parametrize("offer_terms_setting_value", [
        pytest.param(0, marks=[pytestrail.case('C2164')]),
        pytest.param(1, marks=[pytestrail.case('C2165')])])
    def test_offer_terms(self,
                         offer_terms_setting_value,
                         fx_fn_consumer_page: ConsumerPage,
                         message: MessageEntity,
                         fx_fn_chat_manager_send_message):
        offer = fx_fn_consumer_page.view_offer(message.offers[0])
        with allure.step("CHECK - if consumer can view an offer"):
            assert offer.txt_terms.exists(2) == bool(offer_terms_setting_value)
        offer.close()


@allure.feature('Consumer')
@allure.story('Setting')
# @pytest.mark.ktf(reason="AQA-1", strict=False)
class TestXTimeTermsSetting:

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request, fx_fn_set_offer_terms_setting, consumer_name) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @fixture(scope="function")
    @allure.title("Lead info")
    def lead_info(self, consumer_name):
        last_name = names.get_last_name()
        return WebLeadFactory.xtime(first_name=consumer_name,
                                    last_name=last_name,
                                    vehicle_year="2018",
                                    vehicle_make="Kia",
                                    vehicle_model="Sorento",
                                    appointment_services=[ServicesType.REPLACE_TIRE, ServicesType.ROTATE_TIRES],
                                    notes="Terms Setting",
                                    transport_type=TransportType.DROPOFF)

    @fixture(scope="function")
    def fx_fn_set_offer_terms_setting(self, request, offer_terms_setting_value):
        def finalizer():
            if offer_terms_setting_value == 1:
                Gb1Settings.set_offer_terms_setting(offer_id=xtime_id, offer_terms_value=0)
                Gb1Settings.set_xtime_brand_setting(offer_id=xtime_id, brand="")

        request.addfinalizer(finalizer)

        Gb1Settings.set_offer_terms_setting(offer_id=xtime_id, offer_terms_value=offer_terms_setting_value)
        Gb1Settings.set_xtime_brand_setting(offer_id=xtime_id, brand="")

    @pytest.mark.parametrize("offer_terms_setting_value", [
        pytest.param(0, marks=[pytestrail.case('C38051')]),
        pytest.param(1, marks=[pytestrail.case('C38052')])])
    def test_xtime_terms(self, offer_terms_setting_value, fx_fn_consumer_page, lead_info):
        fx_fn_consumer_page.xtime.lead = lead_info
        fx_fn_consumer_page.xtime.contact.open()
        displayed = fx_fn_consumer_page.xtime.contact.txt_offer_terms.exists(2) == bool(offer_terms_setting_value)
        fx_fn_consumer_page.xtime.submit_service_scheduling(lead_info)
        with allure.step("CHECK - if consumer can see terms and condition"):
            Leads.find_service_appointment_lead(lead_info)
            assert displayed
