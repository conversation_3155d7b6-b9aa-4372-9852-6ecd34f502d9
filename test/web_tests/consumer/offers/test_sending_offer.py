import allure
import pytest
from pytest import fixture

from kom_framework.src.web.drivers import UserAgents
from src import offer_id, da_trigger_id, db_trigger_id, bb_trigger_id, po_trigger_id
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.web.chat_console.chat_console_page import ChatConsolePage
from src.web.consumer import AIBotResponse
from src.web.entities.message_entity import MessageFactory, MessageEntity
from test import pytestrail
from test.web_tests import device_mode, ConsumerPage


@allure.feature('Consumer')
@allure.story('Offer')
class TestSendingOffers:

    @fixture(scope="class")
    @allure.title("Message")
    def message(self):
        return MessageFactory.offers(["$50 Towards Purchase Or Lease"])

    @pytestrail.case('C32')
    def test_consumer_can_receive_offer(self,
                                        fx_cl_chat_manager_page: ChatConsolePage,
                                        fx_cl_consumer_page: ConsumerPage,
                                        message: MessageEntity,
                                        fx_cl_chat_manager_send_message):
        with allure.step("CHECK - if consumer can receive an offer"):
            assert fx_cl_consumer_page.chat_component.tbl_chat_content.get_row_by_column_value("offer_title", message.offers[0],
                                                                                  wait_time=5, reversed_order=True)

    @pytestrail.case('C33')
    @pytest.mark.ktf(reason='CU-3014', condition=device_mode in [UserAgents.ANDROID, UserAgents.IOS])
    def test_consumer_can_view_offer(self,
                                     fx_cl_chat_manager_page: ChatConsolePage,
                                     fx_cl_consumer_page: ConsumerPage,
                                     message: MessageEntity,
                                     fx_cl_chat_manager_send_message):
        offer = fx_cl_consumer_page.view_offer(message.offers[0])
        with allure.step("CHECK - if consumer can view an offer"):
            assert offer.exists()
        offer.close()

    @pytestrail.case('C34')
    @pytest.mark.skipif(device_mode in [UserAgents.IOS, UserAgents.ANDROID], reason="Not Applicable for mobile devices")
    def test_consumer_can_ask_about_offer(self,
                                          fx_cl_chat_manager_page: ChatConsolePage,
                                          fx_cl_consumer_page: ConsumerPage,
                                          message: MessageEntity,
                                          fx_cl_chat_manager_send_message):
        fx_cl_consumer_page.ask_about_offer(message.offers[0])
        chat = fx_cl_chat_manager_page.chat_component.get_chat_content(fx_cl_consumer_page.get_chat_id(wait_time=3))
        with allure.step("CHECK - if chat manager can see a question about the offer"):
            chat.get_row_by_column_value("body", f'I have a question about "{message.offers[0]}"',
                                         wait_time=5, reversed_order=True)


@allure.feature('Consumer')
@allure.story('Offer')
class TestOfferEngagement:

    @fixture(scope="class")
    def fx_cl_offer_chat_onclick_event_setting(self, request):
        def finalizer():
            Gb1Settings.set_offer_chat_onclick_event_setting(offer_id=offer_id, event_id="0")
        request.addfinalizer(finalizer)

        Gb1Settings.set_offer_chat_onclick_event_setting(offer_id=offer_id, event_id="1")

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request, consumer_name, fx_cl_offer_chat_onclick_event_setting) -> ConsumerPage:
        def finalizer():
            consumer.quit()
        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @fixture(scope="function")
    def fx_fn_activate_bd_engagement(self, request, tr_id_value=db_trigger_id):
        def finalizer():
            Gb1Settings.set_bd_trigger_status(tr_id=tr_id_value, status="0", enabled_value="0")
        request.addfinalizer(finalizer)
        Gb1Settings.set_bd_trigger_status(tr_id=tr_id_value, status="1", enabled_value="1")

    @pytestrail.case('C236460')
    def test_consumer_can_view_db_offer_linked_to_live_play(self, fx_fn_activate_bd_engagement, fx_fn_consumer_page: ConsumerPage):
        fx_fn_consumer_page.db_trigger()
        with allure.step("CHECK - if consumer can view an offer"):
            fx_fn_consumer_page.offer.offer_name = "$50 Towards Purchase Or Lease"
            offer_form = fx_fn_consumer_page.offer.exists(2)
            offer_in_chat = fx_fn_consumer_page.chat_component.get_offer_from_chat(fx_fn_consumer_page.offer.offer_name, wait_time=10)
            live_play = []
            for response in AIBotResponse.LIVE_PLAY:
                if not fx_fn_consumer_page.chat_component.get_message(response):
                    live_play.append(f"'{response}' did not appear")
            assert not live_play and not offer_form and offer_in_chat

    @fixture(scope="function")
    def fx_fn_activate_da_engagement(self, request, tr_id_value=da_trigger_id):
        def finalizer():
            Gb1Settings.set_da_trigger_status(tr_id=tr_id_value, status="0", enabled_value="0")
        request.addfinalizer(finalizer)
        Gb1Settings.set_da_trigger_status(tr_id=tr_id_value, status="1", enabled_value="1")

    @pytestrail.case('C236457')
    def test_consumer_can_view_da_offer_linked_to_live_play(self, fx_fn_activate_da_engagement, fx_fn_consumer_page: ConsumerPage):
        fx_fn_consumer_page.da_trigger()
        with allure.step("CHECK - if consumer can view an offer"):
            fx_fn_consumer_page.offer.offer_name = "$50 Towards Purchase Or Lease"
            offer_form = fx_fn_consumer_page.offer.exists(2)
            offer_in_chat = fx_fn_consumer_page.chat_component.get_offer_from_chat(fx_fn_consumer_page.offer.offer_name, wait_time=10)
            live_play = []
            for response in AIBotResponse.LIVE_PLAY:
                if not fx_fn_consumer_page.chat_component.get_message(response):
                    live_play.append(f"'{response}' did not appear")
            assert not live_play and not offer_form and offer_in_chat

    @fixture(scope="function")
    def fx_fn_activate_bb_engagement(self, request, tr_id_value=bb_trigger_id):
        def finalizer():
            Gb1Settings.set_bb_trigger_status(tr_id=tr_id_value, status="0", enabled_value="0")
        request.addfinalizer(finalizer)
        Gb1Settings.set_bb_trigger_status(tr_id=tr_id_value, status="1", enabled_value="1")

    @pytestrail.case('C236459')
    def test_consumer_can_view_bb_offer_linked_to_live_play(self, fx_fn_activate_bb_engagement, fx_fn_consumer_page: ConsumerPage):
        fx_fn_consumer_page.bb_trigger()
        with allure.step("CHECK - if consumer can view an offer"):
            fx_fn_consumer_page.offer.offer_name = "$50 Towards Purchase Or Lease"
            offer_form = fx_fn_consumer_page.offer.exists(2)
            offer_in_chat = fx_fn_consumer_page.chat_component.get_offer_from_chat(fx_fn_consumer_page.offer.offer_name, wait_time=10)
            live_play = []
            for response in AIBotResponse.LIVE_PLAY:
                if not fx_fn_consumer_page.chat_component.get_message(response):
                    live_play.append(f"'{response}' did not appear")
            assert not live_play and not offer_form and offer_in_chat

    @fixture(scope="function")
    def fx_fn_activate_po_engagement(self, request, tr_id_value=po_trigger_id):
        def finalizer():
            Gb1Settings.set_po_trigger_status(tr_id=tr_id_value, status="0", enabled_value="0")
        request.addfinalizer(finalizer)
        Gb1Settings.set_po_trigger_status(tr_id=tr_id_value, status="1", enabled_value="1")

    @pytestrail.case('C236458')
    def test_consumer_can_view_po_offer_linked_to_live_play(self, fx_fn_activate_po_engagement, fx_fn_consumer_page: ConsumerPage):
        fx_fn_consumer_page.po_trigger(po_trigger_id)
        with allure.step("CHECK - if consumer can view an offer"):
            fx_fn_consumer_page.offer.offer_name = "$50 Towards Purchase Or Lease"
            offer_form = fx_fn_consumer_page.offer.exists(2)
            offer_in_chat = fx_fn_consumer_page.chat_component.get_offer_from_chat(fx_fn_consumer_page.offer.offer_name, wait_time=10)
            live_play = []
            for response in AIBotResponse.LIVE_PLAY:
                if not fx_fn_consumer_page.chat_component.get_message(response):
                    live_play.append(f"'{response}' did not appear")
            assert not live_play and not offer_form and offer_in_chat




