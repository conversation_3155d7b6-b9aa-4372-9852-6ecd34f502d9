import allure
import names
import pytest
from pytest import fixture

from src import xtime_id
from src.rest_api.services.leads import Leads
from src.utils.admin_tools.gb1_settings import Gb1Settings
from src.web.consumer import ServicesType, TransportType
from src.web.entities.lead_entity import WebLeadFactory
from test import pytestrail
from test.web_tests import ConsumerPage


@allure.feature('Consumer')
@allure.story('XTimeService')
@pytest.mark.ktf(reason="AQA-1", strict=False)
class TestXTimeServiceSubmit:

    @fixture(scope="class")
    def fx_cl_set_time_zone(self, request):
        def finalizer():
            Gb1Settings.set_system_timezone_setting(system_timezone_value='America/New_York')

        request.addfinalizer(finalizer)

        Gb1Settings.set_system_timezone_setting(system_timezone_value="America/Los_Angeles")

    @fixture(scope="class")
    @allure.title("Open Consumer Page")
    def fx_cl_consumer_page(self, request, consumer_name, fx_cl_set_time_zone) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self, consumer_name):
        last_name = names.get_last_name()
        return WebLeadFactory.xtime(first_name=consumer_name,
                                    last_name=last_name,
                                    vehicle_year="2011",
                                    vehicle_make="Toyota",
                                    vehicle_model="Camry",
                                    appointment_services=[ServicesType.ROTATE_TIRES],
                                    notes="This is a test",
                                    transport_type=TransportType.DROPOFF)

    @fixture(scope="class")
    def submit_xtime(self, fx_cl_consumer_page, lead_info):
        return fx_cl_consumer_page.xtime.submit_service_scheduling(lead_info)

    @pytestrail.case('C38050')
    def test_consumer_can_submit_xtime(self, submit_xtime):
        with allure.step("CHECK - if filled offer appeared in leads"):
            Leads.find_service_appointment_lead(submit_xtime)


@allure.feature('Consumer')
@allure.story('XTimeService')
@pytest.mark.ktf(reason="AQA-1", strict=False)
class TestXTimeBrand:

    @fixture(scope="function")
    @allure.title("Open Consumer Page")
    def fx_fn_consumer_page(self, request, consumer_name, fx_cl_set_xtime_brand_setting) -> ConsumerPage:
        def finalizer():
            consumer.quit()

        request.addfinalizer(finalizer)
        consumer = ConsumerPage(consumer_name).open()
        return consumer

    @fixture(scope="class")
    def fx_cl_set_xtime_brand_setting(self, request):
        def finalizer():
            Gb1Settings.set_xtime_brand_setting(offer_id=xtime_id, brand="")

        request.addfinalizer(finalizer)

        Gb1Settings.set_xtime_brand_setting(offer_id=xtime_id, brand="Honda;Nissan")

    @fixture(scope="function")
    def xtime_services(self, fx_fn_consumer_page, lead_info):
        fx_fn_consumer_page.xtime.lead = lead_info
        return fx_fn_consumer_page.xtime.services.open()

    @pytest.mark.parametrize("lead_info", [
        pytest.param(WebLeadFactory.xtime(vehicle_year="2011", vehicle_make="Honda", vehicle_model="CR-V"),
                     marks=[pytestrail.case('C47204')]),
        pytest.param(WebLeadFactory.xtime(vehicle_make="Other"),
                     marks=[pytestrail.case('C47205'), pytest.mark.ktf(reason='CU-4306')])])
    def test_xtime_brand(self, xtime_services, lead_info):
        with allure.step("CHECK - if filled offer appeared in leads"):
            assert xtime_services.exists(3)


@allure.feature('Consumer')
@allure.story('XTimeService')
@pytest.mark.ktf(reason="AQA-1", strict=False)
class TestTermsLinkXTime:

    @fixture(scope="class")
    @allure.title("Open Consumer Page")
    def fx_cl_consumer_page(self, request, consumer_name, fx_cl_set_offer_terms_setting, lead_info) -> ConsumerPage:
        def finalizer():
            consumer.quit()
        request.addfinalizer(finalizer)

        consumer = ConsumerPage(consumer_name).open()
        consumer.xtime.lead = lead_info
        consumer.xtime.contact.open()
        return consumer

    @fixture(scope="class")
    @allure.title("Lead info")
    def lead_info(self):
        return WebLeadFactory.xtime(vehicle_year="2019",
                                    vehicle_make="Mitsubishi",
                                    vehicle_model="Outlander",
                                    appointment_services=[ServicesType.TIRE_ROTATE_AND_BALANCE, ServicesType.BRAKE_SYSTEM_DIAGNOSIS],
                                    notes="Terms Link",
                                    transport_type=TransportType.DROPOFF)

    @fixture(scope="class")
    def fx_cl_set_offer_terms_setting(self, request):
        def finalizer():
            Gb1Settings.set_offer_terms_setting(offer_id=xtime_id, offer_terms_value=0)
        request.addfinalizer(finalizer)

        Gb1Settings.set_offer_terms_setting(offer_id=xtime_id, offer_terms_value=1)

    @fixture(scope="function")
    def fx_fn_close_terms(self, request, fx_cl_consumer_page):
        def finalizer():
            fx_cl_consumer_page.xtime.btn_close_terms.click()
        request.addfinalizer(finalizer)

    @pytestrail.case('C179895')
    def test_consumer_can_view_terms_from_xtime_left_panel(self, fx_cl_consumer_page, fx_fn_close_terms):
        fx_cl_consumer_page.xtime.btn_left_terms.click()
        with allure.step("CHECK - if consumer can view xtime terms in left panel"):
            assert fx_cl_consumer_page.xtime.txt_terms_title.exists(5)

    @pytestrail.case('C179896')
    def test_consumer_can_view_terms_from_xtime_main_panel(self, fx_cl_consumer_page, fx_fn_close_terms):
        fx_cl_consumer_page.xtime.btn_terms.click()
        with allure.step("CHECK - if consumer can view xtime terms in contact panel"):
            assert fx_cl_consumer_page.xtime.txt_terms_title.exists(5)
